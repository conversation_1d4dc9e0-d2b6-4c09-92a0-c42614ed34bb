#!/bin/zsh



#Prerequiste 
# 1. AppStoreconnect -> Integration , create API key 
# 2. Download the .p8 file, and put in the ~/.private_keys/ , take note of the API KEY id and IssueerID  - on Appstore Connect page
# 3. replace the apiKey & issue id here

API_KEY='NRU65NY88D'
ISSUER_ID='eca3e109-3d9e-4a22-9139-ce54664ba37b'
IPA_FILE='build/ios/ipa/Family Link.ipa'


xcrun altool --upload-app --type ios -f $IPA_FILE --apiKey $API_KEY --apiIssuer $ISSUER_ID