# FamilyLink App Documentation

This directory contains comprehensive documentation for the FamilyLink app features, architecture, and implementation details.

## Documentation Structure

```
docs/
├── README.md                    # This file - Documentation index
├── lsnb_scenarios.md           # Complete LSNB scenarios and implementation
└── lsnb_test_summary.md        # LSNB test results and coverage summary
```

## Features Documentation

### LSNB (Local Storage Notification Bridge)
- **Complete Scenarios:** `lsnb_scenarios.md` - Comprehensive documentation covering all UI/UX scenarios, data flows, and implementation details
- **Test Results:** `lsnb_test_summary.md` - Test coverage, results, and organization details

#### What is LSNB?
The Local Storage Notification Bridge handles thread notifications across all app states without relying on Firebase Dynamic Links. It provides:
- Seamless notification handling in foreground, background, and terminated app states
- Robust data persistence and error handling
- Comprehensive test coverage with 24 passing tests
- Feature-based organization for maintainability

## Documentation Standards

### Structure
- **Overview**: High-level feature description
- **Scenarios**: Detailed use cases and edge cases
- **Implementation**: Technical details and code examples
- **Testing**: Coverage and results
- **Integration**: How features work together

### Format
- Markdown format for easy reading and version control
- Code examples and flow diagrams where helpful
- Clear table of contents for navigation
- Cross-references between related documents

## Adding New Documentation

1. Create a new markdown file in the `docs/` directory
2. Follow the established structure and format
3. Update this README with the new documentation
4. Include cross-references to related documents

## Quick Links

- [LSNB Complete Scenarios](./lsnb_scenarios.md)
- [LSNB Test Results](./lsnb_test_summary.md)
- [Test Features Organization](../test/features/README.md) 