import 'package:flutter_test/flutter_test.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';

void main() {
  group('ChatSuggestionModel Pure JSON Parsing Tests', () {
    test('should handle the exact pure JSON example from user request', () {
      final json = {
        'message': '''{
  "confirmation_message": "I've added an extra day to your trip. The new end date is June 21, 2025.",
  "patch": [
    {
      "op": "replace",
      "path": "/to_date",
      "value": "2025-06-21T23:59:59.020Z"
    },
    {
      "op": "add",
      "path": "/itinerary/7",
      "value": {
        "accommodation": "Hotel in Bangkok",
        "activities": [
          {
            "time": "AM",
            "description": "Relax at the hotel or enjoy a leisurely breakfast at a local café.",
            "venue": "Local Café",
            "activityImage": "activity-img-place-holder",
            "city": "Bangkok"
          },
          {
            "time": "PM",
            "description": "Visit the Bangkok Art and Culture Centre for some cultural enrichment.",
            "venue": "Bangkok Art and Culture Centre",
            "activityImage": "activity-img-place-holder",
            "city": "Bangkok"
          }
        ],
        "foodAndUrl": {
          "Thai Iced Tea": "item1-url-place-holder",
          "Pad See Ew": "item2-url-place-holder"
        },
        "imageUrl": "url-activity-place-holder"
      }
    },
    {
      "op": "replace",
      "path": "/hotel_preferences/check_out_date",
      "value": "2025-06-21T00:00:00.000Z"
    },
    {
      "op": "replace",
      "path": "/flight_preferences/return_date",
      "value": "2025-06-21T00:00:00.000Z"
    }
  ]
}''',
      };

      final mockTrip = MockTrip({
        "name": "Bangkok Adventure",
        "to_date": "2025-06-20T23:59:59.020Z",
        "budget": 2000.0,
        "city": "Bangkok",
        "itinerary": [
          {"day": 1, "activities": []},
          {"day": 2, "activities": []},
          {"day": 3, "activities": []},
          {"day": 4, "activities": []},
          {"day": 5, "activities": []},
          {"day": 6, "activities": []},
          {"day": 7, "activities": []},
        ],
        "hotel_preferences": {
          "check_out_date": "2025-06-20T00:00:00.000Z"
        },
        "flight_preferences": {
          "return_date": "2025-06-20T00:00:00.000Z"
        }
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      // Should extract the confirmation message
      expect(model.message, equals("I've added an extra day to your trip. The new end date is June 21, 2025."));
      
      // Should have applied the patches
      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['to_date'], equals('2025-06-21T23:59:59.020Z'));
      
      // Should have added the new itinerary day
      expect(model.aiTrip!['itinerary'], hasLength(8));
      expect(model.aiTrip!['itinerary'][7]['accommodation'], equals('Hotel in Bangkok'));
      expect(model.aiTrip!['itinerary'][7]['activities'], hasLength(2));
      expect(model.aiTrip!['itinerary'][7]['activities'][0]['time'], equals('AM'));
      expect(model.aiTrip!['itinerary'][7]['activities'][1]['time'], equals('PM'));
      
      // Should have updated hotel and flight preferences
      expect(model.aiTrip!['hotel_preferences']['check_out_date'], equals('2025-06-21T00:00:00.000Z'));
      expect(model.aiTrip!['flight_preferences']['return_date'], equals('2025-06-21T00:00:00.000Z'));
      
      // Should not be editing complete
      expect(model.editingComplete, isFalse);
    });

    test('should handle pure JSON with complex nested structures', () {
      final json = {
        'message': '''{
  "confirmation_message": "Updated your trip with new activities and preferences.",
  "patch": [
    {
      "op": "add",
      "path": "/itinerary/0/activities/-",
      "value": {
        "time": "Evening",
        "description": "Sunset dinner cruise",
        "venue": "Chao Phraya River",
        "city": "Bangkok"
      }
    }
  ]
}''',
      };

      final mockTrip = MockTrip({
        "name": "Bangkok Trip",
        "itinerary": [
          {
            "day": 1,
            "activities": [
              {"time": "Morning", "description": "Temple visit"}
            ]
          }
        ]
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.message, equals("Updated your trip with new activities and preferences."));
      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['itinerary'][0]['activities'], hasLength(2));
      expect(model.aiTrip!['itinerary'][0]['activities'][1]['time'], equals('Evening'));
    });

    test('should handle malformed pure JSON gracefully', () {
      final json = {
        'message': '{"confirmation_message": "Invalid JSON", "patch": [',
      };

      final mockTrip = MockTrip({
        "name": "Test Trip",
        "budget": 1000.0,
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      // Should fallback to original message when JSON is malformed
      expect(model.message, equals('{"confirmation_message": "Invalid JSON", "patch": ['));
      expect(model.aiTrip, isNull);
      expect(model.editingComplete, isFalse);
    });

    test('should handle pure JSON for general purpose (non-edit trip)', () {
      final json = {
        'message': '{"suggestion": ["Tokyo", "Kyoto", "Osaka"], "ai_trip": {"name": "Japan Adventure", "duration": "7 days"}}',
      };

      final model = ChatSuggestionModel.fromJson(json);

      expect(model.suggestions, equals(['Tokyo', 'Kyoto', 'Osaka']));
      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['name'], equals('Japan Adventure'));
      expect(model.aiTrip!['duration'], equals('7 days'));
      expect(model.message, equals('')); // Pure JSON should have empty message
    });

    test('should distinguish between pure JSON and text with JSON', () {
      final pureJsonMessage = {
        'message': '{"confirmation_message": "Pure JSON response"}',
      };

      final textWithJsonMessage = {
        'message': 'Here is some text {"confirmation_message": "Mixed response"} with more text',
      };

      final pureJsonModel = ChatSuggestionModel.fromJson(
        pureJsonMessage,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: MockTrip({"name": "Test"}),
      );

      final mixedModel = ChatSuggestionModel.fromJson(
        textWithJsonMessage,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: MockTrip({"name": "Test"}),
      );

      // Pure JSON should extract confirmation message and have empty cleaned message
      expect(pureJsonModel.message, equals('Pure JSON response'));
      
      // Mixed content should extract confirmation message but keep surrounding text
      expect(mixedModel.message, equals('Mixed response'));
    });
  });
}

// Simple mock trip class for testing
class MockTrip {
  final Map<String, dynamic> _data;

  MockTrip(this._data);

  Map<String, dynamic> toJson() => Map<String, dynamic>.from(_data);
}
