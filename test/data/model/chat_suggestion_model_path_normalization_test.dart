import 'package:flutter_test/flutter_test.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';

void main() {
  group('ChatSuggestionModel Path Normalization Tests', () {
    test('should normalize /ai_trip/ prefix in patch paths', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Updated your trip end date!",
  "patch": [
    {
      "op": "replace",
      "path": "/ai_trip/to_date",
      "value": "2025-01-21T23:59:59.999Z"
    }
  ]
}
```
''',
      };

      // Mock trip object
      final mockTrip = MockTrip({
        "name": "Paris Adventure",
        "to_date": "2025-01-20T23:59:59.999Z",
        "from_date": "2025-01-15T00:00:00.000Z",
        "budget": 1500.0
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Updated your trip end date!'));

      // Should successfully apply the patch after normalizing the path
      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['to_date'], equals('2025-01-21T23:59:59.999Z'));
      expect(modifiedTrip['name'], equals('Paris Adventure'));
    });

    test('should normalize /ai_trip/ prefix in move operation paths', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Reordered your itinerary!",
  "patch": [
    {
      "op": "move",
      "from": "/ai_trip/itinerary/1",
      "path": "/ai_trip/itinerary/0"
    }
  ]
}
```
''',
      };

      final mockTrip = MockTrip({
        "name": "Tokyo Trip",
        "itinerary": [
          {
            "day": 1,
            "activities": [
              {"activity": "Visit Temple"}
            ]
          },
          {
            "day": 2,
            "activities": [
              {"activity": "Shopping"}
            ]
          }
        ]
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Reordered your itinerary!'));

      // Should successfully apply the move operation after normalizing paths
      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['itinerary'], hasLength(2));
      // After move operation, day 2 should be at index 0
      expect(modifiedTrip['itinerary'][0]['day'], equals(2));
    });

    test('should handle multiple patches with /ai_trip/ prefix', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Updated multiple fields!",
  "patch": [
    {
      "op": "replace",
      "path": "/ai_trip/name",
      "value": "Amazing Paris Trip"
    },
    {
      "op": "replace",
      "path": "/ai_trip/budget",
      "value": 2000.0
    }
  ]
}
```
''',
      };

      final mockTrip =
          MockTrip({"name": "Paris Trip", "budget": 1500.0, "city": "Paris"});

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Updated multiple fields!'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['name'], equals('Amazing Paris Trip'));
      expect(modifiedTrip['budget'], equals(2000.0));
      expect(modifiedTrip['city'], equals('Paris')); // Unchanged field
    });

    test('should handle normal paths without /ai_trip/ prefix unchanged', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Updated your trip!",
  "patch": [
    {
      "op": "replace",
      "path": "/to_date",
      "value": "2025-01-21T23:59:59.999Z"
    }
  ]
}
```
''',
      };

      final mockTrip = MockTrip({
        "name": "London Trip",
        "to_date": "2025-01-20T23:59:59.999Z",
        "budget": 1800.0
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Updated your trip!'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['to_date'], equals('2025-01-21T23:59:59.999Z'));
      expect(modifiedTrip['name'], equals('London Trip'));
    });

    test('should handle /itinerary/- append operation with /ai_trip/ prefix',
        () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Added a new day to your itinerary!",
  "patch": [
    {
      "op": "add",
      "path": "/ai_trip/itinerary/-",
      "value": {
        "day": 3,
        "activities": [{"activity": "New Activity"}]
      }
    }
  ]
}
```
''',
      };

      final mockTrip = MockTrip({
        "name": "Barcelona Trip",
        "itinerary": [
          {
            "day": 1,
            "activities": [
              {"activity": "Sagrada Familia"}
            ]
          },
          {
            "day": 2,
            "activities": [
              {"activity": "Park Güell"}
            ]
          }
        ]
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.aiTrip, isNotNull);
      expect(model.message, equals('Added a new day to your itinerary!'));

      final modifiedTrip = model.aiTrip!;
      expect(modifiedTrip['itinerary'], hasLength(3));
      expect(modifiedTrip['itinerary'][2]['day'], equals(3));
      expect(modifiedTrip['itinerary'][2]['activities'][0]['activity'],
          equals('New Activity'));
    });
  });
}

// Simple mock trip class for testing
class MockTrip {
  final Map<String, dynamic> _data;

  MockTrip(this._data);

  Map<String, dynamic> toJson() => Map<String, dynamic>.from(_data);
}
