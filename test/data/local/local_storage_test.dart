import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('LocalStorage LSNB Notification', () {
    late SharedPreferences prefs;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
    });

    test('savePendingNotification stores data correctly', () async {
      final data = {'threadId': 'abc', 'message': 'Test', 'type': 'thread'};
      await prefs.setString('pending_notification_data', jsonEncode(data));
      final stored = prefs.getString('pending_notification_data');
      expect(stored, isNotNull);
      final decoded = jsonDecode(stored!) as Map<String, dynamic>;
      expect(decoded['threadId'], 'abc');
      expect(decoded['message'], 'Test');
      expect(decoded['type'], 'thread');
    });

    test('getPendingNotification retrieves correct data', () async {
      final data = {'threadId': 'xyz', 'message': 'Hello', 'type': 'thread'};
      await prefs.setString('pending_notification_data', jsonEncode(data));
      final stored = prefs.getString('pending_notification_data');
      expect(stored, isNotNull);
      final result = jsonDecode(stored!) as Map<String, dynamic>;
      expect(result['threadId'], 'xyz');
      expect(result['message'], 'Hello');
      expect(result['type'], 'thread');
    });

    test('clearPendingNotification removes data completely', () async {
      final data = {'threadId': 'toRemove'};
      await prefs.setString('pending_notification_data', jsonEncode(data));
      await prefs.remove('pending_notification_data');
      final result = prefs.getString('pending_notification_data');
      expect(result, isNull);
    });

    test('handles empty data correctly', () async {
      final data = <String, dynamic>{};
      await prefs.setString('pending_notification_data', jsonEncode(data));
      final stored = prefs.getString('pending_notification_data');
      expect(stored, isNotNull);
      final result = jsonDecode(stored!) as Map<String, dynamic>;
      expect(result, isEmpty);
    });

    test('handles null data correctly', () async {
      final result = prefs.getString('pending_notification_data');
      expect(result, isNull);
    });

    test('handles complex nested data structures', () async {
      final data = {
        'threadId': 'complex123',
        'message': 'Complex message',
        'metadata': {
          'sender': 'user123',
          'timestamp': DateTime.now().toIso8601String(),
          'attachments': ['image1.jpg', 'image2.jpg']
        },
        'type': 'thread'
      };
      await prefs.setString('pending_notification_data', jsonEncode(data));
      final stored = prefs.getString('pending_notification_data');
      expect(stored, isNotNull);
      final result = jsonDecode(stored!) as Map<String, dynamic>;
      expect(result['threadId'], 'complex123');
      expect(result['metadata']['sender'], 'user123');
      expect(result['metadata']['attachments'], isA<List>());
      expect(result['metadata']['attachments'].length, 2);
    });

    test('handles special characters in data', () async {
      final data = {
        'threadId': 'special@#\$%',
        'message': 'Message with émojis 🎉 and special chars: &<>"\'',
        'type': 'thread'
      };
      await prefs.setString('pending_notification_data', jsonEncode(data));
      final stored = prefs.getString('pending_notification_data');
      expect(stored, isNotNull);
      final result = jsonDecode(stored!) as Map<String, dynamic>;
      expect(result['threadId'], 'special@#\$%');
      expect(result['message'], 'Message with émojis 🎉 and special chars: &<>"\'');
    });

    test('handles large data payloads', () async {
      final largeMessage = 'A' * 10000; // 10KB message
      final data = {'threadId': 'large123', 'message': largeMessage, 'type': 'thread'};
      await prefs.setString('pending_notification_data', jsonEncode(data));
      final stored = prefs.getString('pending_notification_data');
      expect(stored, isNotNull);
      final result = jsonDecode(stored!) as Map<String, dynamic>;
      expect(result['message'], largeMessage);
      expect(result['message'].length, 10000);
    });

    test('handles multiple save/retrieve operations', () async {
      // First save
      final data1 = {'threadId': 'first', 'message': 'First message'};
      await prefs.setString('pending_notification_data', jsonEncode(data1));
      var stored = prefs.getString('pending_notification_data');
      var result = jsonDecode(stored!) as Map<String, dynamic>;
      expect(result['threadId'], 'first');

      // Second save (should overwrite)
      final data2 = {'threadId': 'second', 'message': 'Second message'};
      await prefs.setString('pending_notification_data', jsonEncode(data2));
      stored = prefs.getString('pending_notification_data');
      result = jsonDecode(stored!) as Map<String, dynamic>;
      expect(result['threadId'], 'second');
      expect(result['message'], 'Second message');
    });

    test('handles invalid JSON gracefully', () async {
      // Store invalid JSON
      await prefs.setString('pending_notification_data', 'invalid json {');
      final stored = prefs.getString('pending_notification_data');
      expect(stored, 'invalid json {');

      // Should throw when trying to parse
      expect(() => jsonDecode(stored!), throwsA(isA<FormatException>()));
    });
  });
}
