import 'package:rfc_6902/rfc_6902.dart' as rfc;

void main() {
  // Test simple JSON Patch operation
  final originalData = {
    "name": "Paris Adventure",
    "budget": 1500.0,
    "itinerary": [
      {"day": 1, "activity": "Eiffel Tower"},
      {"day": 2, "activity": "Louvre"}
    ]
  };

  final patches = [
    {
      "op": "replace",
      "path": "/budget",
      "value": 2000.0
    }
  ];

  print('Original data: $originalData');
  print('Patches: $patches');

  try {
    final jsonPatch = rfc.JsonPatch(patches);
    final result = jsonPatch.applyTo(originalData);
    
    print('Result type: ${result.runtimeType}');
    print('Result: $result');
    print('Is Map: ${result is Map}');
    print('Is Map<String, dynamic>: ${result is Map<String, dynamic>}');
  } catch (e) {
    print('Error: $e');
  }
}
