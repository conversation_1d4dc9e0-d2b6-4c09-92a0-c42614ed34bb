# Test Features Directory

This directory organizes tests by feature for better maintainability and scalability.

## Structure

```
test/features/
├── README.md                    # This file
├── lsnb/                        # Local Storage Notification Bridge feature
│   ├── lsnb_test_summary.md     # Test results and coverage summary
│   ├── notification_service_lsn_test.dart
│   ├── notification_service_lsn_test.mocks.dart
│   ├── lsnb_integration_test.dart
│   └── lsnb_integration_test.mocks.dart
└── [future-features]/           # Additional features will be added here
```

## Organization Principles

1. **Feature-based**: Each feature gets its own directory
2. **Self-contained**: All test files for a feature are grouped together
3. **Documentation**: Each feature includes a summary of test coverage and results
4. **Scalable**: Easy to add new features without cluttering the main test directory

## Running Tests

### Run all feature tests:
```bash
flutter test test/features/
```

### Run specific feature tests:
```bash
flutter test test/features/lsnb/
```

### Run specific test file:
```bash
flutter test test/features/lsnb/notification_service_lsn_test.dart
```

## Adding New Features

1. Create a new directory: `test/features/[feature-name]/`
2. Add test files with descriptive names
3. Include a summary file documenting test coverage
4. Update this README with the new feature

## Current Features

### LSNB (Local Storage Notification Bridge)
- **Purpose**: Handles thread notifications across all app states without Firebase Dynamic Links
- **Test Coverage**: 24 tests covering unit, integration, and edge cases
- **Status**: ✅ All tests passing
- **Files**: 5 test files + 1 summary 