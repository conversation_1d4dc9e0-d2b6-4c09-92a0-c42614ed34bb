import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:family_app/config/service/notification/notification_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import 'lsnb_integration_test.mocks.dart';

@GenerateMocks([AccountService, FirebaseMessaging, FlutterLocalNotificationsPlugin])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('LSNB Integration Tests', () {
    late SharedPreferences prefs;
    late MockAccountService mockAccountService;
    late MockFirebaseMessaging mockMessaging;
    late MockFlutterLocalNotificationsPlugin mockPlugin;
    late NotificationService notificationService;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();

      mockAccountService = MockAccountService();
      mockMessaging = MockFirebaseMessaging();
      mockPlugin = MockFlutterLocalNotificationsPlugin();

      // Create a simple LocalStorage implementation for testing
      final testLocalStorage = TestLocalStorage(prefs);

      notificationService = NotificationService(
        localStorage: testLocalStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
    });

    test('Complete LSNB flow: store -> retrieve -> clear', () async {
      // Simulate notification received while app is terminated
      final notificationData = {
        'threadId': 'integration123',
        'type': 'thread',
        'message': 'Integration test message',
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Step 1: Store notification data
      await prefs.setString('pending_notification_data', jsonEncode(notificationData));

      // Step 2: Verify data is stored
      final storedString = prefs.getString('pending_notification_data');
      expect(storedString, isNotNull);
      final storedData = jsonDecode(storedString!) as Map<String, dynamic>;
      expect(storedData['threadId'], 'integration123');
      expect(storedData['type'], 'thread');

      // Step 3: Simulate app startup and health check
      final healthStatus = await notificationService.healthCheck();
      expect(healthStatus['status'], 'healthy');
      expect(healthStatus['hasPendingNotification'], true);
      expect(healthStatus['pendingNotificationType'], 'thread');

      // Step 4: Clear notification data (simulate after processing)
      await prefs.remove('pending_notification_data');

      // Step 5: Verify data is cleared
      final clearedString = prefs.getString('pending_notification_data');
      expect(clearedString, isNull);

      // Step 6: Verify health check reflects cleared state
      final finalHealthStatus = await notificationService.healthCheck();
      expect(finalHealthStatus['hasPendingNotification'], false);
      expect(finalHealthStatus['pendingNotificationType'], 'none');
    });

    test('LSNB handles multiple notifications correctly', () async {
      // First notification
      final notification1 = {
        'threadId': 'first123',
        'type': 'thread',
        'timestamp': DateTime.now().toIso8601String(),
      };
      await prefs.setString('pending_notification_data', jsonEncode(notification1));

      var healthStatus = await notificationService.healthCheck();
      expect(healthStatus['hasPendingNotification'], true);
      expect(healthStatus['pendingNotificationType'], 'thread');

      // Second notification (should overwrite first)
      final notification2 = {
        'threadId': 'second456',
        'type': 'event',
        'timestamp': DateTime.now().toIso8601String(),
      };
      await prefs.setString('pending_notification_data', jsonEncode(notification2));

      healthStatus = await notificationService.healthCheck();
      expect(healthStatus['hasPendingNotification'], true);
      expect(healthStatus['pendingNotificationType'], 'event');

      // Verify only the second notification is stored
      final storedString = prefs.getString('pending_notification_data');
      final storedData = jsonDecode(storedString!) as Map<String, dynamic>;
      expect(storedData['threadId'], 'second456');
      expect(storedData['type'], 'event');
    });

    test('LSNB handles app restart with stale data', () async {
      // Simulate old notification data (older than 24 hours)
      final oldTimestamp = DateTime.now().subtract(const Duration(hours: 25)).toIso8601String();
      final staleData = {
        'threadId': 'stale123',
        'type': 'thread',
        'timestamp': oldTimestamp,
      };
      await prefs.setString('pending_notification_data', jsonEncode(staleData));

      // Verify stale data is detected
      final healthStatus = await notificationService.healthCheck();
      expect(healthStatus['hasPendingNotification'], true);
      expect(healthStatus['pendingNotificationTimestamp'], oldTimestamp);

      // Note: In real implementation, periodic cleanup would clear this
      // For test purposes, we manually clear it
      await prefs.remove('pending_notification_data');

      final finalHealthStatus = await notificationService.healthCheck();
      expect(finalHealthStatus['hasPendingNotification'], false);
    });

    test('LSNB handles corrupted data gracefully', () async {
      // Simulate corrupted data in SharedPreferences
      await prefs.setString('pending_notification_data', 'invalid json {');

      // Health check should handle corrupted data gracefully
      final healthStatus = await notificationService.healthCheck();
      expect(healthStatus['status'], 'healthy');
      expect(healthStatus['hasPendingNotification'], false);
    });

    test('LSNB performance under load', () async {
      // Test multiple rapid operations
      final startTime = DateTime.now();

      for (int i = 0; i < 10; i++) {
        final data = {
          'threadId': 'perf$i',
          'type': 'thread',
          'timestamp': DateTime.now().toIso8601String(),
        };

        await prefs.setString('pending_notification_data', jsonEncode(data));
        final retrieved = prefs.getString('pending_notification_data');
        final decoded = jsonDecode(retrieved!) as Map<String, dynamic>;
        expect(decoded['threadId'], 'perf$i');

        await prefs.remove('pending_notification_data');
        final cleared = prefs.getString('pending_notification_data');
        expect(cleared, isNull);
      }

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration.inMilliseconds, lessThan(1000));
    });

    test('LSNB data persistence across service instances', () async {
      // Store data with first service instance
      final data = {
        'threadId': 'persist123',
        'type': 'thread',
        'timestamp': DateTime.now().toIso8601String(),
      };
      await prefs.setString('pending_notification_data', jsonEncode(data));

      // Create new service instance (simulating app restart)
      final testLocalStorage2 = TestLocalStorage(prefs);
      final newNotificationService = NotificationService(
        localStorage: testLocalStorage2,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );

      // Verify data persists
      final healthStatus = await newNotificationService.healthCheck();
      expect(healthStatus['hasPendingNotification'], true);
      expect(healthStatus['pendingNotificationType'], 'thread');

      final storedString = prefs.getString('pending_notification_data');
      final storedData = jsonDecode(storedString!) as Map<String, dynamic>;
      expect(storedData['threadId'], 'persist123');
    });
  });
}

// Simple test implementation of LocalStorage for integration tests
class TestLocalStorage implements LocalStorage {
  final SharedPreferences _prefs;

  TestLocalStorage(this._prefs);

  @override
  Future<void> savePendingNotification(Map<String, dynamic> data) async {
    await _prefs.setString('pending_notification_data', jsonEncode(data));
  }

  @override
  Future<Map<String, dynamic>?> getPendingNotification() async {
    final data = _prefs.getString('pending_notification_data');
    if (data != null) {
      try {
        return jsonDecode(data) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  @override
  Future<void> clearPendingNotification() async {
    await _prefs.remove('pending_notification_data');
  }

  // Implement other required methods with minimal implementations
  @override
  Future<void> cacheAccessToken(String token) async {}
  @override
  Future<void> cacheRefreshToken(String refreshToken) async {}
  @override
  Future<void> cacheUserID(String userId) async {}
  @override
  Future<void> cacheUserRole(String userRole) async {}
  @override
  Future<void> cacheLanguageCode(String language) async {}
  @override
  Future<void> cacheGroupPermission(List<String> permission) async {}
  @override
  Future<void> cacheVerificationId(String verificationId) async {}
  @override
  Future<void> cacheResendToken(int? resendToken) async {}
  @override
  Future<void> cacheUID(String uid) async {}
  @override
  Future<void> cacheRefCode(String refCode) async {}
  @override
  Future<void> cacheEmail(String email) async {}
  @override
  Future<void> cachePassword(String password) async {}
  @override
  Future<void> cacheLoggedIn(bool isLoggedIn) async {}
  @override
  Future<void> cacheSyncWithDeviceCalendar(bool isSync) async {}
  @override
  Future<void> cacheStartOfWeekMonday(bool isStartOfWeekMonday) async {}
  @override
  Future<void> cacheDefaultReminder(int defaultReminder) async {}
  @override
  Future<void> cacheEnabledCalendarIds(Set<String> ids) async {}
  @override
  Future<void> cacheEventInfoList(List<Map<String, String>> eventInfoList) async {}
  @override
  Future<void> cacheAmadeusAccessToken(String token) async {}
  @override
  Future<void> cacheAmadeusTokenExpired(String expired) async {}
  @override
  Future<String?> accessToken() async => null;
  @override
  Future<String?> refreshToken() async => null;
  @override
  Future<String?> userId() async => null;
  @override
  bool loggedIn() => false;
  @override
  Future<String> getEmail() async => '';
  @override
  Future<String> getPassword() async => '';
  @override
  Future<void> setString(String key, String value) async {}
  @override
  Future<String?> getString(String key, {String? defaultValue}) async => defaultValue;
  @override
  Future<void> remove(String key) async {}
  @override
  Future<void> clear() async {}

  // Getters
  String? get amadeusAccessToken => null;
  String? get amadeusTokenExpired => null;
  List<String> get permission => [];
  String get languageCode => 'en';
  String get userRole => '';
  String get verificationId => '';
  int? get resendToken => null;
  String get refCode => '';
  String get uid => '';
  bool get isSyncWithDeviceCalendar => false;
  bool get isStartOfWeekMonday => false;
  int get defaultReminder => 0;
  Set<String> get enabledCalendarIds => {};
  List<Map<String, String>> get eventInfoList => [];
}
