import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:family_app/config/service/notification/notification_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import 'notification_service_lsn_test.mocks.dart';

@GenerateMocks([LocalStorage, AccountService, FirebaseMessaging, FlutterLocalNotificationsPlugin])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('NotificationService LSNB', () {
    test('healthCheck returns healthy with pending notification', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();
      final data = {'threadId': 'xyz', 'type': 'thread', 'timestamp': DateTime.now().toIso8601String()};
      when(mockStorage.getPendingNotification()).thenAnswer((_) async => data);
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
      final result = await service.healthCheck();
      expect(result['status'], 'healthy');
      expect(result['hasPendingNotification'], true);
      expect(result['pendingNotificationType'], 'thread');
      expect(result['pendingNotificationTimestamp'], isA<String>());
      expect(result['timestamp'], isA<String>());
    });

    test('healthCheck returns healthy with no pending notification', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();
      when(mockStorage.getPendingNotification()).thenAnswer((_) async => null);
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
      final result = await service.healthCheck();
      expect(result['status'], 'healthy');
      expect(result['hasPendingNotification'], false);
      expect(result['pendingNotificationType'], 'none');
      expect(result['pendingNotificationTimestamp'], 'none');
      expect(result['timestamp'], isA<String>());
    });

    test('healthCheck handles storage errors gracefully', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();
      when(mockStorage.getPendingNotification()).thenThrow(Exception('Storage error'));
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
      final result = await service.healthCheck();
      expect(result['status'], 'healthy');
      expect(result['hasPendingNotification'], false);
      expect(result['timestamp'], isA<String>());
    });

    test('healthCheck with different notification types', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();

      // Test with event notification
      final eventData = {'threadId': 'event123', 'type': 'event', 'timestamp': DateTime.now().toIso8601String()};
      when(mockStorage.getPendingNotification()).thenAnswer((_) async => eventData);
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
      final result = await service.healthCheck();
      expect(result['pendingNotificationType'], 'event');
    });

    test('healthCheck with complex notification data', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();
      final complexData = {
        'threadId': 'complex123',
        'type': 'thread',
        'timestamp': DateTime.now().toIso8601String(),
        'metadata': {
          'sender': 'user123',
          'message': 'Complex notification',
          'attachments': ['image1.jpg', 'image2.jpg']
        }
      };
      when(mockStorage.getPendingNotification()).thenAnswer((_) async => complexData);
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
      final result = await service.healthCheck();
      expect(result['status'], 'healthy');
      expect(result['hasPendingNotification'], true);
      expect(result['pendingNotificationType'], 'thread');
    });

    test('healthCheck with missing timestamp', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();
      final dataWithoutTimestamp = {'threadId': 'no-timestamp', 'type': 'thread'};
      when(mockStorage.getPendingNotification()).thenAnswer((_) async => dataWithoutTimestamp);
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
      final result = await service.healthCheck();
      expect(result['status'], 'healthy');
      expect(result['hasPendingNotification'], true);
      expect(result['pendingNotificationTimestamp'], 'none');
    });

    test('healthCheck with empty notification data', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();
      final emptyData = <String, dynamic>{};
      when(mockStorage.getPendingNotification()).thenAnswer((_) async => emptyData);
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );
      final result = await service.healthCheck();
      expect(result['status'], 'healthy');
      expect(result['hasPendingNotification'], true);
      expect(result['pendingNotificationType'], 'none');
    });

    test('healthCheck performance - multiple calls', () async {
      final mockStorage = MockLocalStorage();
      final mockAccountService = MockAccountService();
      final mockMessaging = MockFirebaseMessaging();
      final mockPlugin = MockFlutterLocalNotificationsPlugin();
      when(mockStorage.getPendingNotification()).thenAnswer((_) async => null);
      final service = NotificationService(
        localStorage: mockStorage,
        accountService: mockAccountService,
        messaging: mockMessaging,
        plugin: mockPlugin,
      );

      // Multiple health checks should work consistently
      final result1 = await service.healthCheck();
      final result2 = await service.healthCheck();
      final result3 = await service.healthCheck();

      expect(result1['status'], 'healthy');
      expect(result2['status'], 'healthy');
      expect(result3['status'], 'healthy');

      // Verify getPendingNotification was called multiple times
      verify(mockStorage.getPendingNotification()).called(3);
    });
  });
}
