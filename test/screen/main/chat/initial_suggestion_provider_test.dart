import 'package:flutter_test/flutter_test.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/initial_suggestion_provider.dart';

void main() {
  group('InitialSuggestionProvider', () {
    test('should return edit trip suggestions for editATrip purpose', () {
      // Arrange
      const purposeKey = AIPurposeKey.editATrip;

      // Act
      final suggestions = InitialSuggestionProvider.getSuggestions(purposeKey);

      // Assert
      expect(suggestions, isNotEmpty);
      expect(suggestions, contains('Looking for restaurants?'));
      expect(suggestions, contains('Modify your plan'));
      expect(suggestions, contains('Add more activities'));
    });

    test('should return customize trip suggestions for customizeTrip purpose', () {
      // Arrange
      const purposeKey = AIPurposeKey.customizeTrip;

      // Act
      final suggestions = InitialSuggestionProvider.getSuggestions(purposeKey);

      // Assert
      expect(suggestions, isNotEmpty);
      expect(suggestions, contains('Add more days to the trip'));
      expect(suggestions, contains('Change hotel preferences'));
      expect(suggestions, contains('Include family-friendly activities'));
    });

    test('should return plan trip suggestions for planATrip purpose', () {
      // Arrange
      const purposeKey = AIPurposeKey.planATrip;

      // Act
      final suggestions = InitialSuggestionProvider.getSuggestions(purposeKey);

      // Assert
      expect(suggestions, isNotEmpty);
      expect(suggestions, contains('Plan a family vacation'));
      expect(suggestions, contains('Weekend getaway ideas'));
      expect(suggestions, contains('Budget-friendly destinations'));
    });

    test('should return general suggestions for generalPurpose', () {
      // Arrange
      const purposeKey = AIPurposeKey.generalPurpose;

      // Act
      final suggestions = InitialSuggestionProvider.getSuggestions(purposeKey);

      // Assert
      expect(suggestions, isNotEmpty);
      expect(suggestions, contains('Plan a trip'));
      expect(suggestions, contains('Create an event'));
      expect(suggestions, contains('Make a list'));
    });

    test('should return event suggestions for planAnEvent purpose', () {
      // Arrange
      const purposeKey = AIPurposeKey.planAnEvent;

      // Act
      final suggestions = InitialSuggestionProvider.getSuggestions(purposeKey);

      // Assert
      expect(suggestions, isNotEmpty);
      expect(suggestions, contains('Birthday party ideas'));
      expect(suggestions, contains('Family gathering'));
    });

    test('should return list suggestions for createAList purpose', () {
      // Arrange
      const purposeKey = AIPurposeKey.createAList;

      // Act
      final suggestions = InitialSuggestionProvider.getSuggestions(purposeKey);

      // Assert
      expect(suggestions, isNotEmpty);
      expect(suggestions, contains('Travel packing list'));
      expect(suggestions, contains('Shopping list'));
    });

    test('should return contextual suggestions with same base suggestions', () {
      // Arrange
      const purposeKey = AIPurposeKey.editATrip;
      const location = 'Paris';
      final date = DateTime.now();

      // Act
      final baseSuggestions = InitialSuggestionProvider.getSuggestions(purposeKey);
      final contextualSuggestions = InitialSuggestionProvider.getContextualSuggestions(
        purposeKey,
        location: location,
        date: date,
      );

      // Assert
      expect(contextualSuggestions, isNotEmpty);
      // For now, contextual suggestions should return the same as base suggestions
      // This can be enhanced later with location/time-specific suggestions
      expect(contextualSuggestions.length, equals(baseSuggestions.length));
    });
  });
}
