import 'package:flutter_test/flutter_test.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_cubit.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_state.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// Generate mocks
@GenerateMocks([AccountService])
import 'visual_trip_planner_cubit_test.mocks.dart';

void main() {
  group('VisualTripPlannerCubit aiTripIntent extraction', () {
    late MockAccountService mockAccountService;

    setUp(() {
      // Setup GetIt with mock dependencies
      GetIt.instance.reset();
      mockAccountService = MockAccountService();
      GetIt.instance.registerSingleton<AccountService>(mockAccountService);

      // Setup mock behavior
      when(mockAccountService.familyId).thenReturn('test-family-id');
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    test('should extract destination from aiTripIntent', () async {
      // Arrange
      final aiTripIntentData = {
        'trip_intent': 'Discovery',
        'destination': 'Boston',
        'duration': '5 days',
        'time': '2025-07-01T00:00:00.000Z',
      };

      final parameter = VisualTripPlannerParameter(
        initialData: aiTripIntentData,
      );

      final testCubit = VisualTripPlannerCubit(parameter: parameter);

      // Act
      await testCubit.onInit();

      // Assert
      final state = testCubit.state;
      expect(state.initialData, isNotNull);
      expect(state.initialData!['extracted_destination'], equals('Boston'));
      expect(state.initialData!['extracted_duration'], equals('5 days'));
      expect(state.initialData!['extracted_time'],
          equals('2025-07-01T00:00:00.000Z'));

      testCubit.close();
    });

    test('should parse duration correctly', () async {
      // Arrange
      final aiTripIntentData = {
        'trip_intent': 'Direct',
        'destination': 'Berlin',
        'duration': '3 nights',
        'time': '2025-08-15T00:00:00.000Z',
      };

      final parameter = VisualTripPlannerParameter(
        initialData: aiTripIntentData,
      );

      final testCubit = VisualTripPlannerCubit(parameter: parameter);

      // Act
      await testCubit.onInit();

      // Assert
      final state = testCubit.state;
      final parsedDuration =
          state.initialData!['parsed_duration'] as Map<String, dynamic>;
      expect(parsedDuration['number'], equals(3));
      expect(parsedDuration['unit'], equals('night'));
      expect(parsedDuration['days'], equals(4)); // 3 nights = 4 days

      testCubit.close();
    });

    test('should parse time correctly', () async {
      // Arrange
      final aiTripIntentData = {
        'trip_intent': 'Discovery',
        'destination': 'Paris',
        'duration': '1 week',
        'time': '2025-09-10T14:30:00.000Z',
      };

      final parameter = VisualTripPlannerParameter(
        initialData: aiTripIntentData,
      );

      final testCubit = VisualTripPlannerCubit(parameter: parameter);

      // Act
      await testCubit.onInit();

      // Assert
      final state = testCubit.state;
      final parsedStartDate =
          state.initialData!['parsed_start_date'] as DateTime;
      expect(parsedStartDate.year, equals(2025));
      expect(parsedStartDate.month, equals(9));
      expect(parsedStartDate.day, equals(10));
      expect(parsedStartDate.hour, equals(14));
      expect(parsedStartDate.minute, equals(30));

      final parsedDuration =
          state.initialData!['parsed_duration'] as Map<String, dynamic>;
      expect(parsedDuration['days'], equals(7)); // 1 week = 7 days

      testCubit.close();
    });

    test('should handle missing aiTripIntent fields gracefully', () async {
      // Arrange
      final aiTripIntentData = {
        'trip_intent': 'Discovery',
        'destination': 'Tokyo',
        // duration and time are missing
      };

      final parameter = VisualTripPlannerParameter(
        initialData: aiTripIntentData,
      );

      final testCubit = VisualTripPlannerCubit(parameter: parameter);

      // Act
      await testCubit.onInit();

      // Assert
      final state = testCubit.state;
      expect(state.initialData!['extracted_destination'], equals('Tokyo'));
      expect(state.initialData!['extracted_duration'], isNull);
      expect(state.initialData!['extracted_time'], isNull);
      expect(state.initialData!['parsed_duration'], isNull);
      expect(state.initialData!['parsed_start_date'], isNull);

      testCubit.close();
    });

    test('should handle invalid duration format gracefully', () async {
      // Arrange
      final aiTripIntentData = {
        'trip_intent': 'Direct',
        'destination': 'London',
        'duration': 'some time', // Invalid format
        'time': '2025-12-25T00:00:00.000Z',
      };

      final parameter = VisualTripPlannerParameter(
        initialData: aiTripIntentData,
      );

      final testCubit = VisualTripPlannerCubit(parameter: parameter);

      // Act
      await testCubit.onInit();

      // Assert
      final state = testCubit.state;
      expect(state.initialData!['extracted_duration'], equals('some time'));
      final parsedDuration =
          state.initialData!['parsed_duration'] as Map<String, dynamic>;
      expect(parsedDuration.isEmpty,
          isTrue); // Should be empty map for invalid format

      testCubit.close();
    });

    test('should handle invalid time format gracefully', () async {
      // Arrange
      final aiTripIntentData = {
        'trip_intent': 'Discovery',
        'destination': 'Sydney',
        'duration': '4 days',
        'time': 'invalid-date', // Invalid format
      };

      final parameter = VisualTripPlannerParameter(
        initialData: aiTripIntentData,
      );

      final testCubit = VisualTripPlannerCubit(parameter: parameter);

      // Act
      await testCubit.onInit();

      // Assert
      final state = testCubit.state;
      expect(state.initialData!['extracted_time'], equals('invalid-date'));
      expect(state.initialData!['parsed_start_date'],
          isNull); // Should be null for invalid format

      testCubit.close();
    });
  });
}
