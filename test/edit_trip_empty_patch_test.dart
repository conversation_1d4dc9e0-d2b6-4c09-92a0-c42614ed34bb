import 'package:flutter_test/flutter_test.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';

void main() {
  group('Edit Trip Empty Patch Array Tests', () {
    test('should handle empty patch array and show confirmation message', () {
      final json = {
        'message': '''
Here's your updated trip:
```json
{
  "confirmation_message": "No changes were needed. Your trip looks perfect!",
  "patch": []
}
```
''',
      };

      // Mock trip object
      final mockTrip = MockTrip(
          {"name": "Paris Adventure", "budget": 1500.0, "city": "Paris"});

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      // Should extract confirmation message, not show JSON
      expect(model.message,
          equals('No changes were needed. Your trip looks perfect!'));
      expect(model.aiTrip, isNull);
      expect(model.editingComplete, isFalse);
    });

    test('should handle empty patch array with EDITING_COMPLETE status', () {
      final json = {
        'message': '''
```json
{
  "confirmation_message": "Great! Your trip plan is all set.",
  "patch": [],
  "status": "EDITING_COMPLETE"
}
```
''',
      };

      final mockTrip =
          MockTrip({"name": "Tokyo Trip", "budget": 2000.0, "city": "Tokyo"});

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.message, equals('Great! Your trip plan is all set.'));
      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['name'], equals('Tokyo Trip'));
      expect(model.editingComplete, isTrue);
    });

    test('should handle inline JSON with empty patch array', () {
      final json = {
        'message':
            'Your trip is perfect as is! {"confirmation_message": "No changes needed.", "patch": []}',
      };

      final mockTrip =
          MockTrip({"name": "London Trip", "budget": 1800.0, "city": "London"});

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.message, equals('No changes needed.'));
      expect(model.aiTrip, isNull);
    });

    test('should fallback to cleaned message when no confirmation_message', () {
      final json = {
        'message': '''
Your trip looks good!
```json
{
  "patch": []
}
```
No changes were made.
''',
      };

      final mockTrip =
          MockTrip({"name": "Rome Trip", "budget": 1200.0, "city": "Rome"});

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      // Should clean up the message by removing JSON block
      expect(model.message, contains('Your trip looks good!'));
      expect(model.message, contains('No changes were made.'));
      expect(model.message, isNot(contains('```json')));
      expect(model.message, isNot(contains('"patch"')));
      expect(model.aiTrip, isNull);
    });

    test('should handle pure JSON edit trip response', () {
      final json = {
        'message':
            '{"confirmation_message": "I\'ve added an extra day to your trip. The new end date is June 21, 2025.", "patch": [{"op": "replace", "path": "/to_date", "value": "2025-06-21T23:59:59.020Z"}]}',
      };

      final mockTrip = MockTrip({
        "name": "Bangkok Trip",
        "to_date": "2025-06-20T23:59:59.020Z",
        "budget": 1500.0,
        "city": "Bangkok"
      });

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(
          model.message,
          equals(
              'I\'ve added an extra day to your trip. The new end date is June 21, 2025.'));
      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['to_date'], equals('2025-06-21T23:59:59.020Z'));
      expect(model.aiTrip!['name'], equals('Bangkok Trip'));
      expect(model.editingComplete, isFalse);
    });

    test('should handle pure JSON with empty patch array', () {
      final json = {
        'message':
            '{"confirmation_message": "No changes needed.", "patch": []}',
      };

      final mockTrip = MockTrip(
          {"name": "Singapore Trip", "budget": 1800.0, "city": "Singapore"});

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.message, equals('No changes needed.'));
      expect(model.aiTrip, isNull); // No changes, so no trip data returned
      expect(model.editingComplete, isFalse);
    });

    test('should handle pure JSON with EDITING_COMPLETE status', () {
      final json = {
        'message':
            '{"confirmation_message": "Great! Your trip plan is all set.", "patch": [], "status": "EDITING_COMPLETE"}',
      };

      final mockTrip =
          MockTrip({"name": "Seoul Trip", "budget": 2200.0, "city": "Seoul"});

      final model = ChatSuggestionModel.fromJson(
        json,
        purposeKey: AIPurposeKey.editATrip,
        currentTrip: mockTrip,
      );

      expect(model.message, equals('Great! Your trip plan is all set.'));
      expect(model.aiTrip, isNotNull);
      expect(model.aiTrip!['name'], equals('Seoul Trip'));
      expect(model.editingComplete, isTrue);
    });
  });
}

// Simple mock trip class for testing
class MockTrip {
  final Map<String, dynamic> _data;

  MockTrip(this._data);

  Map<String, dynamic> toJson() => Map<String, dynamic>.from(_data);
}
