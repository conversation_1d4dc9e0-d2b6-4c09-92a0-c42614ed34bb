package com.gencare.family

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.BufferedReader
import java.io.InputStreamReader

class MainActivity: FlutterActivity() {
    private val CHANNEL = "app_logs/native_logs"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            call, result ->
            if (call.method == "getNativeLogs") {
                try {
                    val process = Runtime.getRuntime().exec("logcat -d -t 2000")
                    val reader = BufferedReader(InputStreamReader(process.inputStream))
                    val logs = StringBuilder()
                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        logs.append(line).append("\n")
                    }
                    reader.close()
                    result.success(logs.toString())
                } catch (e: Exception) {
                    result.error("UNAVAILABLE", "Could not fetch logcat logs", null)
                }
            } else {
                result.notImplemented()
            }
        }
    }
}
