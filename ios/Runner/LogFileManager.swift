//
//  LogFileManager.swift
//  Globics
//
//  Created by <PERSON><PERSON><PERSON> on 1/6/22.
//  Copyright © 2022 Terralogic. All rights reserved.
//

import Foundation

class LogFileManager {
    static let shared = LogFileManager()
    
    
    
    func redirectConsoleToFile() {
        
        clearLogFileIfNeeded()
        
        let paths = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)
        let documentsDirectory = paths[0]
        let fileName = "application.log"
        
        let logFilePath = (documentsDirectory as NSString).appendingPathComponent(fileName)
        
        if (isatty(STDIN_FILENO) == 0) {
            freopen(logFilePath, "a+", stderr)
            freopen(logFilePath, "a+", stdout)
        }
    }
    
    
    func logFile() -> URL {
     
        let fileManager = FileManager.default
        guard let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first else {
            print("zipLogFile file path is invalid")
            return URL(fileURLWithPath: "/tmp/dummy")
        }
        var sourceURL = URL(fileURLWithPath: path)
        sourceURL.appendPathComponent("application.log")
        let fileSize = sizeLogFile(from: sourceURL)
        //TODO: if log is > 6MB --> truncate it
//        if  fileSize > 6 {
//            let numberLine = numberOfLineLogFile(logFilePath: sourceURL.path)
//            resizeLogFile(logFilePath: sourceURL.path, totalLine: numberLine, fileSize: fileSize)
//        }
        
        
        return sourceURL;
        
    }
    
//    func zipLogFile() -> URL? {
//        let sourceURL = logFile();
//        
//        let zipFileURL = sourceURL.appendingPathExtension("zip")
//        deleteZipFile(with: zipFileURL)
//        do {
//            try fileManager.zipItem(at: sourceURL, to: zipFileURL, shouldKeepParent: false, compressionMethod:.deflate)
//            print("create ZIP file successed")
//            return zipFileURL
//        } catch {
//            print(error)
//            return nil
//        }
//    }
    
    func clearLogFileIfNeeded() {
         
        guard let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first else {
            print("deleteZipFile file path is invalid")
            return
        }
        var sourceURL = URL(fileURLWithPath: path)
        sourceURL.appendPathComponent("application.log")
        
        
        
        do {
            
            var attr = try FileManager.default.attributesOfItem(atPath: path)
            
            let fileSize =  attr[FileAttributeKey.size]
            
            // > 4MB --> Remove it
            if (fileSize as! Int > 4*1024*1024)
            {
                print("Clear log file, it's too big")
                try FileManager.default.removeItem(at: sourceURL)
                redirectConsoleToFile() 
            }
        } catch(let error) {
            print(error.localizedDescription)
        }
        
    }
    
    func deleteZipFile(with path: URL?, removeLog: Bool = false) {
        guard let path = path else {
            return
        }

        if removeLog {
            guard let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first else {
                print("deleteZipFile file path is invalid")
                return
            }
            var sourceURL = URL(fileURLWithPath: path)
            sourceURL.appendPathComponent("application.log")
            
            do {
                try FileManager.default.removeItem(at: sourceURL)
                redirectConsoleToFile()
            } catch(let error) {
                print(error.localizedDescription)
            }
        }
        
        if FileManager.default.fileExists(atPath: path.path) {
            do {
                try FileManager.default.removeItem(at: path)
            } catch(let error) {
                print(error.localizedDescription)
            }
        }
    }
    
    private func sizeLogFile(from path: URL) -> Double {
        do {
            let attribute = try FileManager.default.attributesOfItem(atPath: path.path)
            if let size = attribute[FileAttributeKey.size] as? NSNumber {
                return size.doubleValue / (1024 * 1024)
            }
        } catch {
            print(error)
        }
        return 0
    }
    
    private func numberOfLineLogFile(logFilePath: String) -> Int {
        if FileManager.default.fileExists(atPath: logFilePath) {
            do {
                let dump =  try String(contentsOfFile: logFilePath, encoding: String.Encoding.utf8)
                return dump.numberOfLines()
            } catch(let err) {
                print(err.localizedDescription)
            }
        }
        
        return 0
    }
    
    private func getContentFile(from filePath: String) -> String {
        if FileManager.default.fileExists(atPath: filePath) {
            do {
                let dump =  try String(contentsOfFile: filePath, encoding: String.Encoding.utf8)
                return dump
            } catch(let err) {
                print(err.localizedDescription)
            }
        }
        
        return ""
    }
    
    
    
}



extension String {
    
    func numberOfLines() -> Int {
        return self.numberOfOccurrencesOf(string: "\n") + 1
    }
    
    func numberOfOccurrencesOf(string: String) -> Int {
        return self.components(separatedBy:string).count - 1
    }
}

