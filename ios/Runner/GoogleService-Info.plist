<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>944633290096-vkk616j60gvvq1akmf13m6vvr5kcagjg.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.944633290096-vkk616j60gvvq1akmf13m6vvr5kcagjg</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>944633290096-8lg4n3980kskcd1erkksggkeujhvp3lt.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCNGovjxS_jPnR97Liv6LPFjRWxqLpPmto</string>
	<key>GCM_SENDER_ID</key>
	<string>944633290096</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.gencare.family</string>
	<key>PROJECT_ID</key>
	<string>fl-smarthome</string>
	<key>STORAGE_BUCKET</key>
	<string>fl-smarthome.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:944633290096:ios:a5eb86936bf042dbf283b5</string>
</dict>
</plist>