import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/extension.dart';
import 'package:flutter/material.dart';

abstract class FamilyHomeBasePage<S extends StatefulWidget, P extends BaseState, C extends BaseCubit<P>>
    extends BaseBlocNoAppBarPageState<S, P, C> {
  @override
  bool get hasBackgroundBody => true;

  @override
  Widget buildBackgroundBody(Widget child) {
    return Stack(
      children: [
        Container(
          width: MediaQuery.of(context).size.width,
          height: 155.5.h,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF6BA9FA), Color(0xFFFFFFFF)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(0),
          ),
        ),
        SafeArea(child: child)
      ],
    );
  }
}
