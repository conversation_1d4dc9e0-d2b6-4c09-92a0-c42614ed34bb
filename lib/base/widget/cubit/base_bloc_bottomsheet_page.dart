import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/loading.dart';
import 'package:flutter/material.dart';

class _MyPinnedHeader extends SliverPersistentHeaderDelegate {
  final double minExtent;
  final double maxExtent;
  final Widget child;

  _MyPinnedHeader({
    required this.minExtent,
    required this.maxExtent,
    required this.child,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.transparent,
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant _MyPinnedHeader oldDelegate) {
    return child != oldDelegate.child || minExtent != oldDelegate.minExtent || maxExtent != oldDelegate.maxExtent;
  }
}

abstract class BaseBlocBottomSheetPageState<S extends StatefulWidget, P extends BaseState, C extends BaseCubit<P>>
    extends BaseBlocPageState<S, P, C> {
  @override
  Widget buildView(BuildContext context, C cubit, P state) {
    return ValueListenableBuilder(
      valueListenable: keyboardDismissPaddingNotifier,
      builder: (context, bottomPadding, child) => PopScope(
        canPop: canPop,
        onPopInvoked: (didPop) {
          FocusScope.of(context).unfocus();
          dismissLoading();
          if (didPop) {
            return;
          }
          handleBackEvent();
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: GestureDetector(
                onTap: () => onTapScreen(context),
                behavior: HitTestBehavior.opaque,
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    SafeArea(
                      left: isLeftSafeArea ?? isSafeArea,
                      right: isRightSafeArea ?? isSafeArea,
                      top: isTopSafeArea ?? isSafeArea,
                      bottom: isBottomSafeArea ?? isSafeArea,
                      child: CustomScrollView(
                        // crossAxisAlignment: crossAxisAlignment,
                        // mainAxisSize: MainAxisSize.min,
                        shrinkWrap: true,
                        slivers: [
                          SliverPersistentHeader(
                            pinned: true,
                            delegate: _MyPinnedHeader(
                              minExtent: 66,
                              maxExtent: 66,
                              child: Column(
                                children: [
                                  Container(
                                    color: backgroundColor,
                                    child: Center(
                                      child: Container(
                                        height: 4,
                                        width: 41,
                                        margin: const EdgeInsets.only(top: 8, bottom: 8),
                                        decoration: BoxDecoration(
                                          color: appTheme.borderColorV2,
                                          borderRadius: BorderRadius.circular(2),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Container(
                                    color: backgroundColor,
                                      child: buildAppBar(context, cubit, state)),
                                  Divider(height: 1, color: appTheme.borderColorV2)
                                ],
                              ),
                            ),
                          ),
                          SliverToBoxAdapter(
                            child: hasBackgroundBody
                                ? buildBackgroundBody(buildBody(context, cubit, state))
                                : buildBody(context, cubit, state),
                          ),
                          SliverToBoxAdapter(
                            child: buildBottomView(context, cubit, state),
                          ),
                          SliverToBoxAdapter(
                            child: SizedBox(height: bottomPadding),
                          ),
                        ],
                      ),
                    ),
                    ...buildBackgroundView(context, cubit, state),
                  ],
                )),
          ),
        ),
      ),
    );
  }
}
