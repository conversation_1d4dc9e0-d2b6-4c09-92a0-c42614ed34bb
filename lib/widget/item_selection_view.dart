import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/widget/select_widget.dart';
import 'package:flutter/material.dart';

class ItemSelectionView extends StatelessWidget {
  const ItemSelectionView({super.key, required this.status, this.width, this.height, this.onTap, this.text = ''});

  final double? width;
  final double? height;
  final bool status;
  final VoidCallback? onTap;
  final String text;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Row(
        children: [
          SelectWidget(status: status, onTap: onTap),
          if (text.isNotEmpty) ...[
            SizedBox(width: 8.w),
            Text(text, style: AppStyle.regular14()),
          ]
        ],
      ),
    );
  }
}
