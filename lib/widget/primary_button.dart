import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    this.text = '',
    this.onTap,
    this.textColor,
    this.buttonPadding,
    this.isFullWidth = true,
    this.isActive = true,
    this.icon,
    this.textStyle,
    this.backgroundColor,
    this.isLoading = false,
    this.borderRadius,
  });

  final String text;
  final VoidCallback? onTap;
  final Color? textColor;
  final EdgeInsets? buttonPadding;
  final Widget? icon;
  final bool isFullWidth;
  final bool isActive;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final bool isLoading;
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(borderRadius ?? 1000),
      onTap: !isLoading && isActive ? onTap : null,
      child: Opacity(
        opacity: isActive ? 1 : .6,
        child: Container(
          width: isFullWidth ? double.infinity : null,
          padding: buttonPadding ?? padding(top: 14, bottom: 17),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: backgroundColor,
            border: backgroundColor != null
                ? Border.all(color: isActive ? appTheme.primaryColor : appTheme.fadeBackgroundColor)
                : null,
            borderRadius: BorderRadius.circular(borderRadius ?? 1000),
            gradient: backgroundColor != null
                ? null
                : const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF6BA9FA),
                      Color(0xFF6370FF),
                    ],
                  ),
          ),
          child: isLoading
              ? Center(
                  child: SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: appTheme.whiteText)),
                )
              : icon != null
                  ? Row(
                      mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        icon ?? const SizedBox(),
                        SizedBox(width: 6.w),
                        Text(text, style: textStyle ?? AppStyle.medium16(color: textColor ?? appTheme.whiteText)),
                      ],
                    )
                  : Text(
                      text,
                      style: textStyle ?? AppStyle.medium16(color: textColor ?? appTheme.whiteText),
                    ),
        ),
      ),
    );
  }
}
