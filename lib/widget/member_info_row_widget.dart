import 'dart:io';

import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:flutter/material.dart';

class UserProfileRow extends StatelessWidget {
  final String name;
  final String text;
  final Color? borderColor;
  final File? imageFile;
  final double avatarSize;
  final String imageUrl;
  final Widget? widget;

  const UserProfileRow({
    Key? key,
    required this.name,
    required this.text,
    this.borderColor,
    this.imageFile,
    required this.imageUrl,
    this.avatarSize = 48,
    this.widget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CircleAvatarCustom(
          borderWidth: 1.w,
          borderColor: borderColor ?? appTheme.redE9Color,
          size: avatarSize,
          imageFile: imageFile,
          imageUrl: imageUrl,
          defaultWidget: Container(
            width: 48,
            height: 48,
            padding: padding(all: 8),
            decoration: BoxDecoration(shape: BoxShape.circle, color: appTheme.whiteText),
            child: Image.asset(
              Assets.images.avatarLogo.path,
              width: 39.w,
              color: appTheme.gray87Color,
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(name, maxLines: 1, overflow: TextOverflow.ellipsis , style: AppStyle.bold16()),
              SizedBox(height: 4.h),
              Text(text, style: AppStyle.regular12(color: appTheme.grayV2)),
            ],
          ),
        ),
        SizedBox(width: 12.w),
        if (widget != null) ...[
          widget ?? const SizedBox(),
        ],
      ],
    );
  }
}
