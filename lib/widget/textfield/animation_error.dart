import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

const _animDuration = Duration(milliseconds: 200);

class AnimationError extends StatefulWidget {
  final String? content;

  const AnimationError(this.content, {super.key});

  @override
  State<AnimationError> createState() => _AnimationErrorState();
}

class _AnimationErrorState extends State<AnimationError> {
  @override
  Widget build(BuildContext context) {
    final show = widget.content?.isNotEmpty ?? false;

    return AnimatedOpacity(
      duration: _animDuration,
      opacity: show ? 1 : 0,
      child: AnimatedSize(
        duration: _animDuration,
        alignment: Alignment.center,
        child: Transform.scale(
          scale: show ? 1 : 0,
          child: SizedBox(
            height: show ? 30 : 0,
            child: Padding(
              padding: paddingV2(horizontal: 12, top: 8),
              child: Text(widget.content ?? '', style: AppStyle.regular12V2(color: appTheme.errorV2)),
            ),
          ),
        ),
      ),
    );
  }
}
