import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/emoji_unicode.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/formatter/auto_first_text_formatter.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:family_app/widget/textfield/widget/clear_icon_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TitleTextField extends StatelessWidget {
  const TitleTextField(
      {Key? key,
      this.fieldNode,
      this.title,
      this.controller,
      this.obscureText = false,
      this.suffixIcon,
      this.textFieldPadding,
      this.topPadding,
      this.isRequired = false,
      this.hintText = '',
      this.titleStyle,
      this.inputType = TextInputType.text,
      this.canEdit = true,
      this.content = '',
      this.includeDenyEmoji = true,
      this.onEditingComplete,
      this.validatedForm = false,
      this.isFormError = false,
      this.errorFormText = '',
      this.minLine,
      this.maxLine,
      this.prefix,
      this.inputFormatters = const [],
      this.containVietnam = false,
      this.onTap,
      this.filledColor,
      this.prefixIconConstraints,
      this.node,
      this.inputAction,
      this.radius,
      this.textFieldConstraints,
      this.showLabel = true,
      this.showRequired = true,
      this.hintStyle,
      this.otherViewInField,
      this.errorInputBorder,
      this.inputBorder,
      this.useMaterialLabel = true,
      this.showHint = false,
      this.labelStyle,
      this.emptyText,
      this.maxLength,
      this.textStyle})
      : super(key: key);

  final String? title;
  final TextEditingController? controller;
  final TextFieldHandler? fieldNode;
  final bool obscureText;
  final Widget? suffixIcon;
  final EdgeInsets? textFieldPadding;
  final double? topPadding;
  final bool isRequired;
  final TextInputType inputType;
  final String hintText;
  final TextStyle? titleStyle;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final bool canEdit;
  final bool validatedForm;
  final String errorFormText;
  final bool isFormError;
  final String content;
  final VoidCallback? onEditingComplete;
  final List<TextInputFormatter> inputFormatters;
  final bool includeDenyEmoji;
  final FocusNode? node;
  final Widget? prefix;
  final int? maxLine;
  final int? minLine;
  final bool containVietnam;
  final VoidCallback? onTap;
  final double? radius;
  final BoxConstraints? prefixIconConstraints;
  final TextInputAction? inputAction;
  final BoxConstraints? textFieldConstraints;
  final bool showRequired;
  final Widget? otherViewInField;
  final bool showLabel;
  final InputBorder? inputBorder;
  final InputBorder? errorInputBorder;
  final bool useMaterialLabel;
  final TextStyle? labelStyle;
  final Color? filledColor;
  final bool showHint;
  final String? emptyText;
  final int? maxLength;

  @override
  Widget build(BuildContext context) {
    return _buildValueListenerView(
      (isEmpty, isValid, customError) {
        final hasError = isFormError || ((!isValid || isEmpty) && validatedForm) || customError.isNotEmpty;
        final errorText = customError.isNotEmpty
            ? customError
            : isFormError
                ? errorFormText
                : isEmpty
                    ? emptyText ?? LocaleKeys.empty_error.tr(args: [fieldNode?.title ?? title ?? ''])
                    : fieldNode?.errorText?.call(fieldNode?.textCtrl.text ?? controller?.text);
        return GestureDetector(
          onTap: onTap,
          child: Padding(
            padding: padding(top: topPadding),
            child: Stack(
              children: [
                fieldNode != null
                    ? ValueListenableBuilder(
                        valueListenable: fieldNode!.canEditNotifier,
                        builder: (context, editable, child) =>
                            _buildTextField(editable, hasError: hasError, errorText: errorText),
                      )
                    : _buildTextField(fieldNode?.canEditNotifier.value ?? canEdit,
                        hasError: hasError, errorText: errorText),
                Positioned(left: 0, top: 0, bottom: 0, child: otherViewInField ?? const SizedBox()),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTextField(bool canEdit, {bool hasError = false, String? errorText}) {
    final hint = fieldNode?.hintText ?? hintText;
    return TextField(
      key: fieldNode?.textFieldKey,
      obscureText: obscureText,
      controller: fieldNode?.textCtrl ?? controller,
      keyboardType: inputType,
      onEditingComplete: onEditingComplete,
      textInputAction: inputAction,
      onTap: onTap,
      autofocus: false,
      selectionHeightStyle: BoxHeightStyle.strut,
      scribbleEnabled: true,
      textAlignVertical: TextAlignVertical.center,
      autocorrect: false,
      inputFormatters: [
        ...inputFormatters,
        if (fieldNode?.maxLength != null || maxLength != null) ...[
          LengthLimitingTextInputFormatter(fieldNode?.maxLength ?? maxLength),
        ],
        if (inputType == TextInputType.phone || inputType == TextInputType.number) ...[
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(11),
        ],
        if ((fieldNode?.autoAddFirstText ?? false) &&
            (fieldNode?.firstText ?? fieldNode?.hintText ?? '').isNotEmpty) ...[
          AutoFirstTextFormatter(firstText: fieldNode?.firstText ?? fieldNode?.hintText ?? '')
        ],
        if (includeDenyEmoji) ...formatterEmojiDeny,
      ],
      focusNode: fieldNode?.node ?? node,
      style: textStyle ?? AppStyle.medium18(),
      maxLines: maxLine,
      minLines: minLine,
      readOnly: !canEdit,
      textAlign: TextAlign.start,
      cursorWidth: 1.5,
      decoration: InputDecoration(
        isDense: false,
        isCollapsed: false,
        errorText: hasError ? errorText : null,
        errorStyle: AppStyle.regular12(color: appTheme.errorColor),
        contentPadding: textFieldPadding ?? padding(horizontal: 17, top: 14, bottom: 17),
        floatingLabelStyle: AppStyle.regular12(),
        prefixIcon: prefix,
        prefixIconConstraints: prefixIconConstraints,
        hintText: showHint
            ? hint
            : showLabel
                ? fieldNode?.title ?? title ?? hint
                : null,
        hintStyle: hintStyle ?? AppStyle.regular18(color: appTheme.hintColor),
        suffixIcon: suffixIcon ?? ClearIconText(controller: fieldNode?.textCtrl ?? controller),
        errorMaxLines: 3,
        fillColor: filledColor ?? appTheme.whiteText,
        filled: true,
        // constraints: textFieldConstraints,
        errorBorder: errorInputBorder ?? border.copyWith(borderSide: BorderSide(color: appTheme.errorColor)),
        border: inputBorder ?? border,
        focusedBorder: inputBorder ?? border.copyWith(borderSide: const BorderSide()),
        enabledBorder: inputBorder ?? border,
        focusedErrorBorder: errorInputBorder ?? border,
      ),
    );
  }

  Widget _buildValueListenerView(Widget Function(bool isEmpty, bool isValid, String customError) viewBuilder) {
    if (fieldNode != null) {
      return ValueListenableBuilder<bool>(
        valueListenable: fieldNode!.emptyError,
        builder: (context, isEmpty, child) => ValueListenableBuilder<bool>(
          valueListenable: fieldNode!.isValidNotifier,
          builder: (context, value, child) => ValueListenableBuilder(
              valueListenable: fieldNode!.customErrorNotifier,
              builder: (context, error, child) => viewBuilder(isEmpty, value, error)),
        ),
      );
    }

    return viewBuilder(false, false, '');
  }

  OutlineInputBorder get border =>
      OutlineInputBorder(borderRadius: BorderRadius.circular(16), borderSide: const BorderSide());
}
