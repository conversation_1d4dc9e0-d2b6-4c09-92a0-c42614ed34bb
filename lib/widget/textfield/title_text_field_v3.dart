import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/emoji_unicode.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/formatter/auto_first_text_formatter.dart';
import 'package:family_app/widget/textfield/animation_error.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TitleTextFieldV3 extends StatefulWidget {
  const TitleTextFieldV3({
    this.fieldNode,
    this.controller,
    this.obscureText = false,
    this.suffixIcon,
    this.hintText = '',
    this.keyboardType,
    this.canEdit = true,
    this.includeDenyEmoji = true,
    this.onEditingComplete,
    this.validatedForm = false,
    this.isFormError = false,
    this.errorFormText = '',
    this.minLine,
    this.maxLine = 1,
    this.prefix,
    this.inputFormatters = const [],
    this.prefixIconConstraints,
    this.inputAction,
    this.radius,
    this.otherViewInField,
    this.emptyText,
    this.maxLength,
    this.showError = true,
    this.showTitle = false,
    this.contentPadding,
    this.style,
    this.hintStyle,
    this.fillColor,
    this.filled = false,
    this.showBorder = true,
    super.key,
  });

  final TextEditingController? controller;
  final TextFieldHandler? fieldNode;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final String hintText, errorFormText;
  final VoidCallback? onEditingComplete;
  final List<TextInputFormatter> inputFormatters;
  final Widget? prefix;
  final int? maxLine, minLine;
  final BorderRadius? radius;
  final BoxConstraints? prefixIconConstraints;
  final TextInputAction? inputAction;
  final Widget? otherViewInField;
  final String? emptyText;
  final int? maxLength;
  final bool canEdit, validatedForm, showError, obscureText, isFormError, includeDenyEmoji, showTitle;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final Color? fillColor;
  final bool filled;
  final bool showBorder;

  @override
  State<TitleTextFieldV3> createState() => _TitleTextFieldV3State();
}

class _TitleTextFieldV3State extends State<TitleTextFieldV3> {
  final _focus = ValueNotifier(false);

  OutlineInputBorder get _border => OutlineInputBorder(
        borderRadius: widget.radius ?? BorderRadius.zero,
        borderSide: BorderSide.none,
      );

  late var _focusNodeLocal = widget.fieldNode?.node == null ? FocusNode() : null;

  FocusNode get _focusNode => widget.fieldNode?.node ?? _focusNodeLocal!;

  @override
  void initState() {
    _focusNode.addListener(_listener);

    super.initState();
  }

  @override
  void didUpdateWidget(covariant TitleTextFieldV3 oldWidget) {
    if (oldWidget.fieldNode?.node != widget.fieldNode?.node) {
      _focusNode.removeListener(_listener);
      _focusNodeLocal = widget.fieldNode?.node == null ? FocusNode() : null;
      _focusNode.addListener(_listener);
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_listener);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showTitle || widget.fieldNode?.title == null) return _build();

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(widget.fieldNode!.title, style: AppStyle.regular14V2(color: appTheme.grayV2)),
      SizedBox(height: 4.h2),
      _build(),
    ]);
  }

  Widget _build() {
    final r = Stack(children: [
      ValueListenableBuilder(
        valueListenable: _focus,
        builder: (context, focus, child) => _buildValueListenerView(
          (isEmpty, isValid, customError) {
            final error = widget.isFormError || ((!isValid || isEmpty) && widget.validatedForm) || customError.isNotEmpty;

            return AnimatedContainer(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: focus
                    ? [
                        BoxShadow(
                          color: error ? const Color.fromRGBO(211, 48, 48, 0.2) : const Color.fromRGBO(78, 70, 180, 0.2),
                          spreadRadius: 4,
                          blurRadius: 0,
                          offset: const Offset(0, 0),
                        )
                      ]
                    : null,
                borderRadius: widget.radius ?? BorderRadius.circular(8.w2),
                border: Border.all(
                  color: error
                      ? appTheme.errorV2
                      : focus
                          ? appTheme.primaryColorV2
                          : widget.filled
                              ? (widget.fillColor ?? Colors.transparent)
                              : appTheme.borderColorV2,
                  width: focus ? 1.5.w2 : 1.w2,
                ),
              ),
              duration: const Duration(milliseconds: 200),
              child: child,
            );
          },
        ),
        child: widget.fieldNode != null
            ? ValueListenableBuilder(
                valueListenable: widget.fieldNode!.canEditNotifier,
                builder: (context, editable, child) => _buildTextField(editable),
              )
            : _buildTextField(widget.fieldNode?.canEditNotifier.value ?? widget.canEdit),
      ),
      Positioned(left: 0, top: 0, bottom: 0, child: widget.otherViewInField ?? const SizedBox()),
    ]);

    if (!widget.showError) return r;

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      r,
      _buildValueListenerView((isEmpty, isValid, customError) {
        final hasError = widget.isFormError || ((!isValid || isEmpty) && widget.validatedForm) || customError.isNotEmpty;
        final errorText = customError.isNotEmpty
            ? customError
            : widget.isFormError
                ? widget.errorFormText
                : isEmpty
                    ? widget.emptyText ?? LocaleKeys.empty_error.tr(args: [widget.fieldNode?.title ?? ''])
                    : widget.fieldNode?.errorText?.call(widget.fieldNode?.textCtrl.text ?? widget.controller?.text);

        return AnimationError((errorText?.isNotEmpty ?? false) && hasError ? errorText : null);
      }),
    ]);
  }

  Widget _buildTextField(bool canEdit) {
    return TextField(
      key: widget.fieldNode?.textFieldKey,
      obscureText: widget.obscureText,
      controller: widget.fieldNode?.textCtrl ?? widget.controller,
      keyboardType: widget.keyboardType,
      onEditingComplete: widget.onEditingComplete,
      textInputAction: widget.inputAction,
      autofocus: false,
      selectionHeightStyle: BoxHeightStyle.strut,
      scribbleEnabled: true,
      textAlignVertical: TextAlignVertical.center,
      autocorrect: false,
      inputFormatters: [
        ...widget.inputFormatters,
        if (widget.fieldNode?.maxLength != null || widget.maxLength != null) LengthLimitingTextInputFormatter(widget.fieldNode?.maxLength ?? widget.maxLength),
        if (widget.keyboardType == TextInputType.phone || widget.keyboardType == TextInputType.number) ...[
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(11),
        ],
        if ((widget.fieldNode?.autoAddFirstText ?? false) && (widget.fieldNode?.firstText ?? widget.fieldNode?.hintText ?? '').isNotEmpty) AutoFirstTextFormatter(firstText: widget.fieldNode?.firstText ?? widget.fieldNode?.hintText ?? ''),
        if (widget.includeDenyEmoji) ...formatterEmojiDeny,
      ],
      focusNode: _focusNode,
      style: widget.style ?? AppStyle.regular16V2(),
      maxLines: widget.maxLine,
      minLines: widget.minLine,
      readOnly: !canEdit,
      textAlign: TextAlign.start,
      cursorWidth: 1.5,
      decoration: InputDecoration(
        isDense: false,
        isCollapsed: false,
        contentPadding: widget.contentPadding ?? paddingV2(all: 16),
        prefixIcon: widget.prefix,
        prefixIconConstraints: widget.prefixIconConstraints,
        hintText: widget.fieldNode?.hintText ?? widget.hintText,
        hintStyle: widget.hintStyle ?? AppStyle.regular16V2(color: appTheme.borderColorV2),
        suffixIcon: widget.suffixIcon,
        border: _border,
        errorBorder: _border,
        enabledBorder: _border,
        focusedBorder: _border,
        disabledBorder: _border,
        focusedErrorBorder: _border,
        fillColor: widget.fillColor ?? appTheme.backgroundV2,
        filled: widget.filled,
      ),
    );
  }

  Widget _buildValueListenerView(Widget Function(bool isEmpty, bool isValid, String customError) viewBuilder) {
    if (widget.fieldNode != null) {
      return ValueListenableBuilder<bool>(
        valueListenable: widget.fieldNode!.emptyError,
        builder: (context, isEmpty, child) => ValueListenableBuilder<bool>(
          valueListenable: widget.fieldNode!.isValidNotifier,
          builder: (context, value, child) => ValueListenableBuilder(valueListenable: widget.fieldNode!.customErrorNotifier, builder: (context, error, child) => viewBuilder(isEmpty, value, error)),
        ),
      );
    }

    return viewBuilder(false, false, '');
  }

  void _listener() {
    _focus.value = _focusNode.hasFocus;
  }
}
