import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:family_app/widget/textfield/title_text_field.dart';
import 'package:flutter/material.dart';

class RowTextFieldView extends StatelessWidget {
  const RowTextFieldView({
    super.key,
    required this.handler,
    required this.formHandler,
    this.titleWidth = .2,
    this.bottomSizedBox = 15,
    this.topPadding = 15,
    this.selections = const [],
    this.viewPadding,
    this.maxLine = 1,
    this.titleStyle,
    this.textStyle,
    this.hintStyle,
    this.borderRadius,
    this.border,
    this.backgroundColor,
  });

  final FormTextFieldHandler formHandler;
  final TextFieldHandler handler;
  final double titleWidth;
  final List<String> selections;
  final double topPadding;
  final double bottomSizedBox;
  final EdgeInsets? viewPadding;
  final BorderRadiusGeometry? borderRadius;
  final BoxBorder? border;
  final int maxLine;
  final TextStyle? titleStyle;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final paddingWidth = handler.title.isNotEmpty ? MediaQuery.of(context).size.width * 0.20 : 0.0;
    return Container(
      padding: viewPadding ?? padding(horizontal: 16),
      decoration: BoxDecoration(
        color: backgroundColor ?? appTheme.whiteText,
        borderRadius: borderRadius,
        border: border,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (handler.title.isNotEmpty)
                SizedBox(
                  width: paddingWidth,
                  child: Text(handler.title, style: AppStyle.regular14(color: appTheme.labelColor)),
                ),
              Expanded(
                child: ValueListenableBuilder(
                  valueListenable: formHandler.formValidated,
                  builder: (context, isFormValidate, child) => TitleTextField(
                    fieldNode: handler,
                    validatedForm: isFormValidate,
                    useMaterialLabel: false,
                    showLabel: false,
                    showHint: true,
                    titleStyle: titleStyle,
                    textStyle: textStyle ?? AppStyle.regular16(),
                    hintStyle: hintStyle ?? AppStyle.regular14(color: appTheme.hintColor),
                    inputBorder:InputBorder.none,
                    textFieldPadding: padding(top: topPadding),
                    maxLine: maxLine,
                    errorInputBorder: InputBorder.none,
                    filledColor: backgroundColor ?? appTheme.whiteText,
                  ),
                ),
              ),
            ],
          ),
          if (selections.isNotEmpty) ...[
            ValueListenableBuilder(
              valueListenable: handler.textCtrl,
              builder: (context, textCtrl, value) => Padding(
                padding: padding(left: paddingWidth, top: 0),
                child: Wrap(
                  runSpacing: 8,
                  spacing: 8,
                  crossAxisAlignment: WrapCrossAlignment.start,
                  children: selections.map((e) => _buildSelectionItem(e, textCtrl.text)).toList(),
                ),
              ),
            )
          ] else
            SizedBox(height: bottomSizedBox),
        ],
      ),
    );
  }

  Widget _buildSelectionItem(String item, String currentText) {
    return GestureDetector(
      onTap: () {
        handler.textCtrl.text = item;
      },
      child: Container(
        padding: padding(top: 2, bottom: 5, left: 9, right: 7),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(1000),
          border: Border.all(color: appTheme.hintColor),
        ),
        child: Text(item, style: AppStyle.regular14()),
      ),
    );
  }
}
