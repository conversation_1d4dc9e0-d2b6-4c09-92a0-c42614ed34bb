import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/util.dart';
import 'package:flutter/material.dart';

class UserBorderWidget extends StatelessWidget {
  const UserBorderWidget({required this.name, required this.color, super.key});

  final String name;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: padding(all: 6),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
          ),
          child: Text(
            Utils.removeDiacritics(name.substring(0, 2)),
            style: AppStyle.medium14(color: appTheme.whiteText),
          ),
        ),
        SizedBox(width: 8.w),
        Text(name, style: AppStyle.regular12()),
      ],
    );
  }
}
