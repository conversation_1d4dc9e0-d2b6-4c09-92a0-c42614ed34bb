import 'package:family_app/config/service/image_cache_service.dart';
import 'package:family_app/widget/image_viewer.dart/image_viewer_pager.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:family_app/utils/log/app_logger.dart';

/// A reusable widget that displays images from storage UUIDs with support for ImageViewerPager
class ThreadImageGallery extends StatefulWidget {
  /// List of storage UUIDs to display
  final List<String> imageUuids;

  /// Whether to show images in a grid layout (true) or single image (false)
  final bool isMultipleImages;

  /// Cross axis alignment for the gallery
  final CrossAxisAlignment crossAxisAlignment;

  /// Maximum width for single images
  final double? maxWidth;

  /// Height for single images
  final double? height;

  /// Border radius for images
  final double borderRadius;

  /// Whether to enable image viewer on tap
  final bool enableImageViewer;

  /// Custom title for image viewer
  final String? imageViewerTitle;

  /// Custom description for image viewer
  final String? imageViewerDescription;

  /// Callback when image viewer opens
  final VoidCallback? onImageViewerOpen;

  /// Callback when image viewer closes
  final VoidCallback? onImageViewerClose;

  /// Whether this gallery is being used in an image viewer context
  final bool isImageViewerContext;

  const ThreadImageGallery({
    super.key,
    required this.imageUuids,
    this.isMultipleImages = true,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.maxWidth,
    this.height,
    this.borderRadius = 8.0,
    this.enableImageViewer = true,
    this.imageViewerTitle,
    this.imageViewerDescription,
    this.onImageViewerOpen,
    this.onImageViewerClose,
    this.isImageViewerContext = false,
  });

  @override
  State<ThreadImageGallery> createState() => _ThreadImageGalleryState();
}

class _ThreadImageGalleryState extends State<ThreadImageGallery>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  String? _error;
  bool _hasLoadedOnce = false;
  // Make cache static to persist across widget rebuilds
  static final Map<String, String?> _globalCachedUrls = {};
  static final Map<String, bool> _globalImageLoadedStates =
      {}; // Track which images are actually loaded
  bool _isInitialized = false;

  @override
  bool get wantKeepAlive =>
      true; // Keep this widget alive when scrolled out of view

  @override
  void initState() {
    super.initState();
    // Check if we already have all the required URLs cached and loaded
    final allUuidsCached = widget.imageUuids.every((uuid) =>
        _globalCachedUrls.containsKey(uuid) &&
        _globalCachedUrls[uuid] != null &&
        _globalImageLoadedStates[uuid] == true);

    if (allUuidsCached) {
      // All URLs are already cached and loaded, no need to load
      _isLoading = false;
      _hasLoadedOnce = true;
    } else {
      // Check if we have URLs but they're not loaded yet
      final allUuidsHaveUrls = widget.imageUuids.every((uuid) =>
          _globalCachedUrls.containsKey(uuid) &&
          _globalCachedUrls[uuid] != null);

      if (allUuidsHaveUrls) {
        // We have URLs but they might not be loaded yet, set loading to false to avoid placeholder
        _isLoading = false;
        _hasLoadedOnce = true;
      } else if (widget.isImageViewerContext && allUuidsHaveUrls) {
        // In image viewer context, if we have URLs, don't load again
        _isLoading = false;
        _hasLoadedOnce = true;
      } else {
        // Delay initialization to avoid blocking the UI thread
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && !_isInitialized) {
            _isInitialized = true;
            _loadImages();
          }
        });
      }
    }
  }

  @override
  void didUpdateWidget(ThreadImageGallery oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if any properties that affect the display have changed
    final propertiesChanged = oldWidget.imageUuids != widget.imageUuids ||
        oldWidget.isMultipleImages != widget.isMultipleImages ||
        oldWidget.maxWidth != widget.maxWidth ||
        oldWidget.height != widget.height ||
        oldWidget.borderRadius != widget.borderRadius ||
        oldWidget.enableImageViewer != widget.enableImageViewer;

    // Only reload if the UUIDs have actually changed
    if (oldWidget.imageUuids != widget.imageUuids) {
      // Only clear cache for UUIDs that are no longer needed
      final oldUuids = Set<String>.from(oldWidget.imageUuids);
      final newUuids = Set<String>.from(widget.imageUuids);
      final removedUuids = oldUuids.difference(newUuids);

      for (final uuid in removedUuids) {
        _globalCachedUrls.remove(uuid);
        _globalImageLoadedStates.remove(uuid);
      }

      // Check if all new UUIDs are already cached and loaded
      final allNewUuidsCached = widget.imageUuids.every((uuid) =>
          _globalCachedUrls.containsKey(uuid) &&
          _globalCachedUrls[uuid] != null &&
          _globalImageLoadedStates[uuid] == true);

      if (allNewUuidsCached) {
        // All new UUIDs are already cached and loaded
        _isLoading = false;
        _hasLoadedOnce = true;
        _error = null;
      } else {
        // Check if we have URLs for all new UUIDs
        final allNewUuidsHaveUrls = widget.imageUuids.every((uuid) =>
            _globalCachedUrls.containsKey(uuid) &&
            _globalCachedUrls[uuid] != null);

        if (allNewUuidsHaveUrls) {
          // We have URLs but they might not be loaded yet
          _isLoading = false;
          _hasLoadedOnce = true;
          _error = null;
        } else {
          // Need to load new URLs
          _isLoading = true;
          _error = null;
          _hasLoadedOnce = false;
          _loadImages();
        }
      }
    } else if (propertiesChanged) {
      // Just trigger a rebuild for UI changes, don't reload images
      setState(() {});
    } else {
      // Even if UUIDs are the same, ensure we're not in loading state if we have data
      final allUuidsCached = widget.imageUuids.every((uuid) =>
          _globalCachedUrls.containsKey(uuid) &&
          _globalCachedUrls[uuid] != null);

      if (_hasLoadedOnce && allUuidsCached && _isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadImages() async {
    // If we've already loaded these UUIDs and they're in cache, don't reload
    if (_hasLoadedOnce && _globalCachedUrls.isNotEmpty) {
      final allUuidsCached = widget.imageUuids.every((uuid) =>
          _globalCachedUrls.containsKey(uuid) &&
          _globalCachedUrls[uuid] != null);
      if (allUuidsCached) {
        // Ensure we're not in loading state
        if (_isLoading) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }
    }

    try {
      final futures = widget.imageUuids.map((uuid) async {
        // Check if we already have this UUID cached locally
        if (_globalCachedUrls.containsKey(uuid) &&
            _globalCachedUrls[uuid] != null) {
          return MapEntry(uuid, _globalCachedUrls[uuid]);
        }

        final url = await imageCacheService.getImageUrl(uuid);
        return MapEntry(uuid, url);
      });

      final results = await Future.wait(futures);
      final newCachedUrls = Map.fromEntries(results);

      if (mounted) {
        setState(() {
          _globalCachedUrls.addAll(newCachedUrls);
          _isLoading = false;
          _hasLoadedOnce = true;
        });
      }
    } catch (e) {
      AppLogger.e('🖼️ ThreadImageGallery: Error loading images: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    if (widget.imageUuids.isEmpty) {
      return const SizedBox.shrink();
    }

    // Check if all images are already cached and loaded
    final allImagesCached = widget.imageUuids.every((uuid) =>
        _globalCachedUrls.containsKey(uuid) &&
        _globalCachedUrls[uuid] != null &&
        _globalImageLoadedStates[uuid] == true);

    // Check if we have URLs for all images (even if not loaded yet)
    final allImagesHaveUrls = widget.imageUuids.every((uuid) =>
        _globalCachedUrls.containsKey(uuid) && _globalCachedUrls[uuid] != null);

    // If we have URLs for all images, don't show loading state
    if (allImagesHaveUrls && _isLoading) {
      _isLoading = false;
    }

    // If all images are cached and loaded, ensure we're not in loading state
    if (allImagesCached && _isLoading) {
      _isLoading = false;
    }

    return RepaintBoundary(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: widget.crossAxisAlignment,
          children: [
            if (widget.isMultipleImages && widget.imageUuids.length > 1)
              _buildMultipleImagesGrid(context)
            else
              _buildSingleImage(context, widget.imageUuids.first),
          ],
        ),
      ),
    );
  }

  Widget _buildSingleImage(BuildContext context, String imageUuid) {
    // If we have a URL for this image, don't show loading placeholder
    final hasUrl = _globalCachedUrls.containsKey(imageUuid) &&
        _globalCachedUrls[imageUuid] != null;
    final isLoaded = _globalImageLoadedStates[imageUuid] == true;

    if (_isLoading && !hasUrl) {
      return Container(
        width: widget.maxWidth ?? 200,
        height: widget.height ?? 200,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        child: const Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (_error != null ||
        !hasUrl ||
        _globalCachedUrls[imageUuid]?.isEmpty == true) {
      return Container(
        width: widget.maxWidth ?? 200,
        height: widget.height ?? 200,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        child: const Icon(Icons.error_outline, size: 40, color: Colors.red),
      );
    }

    final imageUrl = _globalCachedUrls[imageUuid]!;

    final imageWidget = ClipRRect(
      borderRadius: BorderRadius.circular(widget.borderRadius),
      child: CachedNetworkImage(
        key: ValueKey('cached_image_$imageUuid'),
        imageUrl: imageUrl,
        width: widget.maxWidth ?? 200,
        height: widget.height ?? 200,
        fit: BoxFit.cover,
        memCacheWidth: (widget.maxWidth ?? 200).toInt(),
        memCacheHeight: (widget.height ?? 200).toInt(),
        maxWidthDiskCache: (widget.maxWidth ?? 200).toInt(),
        maxHeightDiskCache: (widget.height ?? 200).toInt(),
        useOldImageOnUrlChange: true,
        fadeInDuration: const Duration(milliseconds: 50),
        fadeOutDuration: const Duration(milliseconds: 50),
        imageBuilder: (context, imageProvider) {
          // Mark this image as loaded
          _globalImageLoadedStates[imageUuid] = true;
          return Image(image: imageProvider, fit: BoxFit.cover);
        },
        placeholder: (context, url) {
          // Don't show placeholder if we've already loaded this image
          if (isLoaded) {
            return const SizedBox
                .shrink(); // Don't show placeholder if already loaded
          }
          return Container(
            width: widget.maxWidth ?? 200,
            height: widget.height ?? 200,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        },
        errorWidget: (context, url, error) {
          AppLogger.e(
              '🖼️ ThreadImageGallery: Error loading image URL: ${url.substring(0, url.length > 50 ? 50 : url.length)}..., Error: $error');

          // If it's a 403 error (expired URL), clear the cache and retry
          if (error.toString().contains('403') ||
              error.toString().contains('forbidden')) {
            AppLogger.d(
                '🖼️ ThreadImageGallery: Detected 403 error, clearing cache for UUID: $imageUuid');
            imageCacheService.removeFromCache(imageUuid);
            // Trigger a rebuild to retry with fresh URL
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _globalCachedUrls.remove(imageUuid);
                  _loadImages();
                });
              }
            });
          }

          return Container(
            width: widget.maxWidth ?? 200,
            height: widget.height ?? 200,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: const Icon(Icons.error_outline, size: 40, color: Colors.red),
          );
        },
      ),
    );

    if (widget.enableImageViewer) {
      return GestureDetector(
        onTap: () => _showImageViewer(context, [imageUrl], 0),
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildMultipleImagesGrid(BuildContext context) {
    // Check if we have URLs for all images
    final allImagesHaveUrls = widget.imageUuids.every((uuid) =>
        _globalCachedUrls.containsKey(uuid) && _globalCachedUrls[uuid] != null);

    if (_isLoading && !allImagesHaveUrls) {
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.imageUuids.length == 2 ? 2 : 3,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
          childAspectRatio: 1,
        ),
        itemCount: widget.imageUuids.length,
        itemBuilder: (context, index) => Container(
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: const Center(
            child: SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        ),
      );
    }

    if (_error != null) {
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.imageUuids.length == 2 ? 2 : 3,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
          childAspectRatio: 1,
        ),
        itemCount: widget.imageUuids.length,
        itemBuilder: (context, index) => Container(
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: const Icon(Icons.error_outline, color: Colors.red),
        ),
      );
    }

    final validImageUrls = widget.imageUuids
        .where((uuid) =>
            _globalCachedUrls[uuid] != null &&
            _globalCachedUrls[uuid]!.isNotEmpty)
        .map((uuid) => _globalCachedUrls[uuid]!)
        .toList();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.imageUuids.length == 2 ? 2 : 3,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: validImageUrls.length,
      itemBuilder: (context, index) {
        final imageUrl = validImageUrls[index];
        final originalUuid = widget.imageUuids
            .firstWhere((uuid) => _globalCachedUrls[uuid] == imageUrl);
        final isLoaded = _globalImageLoadedStates[originalUuid] == true;

        final imageWidget = ClipRRect(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: CachedNetworkImage(
            key: ValueKey('cached_grid_image_$originalUuid'),
            imageUrl: imageUrl,
            fit: BoxFit.cover,
            memCacheWidth: 200,
            memCacheHeight: 200,
            maxWidthDiskCache: 200,
            maxHeightDiskCache: 200,
            useOldImageOnUrlChange: true,
            fadeInDuration: const Duration(milliseconds: 50),
            fadeOutDuration: const Duration(milliseconds: 50),
            imageBuilder: (context, imageProvider) {
              // Mark this image as loaded
              _globalImageLoadedStates[originalUuid] = true;
              return Image(image: imageProvider, fit: BoxFit.cover);
            },
            placeholder: (context, url) {
              // Don't show placeholder if we've already loaded this image
              if (isLoaded) {
                return const SizedBox
                    .shrink(); // Don't show placeholder if already loaded
              }
              return Container(
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              );
            },
            errorWidget: (context, url, error) {
              AppLogger.e(
                  '🖼️ ThreadImageGallery: Error loading grid image $index URL: ${url.substring(0, url.length > 50 ? 50 : url.length)}..., Error: $error');

              // If it's a 403 error (expired URL), clear the cache and retry
              if (error.toString().contains('403') ||
                  error.toString().contains('forbidden')) {
                AppLogger.d(
                    '🖼️ ThreadImageGallery: Detected 403 error in grid, clearing cache for UUID: $originalUuid');
                imageCacheService.removeFromCache(originalUuid);
                // Trigger a rebuild to retry with fresh URL
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    setState(() {
                      _globalCachedUrls.remove(originalUuid);
                      _loadImages();
                    });
                  }
                });
              }

              return Container(
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                child: const Icon(Icons.error_outline, color: Colors.red),
              );
            },
          ),
        );

        if (widget.enableImageViewer) {
          return GestureDetector(
            onTap: () => _showImageViewer(context, validImageUrls, index),
            child: imageWidget,
          );
        }

        return imageWidget;
      },
    );
  }

  void _showImageViewer(
      BuildContext context, List<String> imageUrls, int initialIndex) {
    if (imageUrls.isEmpty) return;

    // Notify parent that image viewer is opening
    widget.onImageViewerOpen?.call();

    // Use our cached URLs to prevent reloading
    final cachedImageUrls = imageUrls.map((url) {
      // Find the UUID that corresponds to this URL
      final uuid = _globalCachedUrls.entries
          .firstWhere((entry) => entry.value == url,
              orElse: () => MapEntry('', url))
          .key;

      // If we have this UUID cached and loaded, use the cached URL
      if (uuid.isNotEmpty && _globalImageLoadedStates[uuid] == true) {
        return url;
      }

      return url;
    }).toList();

    final imageItems = cachedImageUrls
        .map((url) => ImagePagerItem(
              imageUrl: url,
              title: widget.imageViewerTitle ?? '',
              description: widget.imageViewerDescription ?? '',
              dateTime: DateTime.now(),
            ))
        .toList();

    ImageViewerPager.show(
      context: context,
      items: imageItems,
      initialIndex: initialIndex.clamp(0, imageItems.length - 1),
      doubleTapZoomable: true,
      swipeDismissible: true,
      onViewerDismissed: (index) {
        // Notify parent that image viewer is closing
        widget.onImageViewerClose?.call();
      },
    );
  }
}

/// Convenience widget for single image display
class ThreadSingleImage extends StatelessWidget {
  final String imageUuid;
  final double? maxWidth;
  final double? height;
  final double borderRadius;
  final bool enableImageViewer;
  final String? imageViewerTitle;
  final String? imageViewerDescription;
  final bool isImageViewerContext;

  const ThreadSingleImage({
    super.key,
    required this.imageUuid,
    this.maxWidth,
    this.height,
    this.borderRadius = 8.0,
    this.enableImageViewer = true,
    this.imageViewerTitle,
    this.imageViewerDescription,
    this.isImageViewerContext = false,
  });

  @override
  Widget build(BuildContext context) {
    return ThreadImageGallery(
      imageUuids: [imageUuid],
      isMultipleImages: false,
      maxWidth: maxWidth,
      height: height,
      borderRadius: borderRadius,
      enableImageViewer: enableImageViewer,
      imageViewerTitle: imageViewerTitle,
      imageViewerDescription: imageViewerDescription,
      isImageViewerContext: isImageViewerContext,
    );
  }
}
