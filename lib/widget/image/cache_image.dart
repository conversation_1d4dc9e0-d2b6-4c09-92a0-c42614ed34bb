import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/widget/default_image.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/service/image_cache_service.dart';

/// A widget that displays a network image with caching and fallback support.
/// - Shows initials or SVG fallback while loading or on error.
/// - Supports both direct URLs and storage UUIDs.
class CacheImage extends StatelessWidget {
  const CacheImage({
    super.key,
    this.imageUrl = '',
    this.size,
    this.height,
    this.width,
    this.boxFit,
    this.defaultImage,
  });

  final String imageUrl;
  final double? size;
  final double? height;
  final double? width;
  final BoxFit? boxFit;
  final Widget? defaultImage;

  @override
  Widget build(BuildContext context) {
    final trimmedUrl = imageUrl.trim();
    if (!_isValidUrl(trimmedUrl)) {
      return _defaultImage;
    }
    if (_isStorageUuid(imageUrl)) {
      return _buildStorageImage();
    }
    final imageSize = (width ?? size)?.w ?? 48.0;
    final isAvatar = imageSize <= 80;
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: (width ?? size)?.w,
      height: (height ?? size)?.w,
      fit: boxFit ?? BoxFit.cover,
      memCacheWidth: isAvatar ? imageSize.toInt() : null,
      memCacheHeight: isAvatar ? imageSize.toInt() : null,
      maxWidthDiskCache: isAvatar ? imageSize.toInt() : null,
      maxHeightDiskCache: isAvatar ? imageSize.toInt() : null,
      fadeInDuration: isAvatar ? const Duration(milliseconds: 100) : const Duration(milliseconds: 300),
      fadeOutDuration: isAvatar ? const Duration(milliseconds: 50) : const Duration(milliseconds: 200),
      useOldImageOnUrlChange: true,
      placeholder: (context, url) => _defaultImage,
      errorWidget: (context, url, error) => _defaultImage,
    );
  }

  bool _isValidUrl(String url) {
    if (url.isEmpty || !url.startsWith('http')) return false;
    try {
      Uri.parse(url);
      return true;
    } catch (_) {
      return false;
    }
  }

  bool _isStorageUuid(String url) {
    return url.length == 36 && url.contains('-') && !url.startsWith('http') && url.trim().isNotEmpty;
  }

  Widget _buildStorageImage() {
    return FutureBuilder<String?>(
      future: imageCacheService.getImageUrl(imageUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _defaultImage;
        }
        if (snapshot.hasError || snapshot.data == null || snapshot.data!.isEmpty) {
          return _defaultImage;
        }
        final resolvedUrl = snapshot.data!;
        if (!_isValidUrl(resolvedUrl)) {
          imageCacheService.removeFromCache(imageUrl);
          return _defaultImage;
        }
        final imageSize = (width ?? size)?.w ?? 48.0;
        final isAvatar = imageSize <= 80;
        return CachedNetworkImage(
          imageUrl: resolvedUrl,
          width: (width ?? size)?.w,
          height: (height ?? size)?.w,
          fit: boxFit ?? BoxFit.cover,
          memCacheWidth: isAvatar ? imageSize.toInt() : null,
          memCacheHeight: isAvatar ? imageSize.toInt() : null,
          maxWidthDiskCache: isAvatar ? imageSize.toInt() : null,
          maxHeightDiskCache: isAvatar ? imageSize.toInt() : null,
          fadeInDuration: isAvatar ? const Duration(milliseconds: 100) : const Duration(milliseconds: 300),
          fadeOutDuration: isAvatar ? const Duration(milliseconds: 50) : const Duration(milliseconds: 200),
          useOldImageOnUrlChange: true,
          placeholder: (context, url) => _defaultImage,
          errorWidget: (context, url, error) {
            imageCacheService.removeFromCache(imageUrl);
            return _defaultImage;
          },
        );
      },
    );
  }

  Widget get _defaultImage =>
      defaultImage ??
      DefaultImage(
        fit: boxFit,
        size: size?.w,
        width: (width ?? size)?.w,
        height: (height ?? size)?.w,
      );
}
