import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';

class ImagePickerHandler {
  static final _picker = ImagePicker();

  // static Future<bool> requestPermission() async => ImagePicker.
  // Platform.isAndroid
  //     ? await MyPermissionHandler.systemRequestPermission(Permission.photos)
  //     : await MyPermissionHandler.systemRequestPermission(Permission.storage);

  static Future<File?> onGetImage() async {
    // final isAllow = await requestPermission();
    // if (isAllow) {
    try {
      final result = await _picker.pickImage(source: ImageSource.gallery);
      if (result != null) {
        return File(result.path);
      }
    } catch (e) {
      showSimpleToast(LocaleKeys.deny_permission_photo.tr());
    }
    // } else {
    //   showSimpleToast('You have denied the photo permission');
    // }

    return null;
  }

  static Future<File?> onTakeCamera() async {
    // final isAllow = await requestPermission();
    // if (isAllow) {
    try {
      final result = await _picker.pickImage(source: ImageSource.camera);
      if (result != null) {
        return File(result.path);
      }
    } catch (e) {
      showSimpleToast(LocaleKeys.deny_permission_photo.tr());
    }
    // } else {
    //   showSimpleToast('You have denied the photo permission');
    // }

    return null;
  }

  static Future<List<File>> onGetMultipleImage() async {
    // final isAllow = await requestPermission();
    // if (isAllow) {
    try {
      final result = await _picker.pickMultiImage();
      if (result.isNotEmpty) {
        return result.map((value) => File(value.path)).toList();
      }
    } catch (e) {
      showSimpleToast(LocaleKeys.deny_permission_photo.tr());
    }
    // } else {
    //   showSimpleToast('You have denied the photo permission');
    // }

    return [];
  }

  static showCupertinoImages(
    BuildContext context, {
    bool getOneImage = false,
    Function(List<File> files)? getImages,
  }) {
    return DialogUtils.showCupertinoModal(
      context,
      [
        CupertinoActionModel(
          onTap: () {
            ImagePickerHandler.onTakeCamera().then(
              (values) {
                if (values != null) {
                  Navigator.pop(context);

                  getImages?.call([values]);
                }
              },
            );
          },
          title: LocaleKeys.take_photo.tr(),
        ),
        CupertinoActionModel(
          onTap: () {
            if (getOneImage) {
              ImagePickerHandler.onGetImage().then((values) {
                if (values != null) {
                  Navigator.pop(context);

                  getImages?.call([values]);
                }
              });
            } else {
              ImagePickerHandler.onGetMultipleImage().then((values) => getImages?.call(values));
            }
          },
          title: LocaleKeys.upload_image.tr(),
        ),
      ],
    );
  }

  static Future<String> resizeAndEncodeImage(File file, {int maxSizeKB = 500}) async {
    final bytes = await file.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Unable to decode image');
    }

    final resizedImage = img.copyResize(image, width: 800);

    final resizedBytes = img.encodeJpg(resizedImage, quality: 85);
    final base64Image = base64Encode(resizedBytes);

    if (resizedBytes.lengthInBytes / 1024 > maxSizeKB) {
      throw Exception('Image size exceeds $maxSizeKB KB');
    }

    return base64Image;
  }
}
