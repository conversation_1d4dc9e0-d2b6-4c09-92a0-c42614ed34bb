import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/widget/image/cache_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class Avatar extends StatelessWidget {
  const Avatar(this.url, {super.key, required this.size, this.name, this.isFamily = false, this.radius, this.textStyle, this.backgroundColor});

  final String? url, name;
  final double size;
  final double? radius;
  final bool isFamily;
  final TextStyle? textStyle;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    // Check for empty, null, or whitespace-only URLs
    final trimmedUrl = url?.trim() ?? '';
    if (trimmedUrl.isEmpty) {
      return _errorWidget();
    }

    return _buildWrap(CacheImage(
      imageUrl: url ?? '',
      width: size,
      height: size,
      boxFit: BoxFit.cover,
      defaultImage: _errorWidget(),
    ));
  }

  Widget _buildWrap(Widget child) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(radius ?? size),
      child: ColoredBox(
          color: backgroundColor ?? const Color(0xffFFE9EC), child: child),
    );
  }

  Widget _errorWidget() {
    final initials = _getAvatarName();
    if (isFamily)
      return _buildWrap(Image.asset(Assets.images.avatarFamily.path,
          width: size, height: size, fit: BoxFit.cover));

    return _buildWrap(SizedBox(
      width: size,
      height: size,
      child: Center(
        child: initials.isNotEmpty
            ? Text(
                initials,
                style: textStyle ??
                    TextStyle(
                      fontFamily: 'DM Sans',
                      fontSize: 0.333333333 * size,
                      height: 1.5,
                      color: Colors.black,
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )
            : SvgPicture.asset(Assets.images.avatarDefault.path,
                width: size * 0.75),
      ),
    ));
  }

  String _getAvatarName() {
    if (name == null || name!.trim().isEmpty) {
      return '';
    }
    var names = name!.trim().split(' ');
    names.removeWhere((element) => element.isEmpty);
    if (names.length >= 2) {
      final initials =
          '${names[0][0].toUpperCase()}${names[1][0].toUpperCase()}';
      return initials;
    } else if (names.length == 1 && names[0].isNotEmpty) {
      final initial = names[0][0].toUpperCase();
      return initial;
    } else {
      return '';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Avatar &&
        other.url == url &&
        other.name == name &&
        other.size == size &&
        other.radius == radius &&
        other.isFamily == isFamily;
  }

  @override
  int get hashCode {
    return Object.hash(url, name, size, radius, isFamily);
  }
}
