import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class GradientButton extends StatelessWidget {
  const GradientButton({super.key, required this.firstColor, required this.secondColor, this.onTap, this.text});

  final VoidCallback? onTap;
  final Color firstColor;
  final Color secondColor;
  final String? text;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: padding(top: 6, bottom: 9, horizontal: 23),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              colors: [firstColor, secondColor],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            )),
        child: Text(text ?? '', style: AppStyle.medium14(color: appTheme.whiteText)),
      ),
    );
  }
}
