import 'package:family_app/extension.dart';
import 'package:flutter/material.dart';

class ListHorizontalItems<T> extends StatelessWidget {
  const ListHorizontalItems({
    super.key,
    required this.itemBuilder,
    this.items = const [],
    this.emptyView,
    this.maxLength = 9,
    this.rightPadding = 8,
    this.mainAxisAlignment,
    this.borderCurrentItem = 0,
    this.isCurrentItem,
  });

  final List<T> items;
  final Widget? emptyView;
  final int maxLength;
  final double rightPadding;
  final Widget Function(int index, T item, double width, bool isLastOverLength, int remainLength) itemBuilder;
  final MainAxisAlignment? mainAxisAlignment;
  final double borderCurrentItem;
  final bool Function(T)? isCurrentItem;

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return emptyView ?? const SizedBox();
    }

    final paddingWidth = rightPadding.w;
    var width = (MediaQuery.of(context).size.width - 32 - (paddingWidth * (maxLength - 1))) / maxLength;

    return SizedBox(
      height: (width * 2) * (items.length / maxLength),
      child: Wrap(
        spacing: paddingWidth,
        runSpacing: paddingWidth/2,
        children: List.generate(items.length, (index) {
          // final isLastOverLength = index == maxLength - 1 && index + 1 < count;
          return SizedBox(
            width: width,
            height: width,
            child: itemBuilder(index, items[index], width, false, items.length - maxLength),
          );
        }),
      ),
    );

    // return LayoutBuilder(
    //   builder: (context, constraints) {
    //
    //
    //     return Row(
    //         mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.start,
    //         children: List.generate(length, (index) {
    //           final isLastOverLength =
    //               index == maxLength - 1 && index + 1 < count;
    //           if (isCurrentItem?.call(items[index]) ?? false) {
    //             width += borderCurrentItem;
    //           }
    //           return Padding(
    //             padding:
    //             padding(right: index == maxLength - 1 ? 0 : rightPadding),
    //             child: SizedBox(
    //               width: width,
    //               height: width,
    //               child: itemBuilder(index, items[index], width,
    //                   isLastOverLength, items.length - maxLength),
    //             ),
    //           );
    //         }));
    //   },
    // );
  }
}
