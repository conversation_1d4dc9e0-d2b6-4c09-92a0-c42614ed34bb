import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

class LineItemSelectionView extends StatelessWidget {
  const LineItemSelectionView({
    super.key,
    this.imagePath = '',
    this.title = '',
    this.content = '',
    this.paddingHorizontal = 0,
    this.paddingVertical = 15,
    this.showArrowDown = true,
    this.action,
    this.onTap,
    this.colorText,
    this.contentView,
  });

  final String imagePath;
  final String title;
  final String content;
  final VoidCallback? onTap;
  final double paddingHorizontal;
  final double paddingVertical;
  final bool showArrowDown;
  final Widget? action;
  final Color? colorText;
  final Widget? contentView;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: ColoredBox(
        color: Theme.of(context).cardColor,
        child: Padding(
          padding: padding(vertical: paddingVertical, horizontal: paddingHorizontal),
          child: Row(
            children: [
              if (imagePath.isNotEmpty) ...[ImageAssetCustom(imagePath: imagePath, size: 20), const SizedBox(width: 7)],
              if (contentView != null) ...[
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(text: title, style: AppStyle.regular14(color: colorText ?? appTheme.fadeTextColor)),
                      const TextSpan(text: '    '),
                      TextSpan(text: content, style: AppStyle.regular14())
                    ],
                  ),
                ),
                const SizedBox(width: 7),
                Expanded(child: contentView!)
              ] else ...[
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(text: title, style: AppStyle.regular14(color: colorText ?? appTheme.fadeTextColor)),
                        const TextSpan(text: '    '),
                        TextSpan(text: content, style: AppStyle.regular14())
                      ],
                    ),
                  ),
                )
              ],
              if (showArrowDown || action != null) ...[
                const SizedBox(width: 7),
                action ?? Assets.images.arrowRight.image(width: 20, height: 20),
              ]
            ],
          ),
        ),
      ),
    );
  }
}
