import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class <PERSON><PERSON> extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Color? color;
  final BorderRadius? borderRadius;
  final BoxBorder? border;
  final EdgeInsetsGeometry? padding, margin;
  final double? width, height;
  final List<BoxShadow>? boxShadow;
  final bool disabled, loading;
  final DecorationImage? image;
  final LinearGradient? gradient;
  final Clip clipBehavior;

  const Button({
    super.key,
    this.onTap,
    required this.child,
    this.color,
    this.border,
    this.borderRadius,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.boxShadow,
    this.gradient,
    this.image,
    this.clipBehavior = Clip.none,
    this.disabled = false,
    this.loading = false,
  });

  @override
  Widget build(BuildContext context) {
    final hasDecoration =
        border != null || borderRadius != null || boxShadow != null || image != null || gradient != null;

    final r = InkWell(
      onTap: disabled ? null : onTap,
      borderRadius: borderRadius,
      child: Ink(
        padding: padding,
        width: width,
        height: height,
        color: hasDecoration ? null : color,
        decoration: hasDecoration
            ? BoxDecoration(
                color: color,
                border: border,
                borderRadius: borderRadius,
                boxShadow: boxShadow,
                image: image,
                gradient: gradient,
              )
            : null,
        child: child,
      ),
    );

    final r3 = Opacity(
        opacity: disabled ? 0.4 : 1,
        child: boxShadow == null
            ? Material(borderRadius: borderRadius, clipBehavior: clipBehavior, color: Colors.transparent, child: r)
            : ClipRect(clipBehavior: clipBehavior, child: r));

    final r2 = loading
        ? Stack(alignment: Alignment.center, children: [
            r3,
            Center(
              child: SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: appTheme.whiteText)),
            ),
          ])
        : r3;

    if (margin == null) return r2;

    return Padding(padding: margin!, child: r2);
  }
}
