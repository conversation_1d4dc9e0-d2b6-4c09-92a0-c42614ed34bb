import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';

class BriefEventItemView extends StatelessWidget {
  const BriefEventItemView({super.key, this.eventModels});

  final EventModels? eventModels;

  @override
  Widget build(BuildContext context) {
    final color = eventModels?.color?.toColor ?? appTheme.primaryColor;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: padding(left: 8, top: 3, right: 7, bottom: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: color.withOpacity(.3),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      eventModels?.name ?? '',
                      style: AppStyle.regular14(),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      eventModels?.description ?? '',
                      style: AppStyle.regular12(color: appTheme.fadeTextColor),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 8.w),
              Column(
                children: [
                  Text(eventModels?.fromDate?.toLocalDT.HH_mm ?? '', style: AppStyle.regular12()),
                  SizedBox(height: 2.h),
                  Text(eventModels?.toDate?.toLocalDT.HH_mm ?? '',
                      style: AppStyle.regular12(color: appTheme.labelColor)),
                ],
              ),
            ],
          ),
        ),
        Positioned(
          top: 0,
          bottom: 0,
          child: Container(
            width: 4.w,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(4), color: color),
          ),
        ),
      ],
    );
  }
}
