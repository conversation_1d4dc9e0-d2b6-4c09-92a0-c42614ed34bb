import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class PrimaryButtonV2 extends StatelessWidget {
  const PrimaryButtonV2({
    super.key,
    this.text = '',
    this.onTap,
    this.padding,
    this.isFullWidth = true,
    this.isActive = true,
    this.icon,
    this.isLoading = false,
    this.borderRadius = 16,
    this.border,
    this.color,
    this.bg,
    this.useOpacity = true,
    this.style,
  });

  final String text;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final String? icon;
  final bool isFullWidth, isActive, isLoading, useOpacity;
  final double borderRadius;
  final BoxBorder? border;
  final Color? color, bg;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final height = MediaQuery.of(context).textScaler.scale(24);
    final radius = BorderRadius.circular(borderRadius);
    final label = Text(text,
        style: (style ?? AppStyle.bold16V2()).copyWith(color: color ?? appTheme.whiteText),
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis);
    final r = Material(
      borderRadius: radius,
      color: Colors.transparent,
      child: InkWell(
        borderRadius: radius,
        onTap: !isLoading && isActive ? onTap : null,
        child: Ink(
          width: isFullWidth ? double.infinity : null,
          padding: padding ?? paddingV2(vertical: 16, horizontal: 24),
          decoration: BoxDecoration(color: (bg ?? appTheme.primaryColorV2), borderRadius: radius, border: border),
          child: Center(
            child: isLoading
                ? SizedBox(height: height, width: height, child: CircularProgressIndicator(color: appTheme.whiteText))
                : icon == null
                    ? label
                    : Row(
                        mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(icon!, width: 24.w2),
                          Expanded(child: Padding(padding: paddingV2(left: 8.w2, right: 32.w2), child: label))
                        ],
                      ),
          ),
        ),
      ),
    );

    if (!useOpacity) return r;

    return Opacity(opacity: isActive ? 1 : 0.5, child: r);
  }
}
