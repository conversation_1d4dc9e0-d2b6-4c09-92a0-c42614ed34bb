import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:flutter/material.dart';

class Header extends StatelessWidget {
  final String _title;
  final String? subTitle;
  final Widget? action, bottom;

  const Header(this._title, {super.key, this.action, this.bottom, this.subTitle});

  @override
  Widget build(BuildContext context) {
    final title = Text(_title, textAlign: TextAlign.center, style: AppStyle.bold16());
    final r = Row(children: [
      ButtonIcon(Assets.icons.icArrowLeft.path, context.maybePop, bg: appTheme.backgroundV2),
      Expanded(
        child: Padding(
          padding: paddingV2(left: 16, right: action == null ? 56 : 16),
          child: subTitle == null
              ? title
              : Column(children: [
                  title,
                  Transform.translate(
                    offset: Offset(0, -2.w2),
                    child: Text(subTitle!, style: AppStyle.regular12V2()),
                  )
                ]),
        ),
      ),
      if (action != null) action!,
    ]);

    return Container(
      padding: paddingV2(all: 4),
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(24.w2)),
      child: bottom == null ? r : Column(children: [r, SizedBox(height: 4.w2), bottom!]),
    );
  }
}
