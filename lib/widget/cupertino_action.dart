// import 'package:flutter/cupertino.dart';

// class CupertinoAction extends StatelessWidget {
//   final VoidCallback onTap;
//   final String title;

//   const CupertinoAction({
//     required this.onTap,
//     required this.title,
//     Key? key,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return CupertinoActionSheetAction(
//       onPressed: onTap,
//       child: Text(title),
//     );
//   }
// }
