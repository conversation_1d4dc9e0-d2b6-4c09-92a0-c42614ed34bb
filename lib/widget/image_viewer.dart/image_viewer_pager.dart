import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/screen/main/memories/widget/memories_activity_info.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/custom_easy_image_viewer/easy_image_viewer.dart';
import 'package:family_app/widget/expandable_caption.dart';
import 'package:flutter/material.dart';

import 'image_viewer_provider.dart';

class ImagePagerItem {
  final DateTime dateTime;
  final String? activityId;
  final String? activityName;
  final String title;
  final String description;
  final String imageUrl;

  ImagePagerItem({
    required this.dateTime,
    this.activityId,
    this.activityName,
    required this.title,
    required this.description,
    required this.imageUrl,
  });
}

const _defaultBackgroundColor = Color.fromRGBO(0, 0, 0, 0.78);
const _defaultCloseButtonColor = Colors.white;
final _defaultCloseButtonTooltip = LocaleKeys.close.tr();

class ImageViewerPager {
  static void show({
    required BuildContext context,
    required List<ImagePagerItem> items,
    int initialIndex = 0,
    bool immersive = true,
    void Function(int)? onPageChanged,
    void Function(int)? onViewerDismissed,
    bool? useSafeArea,
    bool? swipeDismissible,
    bool? doubleTapZoomable,
    bool? infinitelyScrollable,
    Color? backgroundColor,
    Color? barrierColor,
    String? closeButtonTooltip,
    Color? closeButtonColor,
  }) {
    final provider = ImageViewerProvider(
      imageUrls: items.map((item) => item.imageUrl).toList(),
      initialIndex: initialIndex,
    );

    Widget buildInfoBuilder(BuildContext context, ImagePagerItem item) {
      return Align(
        alignment: Alignment.bottomLeft,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(top: 24, bottom: 32),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(180),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (item.dateTime.toString().isNotEmpty) ...[
                Text(
                  item.dateTime.formatDateMemories(),
                  style: AppStyle.textMdS.copyWith(color: Colors.white),
                ),
                const SizedBox(height: 8),
              ],
              if (item.activityId != null && item.activityName != null) ...[
                MemoriesActivityInfo(
                  activityId: item.activityId,
                  activityName: item.activityName,
                  textColor: Colors.white,
                ),
                const SizedBox(height: 16),
              ],
              if (item.title.isNotEmpty) ...[
                Text(
                  item.title,
                  style: AppStyle.textMdR.copyWith(color: Colors.white),
                ),
                const SizedBox(height: 6),
              ],
              if (item.description.isNotEmpty) ...[
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ExpandableCaption(
                          text: item.description,
                          style: AppStyle.textMdR.copyWith(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    showImageViewerPager(
      context,
      provider,
      initialIndex: initialIndex,
      onPageChanged: onPageChanged,
      onViewerDismissed: onViewerDismissed,
      immersive: immersive,
      useSafeArea: useSafeArea ?? false,
      swipeDismissible: swipeDismissible ?? false,
      doubleTapZoomable: doubleTapZoomable ?? false,
      infinitelyScrollable: infinitelyScrollable ?? false,
      backgroundColor: backgroundColor ?? _defaultBackgroundColor,
      barrierColor: barrierColor,
      closeButtonTooltip: closeButtonTooltip ?? _defaultCloseButtonTooltip,
      closeButtonColor: closeButtonColor ?? _defaultCloseButtonColor,
      infoBuilder: (context, index) {
        return buildInfoBuilder(context, items[index]);
      },
    );
  }
}
