import 'package:family_app/main.dart';
import 'package:family_app/widget/list_horizontal_items.dart';
import 'package:flutter/material.dart';

class ListColorSelection extends StatelessWidget {
  const ListColorSelection({super.key, this.onSelectColor, this.selectedColor});

  final Function(Color)? onSelectColor;
  final Color? selectedColor;

  @override
  Widget build(BuildContext context) {
    final colors = themeUtil.selectionColor();
    return ListHorizontalItems(
        items: colors,
        maxLength: 8,
        rightPadding: 16,
        itemBuilder: (index, item, width, isLastOverLength, remainLength) => GestureDetector(
              onTap: () => onSelectColor?.call(item),
              behavior: HitTestBehavior.opaque,
              child: Transform.scale(
                scale: item == selectedColor ? 1.15 : 1,
                child: Container(
                  width: width,
                  height: width,
                  decoration: BoxDecoration(
                      color: item,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: item == selectedColor ? appTheme.blackText.withValues(alpha: 0.5) : Colors.transparent,
                        width: 1,
                      )),
                  child: selectedColor == item
                      ? Icon(Icons.check, color: appTheme.blackText.withValues(alpha: 0.5), size: 18)
                      : null,
                ),
              ),
            ));
  }
}
