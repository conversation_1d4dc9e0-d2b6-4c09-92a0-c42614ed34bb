import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/avatar_circle_view.dart';
import 'package:flutter/material.dart';

class MemberHorizontalView extends StatelessWidget {
  const MemberHorizontalView({super.key, this.accounts = const [], this.createBy = ''});

  final List<Account> accounts;
  final String createBy;

  @override
  Widget build(BuildContext context) {
    final newList = <Account>[...accounts];
    final listUuid = newList.map((e) => e.uuid ?? '').toList();
    final accountService = locator.get<AccountService>();
    final myAccountUuid = accountService.account?.uuid;
    if (accountService.account != null) {
      final myAccount = Account(
          uuid: myAccountUuid, fullName: accountService.account?.fullName, photoUrl: accountService.account?.photoUrl);
      if (createBy.isNotEmpty) {
        if (myAccountUuid == createBy && !listUuid.contains(myAccountUuid)) {
          newList.insert(0, myAccount);
        }
      } else if (!listUuid.contains(myAccountUuid)) {
        newList.insert(0, myAccount);
      }
    }

    if (newList.isEmpty) return const SizedBox();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: newList.asMap().entries.map((e) {
          final account = e.value;
          return Padding(padding: padding(right: 10), child: AvatarCircleView(account: account, index: e.key, size: 26)
              // CircleAvatarCustom(
              //   size: 26,
              //   imageUrl: account.photoUrl ?? '',
              //   borderColor: appTheme.redE9Color,
              //   color: appTheme.whiteText,
              //   defaultWidget: CircleItem(
              //       size: 26,
              //       backgroundColor: getRandomSelectionColor(e.key),
              //       child: Center(
              //         child: Text(
              //           (account.fullName?.substring(0, 1) ?? '').toUpperCase(),
              //           style: AppStyle.medium14(color: appTheme.whiteText),
              //         ),
              //       )),
              // ),
              );
        }).toList(),
      ),
    );
  }

  Color getRandomSelectionColor(int index) {
    final colors = themeUtil.selectionColor();
    return colors[index >= colors.length ? 0 : index];
  }
}
