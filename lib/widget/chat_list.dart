import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message.dart';

/// A specialized list widget for chat messages that prevents image reloading
/// and provides better performance than ListView.builder
class ChatList<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(
          BuildContext context, T item, int index, T? previousItem, T? nextItem)
      itemBuilder;
  final ScrollController? controller;
  final EdgeInsetsGeometry? padding;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final bool reverse;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final double? cacheExtent;
  final int? semanticChildCount;
  final DragStartBehavior dragStartBehavior;
  final ScrollViewKeyboardDismissBehavior keyboardDismissBehavior;
  final String? restorationId;
  final Clip clipBehavior;
  final bool? primary;
  final double? itemExtent;
  final Widget? prototypeItem;
  final bool Function(T item)? shouldRebuild;

  const ChatList({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.reverse = false,
    this.physics,
    this.shrinkWrap = false,
    this.cacheExtent,
    this.semanticChildCount,
    this.dragStartBehavior = DragStartBehavior.start,
    this.keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    this.restorationId,
    this.clipBehavior = Clip.hardEdge,
    this.primary,
    this.itemExtent,
    this.prototypeItem,
    this.shouldRebuild,
  });

  @override
  State<ChatList<T>> createState() => _ChatListState<T>();
}

class _ChatListState<T> extends State<ChatList<T>> {
  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: widget.controller,
      reverse: widget.reverse,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      cacheExtent: widget.cacheExtent,
      semanticChildCount: widget.semanticChildCount,
      dragStartBehavior: widget.dragStartBehavior,
      keyboardDismissBehavior: widget.keyboardDismissBehavior,
      restorationId: widget.restorationId,
      clipBehavior: widget.clipBehavior,
      primary: widget.primary,
      slivers: [
        if (widget.padding != null)
          SliverPadding(
            padding: widget.padding!,
            sliver: _buildSliverList(),
          )
        else
          _buildSliverList(),
      ],
    );
  }

  Widget _buildSliverList() {
    if (widget.itemExtent != null) {
      return SliverFixedExtentList(
        itemExtent: widget.itemExtent!,
        delegate: _buildDelegate(),
      );
    } else if (widget.prototypeItem != null) {
      return SliverPrototypeExtentList(
        prototypeItem: widget.prototypeItem!,
        delegate: _buildDelegate(),
      );
    } else {
      return SliverList(
        delegate: _buildDelegate(),
      );
    }
  }

  SliverChildDelegate _buildDelegate() {
    return SliverChildBuilderDelegate(
      (context, index) {
        final item = widget.items[index];
        final previousItem = index > 0 ? widget.items[index - 1] : null;
        final nextItem =
            index < widget.items.length - 1 ? widget.items[index + 1] : null;

        // Use stable key based on item identity, not index
        final stableKey = item is ThreadMessage
            ? ValueKey('chat_item_${item.uuid}')
            : ValueKey('chat_item_${item.hashCode}');

        return _ChatListItem<T>(
          key: stableKey,
          item: item,
          previousItem: previousItem,
          nextItem: nextItem,
          index: index,
          itemBuilder: widget.itemBuilder,
          shouldRebuild: widget.shouldRebuild,
        );
      },
      childCount: widget.items.length,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: widget.addRepaintBoundaries,
    );
  }
}

/// Individual chat list item that prevents unnecessary rebuilds
class _ChatListItem<T> extends StatefulWidget {
  final T item;
  final T? previousItem;
  final T? nextItem;
  final int index;
  final Widget Function(
          BuildContext context, T item, int index, T? previousItem, T? nextItem)
      itemBuilder;
  final bool Function(T item)? shouldRebuild;

  const _ChatListItem({
    super.key,
    required this.item,
    this.previousItem,
    this.nextItem,
    required this.index,
    required this.itemBuilder,
    this.shouldRebuild,
  });

  @override
  State<_ChatListItem<T>> createState() => _ChatListItemState<T>();
}

class _ChatListItemState<T> extends State<_ChatListItem<T>>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Check if we should rebuild this item
    if (widget.shouldRebuild != null && !widget.shouldRebuild!(widget.item)) {
      // Return cached widget to prevent rebuild
      return _CachedChatItem(
        key: ValueKey('cached_${widget.item.hashCode}'),
        child: widget.itemBuilder(context, widget.item, widget.index,
            widget.previousItem, widget.nextItem),
      );
    }

    return RepaintBoundary(
      child: widget.itemBuilder(context, widget.item, widget.index,
          widget.previousItem, widget.nextItem),
    );
  }
}

/// A widget that caches its child to prevent rebuilds
class _CachedChatItem extends StatelessWidget {
  final Widget child;

  const _CachedChatItem({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(child: child);
  }
}
