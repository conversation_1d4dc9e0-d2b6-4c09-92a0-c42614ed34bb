import 'package:flutter/material.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:url_launcher/url_launcher.dart';

/// SuggestionWidget displays a list of SuggestionItem with View and Select actions.
class SuggestionWidget extends StatefulWidget {
  final List<SuggestionItem> suggestions;
  final String? confirmationText; 
  final void Function(List<SuggestionItem>) onSelect;

  const SuggestionWidget({
    Key? key,
    required this.suggestions,
    required this.onSelect,
    this.confirmationText
  }) : super(key: key);

  @override
  State<SuggestionWidget> createState() => _SuggestionWidgetState();
}

class _SuggestionWidgetState extends State<SuggestionWidget> {
  final Set<int> _selectedIds = {};
  bool _multiSelectMode = false;
  int? _expandedId;

  void _handleTap(SuggestionItem item) async {
    // if (_multiSelectMode) {
    //   setState(() {
    //     if (_selectedIds.contains(item.id)) {
    //       _selectedIds.remove(item.id);
    //     } else {
    //       _selectedIds.add(item.id);
    //     }
    //   });
    // } else {

      if (item.address.isNotEmpty) {
        final query = Uri.encodeComponent(item.address);
        final url = 'https://www.google.com/maps/search/?api=1&query=$query';
        if (await canLaunchUrl(Uri.parse(url))) {
          await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        }
      } else {
      //   widget.onView(item);
      }
    // }
  }

  void _handleLongPress(SuggestionItem item) {
    
  }

  void _handleConfirmSelection() {
    final selected = widget.suggestions
        .where((item) => _selectedIds.contains(item.id))
        .toList();
    widget.onSelect(selected);
    setState(() {
      _multiSelectMode = false;
      _selectedIds.clear();
    });
  }

  void _toggleExpand(int id) {
    setState(() {
      if (_expandedId == id) {
        _expandedId = null;
      } else {
        _expandedId = id;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...widget.suggestions.map((item) {
          final isSelected = _selectedIds.contains(item.id);
          final isExpanded = _expandedId == item.id;
          return Card(
            margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
            child: Row(
              children: [
                IconButton(
                  icon: Icon(
                    isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
                    color: isSelected ? Colors.blue : Colors.grey,
                  ),
                  onPressed: () {
                    setState(() {
                      _multiSelectMode = true;
                      if (isSelected) {
                        _selectedIds.remove(item.id);
                      } else {
                        _selectedIds.add(item.id);
                      }
                    });
                  },
                ),
                Expanded(
                  child: InkWell(
                    onTap: () => _handleTap(item),
                    // onLongPress: () => _handleLongPress(item),
                    child: Column(
                      children: [
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          title: Text(item.name),
                          trailing: IconButton(
                            icon: Icon(isExpanded ? Icons.expand_less : Icons.expand_more),
                            onPressed: () {
                              _toggleExpand(item.id);
                            },
                          ),
                        ),
                        if (isExpanded)
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                               
                                if (item.description.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 4.0),
                                    child: Text(item.description, style: const TextStyle(fontSize: 14)),
                                  ),
                                if (item.details != null && item.details!.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 4.0),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: item.details!.entries.map((e) =>
                                        Text('${e.key}: ${e.value}', style: const TextStyle(fontSize: 13, color: Colors.black54))
                                      ).toList(),
                                    ),
                                  ),
                                if (item.address.isNotEmpty)
                                  Row(
                                    children: [
                                      const Icon(Icons.location_on, size: 16, color: Colors.red),
                                      const SizedBox(width: 4),
                                      Expanded(child: Text(item.address, style: const TextStyle(fontSize: 13, color: Colors.black54))),
                                    ],
                                  ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
        if (_multiSelectMode && _selectedIds.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: ElevatedButton(
              onPressed: _handleConfirmSelection,
              child: Text(widget.confirmationText ?? 'Done'),
            ),
          ),
      ],
    );
  }
} 