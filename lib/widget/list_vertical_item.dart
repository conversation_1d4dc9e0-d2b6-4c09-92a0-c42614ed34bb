import 'package:family_app/extension.dart';
import 'package:flutter/material.dart';

class ListVerticalItem<T> extends StatelessWidget {
  const ListVerticalItem({
    required this.itemBuilder,
    super.key,
    this.items = const [],
    this.lineItemCount = 2,
    this.paddingBetweenItem = 8,
    this.paddingBetweenLine = 4,
    this.controller,
    this.divider,
    this.viewPadding,
    this.physics,
    this.isShrinkWrap = true,
  });

  final List<T> items;
  final bool isShrinkWrap;
  final Widget Function(int index, T item) itemBuilder;
  final double paddingBetweenItem;
  final double paddingBetweenLine;
  final int lineItemCount;
  final Widget? divider;
  final ScrollController? controller;
  final ScrollPhysics? physics;
  final EdgeInsets? viewPadding;

  @override
  Widget build(BuildContext context) {
    final itemColumn = items.length ~/ lineItemCount + 1;
    Widget widget;
    widget = ListView.separated(
        controller: controller,
        shrinkWrap: isShrinkWrap,
        physics: physics,
        padding: viewPadding ?? padding(),
        itemBuilder: (context, index) => buildLineItem(index),
        separatorBuilder: (context, index) =>
            divider ?? SizedBox(height: paddingBetweenLine),
        itemCount: itemColumn);
    return widget;
  }

  Widget buildLineItem(int index) {
    final currentIndex = index * lineItemCount;
    if (currentIndex >= items.length) return const SizedBox();
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: List.generate(
          lineItemCount,
          (index) => Expanded(
            child: Padding(
                padding: padding(
                    left: index <= 1 ? 0 : paddingBetweenItem,
                    right: index == 0 ? paddingBetweenItem : 0),
                child: currentIndex + index >= items.length
                    ? Container()
                    : itemBuilder(
                        currentIndex + index, items[currentIndex + index])),
          ),
        ),
      ),
    );
  }
}
