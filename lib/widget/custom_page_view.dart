import 'package:flutter/material.dart';

class CustomPageView extends StatefulWidget {
  final Widget Function(int index) viewBuilder;
  final PageController? pageController;
  final Function(int index)? onPageChanged;

  const CustomPageView({super.key, required this.viewBuilder, this.onPageChanged, this.pageController});

  @override
  State<CustomPageView> createState() => _CustomPageViewState();
}

class _CustomPageViewState extends State<CustomPageView> {
  final itemHeight = ValueNotifier(0.0);
  final currentPage = ValueNotifier(0);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: itemHeight,
        builder: (context, currentItemHeight, __) {
          return Stack(
            children: [
              ValueListenableBuilder(
                valueListenable: currentPage,
                builder: (context, page, __) => Opacity(
                  opacity: 0,
                  child: _SizeNotifierWidget(
                    child: widget.viewBuilder(0),
                    onSizeChange: (size) => itemHeight.value = size.height,
                  ),
                ),
              ),
              SizedBox(
                  height: currentItemHeight,
                  child: PageView.builder(
                    controller: widget.pageController,
                    onPageChanged: (value) {
                      currentPage.value = value;
                      widget.onPageChanged?.call(value);
                    },
                    itemBuilder: (context, index) => widget.viewBuilder(index),
                  )),
            ],
          );
        });
  }
}

class _SizeNotifierWidget extends StatefulWidget {
  final Widget child;
  final ValueChanged<Size> onSizeChange;

  const _SizeNotifierWidget({
    super.key,
    required this.child,
    required this.onSizeChange,
  });

  @override
  State<_SizeNotifierWidget> createState() => __SizeNotifierWidgetState();
}

class __SizeNotifierWidgetState extends State<_SizeNotifierWidget> {
  Size? _oldSize;

  @override
  void didUpdateWidget(covariant _SizeNotifierWidget oldWidget) {
    WidgetsBinding.instance.addPostFrameCallback((_) => _notifySize());
    super.didUpdateWidget(oldWidget);
  }

  @override
  void didChangeDependencies() {
    WidgetsBinding.instance.addPostFrameCallback((_) => _notifySize());
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  void _notifySize() {
    final size = context.size;
    if (size != null && _oldSize != size) {
      _oldSize = size;
      widget.onSizeChange(size);
    }
  }
}
