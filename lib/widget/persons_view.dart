import 'dart:math';

import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

import 'image/circle_avatar_custom.dart';

class PersonsView extends StatelessWidget {
  const PersonsView({
    super.key,
    required this.accounts,
    this.maxLength = 3,
    this.avatarSize = 32,
    this.spacing = 4,

  });

  final List<Account> accounts;
  final int maxLength;
  final double avatarSize;
  final double spacing;


  @override
  Widget build(BuildContext context) {
    int length = min(maxLength, accounts.length);
    return Wrap(
      spacing: spacing,
      children: List.generate(length, (index) {
        final account = accounts[index];
        return Stack(
          children: [
            CircleAvatarCustom(
              color: appTheme.whiteText,
              imageUrl: account.photoUrl ?? '',
              accountName: account.fullName ?? '',
              size: avatarSize,
            ),
            if(accounts.length - length > 0 && index == length - 1)
              Container(
                width: avatarSize,
                height: avatarSize,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: appTheme.blackColor.withValues(alpha: 0.46),
                  boxShadow: [
                    const BoxShadow(color: Color(0x1a000000), offset: Offset(0, 0), blurRadius: 6),
                  ],
                ),
                child: Text(
                  '+${accounts.length - length}',
                  maxLines: 1,
                  style: TextStyle(
                    fontSize: avatarSize / 2,
                    fontWeight: FontWeight.w700,
                    color: appTheme.whiteText,
                  ),
                ),
              )
          ],
        );
      }),
    );
  }
}