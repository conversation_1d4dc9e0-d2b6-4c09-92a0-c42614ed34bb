import 'package:family_app/extension.dart';
import 'package:family_app/widget/svg.dart';
import 'package:flutter/material.dart';

class ButtonIcon extends StatelessWidget {
  final VoidCallback onTap;
  final String icon;
  final Color? bg, colorIcon;
  final double size, sizeIcon;
  final double? radius;
  final Border? border;
  final bool disabled;

  const ButtonIcon(
    this.icon,
    this.onTap, {
    super.key,
    this.border,
    this.radius,
    this.disabled = false,
    this.size = 40,
    this.bg,
    this.sizeIcon = 24,
    this.colorIcon,
  });

  @override
  Widget build(BuildContext context) {
    final bRadius = BorderRadius.circular(radius?.w2 ?? size.w2);

    return Material(
      color: Colors.transparent,
      borderRadius: bRadius,
      child: InkWell(
        onTap: disabled ? null : onTap,
        borderRadius: bRadius,
        child: Ink(
          width: size.w2,
          height: size.w2,
          decoration: BoxDecoration(borderRadius: bRadius, color: bg, border: border),
          child: Center(child: Svg(icon, color: colorIcon, size: sizeIcon)),
        ),
      ),
    );
  }
}
