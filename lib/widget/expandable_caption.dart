import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class ExpandableCaption extends StatefulWidget {
  final String text;
  final int maxLines;
  final TextStyle? style;
  final String suffix;
  final String actionText;
  final Color? actionColor;

  const ExpandableCaption({
    Key? key,
    required this.text,
    this.maxLines = 2,
    this.style,
    this.suffix = '... ',
    this.actionText = 'See more',
    this.actionColor,
  }) : super(key: key);

  @override
  State<ExpandableCaption> createState() => _ExpandableCaptionState();
}

class _ExpandableCaptionState extends State<ExpandableCaption> {
  bool _expanded = false;

  @override
  Widget build(BuildContext context) {
    final textStyle = widget.style ?? DefaultTextStyle.of(context).style;

    return LayoutBuilder(
      builder: (context, size) {
        final span = TextSpan(text: widget.text, style: textStyle);
        final painter = TextPainter(
          text: span,
          maxLines: widget.maxLines,
          textDirection: TextDirection.ltr,
        );
        painter.layout(maxWidth: size.maxWidth);

        // Early return if no overflow
        if (!painter.didExceedMaxLines && widget.text.split('\n').length <= widget.maxLines) {
          return Text(widget.text, style: textStyle);
        }

        return StatefulBuilder(
          builder: (context, setStateInline) {
            if (_expanded) {
              return Text(widget.text, style: textStyle);
            }

            final truncatedText = truncateTextToFit(
              widget.text,
              textStyle,
              size.maxWidth,
              widget.maxLines,
              suffix: widget.suffix,
              actionText: widget.actionText,
            );

            return RichText(
              softWrap: true,
              overflow: TextOverflow.clip,
              maxLines: 2,
              text: TextSpan(
                style: textStyle,
                children: [
                  TextSpan(text: truncatedText),
                  WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        setStateInline(() => _expanded = true);
                        setState(() => _expanded = true);
                      },
                      child: Text(
                        widget.actionText,
                        style: TextStyle(
                          color: widget.actionColor ?? appTheme.grayColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String truncateTextToFit(
    String text,
    TextStyle style,
    double maxWidth,
    int maxLines, {
    required String suffix,
    required String actionText,
  }) {
    final textPainter = TextPainter(
      text: TextSpan(style: style, text: text),
      maxLines: maxLines,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(maxWidth: maxWidth);

    if (!textPainter.didExceedMaxLines) return text;

    int low = 0;
    int high = text.length;

    while (low <= high) {
      int mid = (low + high) ~/ 2;
      String testText = text.substring(0, mid) + suffix + actionText;

      final testPainter = TextPainter(
        text: TextSpan(style: style, text: testText),
        maxLines: maxLines,
        textDirection: TextDirection.ltr,
      );
      testPainter.layout(maxWidth: maxWidth - 3);

      if (testPainter.didExceedMaxLines || testPainter.width > maxWidth) {
        high = mid - 1;
      } else {
        low = mid + 1;
      }
    }

    final fittedLength = high;
    if (fittedLength <= 0) return '';

    return text.substring(0, fittedLength) + suffix;
  }
}
