import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

Widget buttonLineLeftWidget(
  BuildContext context, {
  required String title,
  required String text,
  required String timeTitle,
  required String timeText,
  required Color colorLine,
  required Color color,
}) {
  return Stack(
    clipBehavior: Clip.none,
    children: [
      Container(
        padding: padding(left: 8, top: 3, right: 7, bottom: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: color,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppStyle.regular14(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    text,
                    style: AppStyle.regular12(color: appTheme.fadeTextColor),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w),
            Column(
              children: [
                Text(timeTitle, style: AppStyle.regular12()),
                SizedBox(height: 2.h),
                Text(timeText, style: AppStyle.regular12(color: appTheme.labelColor)),
              ],
            ),
          ],
        ),
      ),
      Positioned(
        top: 0,
        bottom: 0,
        child: Container(
          width: 4.w,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(4), color: colorLine),
        ),
      ),
    ],
  );
}
