import 'dart:async';

import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class Search extends StatefulWidget {
  final String? hint;
  final ValueChanged<String> _onSearch;

  const Search(this._onSearch, {this.hint, super.key});

  @override
  State<Search> createState() => _SearchState();
}

class _SearchState extends State<Search> {
  late final _focusNode = FocusNode()..addListener(_listener);
  final _focus = ValueNotifier(false);
  late final _searchController = TextEditingController()..addListener(_listener);
  late final _search = ValueNotifier('')..addListener(_onSearch);
  Timer? _timer;

  @override
  void dispose() {
    _searchController.dispose();
    _search.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final border = OutlineInputBorder(borderRadius: BorderRadius.circular(24.w2), borderSide: BorderSide.none);

    return TextField(
      focusNode: _focusNode,
      controller: _searchController,
      style: AppStyle.regular14V2(),
      decoration: InputDecoration(
        hintText: widget.hint,
        isDense: true,
        hintStyle: AppStyle.regular14V2(color: appTheme.grayV2),
        filled: true,
        contentPadding: paddingV2(all: 8.w2),
        fillColor: appTheme.backgroundV2,
        border: border,
        focusedBorder: border,
        prefixIcon: SizedBox(
          width: 24.w2,
          height: 24.w2,
          child: Center(child: SvgPicture.asset(Assets.icons.icSearch.path, width: 20.w2)),
        ),
        suffixIcon: ValueListenableBuilder(
          valueListenable: _focus,
          child: SizedBox(
            width: 24.w2,
            height: 24.w2,
            child: Center(
              child: ButtonIcon(Assets.icons.icClose.path, _searchController.clear,
                  bg: Colors.white, size: 24, sizeIcon: 10),
            ),
          ),
          builder: (context, value, child) => value ? child! : const SizedBox.shrink(),
        ),
      ),
    );
  }

  void _listener() {
    _focus.value = _focusNode.hasFocus && _searchController.text.isNotEmpty;
    _search.value = _searchController.text;
  }

  void _onSearch() {
    _timer?.cancel();
    _timer = Timer(const Duration(milliseconds: 200), () => widget._onSearch(_search.value.toLowerCase()));
  }
}
