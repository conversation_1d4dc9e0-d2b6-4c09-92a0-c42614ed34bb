import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

class DefaultImage extends StatelessWidget {
  const DefaultImage({
    super.key,
    this.fit,
    this.size,
    this.height,
    this.width,
    this.imagePath,
  });

  final double? size;
  final BoxFit? fit;
  final double? height;
  final double? width;
  final String? imagePath;

  @override
  Widget build(BuildContext context) {
    return ImageAssetCustom(
      imagePath: imagePath ?? Assets.images.greyLogo.path,
      size: size,
      boxFit: fit,
      width: width,
      height: height,
    );
  }
}
