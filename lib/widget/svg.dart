import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class Svg extends StatelessWidget {
  final String _path;
  final Color? color;
  final double? width, height, size;

  const Svg(this._path, {super.key, this.color, this.width, this.height, this.size});

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      _path,
      width: width ?? size,
      height: height ?? size,
      colorFilter: color != null ? ColorFilter.mode(color!, BlendMode.srcIn) : null,
    );
  }
}
