import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/search_controller.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

class SearchCustomField extends StatefulWidget {
  const SearchCustomField({
    Key? key,
    required this.onGetSearchValue,
    this.hintText,
    this.paddingTextfield,
    this.getSearchStatus,
    this.backgroundColor,
    this.decoration,
    this.hintStyle,
    this.hasBorder = false,
    this.textStyle,
    this.margin,
    this.radius,
    this.prefixIcon,
    this.suffixIcon,
  }) : super(key: key);

  final InputDecoration? decoration;
  final EdgeInsets? paddingTextfield;
  final EdgeInsets? margin;
  final String? hintText;
  final void Function(String) onGetSearchValue;
  final void Function(bool)? getSearchStatus;
  final bool hasBorder;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final double? radius;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  @override
  State<SearchCustomField> createState() => _SearchCustomFieldState();
}

class _SearchCustomFieldState extends State<SearchCustomField> {
  final controller = TextEditingController();
  late final _searchCtrl = SearchStreamController(
    onGetValue: widget.onGetSearchValue,
    updateSearchingStatus: widget.getSearchStatus,
  );

  @override
  void dispose() {
    controller.dispose();
    _searchCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: _searchCtrl.searchStream,
      builder: (context, snapshot) => Container(
        margin: widget.margin,
        padding: widget.paddingTextfield ?? padding(horizontal: 10, vertical: 2),
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? appTheme.background,
          borderRadius: BorderRadius.circular(widget.radius ?? 1000),
          border: widget.hasBorder ? Border.all(color: appTheme.borderColor) : const Border(),
        ),
        child: TextField(
          controller: controller,
          onChanged: _searchCtrl.insertNewText,
          style: widget.textStyle ?? TextStyle(color: appTheme.primaryTextColor, fontSize: 14.fontSize),
          decoration: widget.decoration ?? InputDecoration(
            contentPadding: padding(vertical: 12),
            prefixIcon: widget.prefixIcon ?? Icon(Icons.search, color: appTheme.fadeTextColor),
            suffixIcon: widget.suffixIcon ??
                IconButton(
                  onPressed: () {
                    _searchCtrl.value = '';
                    controller.clear();
                  },
                  icon: ImageAssetCustom(imagePath: Assets.images.clearBorder.path, size: 24.w),
                ),
            border: InputBorder.none,
            hintText: widget.hintText,
            hintStyle: widget.hintStyle ?? TextStyle(color: appTheme.fadeTextColor, fontSize: 14.fontSize),
          ),
        ),
      ),
    );
  }
}
