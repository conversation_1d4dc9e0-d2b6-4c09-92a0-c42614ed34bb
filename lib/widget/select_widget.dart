import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SelectWidget extends StatelessWidget {
  const SelectWidget({
    super.key,
    required this.status,
    this.width,
    this.height,
    this.onTap,
  });

  final double? width;
  final double? height;
  final bool status;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? 16.w,
      height: height ?? 16.w,
      child: InkWell(
        onTap: onTap,
        child: Center(
          child: SvgPicture.asset(
            status ? Assets.icons.checked.path : Assets.icons.borderChecked.path,
          ),
        ),
      ),
    );
  }
}
