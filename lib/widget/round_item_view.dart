import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/assets/shadow_util.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

class RoundItemView extends StatelessWidget {
  const RoundItemView({
    super.key,
    this.imagePath = '',
    this.radius = 8,
    this.viewPadding,
    this.child,
    this.backgroundColor,
    this.text = '',
    this.viewSize,
    this.textColor,
    this.maxLines,
  });

  final String imagePath;
  final EdgeInsets? viewPadding;
  final Color? backgroundColor;
  final String text;
  final double radius;
  final Widget? child;
  final double? viewSize;
  final Color? textColor;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: viewSize,
      height: viewSize,
      padding: viewSize != null ? padding() : viewPadding ?? padding(all: 6),
      alignment: viewSize != null ? Alignment.center : null,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius),
        color: backgroundColor ?? appTheme.whiteText,
        boxShadow: ShadowUtil.backgroundShadow,
      ),
      child: text.isNotEmpty
          ? Text(
              text,
              style: AppStyle.medium14(color: textColor ?? appTheme.whiteText),
              maxLines: maxLines,
              textAlign: TextAlign.center,
            )
          : child ?? ImageAssetCustom(imagePath: imagePath, size: 16),
    );
  }
}
