import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:flutter/material.dart';

class SelectEventBottomSheet extends StatefulWidget {
  const SelectEventBottomSheet({this.onSelected, this.selectList, super.key});

  final List<EventModels>? selectList;
  final Function(List<EventModels>)? onSelected;

  static show(BuildContext context, {List<EventModels>? selectList, Function(List<EventModels>)? onSelected}) {
    BottomSheetUtils.showHeight(context, child: SelectEventBottomSheet(selectList: selectList, onSelected: onSelected));
  }

  @override
  State<SelectEventBottomSheet> createState() => _SelectEventBottomSheetState();
}

class _SelectEventBottomSheetState extends State<SelectEventBottomSheet> {
  final AccountService accountService = locator.get();
  final EventService eventService = locator.get();
  final BaseListStreamController<EventModels> eventModels = BaseListStreamController<EventModels>([]);
  final BaseListStreamController<String> selectedEvent = BaseListStreamController<String>([]);

  @override
  void initState() {
    super.initState();
    onFetchEvent();
    selectedEvent.value = widget.selectList?.map((e) => e.uuid ?? '').toList() ?? <String>[];
  }

  Future<void> onFetchEvent() async {
    if (eventModels.value.isEmpty) {
      final result = await eventService.getEventsByFamilyId(accountService.familyId);
      eventModels.value = result;
    }
  }

  @override
  void dispose() {
    eventModels.dispose();
    selectedEvent.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarCustom(
          title: LocaleKeys.select_event.tr(),
          viewPadding: padding(horizontal: 16, top: 17, bottom: 15),
          showBack: false,
          actions: [
            GestureDetector(onTap: context.maybePop, child: const Icon(Icons.close, size: 22)),
          ],
        ),
        Expanded(
          child: TwoBaseListStreamBuilder<EventModels, String>(
            firstController: eventModels,
            secondController: selectedEvent,
            builder: (eventList, selectEvents) => ListView.separated(
              physics: const BouncingScrollPhysics(),
              padding: padding(horizontal: 16),
              itemBuilder: (context, index) => _buildEvent(index, eventList, selectEvents),
              separatorBuilder: (context, index) => Divider(color: appTheme.borderColor),
              itemCount: eventList.length,
            ),
          ),
        ),
        Container(
          padding: padding(horizontal: 16, vertical: 7),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            boxShadow: [
              BoxShadow(color: Colors.black.withOpacity(.082), blurRadius: 10),
            ],
          ),
          child: SafeArea(
            top: false,
            child: TwoBaseListStreamBuilder(
              firstController: eventModels,
              secondController: selectedEvent,
              builder: (eventsList, selectEvents) {
                final isSelectAll = eventsList.length == selectEvents.length;
                return Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: onUpdateSelectAllValue,
                        behavior: HitTestBehavior.opaque,
                        child: Row(
                          children: [
                            CircleCheckbox(isChecked: isSelectAll, onTap: onUpdateSelectAllValue),
                            const SizedBox(width: 9),
                            Expanded(child: Text(LocaleKeys.select_all.tr(), style: AppStyle.regular14())),
                          ],
                        ),
                      ),
                    ),
                    PrimaryButton(
                      isFullWidth: false,
                      onTap: () {
                        final allevent = eventModels.value.where((value) => selectedEvent.value.contains(value.uuid ?? '')).toList();
                        widget.onSelected?.call(allevent);
                        context.maybePop();
                      },
                      text: LocaleKeys.save.tr(),
                      buttonPadding: padding(top: 7, bottom: 10, horizontal: 31),
                    )
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void onUpdateSelectAllValue() {
    if (selectedEvent.length == eventModels.length) {
      selectedEvent.value = [];
    } else {
      selectedEvent.value = eventModels.value.map((e) => e.uuid ?? '').toList();
    }
  }

  Widget _buildEvent(int index, List<EventModels> eventList, List<String> selectEvents) {
    final hasContain = selectEvents.contains(eventList[index].uuid);
    final event = eventList[index];

    void onChangeStatusContain() {
      if (hasContain) {
        selectedEvent.removeValue((value) => (event.uuid ?? '') == value);
      } else {
        selectedEvent.addValue(event.uuid ?? '');
      }
    }

    return GestureDetector(
      onTap: onChangeStatusContain,
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(top: 15, bottom: 14),
        child: Row(
          children: [
            CircleAvatarCustom(size: 40, borderColor: appTheme.redE9Color),
            const SizedBox(width: 12),
            Expanded(child: Text(event.name ?? '', style: AppStyle.regular14())),
            const SizedBox(width: 12),
            if (event.activityId?.isEmpty == true) CircleCheckbox(isChecked: hasContain, onTap: onChangeStatusContain),
          ],
        ),
      ),
    );
  }
}
