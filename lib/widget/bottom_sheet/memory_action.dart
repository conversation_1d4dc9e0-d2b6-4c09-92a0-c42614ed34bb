import 'dart:ui';

enum ActionType {
  edit,
  delete,
}

class MemoryAction {
  final ActionType actionType;
  final String title;
  final String? image;
  final Color? color;

  MemoryAction({required this.actionType, required this.title, this.image, this.color});

  @override
  String toString() {
    return 'MemoryAction(actionType: $actionType, title: $title, image: $image, color: $color)';
  }
}
