import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:flutter/material.dart';

abstract class BaseSelectBts<T extends StatefulWidget, M> extends State<T> {
  final models = BaseListStreamController<M>([]);
  final selected = BaseListStreamController<String>([]);

  @override
  void dispose() {
    models.dispose();
    selected.dispose();
    super.dispose();
  }

  String get title;

  Widget buildItemView(M item, int index, List<String> listSelect);

  bool isItemSelected(M item, List<String> listSelect);

  void onChangeItemSelect(M item, bool currentStatus);

  void onUpdateSelectAllValue(bool isSelectAll);

  void onConfirm();

  Widget _buildItemSelectView(M item, int index, List<String> listSelect) {
    final isSelected = isItemSelected(item, listSelect);
    return GestureDetector(
      onTap: () {
        onChangeItemSelect(item, isSelected);
      },
      behavior: HitTestBehavior.opaque,
      child: Row(
        children: [
          CircleCheckbox(isChecked: isSelected, onTap: () => onChangeItemSelect(item, isSelected)),
          const SizedBox(width: 10),
          Expanded(child: buildItemView(item, index, listSelect)),
        ],
      ),
    );
  }

  Widget buildDivider() => const SizedBox(height: 12);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarCustom(
          title: title,
          viewPadding: padding(horizontal: 16, top: 17, bottom: 15),
          showBack: false,
          backIcon: Text('Clear selection', style: AppStyle.regular14()),
          actions: [
            GestureDetector(onTap: context.maybePop, child: const Icon(Icons.close, size: 22)),
          ],
        ),
        Expanded(
            child: TwoBaseListStreamBuilder<M, String>(
                firstController: models,
                secondController: selected,
                builder: (models, selected) => ListView.separated(
                    physics: const BouncingScrollPhysics(),
                    padding: padding(horizontal: 16),
                    itemBuilder: (context, index) => _buildItemSelectView(models[index], index, selected),
                    separatorBuilder: (context, index) => buildDivider(),
                    itemCount: models.length))),
        Container(
          padding: padding(horizontal: 16, vertical: 7),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            boxShadow: [
              BoxShadow(color: Colors.black.withOpacity(.082), blurRadius: 10),
            ],
          ),
          child: SafeArea(
            top: false,
            child: TwoBaseListStreamBuilder(
              firstController: models,
              secondController: selected,
              builder: (membersList, selectMembers) {
                final isSelectAll = membersList.length == selectMembers.length;
                return Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => onUpdateSelectAllValue(isSelectAll),
                        behavior: HitTestBehavior.opaque,
                        child: Row(
                          children: [
                            CircleCheckbox(isChecked: isSelectAll, onTap: () => onUpdateSelectAllValue(isSelectAll)),
                            const SizedBox(width: 9),
                            Expanded(child: Text(LocaleKeys.select_all.tr(), style: AppStyle.regular14())),
                          ],
                        ),
                      ),
                    ),
                    PrimaryButton(
                      isFullWidth: false,
                      onTap: () {
                        onConfirm();
                        context.maybePop();
                      },
                      text: LocaleKeys.save.tr(),
                      buttonPadding: padding(top: 7, bottom: 10, horizontal: 31),
                    )
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
