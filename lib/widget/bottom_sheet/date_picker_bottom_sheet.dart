import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/calendar.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/widget/custom_page_view.dart';
import 'package:flutter/material.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:family_app/utils/timezone.dart';

class DatePickerBottomSheet extends StatefulWidget {
  const DatePickerBottomSheet({
    super.key,
    this.initialDate,
    this.title,
    this.onSelected,
    this.use24HourFormat = false,
    this.timezone,
    this.hideTime = false,
    this.startDayOfWeek,
  });

  final String? title;
  final DateTime? initialDate;
  final Function(DateTime)? onSelected;
  final bool use24HourFormat;
  final Timezone? timezone;
  final bool hideTime;
  final int? startDayOfWeek; // 1=Monday, 7=Sunday

  static Future<void> show(
    BuildContext context, {
    DateTime? initialDate,
    String? title,
    Function(DateTime)? onSelected,
    bool use24HourFormat = false,
    Timezone? timezone,
    bool hideTime = false,
    int? startDayOfWeek,
  }) async {
    final tzObj = timezone ?? TimeZoneUtils.getDefaultTimezone();
    final calendarService = locator.get<CalendarService>();
    final weekStartMonday = calendarService.weekStartMondayStream.value;
    final startDay = startDayOfWeek ?? (weekStartMonday ? DateTime.monday : DateTime.sunday);
    return BottomSheetUtils.showHeight(
      context,
      child: DatePickerBottomSheet(
        initialDate: initialDate,
        title: title,
        onSelected: onSelected,
        use24HourFormat: use24HourFormat,
        timezone: tzObj,
        hideTime: hideTime,
        startDayOfWeek: startDay,
      ),
      height: hideTime ? 0.6 : 0.7,
    );
  }

  @override
  State<DatePickerBottomSheet> createState() => _DatePickerBottomSheetState();
}

class _DatePickerBottomSheetState extends State<DatePickerBottomSheet> {
  late final tz.Location _tzLocation;
  late final DateTime _initialDateInTz;

  late final currentDate = ValueNotifier<DateTime>(_initialDateInTz);
  late final currentMonth = ValueNotifier<DateTime>(_initialDateInTz);
  final currentPage = ValueNotifier(0);
  late final timeType = ValueNotifier<String>(TimeType.am);
  final currentTimeHour = ValueNotifier<TimeOfDay>(TimeOfDay.now());

  final startDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tzLocation = TimeZoneUtils.getLocationFromTimezone(widget.timezone);
    final initial = widget.initialDate != null ? TimeZoneUtils.convertDateTimeToTimezone(widget.initialDate!, widget.timezone!) : tz.TZDateTime.now(_tzLocation);
    _initialDateInTz = initial;

    currentDate.value = initial;
    currentMonth.value = initial;
    currentTimeHour.value = TimeOfDay.fromDateTime(initial);

    if (!widget.use24HourFormat) {
      if (initial.hour >= 12) {
        timeType.value = TimeType.pm;
        if (initial.hour > 12) {
          currentTimeHour.value = currentTimeHour.value.replacing(hour: currentTimeHour.value.hour - 12);
        }
      } else {
        timeType.value = TimeType.am;
      }
    }
  }

  @override
  void dispose() {
    currentDate.dispose();
    currentPage.dispose();
    currentMonth.dispose();
    timeType.dispose();
    currentTimeHour.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: padding(horizontal: 13, top: 17, bottom: 25),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(onTap: context.maybePop, child: const Icon(Icons.close, size: 22)),
              Text(widget.title ?? 'title', style: AppStyle.medium17()),
              GestureDetector(
                onTap: () {
                  int hour;
                  if (widget.use24HourFormat) {
                    hour = currentTimeHour.value.hour;
                  } else {
                    hour = timeType.value == TimeType.pm ? (currentTimeHour.value.hour == 12 ? 12 : currentTimeHour.value.hour + 12) : (currentTimeHour.value.hour == 12 ? 0 : currentTimeHour.value.hour);
                  }
                  final selectedDate = tz.TZDateTime(
                    _tzLocation,
                    currentDate.value.year,
                    currentDate.value.month,
                    currentDate.value.day,
                    hour,
                    currentTimeHour.value.minute,
                    0,
                  );
                  widget.onSelected?.call(selectedDate);
                  context.maybePop();
                },
                child: Text(LocaleKeys.save.tr(), style: AppStyle.medium14(color: appTheme.primaryColor)),
              ),
            ],
          ),
        ),
        ValueListenableBuilder(
          valueListenable: currentMonth,
          builder: (_, date, __) {
            return Padding(
              padding: padding(left: 18, bottom: 14),
              child: Text(date.MMMM_YYYY, style: AppStyle.medium20()),
            );
          },
        ),
        _buildWeekDayHeader(),
        _buildCalendar(),
        if (!widget.hideTime) ...[
          Divider(color: appTheme.borderColor),
          _TimePickerRow(
            use24HourFormat: widget.use24HourFormat,
            timeType: timeType,
            currentTimeHour: currentTimeHour,
            onTimeChanged: (TimeOfDay newTime, String newType) {
              currentTimeHour.value = newTime;
              timeType.value = newType;
            },
          ),
        ]
      ],
    );
  }

  Widget _buildWeekDayHeader() {
    final weekStartMonday = (widget.startDayOfWeek ?? DateTime.monday) == DateTime.monday;
    final weekdays = weekStartMonday ? CalendarUtils.daysOfWeekMonday : CalendarUtils.daysOfWeekSunday;
    return Container(
      padding: padding(vertical: 7),
      decoration: BoxDecoration(color: appTheme.whiteText, boxShadow: [
        BoxShadow(
          color: const Color(0xFF7D7E80).withOpacity(.16),
          blurRadius: 10,
          offset: const Offset(0, 2),
        )
      ]),
      child: Row(
        children: List.generate(
          weekdays.length,
          (index) => SizedBox(
            width: MediaQuery.of(context).size.width / 7,
            child: Center(
              child: Text(weekdays[index], style: AppStyle.regular12()),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendar() {
    return SizedBox(
      width: double.infinity,
      child: ValueListenableBuilder(
        valueListenable: currentMonth,
        builder: (context, month, child) => ValueListenableBuilder(
          valueListenable: currentDate,
          builder: (_, date, __) {
            return CustomPageView(
              onPageChanged: (index) {
                currentMonth.value = startDate.nextMonth(index);
              },
              viewBuilder: (index) => Stack(
                alignment: Alignment.center,
                children: [Text(month.month.toString(), style: AppStyle.normal160(color: appTheme.monthColor)), _buildCalendarByMonth(month, date)],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCalendarByMonth(DateTime month, DateTime current) {
    final weekStartMonday = (widget.startDayOfWeek ?? DateTime.monday) == DateTime.monday;
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;

    // Use CalendarUtils to calculate leading empty cells
    int leadingEmpty = CalendarUtils.getLeadingEmptyDays(firstDayOfMonth, weekStartMonday);

    final rows = <Widget>[];
    var days = <Widget>[];

    for (int i = 0; i < leadingEmpty; i++) {
      days.add(_buildDayView());
    }

    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      days.add(_buildDayView(dateTime: date, current: current));
      if (days.length == 7) {
        rows.add(Row(children: days));
        days = [];
      }
    }

    if (days.isNotEmpty) {
      while (days.length < 7) {
        days.add(_buildDayView());
      }
      rows.add(Row(children: days));
    }

    return Column(children: rows);
  }

  Widget _buildDayView({DateTime? dateTime, DateTime? current}) {
    final widthView = MediaQuery.of(context).size.width / 7;
    final isSame = dateTime != null && current != null && dateTime.isSameDay(current);
    return GestureDetector(
      onTap: dateTime != null ? () => currentDate.value = dateTime : null,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: widthView,
        height: widthView,
        decoration: isSame ? BoxDecoration(color: appTheme.primaryColor, borderRadius: BorderRadius.circular(4)) : null,
        child: Center(
          child: Text(
            dateTime == null ? '' : dateTime.day.toString(),
            style: isSame ? AppStyle.regular16(color: appTheme.whiteText) : AppStyle.normal14(),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

class _TimePickerRow extends StatelessWidget {
  final bool use24HourFormat;
  final ValueNotifier<String> timeType;
  final ValueNotifier<TimeOfDay> currentTimeHour;
  final void Function(TimeOfDay, String) onTimeChanged;

  const _TimePickerRow({
    Key? key,
    required this.use24HourFormat,
    required this.timeType,
    required this.currentTimeHour,
    required this.onTimeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding(vertical: 9, left: 18, right: 16),
      child: Row(
        children: [
          Text(LocaleKeys.time.tr(), style: AppStyle.regular17()),
          const Spacer(),
          ValueListenableBuilder(
            valueListenable: currentTimeHour,
            builder: (context, time, child) => GestureDetector(
              onTap: () async {
                final currentType = timeType.value;
                final currentTime = use24HourFormat ? time : (currentType == TimeType.pm ? time.replacing(hour: time.hour + 12) : time.replacing(hour: time.hour));
                final result = await showTimePicker(
                  context: context,
                  initialTime: currentTime,
                  initialEntryMode: TimePickerEntryMode.dialOnly,
                  builder: (context, child) {
                    return MediaQuery(
                      data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: use24HourFormat),
                      child: child!,
                    );
                  },
                );
                if (result != null) {
                  if (use24HourFormat) {
                    onTimeChanged(result, timeType.value);
                  } else {
                    if (result.hour >= 12) {
                      onTimeChanged(
                        result.hour > 12 ? result.replacing(hour: result.hour - 12) : result.replacing(hour: 12),
                        TimeType.pm,
                      );
                    } else {
                      onTimeChanged(
                        result.hour == 0 ? result.replacing(hour: 12) : result,
                        TimeType.am,
                      );
                    }
                  }
                }
              },
              child: Container(
                padding: padding(vertical: 4, horizontal: 8),
                decoration: BoxDecoration(
                  color: appTheme.gray767680.withOpacity(.16),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  use24HourFormat ? '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}' : '${time.hour.toTimeFormat}:${time.minute.toTimeFormat}',
                  style: AppStyle.regular22(),
                ),
              ),
            ),
          ),
          if (!use24HourFormat) ...[
            const SizedBox(width: 6),
            Container(
              padding: padding(all: 2),
              decoration: BoxDecoration(
                color: appTheme.gray767680.withOpacity(.16),
                borderRadius: BorderRadius.circular(6),
              ),
              child: ValueListenableBuilder(
                valueListenable: timeType,
                builder: (context, type, _) => Row(
                  children: [
                    _AMPMButton(type: TimeType.am, currentType: type, onTap: () => onTimeChanged(currentTimeHour.value, TimeType.am)),
                    _AMPMButton(type: TimeType.pm, currentType: type, onTap: () => onTimeChanged(currentTimeHour.value, TimeType.pm)),
                  ],
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }
}

class _AMPMButton extends StatelessWidget {
  final String type;
  final String currentType;
  final VoidCallback onTap;

  const _AMPMButton({
    Key? key,
    required this.type,
    required this.currentType,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isCurrentType = type == currentType;
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: padding(vertical: 7, horizontal: 14.89),
        decoration: isCurrentType
            ? BoxDecoration(
                color: appTheme.whiteText,
                borderRadius: BorderRadius.circular(6.93),
                boxShadow: [
                  BoxShadow(color: Colors.black.withOpacity(.12), offset: const Offset(0, 3), blurRadius: 8),
                  BoxShadow(color: Colors.black.withOpacity(.04), offset: const Offset(0, 3), blurRadius: 1),
                ],
              )
            : const BoxDecoration(),
        child: Text(type, style: isCurrentType ? AppStyle.medium13(height: 1.8) : AppStyle.normal13()),
      ),
    );
  }
}
