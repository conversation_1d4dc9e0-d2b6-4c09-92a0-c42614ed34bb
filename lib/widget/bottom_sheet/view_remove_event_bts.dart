import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/stream/base_stream_controller.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/widget/bottom_sheet/select_event_bottom_sheet.dart';
import 'package:family_app/widget/brief_event_item_view.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:flutter/material.dart';

class ViewRemoveEventBts extends StatefulWidget {
  const ViewRemoveEventBts({super.key, this.updateEvents, this.selectList});

  final List<EventModels>? selectList;
  final Function(List<EventModels>)? updateEvents;

  static show(BuildContext context, {List<EventModels>? selectList, Function(List<EventModels>)? updateEvents}) {
    BottomSheetUtils.showHeight(context, child: ViewRemoveEventBts(selectList: selectList, updateEvents: updateEvents));
  }

  @override
  State<ViewRemoveEventBts> createState() => _ViewRemoveEventBtsState();
}

class _ViewRemoveEventBtsState extends State<ViewRemoveEventBts> {
  late final eventList = BaseStreamController<List<EventModels>>(widget.selectList ?? <EventModels>[]);
  final eventSelectRemove = BaseStreamController<List<String>>([]);

  @override
  void dispose() {
    eventSelectRemove.dispose();
    eventList.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: padding(all: 17),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(),
              Text(LocaleKeys.events.tr(), style: AppStyle.medium17()),
              GestureDetector(
                onTap: context.maybePop,
                child: Icon(Icons.clear, color: appTheme.gray80Color),
              ),
            ],
          ),
        ),
        GestureDetector(
          onTap: () => DialogUtils.showCupertinoModal(context, [
            CupertinoActionModel(
                title: LocaleKeys.select_old_event.tr(),
                onTap: () => SelectEventBottomSheet.show(
                      context,
                      selectList: eventList.value,
                      onSelected: (events) {
                        eventList.value = events;
                        widget.updateEvents?.call(events);
                        eventSelectRemove.value = [];
                      },
                    )),
            CupertinoActionModel(
              title: LocaleKeys.create_new_event.tr(),
              onTap: () async {
                final result = await context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter()));
                if (result != null && result is EventModels) {
                  eventList.value = [...eventList.value, result];
                  widget.updateEvents?.call(eventList.value);
                }
              },
            ),
          ]),
          behavior: HitTestBehavior.opaque,
          child: Padding(
            padding: padding(all: 12),
            child: Text(LocaleKeys.add_new_event.tr(), textAlign: TextAlign.center, style: AppStyle.medium16()),
          ),
        ),
        Expanded(
            child: TwoBaseStreamBuilder(
          firstController: eventList,
          secondController: eventSelectRemove,
          builder: (events, removeList) => ListView.separated(
              padding: padding(horizontal: 16),
              itemBuilder: (context, index) {
                final event = events[index];
                final isSelect = removeList.contains(event.uuid);
                return GestureDetector(
                  onTap: () => onChangeStatusOfEvent(event),
                  behavior: HitTestBehavior.opaque,
                  child: Row(
                    children: [
                      CircleCheckbox(isChecked: isSelect, onTap: () => onChangeStatusOfEvent(event)),
                      const SizedBox(width: 8),
                      Expanded(child: BriefEventItemView(eventModels: event)),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemCount: events.length),
        )),
        Container(
          padding: padding(horizontal: 16, vertical: 7),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            boxShadow: [
              BoxShadow(color: Colors.black.withOpacity(.082), blurRadius: 10),
            ],
          ),
          child: SafeArea(
            top: false,
            child: TwoBaseStreamBuilder(
              firstController: eventList,
              secondController: eventSelectRemove,
              builder: (eventsList, selectEvents) {
                final isSelectAll = eventsList.length == selectEvents.length;
                return Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: onUpdateSelectAllValue,
                        behavior: HitTestBehavior.opaque,
                        child: Row(
                          children: [
                            CircleCheckbox(isChecked: isSelectAll, onTap: onUpdateSelectAllValue),
                            const SizedBox(width: 9),
                            Expanded(child: Text(LocaleKeys.select_all.tr(), style: AppStyle.regular14())),
                          ],
                        ),
                      ),
                    ),
                    PrimaryButton(
                      isFullWidth: false,
                      onTap: () {
                        final remainEvents = [...eventList.value]
                          ..removeWhere((value) => eventSelectRemove.value.contains(value.uuid));
                        eventList.value = remainEvents;
                        eventSelectRemove.value = [];
                        widget.updateEvents?.call(remainEvents);
                      },
                      text: LocaleKeys.remove.tr(),
                      buttonPadding: padding(top: 7, bottom: 10, horizontal: 31),
                    )
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void onUpdateSelectAllValue() {
    if (eventSelectRemove.value.length == eventList.value.length) {
      eventSelectRemove.value = [];
    } else {
      eventSelectRemove.value = eventList.value.map((e) => e.uuid ?? '').toList();
    }
  }

  void onChangeStatusOfEvent(EventModels model) {
    final list = [...eventSelectRemove.value];
    if (list.contains(model.uuid)) {
      list.remove(model.uuid);
    } else {
      list.add(model.uuid ?? '');
    }
    eventSelectRemove.value = list;
  }
}
