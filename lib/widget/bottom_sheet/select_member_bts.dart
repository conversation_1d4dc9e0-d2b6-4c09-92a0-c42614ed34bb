import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/avatar_circle_view.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/search_custom_filed.dart';
import 'package:family_app/widget/square_checkbox.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SelectMemberBts extends StatefulWidget {
  const SelectMemberBts({
    super.key,
      this.onSelected,
      this.isMultipleChoose = true,
      this.selectedMembers = const [],
  });

  final Function(List<Account>)? onSelected;
  final List<String> selectedMembers;
  final bool isMultipleChoose;

  static show(BuildContext context, {List<String> selectedMembers = const [], bool isMultipleChoose = true, Function(List<Account>)? onSelected}) {
    BottomSheetUtils.showHeight(
      context,
      child: SelectMemberBts(
        isMultipleChoose: isMultipleChoose,
        onSelected: onSelected,
        selectedMembers: selectedMembers,
      ),
    );
  }

  @override
  State<SelectMemberBts> createState() => _SelectMemberBtsState();
}

class _SelectMemberBtsState extends State<SelectMemberBts> {
  final IFamilyRepository familyRepository = locator.get();
  final BaseListStreamController<Account> members = BaseListStreamController<Account>([]);
  late final BaseListStreamController<String> selectedMembers =
      BaseListStreamController<String>(widget.selectedMembers);

  final AccountService accountService = locator.get();
  final double searchBorderRadius = 20;

  @override
  void initState() {
    super.initState();
    onFetchFamilyMember();
  }

  Future<void> onFetchFamilyMember() async {
    String currentActiveFamilyUuid = accountService.myActiveFamily.value?.familyUuid ?? '';

    if (currentActiveFamilyUuid.isNotEmpty) {
      final result = await familyRepository.getUserInFamily(currentActiveFamilyUuid);

      members.value = result;
    } else {
      final result = await familyRepository.getFamilyMember();
      members.value = result;
    }
  }

  @override
  void dispose() {
    members.dispose();
    selectedMembers.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarCustom(
          title: LocaleKeys.setVisibility.tr(),
          viewPadding: padding(horizontal: 16, top: 17, bottom: 15),
          showBack: true,
          backIcon: InkWell(
            onTap: () => context.maybePop(),
            child: Text(
              LocaleKeys.cancel.tr(),
              style: AppStyle.medium16(color: appTheme.primaryColorV2),
            ),
          ),
          actions: [
            GestureDetector(
              onTap: () {
                final allMember = members.value
                    .where((value) => selectedMembers.value.contains(value.familyMemberUuid ?? ''))
                    .toList();
                widget.onSelected?.call(allMember);
                context.maybePop();
              },
              child: Text(
                LocaleKeys.done.tr(),
                style: AppStyle.medium16(color: appTheme.primaryColorV2),
              ),
            ),
          ],
        ),
        if(widget.isMultipleChoose)
          _buildCheckAllButton(context),
        Divider(color: appTheme.borderColor, indent: 16, endIndent: 16),
        Expanded(
          child: TwoBaseListStreamBuilder<Account, String>(
            firstController: members,
            secondController: selectedMembers,
            builder: (membersList, selectMembers) => ListView.separated(
              physics: const BouncingScrollPhysics(),
              padding: padding(horizontal: 16),
              itemBuilder: (context, index) => _buildMember(index, membersList, selectMembers),
              separatorBuilder: (context, index) => Divider(color: appTheme.borderColor),
              itemCount: membersList.length,
            ),
          ),
        ),
        // Container(
        //   padding: padding(horizontal: 16, vertical: 7),
        //   decoration: BoxDecoration(
        //     color: appTheme.whiteText,
        //     boxShadow: [
        //       BoxShadow(color: Colors.black.withOpacity(.082), blurRadius: 10),
        //     ],
        //   ),
        //   child: SafeArea(
        //     top: false,
        //     child: TwoBaseListStreamBuilder(
        //       firstController: members,
        //       secondController: selectedMembers,
        //       builder: (membersList, selectMembers) {
        //         final isSelectAll = membersList.length == selectMembers.length;
        //         return Row(
        //           children: [
        //             Expanded(
        //               child: GestureDetector(
        //                 onTap: onUpdateSelectAllValue,
        //                 behavior: HitTestBehavior.opaque,
        //                 child: Row(
        //                   children: [
        //                     CircleCheckbox(isChecked: isSelectAll, onTap: onUpdateSelectAllValue),
        //                     const SizedBox(width: 9),
        //                     Expanded(child: Text(LocaleKeys.select_all.tr(), style: AppStyle.regular14())),
        //                   ],
        //                 ),
        //               ),
        //             ),
        //             PrimaryButton(
        //               isFullWidth: false,
        //               onTap: () {
        //                 final allMember = members.value
        //                     .where((value) => selectedMembers.value.contains(value.familyMemberUuid ?? ''))
        //                     .toList();
        //                 widget.onSelected?.call(allMember);
        //                 context.maybePop();
        //               },
        //               text: LocaleKeys.save.tr(),
        //               buttonPadding: padding(top: 7, bottom: 10, horizontal: 31),
        //             )
        //           ],
        //         );
        //       },
        //     ),
        //   ),
        // ),
      ],
    );
  }

  void onUpdateSelectAllValue() {
    if (selectedMembers.length == members.length) {
      selectedMembers.value = [];
    } else {
      selectedMembers.value = members.value.map((e) => e.familyMemberUuid ?? '').toList();
    }
    setState(() {});
  }

  Widget _buildCheckAllButton(BuildContext context) {
    return Container(
      padding: padding(horizontal: 16, vertical: 12),
      child: TwoBaseListStreamBuilder(
        firstController: members,
        secondController: selectedMembers,
        builder: (membersList, selectMembers) {
          final isChecked = selectMembers.isNotEmpty;
          final isSelectAll = membersList.length == selectMembers.length;
          return GestureDetector(
            onTap: onUpdateSelectAllValue,
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    // shape: BoxShape.circle,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: appTheme.primaryColorV2),
                    color: isChecked ? appTheme.primaryColorV2 : Colors.transparent,
                  ),
                  child:
                      isChecked ? Icon(isSelectAll ? Icons.check : Icons.remove, size: 12, color: Colors.white) : null,
                ),
                // CircleCheckbox(isChecked: isSelectAll, onTap: onUpdateSelectAllValue),
                const SizedBox(width: 9),
                Expanded(child: Text(LocaleKeys.select_all.tr(), style: AppStyle.regular14())),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMember(int index, List<Account> membersList, List<String> selectMembers) {
    final hasContain = selectMembers.contains(membersList[index].familyMemberUuid);
    final member = membersList[index];
    void onChangeStatusContain() {
      if(!widget.isMultipleChoose){
        selectedMembers.value = [member.familyMemberUuid ?? ''];
      }else if (hasContain) {
        selectedMembers.removeValue((value) => (member.familyMemberUuid ?? '') == value);
      } else {
        selectedMembers.addValue(member.familyMemberUuid ?? '');
      }

    }

    return GestureDetector(
      onTap: onChangeStatusContain,
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(vertical: 8),
        child: Row(
          children: [
            SquareCheckbox(
              isChecked: hasContain,
              borderColor: appTheme.primaryColorV2,
              onTap: onChangeStatusContain,
            ),
            const SizedBox(width: 12),
            AvatarCircleView(account: member, index: index, size: 40),
            const SizedBox(width: 12),
            Expanded(
                child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(member.fullName ?? '', style: AppStyle.medium14()),
                Text(member.relationship ?? '', style: AppStyle.regular12()),
              ],
            )),
          ],
        ),
      ),
    );
  }
}
