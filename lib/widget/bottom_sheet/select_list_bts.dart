import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/screen/main/widget/list_item_view.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/widget/bottom_sheet/base_select_bts.dart';
import 'package:flutter/material.dart';

class SelectListBts extends StatefulWidget {
  const SelectListBts({super.key, this.selectList, this.onSelected});

  final List<ListItem>? selectList;
  final Function(List<ListItem>)? onSelected;

  static show(BuildContext context, {List<ListItem>? selectList, Function(List<ListItem>)? onSelected}) {
    BottomSheetUtils.showHeight(context, child: SelectListBts(selectList: selectList, onSelected: onSelected));
  }

  @override
  State<SelectListBts> createState() => _SelectListBtsState();
}

class _SelectListBtsState extends BaseSelectBts<SelectListBts, ListItem> {
  final mainCubit = locator.get<MainCubit>();

  @override
  String get title => LocaleKeys.select_list_text.tr();

  @override
  void initState() {
    super.initState();
    models.value = mainCubit.state.listItems;
    selected.value = widget.selectList?.map((e) => e.uuid ?? '').toList() ?? <String>[];
  }

  @override
  Widget buildItemView(ListItem item, int index, List<String> listSelect) {
    return ListItemView(
      listItem: item,
      onTap: () => onChangeItemSelect(item, isItemSelected(item, listSelect)),
    );
  }

  @override
  void onConfirm() {
    final listSelected = models.value.where((element) => selected.value.contains(element.uuid)).toList();
    widget.onSelected?.call(listSelected);
  }

  @override
  void onUpdateSelectAllValue(bool isSelectAll) {
    final newStatus = !isSelectAll;
    if (newStatus) {
      final listIds = models.value.map((e) => e.uuid ?? '').toList();
      selected.value = listIds;
    } else {
      selected.value = [];
    }
  }

  @override
  bool isItemSelected(ListItem item, List<String> listSelect) {
    return listSelect.contains(item.uuid ?? '');
  }

  @override
  void onChangeItemSelect(ListItem item, bool currentStatus) {
    final selectList = [...selected.value];
    if (currentStatus) {
      selectList.remove(item.uuid ?? '');
    } else {
      selectList.add(item.uuid ?? '');
    }
    selected.value = selectList;
  }
}
