import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/stream/base_stream_controller.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/lazy_list/lazy_list.dart';
import 'package:family_app/utils/lazy_list/lazy_list_controller.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:flutter/material.dart';

abstract class BaseLoadSelectBtsPage<M> extends StatefulWidget {
  const BaseLoadSelectBtsPage({super.key, this.item, this.onSelected});

  final M? item;
  final Function(M)? onSelected;
}

abstract class BaseLoadSelectBts<M, T extends BaseLoadSelectBtsPage<M>> extends State<T> {
  // final models = BaseListStreamController<M>([]);
  final selected = BaseStreamController<M?>(null);

  late final lazyCtrl = LazyListController(onLoad: (page) => getList(page: page));

  Future<List<M>> getList({int page = 1, int limit = LIMIT});

  @override
  void initState() {
    selected.value = widget.item;
    super.initState();
  }

  @override
  void dispose() {
    lazyCtrl.dispose();
    // models.dispose();
    selected.dispose();
    super.dispose();
  }

  String get title;

  Widget buildItemView(M item, int index, M? selected);

  bool isItemSelected(M item, M? selected);

  Widget _buildItemSelectView(M item, int index, M? selected) {
    final isSelected = isItemSelected(item, selected);
    return GestureDetector(
      onTap: () {
        this.selected.value = item;
      },
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(horizontal: 16),
        child: Row(
          children: [
            CircleCheckbox(isChecked: isSelected, onTap: () => this.selected.value = item),
            const SizedBox(width: 10),
            Expanded(child: buildItemView(item, index, selected)),
          ],
        ),
      ),
    );
  }

  Widget buildDivider() => const SizedBox(height: 12);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarCustom(
          title: title,
          viewPadding: padding(horizontal: 16, top: 17, bottom: 15),
          showBack: false,
          backIcon: Text('Clear selection', style: AppStyle.regular14()),
          actions: [
            GestureDetector(onTap: context.maybePop, child: const Icon(Icons.close, size: 22)),
          ],
        ),
        Expanded(
            child: BaseStreamBuilder(
          controller: selected,
          builder: (value) => LazyListView(
            itemBuilder: (index, item) => _buildItemSelectView(item, index, value),
            controller: lazyCtrl,
            hasRefresh: true,
          ),
        )
            // TwoBaseListStreamBuilder<M, String>(
            //     firstController: models,
            //     secondController: selected,
            //     builder: (models, selected) => ListView.separated(
            //         physics: const BouncingScrollPhysics(),
            //         padding: padding(horizontal: 16),
            //         itemBuilder: (context, index) => _buildItemSelectView(models[index], index, selected),
            //         separatorBuilder: (context, index) => buildDivider(),
            //         itemCount: models.length))

            ),
        Container(
          padding: padding(horizontal: 16, vertical: 7),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            boxShadow: [
              BoxShadow(color: Colors.black.withOpacity(.082), blurRadius: 10),
            ],
          ),
          child: SafeArea(
            top: false,
            child: BaseStreamBuilder(
              controller: selected,
              builder: (value) {
                return Row(
                  children: [
                    // Expanded(
                    //   child: GestureDetector(
                    //     onTap: () => onUpdateSelectAllValue(isSelectAll),
                    //     behavior: HitTestBehavior.opaque,
                    //     child: Row(
                    //       children: [
                    //         CircleCheckbox(isChecked: isSelectAll, onTap: () => onUpdateSelectAllValue(isSelectAll)),
                    //         const SizedBox(width: 9),
                    //         Expanded(child: Text(LocaleKeys.select_all.tr(), style: AppStyle.regular14())),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                    const Spacer(),
                    PrimaryButton(
                      isFullWidth: false,
                      isActive: value != null,
                      onTap: () {
                        widget.onSelected?.call(value as M);
                        context.maybePop();
                      },
                      text: LocaleKeys.save.tr(),
                      buttonPadding: padding(top: 7, bottom: 10, horizontal: 31),
                    )
                  ],
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
