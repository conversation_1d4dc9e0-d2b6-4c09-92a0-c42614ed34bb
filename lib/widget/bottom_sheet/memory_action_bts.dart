import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/bottom_sheet/memory_action.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MemoryActionBts extends StatelessWidget {
  const MemoryActionBts({super.key});

  static Future<dynamic> showHeightReturnValue(BuildContext context) async {
    return BottomSheetUtils.showHeightReturnValue(
      context,
      height: 0.25,
      child: const MemoryActionBts(),
    );
  }

  List<MemoryAction> onFeatchActions() {
    final actions = <MemoryAction>[];
    actions.add(MemoryAction(
        actionType: ActionType.edit, title: 'Edit', image: Assets.icons.iconEditPost.path, color: appTheme.blackColor));
    actions.add(MemoryAction(
        actionType: ActionType.delete,
        title: 'Delete',
        image: Assets.icons.iconDeletePost.path,
        color: appTheme.redColor));
    return actions;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBarCustom(
          title: 'Posts Settings',
          showBack: true,
          viewPadding: padding(horizontal: 16, top: 16),
          backIcon: GestureDetector(
            onTap: () async {
              // Go back to the previous screen
              context.maybePop();
            },
            behavior: HitTestBehavior.opaque,
            child: CircleItem(
              backgroundColor: Colors.transparent,
              padding: padding(all: 8),
              child: SvgPicture.asset(Assets.icons.iconActionBack.path),
            ),
          ),
        ),
        Divider(color: appTheme.transparentColor, indent: 16, endIndent: 16),
        Expanded(
          child: BaseListStreamBuilder<MemoryAction>(
            controller: BaseListStreamController<MemoryAction>(onFeatchActions()),
            builder: (actions) => ListView.separated(
              physics: const BouncingScrollPhysics(),
              padding: padding(horizontal: 16),
              itemBuilder: (context, index) => _buildAction(context, index, actions),
              separatorBuilder: (context, index) => Divider(color: appTheme.transparentColor),
              itemCount: actions.length,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAction(BuildContext context, int index, List<MemoryAction> actions) {
    final action = actions[index];
    return InkWell(
      onTap: () => Navigator.of(context).pop(action),
      borderRadius: BorderRadius.circular(10),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
        child: Row(
          children: [
            SvgPicture.asset(action.image ?? '', width: 24, height: 24),
            const SizedBox(width: 16),
            Text(action.title, style: AppStyle.medium16(color: action.color ?? appTheme.blackColor)),
          ],
        ),
      ),
    );
  }
}
