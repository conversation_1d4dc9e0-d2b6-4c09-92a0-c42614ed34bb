import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/widget/bottom_sheet/base_load_select_bts.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:flutter/material.dart';

class SelectActivityBts extends BaseLoadSelectBtsPage<ActivityModel> {
  const SelectActivityBts({super.key, super.item, super.onSelected});

  static show(BuildContext context, {ActivityModel? item, Function(ActivityModel)? onSelected}) {
    DialogUtils.showDialogView(context, SelectActivityBts(item: item, onSelected: onSelected));
  }

  @override
  State<SelectActivityBts> createState() => _SelectActivityBtsState();
}

class _SelectActivityBtsState extends BaseLoadSelectBts<ActivityModel, SelectActivityBts> {
  final IActivityRepository activityRepository = locator.get();
  final AccountService accountService = locator.get();

  @override
  String get title => LocaleKeys.select_list_text.tr();

  @override
  Future<List<ActivityModel>> getList({int page = 1, int limit = LIMIT}) async {
    try {
      final result = await activityRepository.getAllActivities(accountService.familyId);
      return result;
    } catch (e) {
      return [];
    }
  }

  @override
  Widget buildItemView(ActivityModel item, int index, ActivityModel? selected) {
    return Padding(
      padding: padding(top: 15, bottom: 14),
      child: Row(
        children: [
          CircleAvatarCustom(size: 40, borderColor: appTheme.redE9Color),
          const SizedBox(width: 12),
          Expanded(child: Text(item.name ?? '', style: AppStyle.regular14())),
          const SizedBox(width: 12),
          // CircleCheckbox(isChecked: selected?.uuid == item.uuid),
        ],
      ),
    );
  }

  @override
  bool isItemSelected(ActivityModel item, ActivityModel? selected) {
    return item.uuid == selected?.uuid;
  }
}
