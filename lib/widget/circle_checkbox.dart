import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class CircleCheckbox extends StatelessWidget {
  const CircleCheckbox({super.key, this.isChecked = false, this.checkColor, this.onTap});

  final bool isChecked;
  final VoidCallback? onTap;
  final Color? checkColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 16,
        height: 16,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: isChecked ? checkColor ?? appTheme.primaryColor : appTheme.hintColor),
          color: isChecked ? checkColor ?? appTheme.primaryColor : Colors.transparent,
        ),
        child: isChecked ? const Icon(Icons.check, size: 12, color: Colors.white) : null,
      ),
    );
  }
}
