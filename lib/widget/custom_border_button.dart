import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:flutter/material.dart';

import '../../main.dart';

class CustomBorderButton extends StatelessWidget {
  final Function? onPressed;
  final String buttonText;
  final bool transparent;
  final EdgeInsets? margin;
  final double? width;
  final double radius;
  final Widget? icon;
  final Color? color;
  final Color? textColor;
  final bool isLoading;
  final EdgeInsets? buttonPadding;
  final TextStyle? textStyle;

  const CustomBorderButton({
    super.key,
    this.onPressed,
    required this.buttonText,
    this.transparent = false,
    this.margin,
    this.width,
    this.radius = 12,
    this.icon,
    this.color,
    this.textColor,
    this.isLoading = false,
    this.buttonPadding,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: onPressed == null ? 0.6 : 1,
      child: Padding(
        padding: margin == null ? padding() : margin!,
        child: InkWell(
          onTap: isLoading
              ? null
              : () {
                  FocusScope.of(context).unfocus();
                  onPressed?.call();
                },
          borderRadius: BorderRadius.circular(radius),
          child: Container(
            width: width != null ? width! : double.infinity,
            padding: buttonPadding ?? padding(top: 14, bottom: 17),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(radius),
              border: Border.all(width: 1.w, color: color ?? appTheme.hintColor),
              color: appTheme.whiteText,
            ),
            child: isLoading
                ? Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 15.w,
                          width: 15.w,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(appTheme.whiteText),
                            strokeWidth: 2,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          'loading'.tr(),
                          style: AppStyle.regular12(color: appTheme.blackText),
                        ),
                      ],
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      icon != null
                          ? Padding(
                              padding: padding(right: 8),
                              child: icon,
                            )
                          : const SizedBox(),
                      Text(
                        buttonText,
                        textAlign: TextAlign.center,
                        style: textStyle ?? AppStyle.medium16(color: textColor ?? appTheme.blackText),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
