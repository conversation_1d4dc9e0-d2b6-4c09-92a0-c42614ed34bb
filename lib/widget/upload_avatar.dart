import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/image/avatar.dart';
import 'package:flutter/material.dart';

class UploadAvatar extends StatelessWidget {
  final String? url;
  final ValueChanged<String> onChange;
  final bool isFamily;

  const UploadAvatar(this.url, this.onChange, {super.key, this.isFamily = false});

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Container(
        padding: paddingV2(all: 2),
        decoration:
            BoxDecoration(shape: BoxShape.circle, border: Border.all(width: 2.w2, color: appTheme.borderColorV2)),
        child: Avatar(url, size: 88.w2, isFamily: isFamily),
      ),
      Positioned(
        right: 0,
        bottom: 0,
        child: ButtonIcon(
          Assets.icons.icPlus.path,
          () async {
            // final action = await BottomSheetUtils.showActionSheet(context);

            // switch (action) {
            //   case 1:
            //     final r = await ImagePickerHandler.onGetImage();

            //     if (r != null) onChange(r.path);
            //     break;
            //   default:
            //     break;
            // }
          },
          size: 24,
          sizeIcon: 16,
          bg: appTheme.primaryColorV2,
          colorIcon: Colors.white,
          border: Border.all(width: 2.w2, color: Colors.white),
        ),
      ),
    ]);
  }
}
