import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class SquareCheckbox extends StatelessWidget {
  const SquareCheckbox({
    super.key,
    this.isChecked = false,
    this.borderColor = const Color(0XFFC5CEDB),
    this.checkColor,
    this.onTap,
  });

  final bool isChecked;
  final VoidCallback? onTap;
  final Color? checkColor;
  final Color borderColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 12),
        child: Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            // shape: BoxShape.circle,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: isChecked ? checkColor ?? appTheme.primaryColorV2 : borderColor, width: 1),
            color: isChecked ? checkColor ?? appTheme.primaryColorV2 : Colors.transparent,
          ),
          child: isChecked ? const Icon(Icons.check, size: 12, color: Colors.white) : null,
        ),
      ),
    );
  }
}
