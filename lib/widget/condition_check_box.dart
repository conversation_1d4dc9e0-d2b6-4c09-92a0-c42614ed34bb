import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class ConditionC<PERSON><PERSON><PERSON>ox extends StatelessWidget {
  const ConditionCheckBox({
    super.key,
    required this.value,
    required this.onChanged,
    this.disabled = false,
    required this.label,
    this.isCircle = false,
  });

  final bool value, disabled, isCircle;
  final ValueChanged<bool> onChanged;
  final Widget label;

  @override
  Widget build(BuildContext context) {
    final s = 16.w2;

    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: disabled ? 0.6 : 1,
      child: Row(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.start, children: [
        Container(
          margin: paddingV2(top: 4),
          width: s,
          height: s,
          child: Transform.scale(
            scale: 1,
            child: Checkbox(
              checkColor: Colors.white,
              side: WidgetStateBorderSide.resolveWith((states) {
                return BorderSide(
                  width: 1.5.w2,
                  color: states.any({WidgetState.selected}.contains) ? appTheme.primaryColorV2 : appTheme.grayV2,
                );
              }),
              fillColor: WidgetStateProperty.resolveWith((states) {
                return states.any({WidgetState.selected}.contains) ? appTheme.primaryColorV2 : Colors.transparent;
              }),
              shape:
                  isCircle ? const CircleBorder() : RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.w2)),
              value: value,
              onChanged: disabled ? null : (bool? v) => onChanged(v!),
            ),
          ),
        ),
        SizedBox(width: 8.w2),
        Flexible(fit: FlexFit.loose, child: label),
      ]),
    );
  }
}
