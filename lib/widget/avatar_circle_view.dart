import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:flutter/material.dart';

class AvatarCircleView extends StatelessWidget {
  const AvatarCircleView({
    super.key,
    this.account,
    this.borderColor,
    this.size = 24,
    this.index = 0,
    this.borderWidth = 0,
  });

  final Account? account;
  final int index;
  final double size;
  final double borderWidth;
  final Color? borderColor;

  String getInitials() {
    final name = account?.fullName ?? '';
    if (name.isEmpty) return '';
    return name.substring(0, 1).toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    return CircleAvatarCustom(
      size: size,
      imageUrl: account?.photoUrl ?? '',
      borderColor: borderColor,
      borderWidth: borderWidth,
      color: appTheme.whiteText,
      defaultWidget: CircleItem(
        size: size,
        backgroundColor: getRandomSelectionColor(),
        child: Center(
          child: Text(
            getInitials(),
            style: AppStyle.medium14(color: appTheme.whiteText),
          ),
        ),
      ),
    );
  }

  Color getRandomSelectionColor() {
    final colors = themeUtil.selectionColor();
    return colors[index >= colors.length ? 0 : index];
  }
}
