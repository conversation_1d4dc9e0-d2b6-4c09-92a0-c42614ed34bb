import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';

class EventCardWidget extends StatelessWidget {
  final EventModels event;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showSelectionBorder;

  const EventCardWidget({
    super.key,
    required this.event,
    this.isSelected = false,
    this.onTap,
    this.showSelectionBorder = false,
  });

  @override
  Widget build(BuildContext context) {
    final color = event.color?.toColor ?? appTheme.primaryColor;

    Widget eventCard = Container(
      margin: const EdgeInsets.all(2), // Add margin to prevent border overlap
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: padding(left: 8, top: 3, right: 7, bottom: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: color.withOpacity(0.3),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.name ?? '',
                        style: AppStyle.regular14(),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 2.h),
                      if (event.description?.isNotEmpty == true)
                        Text(
                          event.description ?? '',
                          style:
                              AppStyle.regular12(color: appTheme.fadeTextColor),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
                SizedBox(width: 8.w),
                Column(
                  children: [
                    Text(
                      event.fromDate?.HH_mm ?? '',
                      style: AppStyle.regular12(),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      event.toDate?.HH_mm ?? '',
                      style: AppStyle.regular12(color: appTheme.labelColor),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Positioned(
            top: 0,
            bottom: 0,
            child: Container(
              width: 4.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: color,
              ),
            ),
          ),
        ],
      ),
    );

    // If selection border is enabled, wrap with selection container
    if (showSelectionBorder) {
      eventCard = Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(color: appTheme.primaryColorV2, width: 2)
              : null,
          color: isSelected
              ? appTheme.primaryColorV2.withOpacity(0.05)
              : Colors.transparent,
        ),
        child: eventCard,
      );
    }

    // If onTap is provided, wrap with InkWell
    if (onTap != null) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: eventCard,
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: eventCard,
    );
  }
}
