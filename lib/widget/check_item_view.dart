import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'square_checkbox.dart';

class CheckItemView extends StatelessWidget {
  const CheckItemView({
    super.key,
    required this.checkItem,
    this.textView,
    this.onDelete,
    this.updateStatus,
    this.checkColor,
    this.textDecoration,
  });

  final CheckListItem checkItem;
  final Widget? textView;
  final VoidCallback? onDelete;
  final VoidCallback? updateStatus;
  final Color? checkColor;
  final TextDecoration? textDecoration;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: updateStatus,
      behavior: HitTestBehavior.opaque,
      child: Row(
        children: [
          SquareCheckbox(
            isChecked: checkItem.isDone ?? false,
            onTap: updateStatus,
            checkColor: checkColor,
            borderColor: appTheme.blackColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: textView ??
                Text(checkItem.name ?? '',
                    style: AppStyle.regular14(color: appTheme.blackColor).copyWith(decoration: textDecoration)),
          ),
          if (onDelete != null) ...[
            const SizedBox(width: 8),
            // GestureDetector(onTap: onDelete, child: Assets.images.trash.image(width: 16, height: 16))
            GestureDetector(onTap: onDelete, child: SvgPicture.asset(Assets.icons.icDelete.path, width: 35, height: 35)),
          ],
        ],
      ),
    );
  }
}
