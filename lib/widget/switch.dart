import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class SwitchCustom extends StatelessWidget {
  final bool _value, disabled;
  final ValueChanged<bool> _onChanged;

  const SwitchCustom(this._value, this._onChanged, {this.disabled = false, super.key});

  @override
  Widget build(BuildContext context) {
    final trackColor = WidgetStateProperty<Color?>.fromMap({
      WidgetState.selected: appTheme.primaryColorV2,
    });
    final overlayColor = WidgetStateProperty<Color?>.fromMap({
      WidgetState.selected: Colors.blue,
      WidgetState.disabled: Colors.grey.shade400,
    });

    return Switch(
      value: _value,
      overlayColor: overlayColor,
      trackColor: trackColor,
      thumbColor: const WidgetStatePropertyAll(Colors.white),
      onChanged: disabled ? null : _onChanged,
    );
  }
}
