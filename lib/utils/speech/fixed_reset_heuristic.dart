import 'dart:async';
import 'package:family_app/utils/speech/speech_heuristic.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';
// Import the base heuristic file
// import 'speech_heuristic.dart';

/// Strategy 1A: The simplest heuristic. It uses a fixed timeout duration
/// that is reset every time new speech is detected.
class FixedResetHeuristic implements SpeechHeuristic {
  // --- Tuning Constant ---
  /// The fixed duration to wait for after the last speech activity.
  static const _FIXED_TIMEOUT = Duration(milliseconds: 2000);

  @override
  Duration get dynamicTimeout => _FIXED_TIMEOUT;

  @override
  void onResult(SpeechRecognitionResult result, DateTime timestamp) {
    // This heuristic does not need to process the result.
    // Its timeout is constant. The timer reset is handled by the controller.
    print(
        'FixedResetHeuristic: Activity detected. Using fixed timeout of ${_FIXED_TIMEOUT.inMilliseconds}ms');
  }

  @override
  void reset() {
    // No internal state to reset.
  }
}
