import 'dart:math';
import 'package:collection/collection.dart';
import 'package:family_app/utils/speech/speech_heuristic.dart';
import 'package:speech_to_text/speech_recognition_result.dart';

// (Requires the SpeechHeuristic abstract class from above)

/// Strategy 2.0: A noise-resistant model that analyzes pause-per-word ratios
/// and statistical outliers to differentiate user pauses from STT latency.
class NoiseResistantHeuristic implements SpeechHeuristic {
  // --- State from base adaptive model ---
  List<double> _speechPaceHistory = [];
  Duration _calculatedTimeout = _INITIAL_TIMEOUT;

  // --- State for advanced heuristics ---
  List<Duration> _interimPauseHistory = [];
  int _lastWordCount = 0;
  DateTime? _lastResultTimestamp;

  // --- Tuning Constants ---
  static const _INITIAL_TIMEOUT = Duration(milliseconds: 2500);
  static const _MIN_TIMEOUT = Duration(milliseconds: 1750);
  static const _MAX_TIMEOUT = Duration(milliseconds: 5000);
  static const _PATIENCE_BONUS = Duration(milliseconds: 1500);
  static const _MIN_PACE = 1.0;
  static const _MAX_PACE = 4.5;
  static const _PACE_HISTORY_SIZE = 5;
  static const _PAUSE_HISTORY_SIZE = 10;

  // --- Heuristic Thresholds ---
  /// How much larger a pause must be than the average (in standard deviations) to be an outlier.
  static const _OUTLIER_SIGMA_FACTOR = 2.0;

  /// The pause-per-word ratio (ms/word) threshold to detect cognitive struggle.
  static const _RATIO_THRESHOLD = 1200;

  @override
  Duration get dynamicTimeout => _calculatedTimeout;

  @override
  void reset() {
    _speechPaceHistory.clear();
    _interimPauseHistory.clear();
    _lastWordCount = 0;
    _lastResultTimestamp = null;
    _calculatedTimeout = _INITIAL_TIMEOUT;
  }

  @override
  void onResult(SpeechRecognitionResult result, DateTime timestamp) {
    final wordCount = result.recognizedWords.isEmpty
        ? 0
        : result.recognizedWords.split(' ').length;
    bool bonusApplied = false;

    if (_lastResultTimestamp != null) {
      final currentInterimPause = timestamp.difference(_lastResultTimestamp!);
      final wordsAdded = wordCount - _lastWordCount;

      if (wordsAdded > 0) {
        // --- Heuristic Layer 1: Pause-per-Word Ratio ---
        final pausePerWordRatio =
            currentInterimPause.inMilliseconds / wordsAdded;

        // --- Heuristic Layer 2: Statistical Outlier ---
        final (mean, stdDev) = _calculatePauseStats();
        final isOutlier = currentInterimPause.inMilliseconds >
            (mean.inMilliseconds +
                stdDev.inMilliseconds * _OUTLIER_SIGMA_FACTOR);

        if (pausePerWordRatio > _RATIO_THRESHOLD && isOutlier) {
          _calculatedTimeout = _MAX_TIMEOUT + _PATIENCE_BONUS;
          bonusApplied = true;
          print(
              'NoiseResistantHeuristic: High Ratio & Outlier DETECTED! Applying bonus timeout: ${_calculatedTimeout.inMilliseconds}ms');
        }

        // Update rhythm history
        _interimPauseHistory.add(currentInterimPause);
        if (_interimPauseHistory.length > _PAUSE_HISTORY_SIZE) {
          _interimPauseHistory.removeAt(0);
        }
      }
    }

    // If no bonus was applied, fall back to the standard pace calculation
    if (!bonusApplied) {
      _recalculateTimeoutBasedOnPace(wordCount, timestamp);
    }

    _lastResultTimestamp = timestamp;
    _lastWordCount = wordCount;
  }

  void _recalculateTimeoutBasedOnPace(
      int currentWordCount, DateTime timestamp) {
    if (_lastResultTimestamp == null) {
      _calculatedTimeout = _INITIAL_TIMEOUT;
      return;
    }
    // (This logic is identical to AdaptivePaceHeuristic)
    final timeElapsed = timestamp.difference(_lastResultTimestamp!);
    final wordsAdded = currentWordCount - _lastWordCount;
    if (timeElapsed.inMilliseconds > 100 && wordsAdded > 0) {
      final currentPace = wordsAdded / (timeElapsed.inMilliseconds / 1000.0);
      _speechPaceHistory.add(currentPace);
      if (_speechPaceHistory.length > _PACE_HISTORY_SIZE) {
        _speechPaceHistory.removeAt(0);
      }
    }
    if (_speechPaceHistory.isEmpty) {
      _calculatedTimeout = _INITIAL_TIMEOUT;
      return;
    }
    ;
    final averagePace = _speechPaceHistory.average;
    final normalizedPace =
        ((averagePace - _MIN_PACE) / (_MAX_PACE - _MIN_PACE)).clamp(0.0, 1.0);
    final timeoutRange = (_MAX_TIMEOUT - _MIN_TIMEOUT).inMilliseconds;
    final newTimeoutMs =
        _MAX_TIMEOUT.inMilliseconds - (normalizedPace * timeoutRange);
    _calculatedTimeout = Duration(milliseconds: newTimeoutMs.toInt());
    print(
        'NoiseResistantHeuristic: Pace=${averagePace.toStringAsFixed(1)} WPS -> Timeout=${_calculatedTimeout.inMilliseconds}ms');
  }

  (Duration, Duration) _calculatePauseStats() {
    if (_interimPauseHistory.length < 2) return (Duration.zero, Duration.zero);

    final totalMillis = _interimPauseHistory.map((d) => d.inMilliseconds).sum;
    final meanMillis = totalMillis / _interimPauseHistory.length;

    final sumOfSquares = _interimPauseHistory.map((d) {
      return pow(d.inMilliseconds - meanMillis, 2);
    }).sum;

    final stdDevMillis = sqrt(sumOfSquares / _interimPauseHistory.length);

    return (
      Duration(milliseconds: meanMillis.round()),
      Duration(milliseconds: stdDevMillis.round())
    );
  }
}
