import 'dart:async';
import 'package:family_app/utils/speech/adaptive_pace_heuristic.dart';
import 'package:family_app/utils/speech/fixed_reset_heuristic.dart';
import 'package:family_app/utils/speech/noise_resistance_heuristic.dart';
import 'package:family_app/utils/speech/speech_heuristic.dart';
import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
// Import all three heuristic files
// import 'speech_heuristic.dart';
// import 'fixed_reset_heuristic.dart'; // <-- ADD THIS
// import 'adaptive_pace_heuristic.dart';
// import 'noise_resistant_heuristic.dart';

enum HeuristicType {
  /// Strategy 1A: Fixed timer reset.
  fixedReset, // <-- ADD THIS OPTION
  /// Strategy 1B: Simple pace-based adaptation.
  adaptivePace,

  /// Strategy 2.0: Advanced noise-resistant model.
  noiseResistant,
}

class SpeechController extends ChangeNotifier {
  final SpeechToText _speech = SpeechToText();
  final SpeechHeuristic _heuristic;

  Timer? _inactivityTimer;
  bool _isListening = false;
  String _text = "";
  String _currentLocaleId = 'en-US';
  String? _errorMessage;

  // Callbacks
  Function(String)? _onSpeechFinalized;
  Function(String)? _onError;
  Function(String)? _onStatus;
  Function(bool)? _onRecordingStateChanged;

  bool get isListening => _isListening;
  String get text => _text;
  String get currentLocaleId => _currentLocaleId;
  String? get errorMessage => _errorMessage;

  // Private constructor
  SpeechController._({required HeuristicType type})
      : _heuristic = _createHeuristic(type);

  // Factory constructor to easily create a controller with a specific strategy
  factory SpeechController.create({required HeuristicType type}) {
    return SpeechController._(type: type);
  }

  // UPDATED factory method
  static SpeechHeuristic _createHeuristic(HeuristicType type) {
    switch (type) {
      case HeuristicType.fixedReset: // <-- ADD THIS CASE
        AppLogger.d("Using Heuristic: FixedResetHeuristic (1A)");
        return FixedResetHeuristic();
      case HeuristicType.adaptivePace:
        AppLogger.d("Using Heuristic: AdaptivePaceHeuristic (1B)");
        return AdaptivePaceHeuristic();
      case HeuristicType.noiseResistant:
        AppLogger.d("Using Heuristic: NoiseResistantHeuristic (2.0)");
        return NoiseResistantHeuristic();
    }
  }

  // Set callbacks
  void setCallbacks({
    Function(String)? onSpeechFinalized,
    Function(String)? onError,
    Function(String)? onStatus,
    Function(bool)? onRecordingStateChanged,
  }) {
    _onSpeechFinalized = onSpeechFinalized;
    _onError = onError;
    _onStatus = onStatus;
    _onRecordingStateChanged = onRecordingStateChanged;
  }

  Future<void> initialize() async {
    try {
      AppLogger.d("Initializing Speech to Text");
      final hasSpeech = await _speech.initialize(
        onError: _errorListener,
        onStatus: _statusListener,
      );

      if (hasSpeech) {
        final LocaleName? systemLocale = await _speech.systemLocale();
        String systemLocaleId = systemLocale?.localeId ?? 'en_US';
        _currentLocaleId = systemLocaleId.replaceAll('_', '-');
        AppLogger.d("STT initialized with locale: $_currentLocaleId");
      } else {
        AppLogger.e("Speech recognition not available");
        _errorMessage = "Speech recognition not available";
        _onError?.call(_errorMessage!);
      }
    } catch (e) {
      AppLogger.e("Error initializing STT: $e");
      _errorMessage = "Error initializing speech recognition: $e";
      _onError?.call(_errorMessage!);
    }
  }

  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.status;

    if (status.isDenied) {
      final result = await Permission.microphone.request();
      if (!result.isGranted) {
        _errorMessage = 'Microphone permission denied';
        _onError?.call(_errorMessage!);
        return false;
      }
    } else if (status.isRestricted || status.isPermanentlyDenied) {
      _errorMessage = 'Microphone permission required';
      _onError?.call(_errorMessage!);
      return false;
    }

    return true;
  }

  Future<void> startListening({
    Duration? pauseFor,
    Duration? listenFor,
    String? localeId,
    SpeechListenOptions? options,
    uiUpdate = false,
  }) async {
    if (_isListening) return;

    // Request permissions
    final hasPermission = await requestMicrophonePermission();
    if (!hasPermission) {
      return;
    }

    try {
      _heuristic.reset();
      _errorMessage = null;

      final listenOptions = options ??
          SpeechListenOptions(
            partialResults: true,
            cancelOnError: true,
            autoPunctuation: true,
            enableHapticFeedback: true,
            listenMode: ListenMode.deviceDefault,
            sampleRate: 48000,
          );

      await _speech.listen(
        onResult: _onSpeechResult,
        localeId: localeId ?? _currentLocaleId,
        pauseFor: pauseFor ?? Duration(seconds: 10),
        listenFor: listenFor ?? Duration(minutes: 15),
        listenOptions: listenOptions,
      );

      _isListening = true;
      if (uiUpdate == true) {
        _onRecordingStateChanged?.call(true);
        notifyListeners();
      }

      AppLogger.d(
          "STT: Started listening with locale: ${localeId ?? _currentLocaleId}");
    } catch (e) {
      AppLogger.e("Speech recognition error: $e");
      _errorMessage = "Speech recognition error: $e";
      _onError?.call(_errorMessage!);
      _isListening = false;

      if (uiUpdate == true) {
        _onRecordingStateChanged?.call(false);
        notifyListeners();
      }
    }
  }

  void _onSpeechResult(SpeechRecognitionResult result) async {
    logd(
        "speechResult: ${result.recognizedWords}, final?:${result.finalResult} , islistening: $_isListening");
    if (result.finalResult) {
      // If this is a final result,
      //   no point to apply heuristic , we'll need to send the command and restart after AI has responded, so we cancel the timer too.
      //   AND  call the finalization callback

      _text = result.recognizedWords;

      AppLogger.d("Finalized Speech: '$_text'");
      _inactivityTimer?.cancel();

      if (_text.isNotEmpty) {
        //set _isListening to false only if the text is not empty, to stop restarting effect
        _isListening = false;

        _onSpeechFinalized?.call(_text);
      } else {
        logd("Speech is empty, restarting listening later 11");
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } else {
      // if the result is NOT final,
      //    we will apply heuristic on the non-final result to findout the next timeout value.

      _heuristic.onResult(result, DateTime.now());
      _inactivityTimer?.cancel();
      _startInactivityTimer();
    }
    notifyListeners();
  }

  void _startInactivityTimer() {
    // if dynamicTimeout is < 2sec, there can be chances that the result.finalResult is false when this is called
    _inactivityTimer = Timer(_heuristic.dynamicTimeout, _finalizeSpeech);
  }

  void _finalizeSpeech() {
    logd("_timer fired: listening is: $_isListening ");

    if (_isListening) {
      // stop() will trigger final result to be returned in _onSpeechResult() ,
      // so we'll just call it here and handle the finalized text in the onSpeechResult()
      _speech.stop();

      _inactivityTimer?.cancel();
      // Do not call _onSpeechFinalized here; it will be called in _onSpeechResult when finalResult is true.
    }
  }

  Future<void> stopListening() async {
    try {
      _finalizeSpeech();
    } catch (e) {
      AppLogger.e('Error stopping speech recognition: $e');
      _errorMessage = 'Failed to stop speech recognition';
      _onError?.call(_errorMessage!);
    }
  }

  Future<void> cancelListening({uiUpdate = false}) async {
    try {
      _inactivityTimer?.cancel();
      logd("cancelListening: _isListening: $_isListening");
      _isListening = false;

      await _speech.cancel();
      if (uiUpdate == true) {
        _onRecordingStateChanged?.call(false);
        notifyListeners();
      }
    } catch (e) {
      AppLogger.e('Error canceling speech recognition: $e');
      _errorMessage = 'Failed to cancel speech recognition';
      _onError?.call(_errorMessage!);
    }
  }

  void _errorListener(SpeechRecognitionError error) {
    AppLogger.e("STT: Speech recognition error: $error");
    _errorMessage = "Speech recognition error: $error";
    _onError?.call(_errorMessage!);
  }

  void _statusListener(String status) {
    AppLogger.d(
        "STT: Speech recognition status: $status , _isListening: $_isListening");
    _onStatus?.call(status);
  }

  @override
  void dispose() {
    _inactivityTimer?.cancel();
    _speech.cancel();
    _speech.errorListener = null; //clean up
    _speech.statusListener = null;
    super.dispose();
  }
}
