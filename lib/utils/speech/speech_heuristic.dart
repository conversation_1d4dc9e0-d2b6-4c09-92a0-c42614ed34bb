import 'dart:async';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';

/// Defines the contract for a speech recognition heuristic.
/// Each strategy (1B, 2.0) will implement this contract,
/// allowing them to be swapped easily in the main controller.
abstract class SpeechHeuristic {
  /// Processes a new speech result to update its internal state.
  void onResult(SpeechRecognitionResult result, DateTime timestamp);

  /// Returns the calculated timeout duration based on the heuristic's logic.
  Duration get dynamicTimeout;

  /// Resets the heuristic's state for a new listening session.
  void reset();
}
