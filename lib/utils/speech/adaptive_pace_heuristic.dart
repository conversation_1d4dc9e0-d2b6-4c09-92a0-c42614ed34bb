import 'dart:math';
import 'package:collection/collection.dart';
import 'package:family_app/utils/speech/speech_heuristic.dart';
import 'package:speech_to_text/speech_recognition_result.dart';

// (Requires the SpeechHeuristic abstract class from above)

/// Strategy 1B: Adapts the timeout based on the user's Words Per Second (WPS) pace.
class AdaptivePaceHeuristic implements SpeechHeuristic {
  // --- State Variables ---
  List<double> _speechPaceHistory = [];
  int _lastWordCount = 0;
  DateTime? _lastResultTimestamp;
  Duration _calculatedTimeout = _INITIAL_TIMEOUT;

  // --- Tuning Constants ---
  static const _INITIAL_TIMEOUT = Duration(milliseconds: 2500);
  static const _MIN_TIMEOUT = Duration(milliseconds: 1500);
  static const _MAX_TIMEOUT = Duration(milliseconds: 4000);
  static const _MIN_PACE = 1.0; // Words per second
  static const _MAX_PACE = 4.5; // Words per second
  static const _HISTORY_SIZE = 5;

  @override
  Duration get dynamicTimeout => _calculatedTimeout;

  @override
  void reset() {
    _speechPaceHistory.clear();
    _lastWordCount = 0;
    _lastResultTimestamp = null;
    _calculatedTimeout = _INITIAL_TIMEOUT;
  }

  @override
  void onResult(SpeechRecognitionResult result, DateTime timestamp) {
    final wordCount = result.recognizedWords.isEmpty
        ? 0
        : result.recognizedWords.split(' ').length;

    if (_lastResultTimestamp != null && wordCount > _lastWordCount) {
      final timeElapsed = timestamp.difference(_lastResultTimestamp!);
      final wordsAdded = wordCount - _lastWordCount;

      if (timeElapsed.inMilliseconds > 100) {
        // Calculate current pace in Words Per Second
        final currentPace = wordsAdded / (timeElapsed.inMilliseconds / 1000.0);
        _updatePaceHistory(currentPace);
        _recalculateTimeout();
      }
    }

    _lastResultTimestamp = timestamp;
    _lastWordCount = wordCount;
  }

  void _updatePaceHistory(double currentPace) {
    _speechPaceHistory.add(currentPace);
    if (_speechPaceHistory.length > _HISTORY_SIZE) {
      _speechPaceHistory.removeAt(0);
    }
  }

  void _recalculateTimeout() {
    if (_speechPaceHistory.isEmpty) return;

    final averagePace = _speechPaceHistory.average;

    // Normalize the pace between 0.0 and 1.0
    final normalizedPace =
        ((averagePace - _MIN_PACE) / (_MAX_PACE - _MIN_PACE)).clamp(0.0, 1.0);

    // Interpolate the timeout: high pace -> low timeout
    final timeoutRange = (_MAX_TIMEOUT - _MIN_TIMEOUT).inMilliseconds;
    final newTimeoutMs =
        _MAX_TIMEOUT.inMilliseconds - (normalizedPace * timeoutRange);

    _calculatedTimeout = Duration(milliseconds: newTimeoutMs.toInt());
    print(
        'AdaptivePaceHeuristic: Pace=${averagePace.toStringAsFixed(1)} WPS -> Timeout=${_calculatedTimeout.inMilliseconds}ms');
  }
}
