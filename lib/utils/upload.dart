import 'dart:convert';
import 'dart:io';

import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/utils/image_scaler.dart';
import 'package:family_app/utils/log/app_logger.dart';

class Upload {
  // final IUploadRepository uploadRepository;
  // final IFamilyRepository familyRepository;
  final String familyId;

  Upload({
    // required this.uploadRepository,
    // required this.familyRepository,
    required this.familyId,
  });

  Future<StorageModel> uploadImage(File imageFile, String? optionalFileName) async {
    final IUploadRepository uploadRepository = locator.get();
    final IFamilyRepository familyRepository = locator.get();
    List<File> images = [];
    try {
      // Scale the image to 3 resolutions
      images = await ImageScaler().scale(imageFile);
      final presignUrl = await familyRepository.getStoragePresignedUrls(familyId);
      // AppLogger.d('Presigned URL: ${jsonEncode(presignUrl)}');

      var fileExt = '';
      if (optionalFileName != null) {
        fileExt = imageFile.path.split('.').last;
        optionalFileName = '$optionalFileName.$fileExt';
      }

      final fileName = optionalFileName ?? imageFile.uri.pathSegments.last;

      AppLogger.d('Uploading image: ${images[0].path}');
      final fullUploadSuccess = await uploadRepository.uploadFileToS3(presignUrl.url!, images[0]);
      AppLogger.d('Uploading md image: ${images[1].path}');
      final mdUploadSuccess = await uploadRepository.uploadFileToS3(presignUrl.urlMd!, images[1]);
      AppLogger.d('Uploading sm image: ${images[2].path}');
      final smUploadSuccess = await uploadRepository.uploadFileToS3(presignUrl.urlSm!, images[2]);

      if (fullUploadSuccess && mdUploadSuccess && smUploadSuccess) {
        AppLogger.d('Images uploaded successfully.');
        final storageModel = await familyRepository.updateFileInStorage(presignUrl.uuid!, fileName);
        // AppLogger.d('Image name updating result: ${jsonEncode(storageModel)}');
        return storageModel;
      } else {
        AppLogger.d('Image upload failed.');
      }
    } catch (e) {
      AppLogger.d('Image upload error: $e');
    } finally {
      if (images.length >= 3) {
        // Delete the scaled images, keep the original one
        images[1].delete();
        images[2].delete();
      }
    }
    return StorageModel();
  }

  Future<StorageModel> uploadFile(File file, String? optionalFileName) async {
    final IUploadRepository uploadRepository = locator.get();
    final IFamilyRepository familyRepository = locator.get();
    try {
      final presignUrl = await familyRepository.getStoragePresignedUrls(familyId);
      AppLogger.d('File presigned URL: ${jsonEncode(presignUrl)}');

      final fileName = file.uri.pathSegments.last;
      AppLogger.d('Uploading file: ${file.path}');
      final fullUploadSuccess = await uploadRepository.uploadFileToS3(presignUrl.url!, file);

      if (fullUploadSuccess) {
        AppLogger.d('File uploaded successfully.');
        final storageModel = await familyRepository.updateFileInStorage(presignUrl.uuid!, optionalFileName ?? fileName);
        AppLogger.d('File name updating result: ${jsonEncode(storageModel)}');
        return storageModel;
      } else {
        AppLogger.d('File upload failed.');
      }
    } catch (e) {
      AppLogger.d('File upload error: $e');
    }
    return StorageModel();
  }
}
