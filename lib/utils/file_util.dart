import 'dart:io';

import 'package:path_provider/path_provider.dart';

class FileUtil {
  static Future<String> getFilePath(String filename, {bool isGetTemporary = false}) async {
    Directory? dir;

    try {
      if (Platform.isIOS) {
        dir = await getApplicationDocumentsDirectory(); // for iOS
      } else {
        if (isGetTemporary) {
          dir = await getExternalStorageDirectory(); // for android
        } else {
          dir = Directory('/storage/emulated/0/Download/'); // for android
          if (!await dir.exists()) {
            dir = (await getExternalStorageDirectory())!;
          }
        }
      }
    } catch (err) {
      print(err);
    }
    return Platform.isIOS ? "${dir?.absolute.path}/$filename" : "${dir?.path}$filename";
  }
}
