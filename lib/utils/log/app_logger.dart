import 'dart:io';

import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';

// Shorthand logging functions for global use
void logd(String message, {String? tag}) => AppLogger.d(tag != null ? "[$tag] $message" : message);
void logi(String message) => AppLogger.i(message);
void logw(String message) => AppLogger.w(message);
void loge(String message, [Exception? error, StackTrace? stackTrace]) => AppLogger.e(message, error, stackTrace);

class AppLogger {
  static const bool _DEBUG = false;

  // static final _logger = Logger(
  //     printer: FullLogPrinter(
  //   SimplePrinter(),
  //   dateTimeFormat: DateTimeFormat.dateAndTime,
  // ));

  static Logger _logger = _DEBUG
      ? Logger(
          printer: FullLogPrinter(
          SimplePrinter(),
          dateTimeFormat: DateTimeFormat.dateAndTime,
        ))
      : Logger(
          printer: PrettyPrinter(methodCount: 0, printTime: true, printEmojis: false),
          // xxx: output: MultiOutput(multiOutput),
          filter: PermissiveFilter());

  // static final _devLogger = Logger(
  //   printer: PrettyPrinter(), // Development environment
  // );
  // static final _prodLogger = Logger(
  //   printer: SimplePrinter(), // Production environment
  // );

  static void d(String message) => _logger.d(message);
  static void i(String message) => _logger.i(message);
  static void w(String message) => _logger.w(message);
  static void e(String message, [Exception? error, StackTrace? stackTrace]) =>
      _logger.e(message, error: error, stackTrace: stackTrace);

  // File logging support
  static File? mFile;
  static const String _logFileName = 'app_log_active.txt';

  static Future<void> initFileLogging(Directory logDir) async {
    mFile = File('${logDir.path}/$_logFileName');
    if (!await mFile!.exists()) {
      await mFile!.create(recursive: true);
    }
    final outputs = [OriFileOutput(file: mFile!), ConsoleOutput()];
    _logger = Logger(
      printer: PrettyPrinter(methodCount: 0, printTime: true, printEmojis: false),
      output: MultiOutput(outputs),
      filter: PermissiveFilter(),
    );
  }

  File? get logFile => mFile;
}

class FullLogPrinter extends PrettyPrinter {
  final LogPrinter _realPrinter;

  FullLogPrinter(this._realPrinter, {dateTimeFormat = DateTimeFormat.none}) : super(dateTimeFormat: dateTimeFormat);

  @override
  List<String> log(LogEvent event) {
    const int chunkSize = 900; // Maximum chunk size (adjust as needed)
    final message = event.message.toString();

    var timestamp = '';
    if (printTime) {
      timestamp = getTime(DateTime.now());
    }

    // Split the message into chunks
    final chunks = <String>[];
    for (var i = 0; i < message.length; i += chunkSize) {
      chunks.add(message.substring(i, i + chunkSize > message.length ? message.length : i + chunkSize));
    }

    // Process each chunk through the real printer for formatting
    final formattedChunks = chunks
        .expand((chunk) => _realPrinter
            .log(LogEvent(event.level, '[$timestamp] $chunk ', error: event.error, stackTrace: event.stackTrace)))
        .toList();

    return formattedChunks;
  }
}

class PermissiveFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // TODO: implement shouldLog
    return true;
  }
}

//create an ouputfile to store the logs
class OriFileOutput extends LogOutput {
  OriFileOutput({required File file}) : _file = file;

  File _file;

  var _counter = 1000;
  final int _maxFileSize = 3 * 1024 * 1024;

  @override
  void output(OutputEvent event) {
    //check the file size every 1000 lines
    if (_counter-- < 0) {
      _counter = 1000;
      _rotateLogIfNeeded();
    }

    for (var line in event.lines) {
      _file.writeAsStringSync("${line.toString()}\n", mode: FileMode.writeOnlyAppend);
    }
  }

  void _rotateLogIfNeeded() {
    try {
      if (_file.lengthSync() > _maxFileSize) {
        final directory = _file.parent;
        final originalFileName = _file.uri.pathSegments.last;
        final backupFileName = '${originalFileName}_old.log';
        final backupPath = '${directory.path}/$backupFileName';
        final backupFile = File(backupPath);

        if (backupFile.existsSync()) {
          backupFile.deleteSync();
        }

        _file.renameSync(backupPath);
        _file = File('${directory.path}/$originalFileName');
      }
    } catch (e) {
      print('Error rotating log file: $e');
    }
  }
}
