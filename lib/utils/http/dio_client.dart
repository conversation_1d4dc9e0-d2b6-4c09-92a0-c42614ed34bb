import 'package:dio/dio.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';

/// HTTP client specifically designed for external API integrations
/// (Google Places, Foursquare, OpenStreetMap, TripAdvisor, etc.)
/// 
/// This is separate from the main app's DioProvider to avoid conflicts
/// and provide specialized configuration for external API calls.
class DioClient {
  static final Map<String, Dio> _instances = {};
  
  /// Get a shared instance for external APIs with standard configuration
  static Dio get instance => _getOrCreateInstance('default');
  
  /// Get a named instance for specific API configurations
  static Dio getInstance(String name) => _getOrCreateInstance(name);
  
  /// Create a custom instance with specific configuration
  static Dio createCustomInstance({
    String? name,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Map<String, dynamic>? headers,
    bool enableRetry = true,
    int retries = 3,
    bool enableLogging = true,
  }) {
    final dio = Dio(
      BaseOptions(
        connectTimeout: connectTimeout ?? const Duration(seconds: 30),
        receiveTimeout: receiveTimeout ?? const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...?headers,
        },
      ),
    );

    if (enableLogging) {
      _addLoggingInterceptor(dio, name ?? 'Custom');
    }
    
    _addErrorHandlingInterceptor(dio, name ?? 'Custom');
    
    if (enableRetry) {
      _addRetryInterceptor(dio, retries, name ?? 'Custom');
    }

    if (name != null) {
      _instances[name] = dio;
    }
    
    return dio;
  }

  static Dio _getOrCreateInstance(String name) {
    if (_instances.containsKey(name)) {
      return _instances[name]!;
    }

    final dio = Dio(
      BaseOptions(
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

//    if (kDebugMode)
    //   _addLoggingInterceptor(dio, name);

    _addErrorHandlingInterceptor(dio, name);
    _addRetryInterceptor(dio, 3, name);

    _instances[name] = dio;
    return dio;
  }

  static void _addLoggingInterceptor(Dio dio, String name) {
    dio.interceptors.add(LogInterceptor(
      requestBody: false, // Don't log request body to avoid sensitive data
      responseBody: false, // Don't log response body to avoid large payloads
      requestHeader: false, // Don't log headers to avoid API keys
      responseHeader: false,
      error: true,
      request: true,
      logPrint: (obj) => logd('ExternalApiClient[$name]: $obj'),
    ));
  }

  static void _addErrorHandlingInterceptor(Dio dio, String name) {
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        logd('ExternalApiClient[$name] Request: ${options.method} ${options.uri}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        logd('ExternalApiClient[$name] Response: ${response.statusCode} ${response.requestOptions.uri}');
        handler.next(response);
      },
      onError: (error, handler) {
        logd('ExternalApiClient[$name]URL: ${error.requestOptions.uri} Error: ${error.message}');
        if (error.response != null) {
          logd('ExternalApiClient[$name] Error Status: ${error.response?.statusCode}');
        }
        handler.next(error);
      },
    ));
  }

  static void _addRetryInterceptor(Dio dio, int retries, String name) {
    dio.interceptors.add(_ExternalApiRetryInterceptor(
      dio: dio,
      logPrint: (message) => logd('ExternalApiClient[$name] Retry: $message'),
      retries: retries,
    ));
  }

  /// Clear all cached instances (useful for testing or memory management)
  static void clearInstances() {
    _instances.clear();
  }

  /// Remove a specific named instance
  static void removeInstance(String name) {
    _instances.remove(name);
  }
}

/// Retry interceptor specifically for external API calls
class _ExternalApiRetryInterceptor extends Interceptor {
  final Dio dio;
  final int retries;
  final List<Duration> retryDelays;
  final void Function(String message) logPrint;

  _ExternalApiRetryInterceptor({
    required this.dio,
    required this.logPrint,
    this.retries = 3,
    List<Duration>? retryDelays,
  }) : retryDelays = retryDelays ?? const [
      Duration(seconds: 1),
      Duration(seconds: 2),
      Duration(seconds: 3),
    ];

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    final extra = err.requestOptions.extra;
    final retryCount = extra['retryCount'] ?? 0;

    if (retryCount < retries && _shouldRetry(err)) {
      logPrint('Retrying request (${retryCount + 1}/$retries): ${err.requestOptions.uri}');
      
      // Use exponential backoff with jitter
      final delay = retryCount < retryDelays.length 
          ? retryDelays[retryCount] 
          : Duration(seconds: (retryCount + 1) * 2);
      
      await Future.delayed(delay);
      
      // Clone the request options and add retry count
      final clonedOptions = err.requestOptions.copyWith(
        extra: {...extra, 'retryCount': retryCount + 1},
      );

      try {
        final response = await dio.fetch(clonedOptions);
        handler.resolve(response);
      } catch (e) {
        super.onError(err, handler);
      }
    } else {
      super.onError(err, handler);
    }
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.connectionError ||
        (err.response?.statusCode != null && err.response!.statusCode! >= 500);
  }
}
