import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class ShadowUtil {
  static List<BoxShadow> backgroundShadow = <BoxShadow>[
    BoxShadow(
        color: appTheme.blackColor.withOpacity(.082),
        offset: Offset(0, 2),
        blurRadius: 14)
  ];

  static List<BoxShadow> cardShadow = <BoxShadow>[
    BoxShadow(
      color: appTheme.thirdColor.withAlpha(64),
      blurRadius: 12,
      offset: const Offset(0, 4),
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> itemCardShadow = <BoxShadow>[
    BoxShadow(
      color: appTheme.thirdColor.withAlpha(26),
      blurRadius: 12,
      offset: const Offset(0, 4),
      spreadRadius: 0,
    ),
  ];
}
