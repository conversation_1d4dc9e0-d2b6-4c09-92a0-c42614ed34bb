import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/brief_event_item_view.dart';
import 'package:flutter/material.dart';

class BottomSheetUtils {
  static Future<void> showHeight(BuildContext context, {double height = .7, required Widget child}) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: appTheme.whiteText,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (BuildContext context) {
        return SizedBox(height: MediaQuery.of(context).size.height * height, width: double.infinity, child: child);
      },
    );
  }

  static Future<bool?> showHeightReturnBool(BuildContext context, {double height = .7, required Widget child}) async {
    var result = showModalBottomSheet<bool?>(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: appTheme.whiteText,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (BuildContext context) {
        return SizedBox(height: MediaQuery.of(context).size.height * height, width: double.infinity, child: child);
      },
    );
    return result;
  }

  static Future<dynamic> showHeightReturnValue(BuildContext context,
      {double height = 0.7, required Widget child}) async {
    var result = showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: appTheme.whiteText,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (BuildContext context) {
        return SizedBox(height: MediaQuery.of(context).size.height * height, width: double.infinity, child: child);
      },
    );
    return result;
  }

  static void showWrap(BuildContext context, {required Widget child}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      backgroundColor: appTheme.whiteText,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      builder: (BuildContext context) {
        return SizedBox(width: double.infinity, child: Wrap(children: [child]));
      },
    );
  }

  static showScrollable(
    BuildContext context, {
    required Widget child,
    bool isDismissible = false,
    bool shouldCloseOnMinExtent = false,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      enableDrag: false,
      isDismissible: isDismissible,
      backgroundColor: appTheme.transparentColor,
      builder: (BuildContext context) {
        return Container(
            margin: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 40,
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: child);
      },
    );
  }

  static void showWrapV2(BuildContext context, String title, Widget Function(BuildContext context) builder) {
    showModalBottomSheet(
      context: context,
      useRootNavigator: true,
      backgroundColor: appTheme.whiteText,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(24.w2), topRight: Radius.circular(24.w2))),
      builder: (context) => SizedBox(
        width: double.infinity,
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          Container(margin: paddingV2(top: 8, bottom: 16), width: 41.w2, height: 4.h2, color: appTheme.borderColorV2),
          Padding(
            padding: paddingV2(horizontal: 20.w2, bottom: 15),
            child: Text(title, style: AppStyle.bold18V2()),
          ),
          Container(height: 1.h2, color: appTheme.borderColorV2),
          builder(context),
          SizedBox(height: context.bottom),
        ]),
      ),
    );
  }

  static Future<void> showListEvent(BuildContext context,
      {required DateTime date,
      DateTime? to,
      List<EventModels> listEvent = const [],
      Function(EventModels model)? onEventTap}) {
    return BottomSheetUtils.showHeight(
      context,
      height: .6,
      child: Column(
        children: [
          Padding(
            padding: padding(all: 17),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(),
                Text('${date.MMM_d_yyyy}${to != null ? ' - ${to.MMM_d_yyyy}' : ''}', style: AppStyle.medium17()),
                GestureDetector(
                  onTap: context.maybePop,
                  child: Icon(Icons.clear, color: appTheme.gray80Color),
                ),
              ],
            ),
          ),
          Expanded(
              child: ListView.separated(
                  padding: padding(horizontal: 16),
                  itemBuilder: (context, index) {
                    final event = listEvent[index];
                    return GestureDetector(
                        onTap: () {
                          context.maybePop();
                          onEventTap?.call(event);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: BriefEventItemView(eventModels: event));
                  },
                  separatorBuilder: (context, index) => const SizedBox(height: 8),
                  itemCount: listEvent.length)),
        ],
      ),
    );
  }
}
