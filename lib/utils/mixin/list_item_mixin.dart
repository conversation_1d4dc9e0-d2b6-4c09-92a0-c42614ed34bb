import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/category.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';

mixin ListItemMixin {
  final IListRepository listRepository = locator.get();

  ///  Set the Category to a List,
  Future<ListItem> getDetailListItem(List<Category> categories, ListItem aList) async {
    logd("no need to do anything, aList.cat is : ${aList.categoryId}");

    // final category = categories.firstOrNullWhere((element) => element.uuid == aList.listUuid);

    // aList.categoryId = category?.name;

//    aList.items = await listRepository.getAllItemInList(aList.uuid ?? '');

    return aList;
  }
}
