import 'dart:convert';
import 'dart:math';

import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/http/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Configuration constants for Google Places API integration
class _PlacesConfig {
  static const String newPlacesBaseUrl = 'https://places.googleapis.com/v1';

  static const int defaultPageSize = 10;
  static const double defaultSearchRadius = 50000.0; // 50km
  static const int maxImageWidth = 400;
  static const int maxImageHeight = 400;

  static const Duration cacheExpiry = Duration(hours: 6);
  static const int maxCacheSize = 100; // Prevent memory issues
}

/// TIER PROTECTION SYSTEM for Google Places API Cost Control
///
/// This system prevents accidental escalation to expensive Pro/Enterprise tiers
/// by validating all field masks before API calls.
///
/// PRICING STRUCTURE:
/// - ESSENTIALS: $5/1000 (or FREE for IDs Only APIs)
/// - PRO: $17-32/1000 (adds rating, reviews, opening_hours, website, phone)
/// - ENTERPRISE: $20-35/1000 (adds atmosphere, editorial_summary, price_level)
///
/// CRITICAL: Field masks determine tier - requesting ANY Pro/Enterprise field
/// automatically escalates ALL requests to that tier, even basic ones!
class _TierProtection {
  /// ESSENTIALS TIER - SAFE FIELDS (FREE/unlimited for IDs Only APIs)
  static const Set<String> ESSENTIALS_SAFE_FIELDS = {
    'id',
    'displayName',
    'formattedAddress',
    'location',
    'types',
    // Note: photos is NOT in essentials - costs $7/1000
  };

  /// PRO TIER - DANGER FIELDS (triggers $17-32/1000 escalation)
  static const Set<String> PRO_TIER_FIELDS = {
    'rating',
    'userRatingCount',
    'reviews',
    'openingHours',
    'currentOpeningHours', // This is a Pro field!
    'website',
    'internationalPhoneNumber',
    'nationalPhoneNumber',
    'businessStatus',
    'priceLevel', // Actually Enterprise but commonly confused
  };

  /// ENTERPRISE TIER - CRITICAL DANGER (triggers $20-35/1000 escalation)
  static const Set<String> ENTERPRISE_TIER_FIELDS = {
    'editorialSummary',
    'reviews.atmosphere',
    'plusCode',
    'addressComponents',
    'adrFormatAddress',
    'primaryType',
    'shortFormattedAddress',
  };

  /// PHOTO FIELD - SEPARATE SKU ($7/1000)
  static const Set<String> PHOTO_FIELDS = {
    'photos',
    'photos.name',
  };

  /// Validates field mask to ensure it only contains Essentials fields
  static String validateFieldMask(String fieldMask,
      {bool allowPhotos = false}) {
    final requestedFields = fieldMask.split(',').map((f) => f.trim()).toSet();

    // Check for Pro tier fields
    final proFields = requestedFields.intersection(PRO_TIER_FIELDS);
    if (proFields.isNotEmpty) {
      throw Exception(
          'TIER ESCALATION DETECTED: Requesting Pro fields $proFields would escalate to \$17-32/1000 tier!');
    }

    // Check for Enterprise tier fields
    final enterpriseFields =
        requestedFields.intersection(ENTERPRISE_TIER_FIELDS);
    if (enterpriseFields.isNotEmpty) {
      throw Exception(
          'TIER ESCALATION DETECTED: Requesting Enterprise fields $enterpriseFields would escalate to \$20-35/1000 tier!');
    }

    // Check for photo fields (separate cost)
    final photoFields = requestedFields.intersection(PHOTO_FIELDS);
    if (photoFields.isNotEmpty && !allowPhotos) {
      throw Exception(
          'PHOTO COST DETECTED: Requesting photos would add \$7/1000 cost. Set allowPhotos=true if intentional.');
    }

    // Ensure all fields are in essentials or photos (if allowed)
    final allowedFields = Set<String>.from(ESSENTIALS_SAFE_FIELDS);
    if (allowPhotos) allowedFields.addAll(PHOTO_FIELDS);

    final invalidFields = requestedFields.difference(allowedFields);
    if (invalidFields.isNotEmpty) {
      throw Exception(
          'INVALID FIELDS DETECTED: $invalidFields are not in approved Essentials tier fields!');
    }

    return fieldMask; // Return original if validation passes
  }

  /// Get the safest possible field mask for Text Search/Nearby Search (IDs Only)
  static String getIdOnlyFieldMask() {
    return 'places.id'; // FREE/unlimited
  }

  /// Get the safest possible field mask for Place Details (Essentials only)
  static String getEssentialsFieldMask() {
    return 'id,displayName,formattedAddress,location,types'; // FREE for IDs Only API, $5/1000 for regular
  }

  /// Get field mask for photos (use sparingly - $7/1000)
  static String getPhotoFieldMask() {
    return 'photos.name'; // $7/1000
  }

  /// Log tier protection action
  static void logTierProtection(String action, String fieldMask) {
    logd('🛡️ TIER PROTECTION: $action - Using safe field mask: $fieldMask',
        tag: 'TierProtection');
  }
}

abstract class ContentProvider {
  Future<String> fetchImageUrl(String placeName, {LatLng? location, int? quantity});
  Future<String> fetchLocationDetails(String placeName, {LatLng? location});
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location});
}

class PlaceSearchResult {
  final String placeId;
  final String name;
  final String address;
  final LatLng? location;
  final String? photoReference;

  PlaceSearchResult({
    required this.placeId,
    required this.name,
    required this.address,
    this.location,
    this.photoReference,
  });
}

/// OPTIMIZED Google Places Provider with Tier Protection System
///
/// This provider ensures 100% adherence to Essentials tier pricing to prevent
/// accidental escalation to expensive Pro ($17-32/1000) or Enterprise ($20-35/1000) tiers.
///
/// COST OPTIMIZATION STRATEGY:
/// - Text Search: Uses IDs Only API (FREE/unlimited) + Essentials Details ($5/1000)
/// - Place Details: Uses only Essentials tier fields ($5/1000)
/// - Photos: Avoided in standard operations (saves $7/1000)
/// - Opening Hours: Removed (Pro field that escalates to $17/1000)
///
/// TIER PROTECTION: All field masks are validated before API calls to prevent
/// accidental tier escalation that could dramatically increase costs.
class GooglePlacesProvider implements ContentProvider {
  final String apiKey;
  final Dio _dio = DioClient.instance;

  GooglePlacesProvider(this.apiKey);

  /// Smart multi-API search with cost optimization
  @override
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location}) async {
    try {
      // Check cache first
      final cachedResults = _getCachedResults(query, location);
      if (cachedResults != null && cachedResults.isNotEmpty) {
        return cachedResults;
      }

      // Strategy 2: Use New Places API for better results
      final placesResults = await _searchNewPlacesAPI(query, location);
      if (placesResults.isNotEmpty) {
        _cacheResults(query, location, placesResults);
        return placesResults;
      }

      // Fallback: return what we have
      return []; //nominatimResults;
    } catch (e) {
      logd('searchPlaces error: $e');
      return [];
    }
  }

  /// OPTIMIZED New Places API Text Search - FREE with IDs Only pattern
  /// Uses Essentials tier only to avoid $17-32/1000 Pro escalation
  Future<List<PlaceSearchResult>> _searchNewPlacesAPI(
      String query, LatLng? location) async {
    try {
      final String url = '${_PlacesConfig.newPlacesBaseUrl}/places:searchText';

      final Map<String, dynamic> requestBody = {
        'textQuery': query,
        'pageSize': _PlacesConfig.defaultPageSize,
        'languageCode': 'en',
      };

      // Add location bias if available
      if (location != null) {
        requestBody['locationBias'] = {
          'circle': {
            'center': {
              'latitude': location.latitude,
              'longitude': location.longitude,
            },
            'radius': _PlacesConfig.defaultSearchRadius,
          }
        };
      }

      // Use IDs Only field mask for FREE tier
      final fieldMask = _TierProtection.getIdOnlyFieldMask();
      _TierProtection.logTierProtection('Text Search', fieldMask);

      final response = await _dio.post(
        url,
        data: requestBody,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask': fieldMask, // FREE/unlimited usage
          },
        ),
      );

      if (response.statusCode == 200) {
        return await _processSearchResults(response.data);
      }
    } catch (e) {
      AppLogger.e('New Places API search error for "$query": $e');
    }
    return [];
  }

  /// Process search results and fetch details for each place
  Future<List<PlaceSearchResult>> _processSearchResults(
      Map<String, dynamic> data) async {
    final List<PlaceSearchResult> results = [];

    if (data['places'] != null) {
      // First get all place IDs (FREE)
      for (final place in data['places']) {
        final placeId = place['id'] ?? '';
        if (placeId.isNotEmpty) {
          // Fetch details for each place using Essentials tier only
          final placeDetails = await _getEssentialsPlaceDetails(placeId);
          if (placeDetails != null) {
            results.add(_createPlaceSearchResult(placeId, placeDetails));
          }
        }
      }
    }

    return results;
  }

  /// Create PlaceSearchResult from place details
  PlaceSearchResult _createPlaceSearchResult(
      String placeId, Map<String, dynamic> details) {
    return PlaceSearchResult(
      placeId: placeId,
      name: details['displayName']?['text'] ?? '',
      address: details['formattedAddress'] ?? '',
      location: _extractLocationFromDetails(details),
      photoReference: null, // No photos in Essentials tier - saves $7/1000
    );
  }

  /// Extract LatLng from place details
  LatLng? _extractLocationFromDetails(Map<String, dynamic> details) {
    final location = details['location'];
    if (location != null) {
      final lat = location['latitude']?.toDouble();
      final lng = location['longitude']?.toDouble();
      if (lat != null && lng != null) {
        return LatLng(lat, lng);
      }
    }
    return null;
  }

  /// Smart image fetching with enhanced error handling and fallback strategies
  /// First tries free sources, then fetches photos only when explicitly needed
  @override
  Future<String> fetchImageUrl(String placeName, {LatLng? location, int? quantity}) async {
    if (placeName.trim().isEmpty) {
      AppLogger.w('Empty place name provided to fetchImageUrl');
      return '';
    }

    try {
      AppLogger.d(
          'Fetching image for: $placeName${location != null ? ' at ${location.latitude},${location.longitude}' : ''}');

      // Strategy 1: First search for the place (without photos to save cost)
      final places = await searchPlaces(placeName, location: location);
      if (places.isEmpty) {
        AppLogger.w('No places found for: $placeName');
        return '';
      }

      final place = places.first;
      AppLogger.d('Found place: ${place.name} (${place.placeId})');

      // Strategy 2: If we have a Google place ID, fetch photo separately
      // This is a conscious trade-off: $7/1000 for photos when explicitly needed
      if (place.placeId.isNotEmpty && _isGooglePlaceId(place.placeId)) {
        final photoUrl = await _fetchPlacePhoto(place.placeId);
        if (photoUrl.isNotEmpty) {
          AppLogger.d('Successfully fetched photo for: $placeName');
          return photoUrl;
        }
      }

      AppLogger.d('No photo available for: $placeName');
      return '';
    } catch (e) {
      AppLogger.e('fetchImageUrl error for "$placeName": $e');
      return '';
    }
  }

  /// Check if place ID is from Google Places (vs Nominatim)
  bool _isGooglePlaceId(String placeId) {
    // Google Place IDs are typically alphanumeric strings
    // Nominatim place IDs are usually numeric
    return placeId.isNotEmpty && !RegExp(r'^\d+$').hasMatch(placeId);
  }

  /// Fetch place photo by ID (conscious $7/1000 cost for photos)
  /// Only called when images are explicitly needed
  Future<String> _fetchPlacePhoto(String placeId) async {
    try {
      final url = '${_PlacesConfig.newPlacesBaseUrl}/places/$placeId';

      // Use photo field mask (conscious $7/1000 cost)
      final fieldMask = _TierProtection.getPhotoFieldMask();
      _TierProtection.logTierProtection('Photo Fetch', fieldMask);

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask': fieldMask, // photos.name field
          },
        ),
      );

      if (response.statusCode == 200) {
        return _extractPhotoUrl(response.data);
      }
    } catch (e) {
      AppLogger.e('Photo fetch error for place $placeId: $e');
    }
    return '';
  }

  /// Extract photo URL from place details response
  String _extractPhotoUrl(Map<String, dynamic> data) {
    final photos = data['photos'];
    if (photos != null && photos.isNotEmpty) {
      final photoName = photos[0]['name'];
      if (photoName != null) {
        return '${_PlacesConfig.newPlacesBaseUrl}/$photoName/media?maxWidthPx=${_PlacesConfig.maxImageWidth}&maxHeightPx=${_PlacesConfig.maxImageHeight}&key=$apiKey';
      }
    }
    return '';
  }

  /// Get location details using New Places API Place Details
  @override
  Future<String> fetchLocationDetails(String placeName,
      {LatLng? location}) async {
    if (placeName.trim().isEmpty) {
      AppLogger.w('Empty place name provided to fetchLocationDetails');
      return '';
    }

    try {
      AppLogger.d('Fetching location details for: $placeName');

      // First search for the place
      final places = await searchPlaces(placeName, location: location);
      if (places.isEmpty) {
        AppLogger.w('No places found for location details: $placeName');
        return '';
      }

      final place = places.first;

      // Get detailed information using New Places API if Google Place ID
      if (_isGooglePlaceId(place.placeId)) {
        final details = await _getEssentialsPlaceDetails(place.placeId);
        if (details != null) {
          AppLogger.d('Successfully fetched details for: $placeName');
          return jsonEncode(details);
        }
      }

      // Fallback to basic place information from search results
      final fallbackDetails = _createFallbackLocationDetails(place);
      AppLogger.d('Using fallback details for: $placeName');
      return jsonEncode(fallbackDetails);
    } catch (e) {
      AppLogger.e('fetchLocationDetails error for "$placeName": $e');
      return '';
    }
  }

  /// Create fallback location details from search result
  Map<String, dynamic> _createFallbackLocationDetails(PlaceSearchResult place) {
    return {
      'name': place.name,
      'formatted_address': place.address,
      'geometry': place.location != null
          ? {
              'location': {
                'lat': place.location!.latitude,
                'lng': place.location!.longitude,
              }
            }
          : null,
      'place_id': place.placeId,
      'types': ['establishment'], // Generic type for fallback
    };
  }

  /// OPTIMIZED New Places API Place Details - Uses Essentials tier only ($5/1000)
  /// Avoids Pro tier fields that would escalate to $17/1000
  Future<Map<String, dynamic>?> _getEssentialsPlaceDetails(
      String placeId) async {
    try {
      final url = '${_PlacesConfig.newPlacesBaseUrl}/places/$placeId';

      // Use only Essentials tier fields - NO currentOpeningHours (Pro field!)
      final fieldMask = _TierProtection.getEssentialsFieldMask();
      _TierProtection.logTierProtection('Place Details', fieldMask);

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'X-Goog-Api-Key': apiKey,
            'X-Goog-FieldMask': fieldMask, // Safe Essentials fields only
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      }
    } catch (e) {
      AppLogger.e('Place details fetch error for $placeId: $e');
    }
    return null;
  }

  // Enhanced cache system to reduce API calls and prevent memory issues
  static final Map<String, List<PlaceSearchResult>> _searchCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};

  /// Get cached search results if available and not expired
  List<PlaceSearchResult>? _getCachedResults(String query, LatLng? location) {
    final cacheKey = _getCacheKey(query, location);
    final timestamp = _cacheTimestamps[cacheKey];

    if (timestamp != null &&
        DateTime.now().difference(timestamp) < _PlacesConfig.cacheExpiry) {
      AppLogger.d('Cache hit for query: $query');
      return _searchCache[cacheKey];
    }

    // Clean up expired cache entry
    if (timestamp != null) {
      _searchCache.remove(cacheKey);
      _cacheTimestamps.remove(cacheKey);
      AppLogger.d('Cleaned expired cache entry for: $query');
    }

    return null;
  }

  /// Cache search results with memory management
  void _cacheResults(
      String query, LatLng? location, List<PlaceSearchResult> results) {
    // Prevent cache from growing too large
    _evictOldestCacheEntries();

    final cacheKey = _getCacheKey(query, location);
    _searchCache[cacheKey] = results;
    _cacheTimestamps[cacheKey] = DateTime.now();

    AppLogger.d('Cached ${results.length} results for query: $query');
  }

  /// Evict oldest cache entries if cache is too large
  void _evictOldestCacheEntries() {
    if (_searchCache.length >= _PlacesConfig.maxCacheSize) {
      // Find and remove the oldest entry
      String? oldestKey;
      DateTime? oldestTime;

      for (final entry in _cacheTimestamps.entries) {
        if (oldestTime == null || entry.value.isBefore(oldestTime)) {
          oldestTime = entry.value;
          oldestKey = entry.key;
        }
      }

      if (oldestKey != null) {
        _searchCache.remove(oldestKey);
        _cacheTimestamps.remove(oldestKey);
        AppLogger.d('Evicted oldest cache entry: $oldestKey');
      }
    }
  }

  /// Generate cache key with location precision
  String _getCacheKey(String query, LatLng? location) {
    if (location != null) {
      // Use 3 decimal places for location precision (~100m accuracy)
      return '${query}_${location.latitude.toStringAsFixed(3)}_${location.longitude.toStringAsFixed(3)}';
    }
    return query.toLowerCase().trim();
  }

  /// Clear expired cache entries (call periodically for maintenance)
  static void clearExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) >= _PlacesConfig.cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _searchCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      AppLogger.d('Cleared ${expiredKeys.length} expired cache entries');
    }
  }
}

class FoursquareProvider implements ContentProvider {
  final String apiKey;
  final Dio _dio = DioClient.instance;

  FoursquareProvider(this.apiKey);

  /// Modern Foursquare Places API v3 - More cost-effective than Google Places
  @override
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location}) async {
    try {
      // Use new Foursquare Places API v3
      const String baseUrl = 'https://api.foursquare.com/v3/places/search';

      final Map<String, String> params = {
        'query': query,
        'limit': '20',
        'fields': 'fsq_id,name,location,photos,categories,rating,price,hours',
      };

      // Add location bias if available
      if (location != null) {
        params['ll'] = '${location.latitude},${location.longitude}';
        params['radius'] = '50000'; // 50km radius
        params['sort'] = 'DISTANCE';
      }

      final response = await _dio.get(
        baseUrl,
        queryParameters: params,
        options: Options(
          headers: {
            'Authorization': apiKey,
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final List<PlaceSearchResult> results = [];

        if (data['results'] != null) {
          for (final place in data['results']) {
            final geocodes = place['geocodes']?['main'];
            if (geocodes == null) continue;

            String address = '';
            if (place['location']?['formatted_address'] != null) {
              address = place['location']['formatted_address'];
            } else if (place['location']?['address'] != null) {
              address = place['location']['address'];
            }

            // Get primary photo reference
            String? photoReference;
            if (place['photos'] != null && place['photos'].isNotEmpty) {
              final photo = place['photos'][0];
              photoReference = '${photo['prefix']}original${photo['suffix']}';
            }

            results.add(PlaceSearchResult(
              placeId: place['fsq_id'] ?? '',
              name: place['name'] ?? '',
              address: address,
              location: LatLng(
                geocodes['latitude']?.toDouble() ?? 0.0,
                geocodes['longitude']?.toDouble() ?? 0.0,
              ),
              photoReference: photoReference,
            ));
          }
        }

        return results;
      }
    } catch (e) {
      logd('FoursquareProvider searchPlaces error: $e');
    }
    return [];
  }

  @override
  Future<String> fetchImageUrl(String placeName, {LatLng? location, int? quantity}) async {
    try {
      final places = await searchPlaces(placeName, location: location);
      if (places.isEmpty) return '';

      final place = places.first;
      if (place.photoReference != null) {
        return place.photoReference!; // Already formatted URL from v3 API
      }
    } catch (e) {
      logd('FoursquareProvider fetchImageUrl error: $e');
    }
    return '';
  }

  @override
  Future<String> fetchLocationDetails(String placeName,
      {LatLng? location}) async {
    try {
      final places = await searchPlaces(placeName, location: location);
      if (places.isEmpty) return '';

      final place = places.first;

      // Get enhanced details for the place
      final details = await _getPlaceDetails(place.placeId);
      if (details != null) {
        return jsonEncode(details);
      }

      // Fallback to basic place information
      return jsonEncode({
        'name': place.name,
        'formatted_address': place.address,
        'geometry': place.location != null
            ? {
                'location': {
                  'lat': place.location!.latitude,
                  'lng': place.location!.longitude,
                }
              }
            : null,
      });
    } catch (e) {
      logd('FoursquareProvider fetchLocationDetails error: $e');
    }
    return '';
  }

  /// Get detailed place information using Foursquare Places API v3
  Future<Map<String, dynamic>?> _getPlaceDetails(String placeId) async {
    try {
      final String baseUrl = 'https://api.foursquare.com/v3/places/$placeId';

      final response = await _dio.get(
        baseUrl,
        queryParameters: {
          'fields':
              'fsq_id,name,location,photos,categories,rating,price,hours,website,tel,email,description,social_media,tips'
        },
        options: Options(
          headers: {
            'Authorization': apiKey,
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // Process opening hours
        String? openingHours;
        String? timeRange;
        if (data['hours'] != null) {
          final hours = data['hours'];
          if (hours['display'] != null) {
            openingHours = hours['display'];
          }
          if (hours['regular'] != null && hours['regular'].isNotEmpty) {
            final today = DateTime.now().weekday % 7;
            final todayHours = hours['regular'].firstWhere(
              (h) => h['day'] == today,
              orElse: () => null,
            );
            if (todayHours != null &&
                todayHours['open'] != null &&
                todayHours['close'] != null) {
              timeRange = '${todayHours['open']} - ${todayHours['close']}';
            }
          }
        }

        return {
          'name': data['name'],
          'formatted_address': data['location']?['formatted_address'],
          'rating': data['rating'],
          'price': data['price'],
          'website': data['website'],
          'phone': data['tel'],
          'openingHours': openingHours,
          'timeRange': timeRange ?? '10:00 - 12:00',
          'description': data['description'],
          'categories': data['categories']?.map((c) => c['name']).join(', '),
          'geometry': data['geocodes']?['main'] != null
              ? {
                  'location': {
                    'lat': data['geocodes']['main']['latitude'],
                    'lng': data['geocodes']['main']['longitude'],
                  }
                }
              : null,
        };
      }
    } catch (e) {
      logd('FoursquareProvider _getPlaceDetails error: $e');
    }
    return null;
  }

  /// Search for nearby places by category - great for POI discovery
  Future<List<PlaceSearchResult>> searchNearbyByCategory({
    required LatLng location,
    required String category,
    int radius = 1000,
    int limit = 20,
  }) async {
    try {
      const String baseUrl = 'https://api.foursquare.com/v3/places/nearby';

      final response = await _dio.get(
        baseUrl,
        queryParameters: {
          'll': '${location.latitude},${location.longitude}',
          'radius': radius.toString(),
          'categories': category,
          'limit': limit.toString(),
          'sort': 'DISTANCE',
          'fields': 'fsq_id,name,location,photos,categories,rating,price',
        },
        options: Options(
          headers: {
            'Authorization': apiKey,
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final List<PlaceSearchResult> results = [];

        if (data['results'] != null) {
          for (final place in data['results']) {
            final geocodes = place['geocodes']?['main'];
            if (geocodes == null) continue;

            String? photoReference;
            if (place['photos'] != null && place['photos'].isNotEmpty) {
              final photo = place['photos'][0];
              photoReference = '${photo['prefix']}original${photo['suffix']}';
            }

            results.add(PlaceSearchResult(
              placeId: place['fsq_id'] ?? '',
              name: place['name'] ?? '',
              address: place['location']?['formatted_address'] ?? '',
              location: LatLng(
                geocodes['latitude']?.toDouble() ?? 0.0,
                geocodes['longitude']?.toDouble() ?? 0.0,
              ),
              photoReference: photoReference,
            ));
          }
        }

        return results;
      }
    } catch (e) {
      logd('FoursquareProvider searchNearbyByCategory error: $e');
    }
    return [];
  }
}

class TripAdvisorProvider implements ContentProvider {
  final String apiKey;
  final Dio _dio = DioClient.instance;

  TripAdvisorProvider(this.apiKey);

  @override
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location}) async {
    try {
      final url =
          'https://api.tripadvisor.com/api/partner/2.0/location/search?query=${Uri.encodeComponent(query)}&key=$apiKey';
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        final data = response.data;
        final List<PlaceSearchResult> results = [];

        if (data['data'] != null) {
          for (final item in data['data']) {
            results.add(PlaceSearchResult(
              placeId: item['location_id']?.toString() ?? '',
              name: item['name'] ?? '',
              address: item['address_obj']?['address_string'] ?? '',
              location: item['latitude'] != null && item['longitude'] != null
                  ? LatLng(
                      double.tryParse(item['latitude']?.toString() ?? '0') ??
                          0.0,
                      double.tryParse(item['longitude']?.toString() ?? '0') ??
                          0.0,
                    )
                  : null,
              photoReference: item['photo']?['images']?['large']?['url'],
            ));
          }
        }

        return results;
      }
    } catch (e) {
      logd('TripAdvisorProvider searchPlaces error: $e');
    }
    return [];
  }

  @override
  Future<String> fetchImageUrl(String placeName, {LatLng? location, int? quantity}) async {
    try {
      final places = await searchPlaces(placeName, location: location);
      if (places.isEmpty) return '';

      final place = places.first;
      if (place.photoReference != null) {
        return place.photoReference!;
      }
    } catch (e) {
      logd('TripAdvisorProvider fetchImageUrl error: $e');
    }
    return '';
  }

  @override
  Future<String> fetchLocationDetails(String placeName,
      {LatLng? location}) async {
    try {
      final places = await searchPlaces(placeName, location: location);
      if (places.isEmpty) return '';

      final place = places.first;
      return jsonEncode({
        'name': place.name,
        'formatted_address': place.address,
        'geometry': place.location != null
            ? {
                'location': {
                  'lat': place.location!.latitude,
                  'lng': place.location!.longitude,
                }
              }
            : null,
      });
    } catch (e) {
      logd('TripAdvisorProvider fetchLocationDetails error: $e');
    }
    return '';
  }
}

class OpenStreetMapProvider implements ContentProvider {
  final Dio _dio = DioClient.instance;

  @override
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location}) async {
    try {
      String url =
          'https://nominatim.openstreetmap.org/search?q=${Uri.encodeComponent(query)}&format=json&addressdetails=1&limit=10';

      // Add location bias if available
      if (location != null) {
        url +=
            '&lat=${location.latitude}&lon=${location.longitude}&bounded=1&viewbox=${location.longitude - 0.5},${location.latitude + 0.5},${location.longitude + 0.5},${location.latitude - 0.5}';
      }

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'User-Agent': 'FamilyApp/1.0 (<EMAIL>)',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data as List;
        final List<PlaceSearchResult> results = [];

        for (final item in data) {
          results.add(PlaceSearchResult(
            placeId: item['place_id']?.toString() ?? '',
            name: item['display_name']?.split(',')[0] ?? '',
            address: item['display_name'] ?? '',
            location: LatLng(
              double.tryParse(item['lat']?.toString() ?? '0') ?? 0.0,
              double.tryParse(item['lon']?.toString() ?? '0') ?? 0.0,
            ),
            photoReference: null, // OSM doesn't provide photos
          ));
        }

        return results;
      }
    } catch (e) {
      logd('OpenStreetMapProvider searchPlaces error: $e');
    }
    return [];
  }

  @override
  Future<String> fetchImageUrl(String placeName, {LatLng? location, int? quantity}) async {
    // OpenStreetMap does not provide images directly, so we return an empty string
    return '';
  }

  @override
  Future<String> fetchLocationDetails(String placeName,
      {LatLng? location}) async {
    try {
      final places = await searchPlaces(placeName, location: location);
      if (places.isEmpty) return '';

      final place = places.first;
      return jsonEncode({
        'name': place.name,
        'formatted_address': place.address,
        'geometry': place.location != null
            ? {
                'location': {
                  'lat': place.location!.latitude,
                  'lng': place.location!.longitude,
                }
              }
            : null,
      });
    } catch (e) {
      logd('OpenStreetMapProvider fetchLocationDetails error: $e');
    }
    return '';
  }
}

class PexelsProvider implements ContentProvider {
  final String apiKey;
  final Dio _dio = DioClient.instance;

  PexelsProvider(this.apiKey);

  /// Search for photos on Pexels matching the query
  @override
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location, int? quantity}) async {
    try {
      final String url = 'https://api.pexels.com/v1/search';
      final response = await _dio.get(
        url,
        queryParameters: {
          'query': query,
          'per_page': quantity ?? 1,
          'page': 1,
        },
        options: Options(
          headers: {
            'Authorization': apiKey,
          },
        ),
      );
      if (response.statusCode == 200) {
        final data = response.data;
        final List<PlaceSearchResult> results = [];
        if (data['photos'] != null) {
          for (final photo in data['photos']) {
            results.add(PlaceSearchResult(
              placeId: photo['id'].toString(),
              name: photo['alt'] ?? photo['url'] ?? 'Pexels Photo',
              address: photo['photographer'] ?? '',
              location: null, // Pexels does not provide geolocation
              photoReference: photo['src']?['large2x'] ??
                  photo['src']?['original'] ??
                  photo['src']?['large'],
            ));
          }
        }
        return results;
      }
    } catch (e) {
      logd('PexelsProvider searchPlaces error: $e');
    }
    return [];
  }

  /// Fetch the first image URL for a given query from Pexels
  @override
  Future<String> fetchImageUrl(String placeName, {LatLng? location, int? quantity}) async {
    try {
      final results = await searchPlaces(placeName, location: location);
      if (results.isNotEmpty && results.first.photoReference != null) {
        return results.first.photoReference!;
      }
    } catch (e) {
      logd('PexelsProvider fetchImageUrl error: $e');
    }
    return '';
  }

  /// Fetches details for a photo (returns photographer and url as JSON)
  @override
  Future<String> fetchLocationDetails(String placeName,
      {LatLng? location}) async {
    try {
      final results = await searchPlaces(placeName, location: location);
      if (results.isNotEmpty) {
        final place = results.first;
        return jsonEncode({
          'name': place.name,
          'photographer': place.address,
          'photo_url': place.photoReference,
          'place_id': place.placeId,
        });
      }
    } catch (e) {
      logd('PexelsProvider fetchLocationDetails error: $e');
    }
    return '';
  }
}

ContentProvider provider = TripPlaceImageSearchSingleton()._defaultProvider;

class EnhancedMultiApiProvider implements ContentProvider {
  final GooglePlacesProvider _googleProvider;
  final FoursquareProvider _foursquareProvider;
  final PexelsProvider _pexelsProvider;
  final OpenStreetMapProvider _osmProvider;

  EnhancedMultiApiProvider({
    required String googleApiKey,
    required String foursquareApiKey,
    required String pexelsApiKey,
  })  : _googleProvider = GooglePlacesProvider(googleApiKey),
        _foursquareProvider = FoursquareProvider(foursquareApiKey),
        _pexelsProvider = PexelsProvider(pexelsApiKey),
        _osmProvider = OpenStreetMapProvider();

  @override
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location}) async {
    try {
      // Check cache first
      final cacheKey = _getCacheKey(query, location);
      final cachedResults = _searchCache[cacheKey];
      final timestamp = _cacheTimestamps[cacheKey];

      if (cachedResults != null &&
          timestamp != null &&
          DateTime.now().difference(timestamp) < const Duration(hours: 6)) {
        return cachedResults;
      }

      List<PlaceSearchResult> results = [];

      // Strategy 1: Try FREE OpenStreetMap first (0 cost)
      final osmResults =
          await _osmProvider.searchPlaces(query, location: location);
      if (osmResults.isNotEmpty && osmResults.length >= 3) {
        _cacheResults(query, location, osmResults);
        return osmResults;
      }

      // Strategy 2: Try Foursquare (often cheaper than Google for POI)
      final foursquareResults =
          await _foursquareProvider.searchPlaces(query, location: location);
      if (foursquareResults.isNotEmpty && foursquareResults.length >= 2) {
        results.addAll(foursquareResults);
      }

      // Strategy 3: Add Google results for comprehensive coverage
      final googleResults =
          await _googleProvider.searchPlaces(query, location: location);
      results.addAll(googleResults);

      // Strategy 4: Add Pexels results for comprehensive coverage
      final pexelsResults =
          await _pexelsProvider.searchPlaces(query, location: location);
      results.addAll(pexelsResults);

      // Remove duplicates and take best results
      final uniqueResults = _removeDuplicates(results);
      final bestResults = uniqueResults.take(20).toList();

      _cacheResults(query, location, bestResults);
      return bestResults;
    } catch (e) {
      logd('EnhancedMultiApiProvider searchPlaces error: $e');
      // Fallback to Google only
      return await _googleProvider.searchPlaces(query, location: location);
    }
  }

  @override
  Future<String> fetchImageUrl(String placeName, {LatLng? location, int? quantity}) async {
    // Try Google first (prioritize Google Places)
    final googleUrl =
        await _googleProvider.fetchImageUrl(placeName, location: location);
    if (googleUrl.isNotEmpty) return googleUrl;

    // Fallback to Foursquare
    return await _foursquareProvider.fetchImageUrl(placeName,
        location: location);
  }

  @override
  Future<String> fetchLocationDetails(String placeName,
      {LatLng? location}) async {
    // Try Foursquare first (often has richer details)
    final foursquareDetails = await _foursquareProvider
        .fetchLocationDetails(placeName, location: location);
    if (foursquareDetails.isNotEmpty) return foursquareDetails;

    // Fallback to Google
    return await _googleProvider.fetchLocationDetails(placeName,
        location: location);
  }

  /// Remove duplicate places based on proximity and name similarity
  List<PlaceSearchResult> _removeDuplicates(List<PlaceSearchResult> places) {
    final List<PlaceSearchResult> unique = [];

    for (final place in places) {
      bool isDuplicate = false;

      for (final existing in unique) {
        // Check if places are very close (within 50 meters) and have similar names
        if (place.location != null && existing.location != null) {
          final distance =
              _calculateDistance(place.location!, existing.location!);
          final nameSimilarity =
              _calculateNameSimilarity(place.name, existing.name);

          if (distance < 50 && nameSimilarity > 0.7) {
            isDuplicate = true;
            break;
          }
        }
      }

      if (!isDuplicate) {
        unique.add(place);
      }
    }

    return unique;
  }

  /// Calculate distance between two points in meters
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // Earth radius in meters
    final double dLat = (point2.latitude - point1.latitude) * (pi / 180);
    final double dLon = (point2.longitude - point1.longitude) * (pi / 180);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(point1.latitude * (pi / 180)) *
            cos(point2.latitude * (pi / 180)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  /// Calculate name similarity (simple implementation)
  double _calculateNameSimilarity(String name1, String name2) {
    final String n1 = name1.toLowerCase().trim();
    final String n2 = name2.toLowerCase().trim();

    if (n1 == n2) return 1.0;
    if (n1.contains(n2) || n2.contains(n1)) return 0.8;

    // Simple character-based similarity
    final Set<String> chars1 = n1.split('').toSet();
    final Set<String> chars2 = n2.split('').toSet();
    final int common = chars1.intersection(chars2).length;
    final int total = chars1.union(chars2).length;

    return total > 0 ? common / total : 0.0;
  }

  // Cache management
  static final Map<String, List<PlaceSearchResult>> _searchCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};

  String _getCacheKey(String query, LatLng? location) {
    if (location != null) {
      return '${query}_${location.latitude.toStringAsFixed(3)}_${location.longitude.toStringAsFixed(3)}';
    }
    return query;
  }

  void _cacheResults(
      String query, LatLng? location, List<PlaceSearchResult> results) {
    final cacheKey = _getCacheKey(query, location);
    _searchCache[cacheKey] = results;
    _cacheTimestamps[cacheKey] = DateTime.now();
  }

  /// Access to Foursquare provider for category searches
  FoursquareProvider get foursquareProvider => _foursquareProvider;

  /// Access to Pexels provider for direct image search
  PexelsProvider get pexelsProvider => _pexelsProvider;
}

class TripPlaceImageSearchSingleton {
  static final TripPlaceImageSearchSingleton _instance =
      TripPlaceImageSearchSingleton._internal();
  final ContentProvider _defaultProvider;

  factory TripPlaceImageSearchSingleton({ContentProvider? provider}) {
    return _instance;
  }

  static final String apiKey = AppConfig.GOOGLE_MAPS_API_KEY;
  static const proxyUrl = 'https://vrelay-vn1.5gencare.com/proxy/';
  static const foursquareApiKey =
      'YOUR_FOURSQUARE_API_KEY'; // Add your Foursquare API key here
  static final String pexelsApiKey = AppConfig.PEXELS_API_KEY;

  // TripPlaceImageSearchSingleton._internal()
  //     : _defaultProvider = EnhancedMultiApiProvider(
  //         googleApiKey: apiKey,
  //         googleProxyUrl: proxyUrl,
  //         foursquareApiKey: foursquareApiKey,
  //         pexelsApiKey: pexelsApiKey,
  //       );

  TripPlaceImageSearchSingleton._internal()
      : _defaultProvider = PexelsProvider(pexelsApiKey);

  /// Smart search with multi-API strategy and caching
  Future<List<PlaceSearchResult>> searchPlaces(String query,
      {LatLng? location}) {
    return _defaultProvider.searchPlaces(query, location: location);
  }

  /// Get image URL with location bias for better results
  Future<String> fetchImageUrl(String placeName, {LatLng? location}) {
    return _defaultProvider.fetchImageUrl(placeName, location: location);
  }

  /// Get location details with enhanced data
  Future<String> fetchLocationDetails(String placeName, {LatLng? location}) {
    return _defaultProvider.fetchLocationDetails(placeName, location: location);
  }

  /// Search nearby POIs by category using Foursquare
  Future<List<PlaceSearchResult>> searchNearbyByCategory({
    required LatLng location,
    required String category,
    int radius = 100,
  }) async {
    // Direct access to the enhanced provider
    final enhancedProvider = _defaultProvider;
    if (enhancedProvider is EnhancedMultiApiProvider) {
      return await enhancedProvider.foursquareProvider.searchNearbyByCategory(
        location: location,
        category: category,
        radius: radius,
      );
    }

    return [];
  }
}

// --- Provider Singletons and Selector ---
ContentProvider getPhotoProvider({required bool isPremium}) {
  return isPremium ? GooglePlacesProvider(AppConfig.GOOGLE_MAPS_API_KEY) : PexelsProvider(AppConfig.PEXELS_API_KEY);
}
