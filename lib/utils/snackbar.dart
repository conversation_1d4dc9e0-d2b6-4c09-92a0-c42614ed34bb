import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class SnackBarUtils {
  static showSucceed(BuildContext context, {required String title, required String content}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        backgroundColor: appTheme.greenV2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: AppStyle.bold14(color: appTheme.whiteText)),
                  Text(content, style: AppStyle.regular12(color: appTheme.whiteText)),
                ],
              ),
            ),
            const SizedBox(width: 8),
            InkWell(
              onTap: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
              child: Icon(Icons.close, color: appTheme.whiteText),
            )
          ],
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
