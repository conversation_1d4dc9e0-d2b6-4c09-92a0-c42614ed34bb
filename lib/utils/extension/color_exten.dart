import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

extension ColorExtension on Color {
  String get text {
    var textColor = '#${value.toRadixString(16).padLeft(8, '0').toUpperCase()}';
    if (textColor.startsWith('#FF')) {
      textColor = textColor.replaceFirst('#FF', '#');
    }
    return textColor;
  }
}

extension TextColorExtension on String? {
  Color get toColor {
    try {
      var hexString = (this ?? '').replaceAll('#', ''); // Remove the leading #
      if (hexString.length == 6) {
        hexString = 'FF$hexString'; // Add alpha if missing
      }
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return appTheme.primaryColor;
    }
  }
}
