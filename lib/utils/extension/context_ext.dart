import 'package:flutter/material.dart';

extension BuildContextExt on BuildContext {
  void hideKeyboard() {
    FocusNode node = FocusScope.of(this);
    if (!node.hasPrimaryFocus) {
      node.unfocus();
    }
  }

  double get top => MediaQuery.of(this).viewPadding.top;
  double get right => MediaQuery.of(this).viewPadding.right;

  double get bottom => keyboard > 0 ? 0 : MediaQuery.of(this).viewPadding.bottom;

  double get keyboard => MediaQuery.of(this).viewInsets.bottom;
}
