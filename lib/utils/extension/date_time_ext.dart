import 'package:dartx/dartx.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/utils/util.dart';
import 'package:jiffy/jiffy.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../main.dart';

extension StringNulDateTimeExt on String? {
  DateTime? get tryParseMillisecond {
    if ((this ?? '').isNotEmpty) {
      return DateTime.fromMillisecondsSinceEpoch(this!.toInt()).toLocal();
    }
    return null;
  }

  String get fromNowTime {
    final dateTime = (this ?? '').convertDateTime;
    if (dateTime == null) return '';
    final duration = DateTime.now().difference(dateTime);
    if (duration.inDays >= 3) {
      return dateTime.ddMMyy;
    }
    return Jiffy.parseFromDateTime(dateTime).fromNow();
  }

  /// Enhanced relative time formatting using timeago with custom messages
  String getRelativeTimeFromNow({String? locale}) {
    final dateTime = (this ?? '').convertUtcDateTime;
    if (dateTime == null) return '';
    final duration = DateTime.now().toUtc().difference(dateTime.toUtc());
    if (duration.inDays >= 3) {
      return dateTime.ddMMyy;
    }

    // Use timeago with optional locale
    return timeago.format(dateTime, locale: locale ?? 'en');
  }
}

extension StringDateTimeExt on String {
  DateTime toDateTime() {
    if (isEmpty) return DateTime.now();
    return DateTime.parse(this);
  }

  String toLocal() {
    final DateTime dateTime = DateTime.parse(this).toLocal();
    return dateTime.toIso8601String();
  }

  DateTime get toLocalDT {
    if (isEmpty) return DateTime.now();
    final time = int.tryParse(this);
    if (time != null) {
      return DateTime.fromMillisecondsSinceEpoch(time).toLocal();
    }
    return DateTime.parse(this).toLocal();
  }

  DateTime? get convertDateTime {
    return DateTime.tryParse(this)?.toLocal();
  }

  /// Parse dateTime as UTC time and convert to local time
  DateTime? get convertUtcDateTime {
    final dateTime = DateTime.tryParse(this);
    if (dateTime == null) return null;
    final utcDateTime = DateTime.utc(
      dateTime.year,
      dateTime.month,
      dateTime.day,
      dateTime.hour,
      dateTime.minute,
      dateTime.second,
      dateTime.millisecond,
      dateTime.microsecond,
    );
    return utcDateTime.toLocal();
  }

  String get HH_mm {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('HH:mm').format(dateTime);
  }

  String get dd_MM_yyy {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('dd-MM-yyyy').format(dateTime);
  }

  String get until_time {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return 'until ${DateFormat('MMM DD, yyyy').format(dateTime)}';
  }

  String get until_time_MMMDD {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return 'until ${DateFormat('MMM dd').format(dateTime)}';
  }

  String get MMMDDyyyyTime {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('MMM dd yyyy').format(dateTime);
  }

  String get ddMMyy {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  String get ddMMyyTime {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('dd/MM/yyyy, HH:mm').format(dateTime);
  }

  String get MMM_DD_YYYY {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('MMMM dd, yyyy HH:mm').format(dateTime);
  }

  String get EEEE_dd_MM_yyyy {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('EEEE, dd/MM/yyyy', navigatorKey.currentContext!.locale.languageCode).format(dateTime);
  }

  String get eeee_mmmm_d {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('EEEE, MMMM d', navigatorKey.currentContext!.locale.languageCode).format(dateTime);
  }

  String get d_MMMM_comma_hhmm {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('d MMMM, HH:mm', navigatorKey.currentContext!.locale.languageCode).format(dateTime);
  }

  String get yyyy_MM_dd {
    final dateTime = convertDateTime;
    if (dateTime == null) return '';
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  DateTime get fullDayFormat {
    return DateFormat("yyyy-MM-dd HH:mm:ss").parse(this);
  }

  String get age {
    final today = DateTime.now();
    final date = toLocalDT;
    final diff = today.year - date.year;
    return diff.toString();
  }
}

extension DateTimeExt2 on DateTime {
  String get weekDayLocale {
    switch (weekday) {
      case 1:
        return 'mon'.tr();
      case 2:
        return 'tue'.tr();
      case 3:
        return 'wed'.tr();
      case 4:
        return 'thur'.tr();
      case 5:
        return 'fri'.tr();
      case 6:
        return 'sat'.tr();
      case 7:
        return 'sun'.tr();
      default:
        return '';
    }
  }

  String get dayFormat => Utils.getTimeString(day);

  String get messageDate {
    if (isToday) {
      return 'Today $HH_mm';
    } else {
      return DateFormat("MMMM, dd yyyy HH:mm", navigatorKey.currentContext!.locale.languageCode).format(this);
    }
  }

  String get messageDateMMMMdd {
    if (isToday) {
      return 'Today $HH_mm';
    } else {
      return DateFormat("MMMM dd, yyyy HH:mm", navigatorKey.currentContext!.locale.languageCode).format(this);
    }
  }

  String get eventDate_ddMMMCommaYYYY {
    var dateFormat = DateFormat("dd MMM, yyyy", navigatorKey.currentContext!.locale.languageCode);
    if (isToday) {
      return 'Today - ${dateFormat.format(this)}';
    } else {
      return dateFormat.format(this);
    }
  }

  String get monthLocale {
    switch (month) {
      case 1:
        return 'jan'.tr();
      case 2:
        return 'feb'.tr();
      case 3:
        return 'mar'.tr();
      case 4:
        return 'Apr'.tr();
      case 5:
        return 'may'.tr();
      case 6:
        return 'jun'.tr();
      case 7:
        return 'jul'.tr();
      case 8:
        return 'aug'.tr();
      case 9:
        return 'sep'.tr();
      case 10:
        return 'oct'.tr();
      case 11:
        return 'nov'.tr();
      case 12:
        return 'dec'.tr();
      default:
        return '';
    }
  }

  DateTime get startDay {
    return DateTime(year, month, day, 0, 0);
  }

  DateTime get endDay {
    return DateTime(year, month, day, 23, 59, 59);
  }

  String get MM_YYYY {
    return DateFormat('MMM yyyy', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get MMMM_YYYY {
    return DateFormat('MMMM yyyy', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get MMM {
    return DateFormat('MMM', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get MMM_d_yyyy {
    return DateFormat('MMM d yyyy', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get HH_mm {
    return DateFormat('HH:mm', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get hh_mm {
    var isMorning = true;
    if (hour > 12) {
      isMorning = false;
    }
    return '${DateFormat('hh:mm', navigatorKey.currentContext!.locale.languageCode).format(this)} ${isMorning ? 'AM' : 'PM'}';
  }

  String get dd_MM_yyy {
    return DateFormat('dd-MM-yyyy', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get yyyy_MM_dd {
    return DateFormat('yyyy-MM-dd', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get EEE {
    return DateFormat('EEE', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get EE_dd_MM_yyy {
    return DateFormat('EE,MMM dd, yy', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get ddMMyy {
    return DateFormat('dd/MM/yyyy', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get ddMM {
    return DateFormat('dd/MM', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get EEEE_dd_MM_yyyy {
    return DateFormat('EEEE, dd/MM/yyyy', navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get EEEE_dd_MM_yyy {
    return DateFormat("EEEE , dd-MM-yyyy", navigatorKey.currentContext!.locale.languageCode).format(this);
  }

  String get MMM_DD_TIME {
    // final text = DateFormat(
    //         "MMM dd hh:mm", navigatorKey.currentContext!.locale.languageCode)
    //     .format(this);
    final text = DateFormat("dd-MM-yyyy hh:mm", navigatorKey.currentContext!.locale.languageCode).format(this);
    if (hour > 12) {
      return '$text PM';
    } else {
      return '$text AM';
    }
  }

  // Return true if this Date is before TODAY's date
  bool get isBeforeNow {
    final now = DateTime.now();
    if (now.year == year) {
      if (now.month == month) {
        return day < now.day; // this Date is Before TODAY
      } else if (month < now.month) {
        return true; // this Date is BEFORE TODAY
      } else {
        return false; // this Date is AFTER TODAY
      }
    } else if (year < now.year) {
      return true;
    }
    return false;
  }

  String get until_time {
    return LocaleKeys.until_time.tr(args: [(DateFormat('MMM dd, yyyy').format(this))]);
  }

  String get shortDay {
    return DateFormat('MMM dd').format(this);
  }

  String get shortDayReverse {
    return DateFormat('dd MMM').format(this);
  }

  String get timeddMMMyyyy {
    return DateFormat('dd MMM yyyy').format(this);
  }

  String get timeMMMddyyyy {
    return DateFormat('MMM dd yyyy').format(this);
  }

  bool isNotSameDay(DateTime time) {
    if (time.year == year) {
      if (time.month == month) {
        return time.day != day;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  bool isSame(DateTime time) {
    return year == time.year && month == time.month && day == time.day && hour == time.hour && minute == time.minute;
  }

  DateTime get tomorrow {
    return add(const Duration(days: 1));
  }

  DateTime nextMonth(int number) {
    return DateTime(year, month + number, day, 0, 0);
  }

  DateTime get startDayOfWeek {
    int currentDayOfWeek = weekday;

    int daysToSubtract = (currentDayOfWeek % 7);

    DateTime startOfWeek = date.subtract(Duration(days: daysToSubtract));

    if (currentDayOfWeek == DateTime.sunday) {
      startOfWeek = date;
    }

    return startOfWeek;
  }

  DateTime get endDayOfWeek {
    int currentDayOfWeek = weekday;

    int daysToAdd = (6 - currentDayOfWeek % 7);

    DateTime endOfWeek = date.add(Duration(days: daysToAdd));

    if (currentDayOfWeek == DateTime.saturday) {
      endOfWeek = date;
    }

    return endOfWeek.copyWith(hour: 23, minute: 59, second: 59);
  }

  DateTime get startOfMonth {
    return date.copyWith(day: 1);
  }

  DateTime get endOfMonth {
    final newDate = date.copyWith(day: 1, month: date.month + 1, hour: 23, minute: 59, second: 59);
    return newDate.subtract(Duration(days: 1));
  }

  String get age {
    final today = DateTime.now();
    final diff = today.year - year;
    return diff.toString();
  }

  String formatDateMemories() {
    // Format the date as needed (Today, Yesterday, dd/mm/yyyy)
    if (day == DateTime.now().day) {
      return "Today, $MMM_d_yyyy at $hh_mm";
    } else if (day == DateTime.now().day - 1) {
      return "Yesterday, $MMM_d_yyyy at $hh_mm";
    } else {
      return "$MMM_d_yyyy at $hh_mm";
    }
  }
}

extension DurationExt on Duration {
  String format() {
    final minutes = inMinutes.remainder(60);
    return "${inHours < 10 ? "0$inHours" : inHours}:${minutes < 10 ? "0$minutes" : minutes}";
  }
}

extension StringToThreadDateTimeExt on String? {
  /// Converts a string to DateTime? for thread message time.
  DateTime? get toThreadDateTime {
    if (this == null || this!.isEmpty) return null;
    final inputString = this!;
    if (inputString.contains("Z")) {
      return inputString.toLocalDT;
    } else {
      try {
        return DateTime.parse("${inputString}Z").toLocal();
      } catch (_) {
        return null;
      }
    }
  }
}

extension DateTimeThreadFormatExt on DateTime {
  /// Formats a date for chat/thread message time display.
  /// - If today: returns 'HH:mm'
  /// - If this year: returns 'dd/MM'
  /// - Else: returns 'dd/MM/yy'
  String get threadMessageTime {
    final local = toLocal();
    final now = DateTime.now();
    if (local.year == now.year && local.month == now.month && local.day == now.day) {
      return DateFormat('HH:mm').format(local);
    } else if (local.year == now.year) {
      return DateFormat('dd/MM').format(local);
    } else {
      return DateFormat('dd/MM/yy').format(local);
    }
  }
}

extension StringThreadFormatExt on String? {
  /// Formats a date string for chat/thread message time display.
  ///
  /// - If the string is null or empty, returns an empty string.
  /// - Accepts ISO8601 date strings (with or without 'Z'), or milliseconds since epoch.
  /// - If the date is today: returns 'HH:mm'.
  /// - If the date is this year: returns 'dd/MM'.
  /// - Else: returns 'dd/MM/yy'.
  ///
  /// Returns an empty string if parsing fails.
  String get threadMessageTime {
    if (this == null || this!.isEmpty) return "";
    final inputString = this!;
    DateTime? dateTime = inputString.toThreadDateTime;
    return dateTime?.threadMessageTime ?? "";
  }
}
