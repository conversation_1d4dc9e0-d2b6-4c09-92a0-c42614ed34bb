import 'package:easy_localization/easy_localization.dart';

extension IntExtension on int? {
  String get currency {
    if (this == null) {
      return '0';
    }
    return NumberFormat('#,###').format(this);
  }

  String get distanceFilter {
    if (this == null) {
      // return LocaleKeys.choose_distance.tr();
    }
    if (this == 20) {
      return 'under_distance_default'.tr(args: [' $this']);
    }
    if (this == -1) {
      return 'no_limit'.tr();
    }
    return 'under_distance'.tr(args: [' $this']);
  }

  String get recordTime {
    final duration = Duration(seconds: this ?? 0);
    final minutes = duration.inMinutes;
    final seconds = minutes > 0 ? duration.inSeconds % 60 : duration.inSeconds;

    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get toTimeFormat {
    return toString().padLeft(2, '0');
  }
}

extension DoubleExtension on double? {
  String get currency {
    if (this == null) {
      return '0';
    }
    return NumberFormat('#,###').format((this ?? 0).toInt());
  }

  String get routeDistance {
    final km = this?.toInt() ?? 0;
    if (km > 0) {
      return '${km}km';
    } else {
      final m = ((this ?? 0) - km).toStringAsFixed(2).replaceFirst('0.', '');
      return '${m}m';
    }
  }
}
