import 'dart:ui';

import 'package:device_calendar/device_calendar.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/utils/calendar.dart';
import 'package:family_app/utils/event.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/timezone.dart';
import 'package:timezone/timezone.dart' as tz;

extension EventConverter on Event {
  String get uuid => CalendarUtils.generateEventId(eventId);

  bool get isAllDayEvent => allDay ?? false;

  DateTime? get startDate {
    if (start == null) return null;
    if (start is tz.TZDateTime) {
      final s = EventUtils.convertToMyLocalTime(start as tz.TZDateTime);
      // return EventUtils.keepWallTimeInUTC(s).toUtc();
      return s.toUtc();
    }
    return start?.toUtc();
  }

  DateTime? get endDate {
    if (end == null) return null;
    if (end is tz.TZDateTime) {
      final e = EventUtils.convertToMyLocalTime(end as tz.TZDateTime);
      // return EventUtils.keepWallTimeInUTC(e).toUtc();
      return e.toUtc();
    }
    return end?.toUtc();
  }

  int? get reminder => reminders?.firstOrNull?.minutes?.abs();

  DateTime? get reminderTime => (reminder != null && startDate != null) ? startDate!.subtract(Duration(minutes: reminder!)) : null;

  String? get repeatString {
    if (recurrenceRule == null) return null;
    final rule = recurrenceRule!;
    Frequency? freq = CalendarUtils.getRecurrenceFrequencyForDeviceCalendar(rule.recurrenceFrequency);
    RepeatConfig? repeatConfig;
    if (freq != null) {
      repeatConfig = RepeatConfig(
        frequency: freq,
        interval: rule.interval,
        count: rule.totalOccurrences,
        until: rule.endDate,
        byDayOfWeek: CalendarUtils.getByDayOfWeek(rule.daysOfWeek),
        byMonthDay: rule.dayOfMonth != null ? [rule.dayOfMonth!] : null,
        byMonth: rule.monthOfYear != null ? [rule.monthOfYear!.value] : null,
      );
    }
    if (reminder != null && repeatConfig != null) {
      repeatConfig.reminder = reminder;
    }
    return repeatConfig?.toJSONString();
  }

  List<String> get memberEmails => attendees?.where((a) => a != null).map((a) => a!.emailAddress ?? '').toList() ?? [];

  EventParameter toEventParameter(String familyId, int? calendarColor) {
    final String? repeat = repeatString;
    DateTime? notificationTime = reminderTime;

    // If not a repeat event, but reminder exists, set notificationTime
    if (recurrenceRule == null && reminder != null && startDate != null) {
      notificationTime = startDate!.subtract(Duration(minutes: reminder!));
    }

    final timeZone = start != null ? TimeZoneUtils.extractTimezone(start) : (end != null ? TimeZoneUtils.extractTimezone(end) : TimeZoneUtils.getDefaultTimezone());

    return EventParameter(
      uuid: uuid,
      name: (title ?? '').trim(),
      fromDate: startDate?.toUtc().toIso8601String() ?? '',
      toDate: endDate?.toUtc().toIso8601String() ?? '',
      color: colorHexString(calendarColor),
      caption: '',
      allDay: isAllDayEvent ? 1 : 0,
      description: (description ?? '').trim(),
      familyId: familyId,
      activityId: '',
      notificationStatus: 'some_message',
      notificationTime: notificationTime?.toUtc().toIso8601String() ?? '',
      repeat: repeat,
      members: memberEmails,
      timeZone: timeZone.offset,
    );
  }

  String colorHexString(int? calendarColor) {
    if (calendarColor == null) return '';
    return Color(calendarColor).text;
  }
}
