class BatchUtils {
  /// Splits a list into chunks of [chunkSize]
  static List<List<T>> chunkList<T>(List<T> list, int chunkSize) {
    final List<List<T>> chunks = [];
    for (var i = 0; i < list.length; i += chunkSize) {
      chunks.add(list.sublist(
        i,
        (i + chunkSize).clamp(0, list.length),
      ));
    }
    return chunks;
  }

  /// Processes chunks in parallel with throttling
  static Future<void> processChunksWithPool<T>(
    List<List<T>> chunks,
    Future<void> Function(List<T> chunk) processChunk, {
    int maxConcurrent = 3,
  }) async {
    final List<Future<void>> pool = [];
    for (final chunk in chunks) {
      if (pool.length >= maxConcurrent) {
        await pool.first;
        pool.removeAt(0);
      }
      pool.add(processChunk(chunk));
    }
    await Future.wait(pool);
  }
}
