// Utility for concurrency-safe operation queueing.

import 'dart:async';

import 'package:family_app/utils/log/app_logger.dart';

/// QueueOperation provides a concurrency-safe queue for asynchronous operations,
/// with optional debounce support.
///
/// - Ensures only one operation runs at a time, queuing additional requests.
/// - Supports debouncing: if enqueueDebounced is called repeatedly within the debounce window,
///   only the last operation is executed after the specified delay.
/// - Useful for batching or serializing async actions (e.g., network requests, state updates)
///   that should not run concurrently or too frequently (such as rapid UI events).
///
/// Usage:
///   final queue = QueueOperation();
///   queue.enqueue(() async { ... }); // runs immediately (or after previous ops)
///   queue.enqueueDebounced(() async { ... }, debounceDuration: Duration(milliseconds: 500));
///   queue.cancelDebounce(); // cancels any pending debounced operation (e.g. on dispose)
class QueueOperation {
  final List<Future<void> Function()> _queue = [];
  bool _running = false;
  Timer? _debounceTimer;
  Future<void>? _debounceCompleterFuture;

  /// Cancels any pending debounced operation (e.g. on dispose).
  void cancelDebounce() {
    _debounceTimer?.cancel();
    _debounceTimer = null;
    _debounceCompleterFuture = null;
    // Log cancel
    AppLogger.d('[QueueOperation] Debounce cancelled');
  }

  /// Enqueue an operation to run after [debounceDuration]. If called again before the timer fires, resets the timer.
  /// If [debounceDuration] is null, enqueues immediately.
  ///
  /// Only the last operation within the debounce window will be executed.
  Future<void> enqueueDebounced(
    Future<void> Function() op, {
    Duration? debounceDuration,
  }) {
    final completer = Completer<void>();

    void enqueueNow() {
      _debounceTimer = null;
      _debounceCompleterFuture = null;
      // Only keep the last debounced operation
      _queue.clear();
      // Log enqueue
      AppLogger.d('[QueueOperation] Enqueue operation at ${DateTime.now()}');
      _queue.add(() async {
        // Log execution
        AppLogger.d('[QueueOperation] Executing operation at ${DateTime.now()}');
        try {
          await op();
          completer.complete();
        } catch (e, st) {
          completer.completeError(e, st);
        }
      });
      _processQueue();
    }

    if (debounceDuration != null) {
      // Log debounce request
      AppLogger.d('[QueueOperation] Debounce requested at ${DateTime.now()}');
      _debounceTimer?.cancel();
      _debounceTimer = Timer(debounceDuration, enqueueNow);
      _debounceCompleterFuture ??= completer.future;
      return _debounceCompleterFuture!;
    } else {
      enqueueNow();
      return completer.future;
    }
  }

  /// Enqueue immediately (no debounce).
  Future<void> enqueue(Future<void> Function() op) => enqueueDebounced(op);

  void _processQueue() async {
    if (_running) return;
    _running = true;
    while (_queue.isNotEmpty) {
      final fn = _queue.removeAt(0);
      // Log process queue
      AppLogger.d('[QueueOperation] Processing next operation at ${DateTime.now()}');
      await fn();
    }
    _running = false;
    // Log queue empty
    AppLogger.d('[QueueOperation] Queue empty at ${DateTime.now()}');
  }
}
