import 'dart:io';

import 'package:image/image.dart' as img;

class ImageScaler {
  Future<List<File>> scale(File imageFile) async {
    final image = img.decodeImage(imageFile.readAsBytesSync());

    if (image == null) {
      throw Exception('Unable to decode image');
    }

    // print('Original image res: ${image.width}x${image.height} , size: ${imageFile.lengthSync()}');
    final scaledImages = <File>[];
    // Add original image
    scaledImages.add(imageFile);

    // Define the resolutions
    final resolutions = [
      {'suffix': '_md', 'width': (image.width * 0.5).toInt(), 'height': (image.height * 0.5).toInt()},
      {'suffix': '_sm', 'width': (image.width * 0.25).toInt(), 'height': (image.height * 0.25).toInt()},
    ];

    for (final res in resolutions) {
      final scaledImage = img.copyResize(image, width: res['width'] as int?, height: res['height'] as int?);

      /* generate the image path from: 
           /data/user/0/com.gencare.family/cache/1000006227.jpg
        to 
        /data/user/0/com.gencare.family/cache/1000006227[_suffix].jpg
      */

      var index = imageFile.path.lastIndexOf('.');
      String suffix = res['suffix'] as String;
      var scaledImagePath = imageFile.path.substring(0, index) + suffix + '.jpg';
      final scaledImageFile = File(scaledImagePath)..writeAsBytesSync(img.encodeJpg(scaledImage, quality: 75));
      scaledImages.add(scaledImageFile);
    }

    return scaledImages;
  }
}
