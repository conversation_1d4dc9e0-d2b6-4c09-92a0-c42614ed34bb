import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:family_app/data/model/device_event.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_state.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:timezone/timezone.dart' as tz;

class EventUtils {
  // ----------- Helpers for UpsertEventCubit -----------
  static bool isDeviceEvent(EventModels? model) => model is DeviceEventModel;

  static bool hasValidModelUuid(EventModels? model) => model?.uuid?.isNotEmpty ?? false;

  static int? getReminder(EventModels model) {
    final repeat = model.repeat;
    if (repeat != null && repeat.reminder != null && repeat.reminder! > 0) {
      return repeat.reminder;
    }
    final notificationTime = model.notificationTime;
    if (notificationTime != null && notificationTime.isNotEmpty && repeat == null) {
      final dateTime = DateTime.parse(notificationTime);
      DateTime? fromDate;
      if (model.fromDate != null && model.fromDate!.isNotEmpty) {
        fromDate = DateTime.parse(model.fromDate!);
      }
      if (fromDate != null) {
        final duration = fromDate.difference(dateTime);
        if (duration.inMinutes > 0) {
          return duration.inMinutes;
        }
      }
    }
    return null;
  }

  static RepeatTypeAndDays getRepeatTypeAndDays(RepeatConfig? repeat) {
    if (repeat != null) {
      if ((repeat.byDayOfWeek ?? []).isNotEmpty) {
        return RepeatTypeAndDays(EventRepeatType.custom, repeat.byDayOfWeek!);
      } else {
        final repeatType = () {
          switch (repeat.frequency) {
            case Frequency.daily:
              return EventRepeatType.daily;
            case Frequency.weekly:
              return EventRepeatType.weekly;
            case Frequency.monthly:
              return EventRepeatType.monthly;
            case Frequency.yearly:
              return EventRepeatType.yearly;
          }
        }();
        return RepeatTypeAndDays(repeatType, []);
      }
    }
    return RepeatTypeAndDays(EventRepeatType.none, []);
  }

  /// Functional helper to convert UpsertEventState and related data to EventParameter
  static EventParameter toEventParameter({
    required UpsertEventState state,
    required UpsertEventParameter parameter,
    required TextFieldHandler title,
    required TextFieldHandler description,
    required AccountService accountService,
  }) {
    DateTime startDate = state.startDate!;
    DateTime endDate = state.endDate!;
    if (state.isAllDay) {
      startDate = DateTime(startDate.year, startDate.month, startDate.day);
      endDate = DateTime(endDate.year, endDate.month, endDate.day, 23, 59);
    }

    RepeatConfig? repeatConfig = _buildRepeatConfig(state);

    DateTime? reminderTime;
    if (state.reminder != null && state.reminder! > 0) {
      if (repeatConfig != null) {
        repeatConfig.reminder = state.reminder;
      } else {
        reminderTime = startDate.subtract(Duration(minutes: state.reminder!));
      }
    }

    // Add timezone (offset string) to EventParameter
    final timezoneOffset = state.timezone?.offset ?? '';

    return EventParameter(
      uuid: parameter.model?.uuid ?? '',
      name: title.text.trim(),
      fromDate: startDate.toUtc().toIso8601String(),
      toDate: endDate.toUtc().toIso8601String(),
      color: state.selectedColor?.text ?? '',
      caption: '',
      allDay: state.isAllDay ? 1 : 0,
      description: description.text.trim(),
      familyId: accountService.familyId,
      activityId: state.activity?.uuid ?? '',
      notificationStatus: 'some_message',
      notificationTime: reminderTime?.toUtc().toIso8601String() ?? '',
      repeat: repeatConfig?.toJSONString(),
      members: state.memberList.map((e) => e.familyMemberUuid ?? '').toList(),
      timeZone: timezoneOffset,
    );
  }

  static RepeatConfig? _buildRepeatConfig(UpsertEventState state) {
    if (state.repeatType == EventRepeatType.custom) {
      return RepeatConfig(
        frequency: Frequency.weekly,
        byDayOfWeek: state.selectedDayOfWeek,
      );
    } else if (state.repeatType != EventRepeatType.none) {
      return RepeatConfig(frequency: _mapRepeatTypeToFrequency(state.repeatType));
    }
    return null;
  }

  static Frequency _mapRepeatTypeToFrequency(EventRepeatType type) {
    switch (type) {
      case EventRepeatType.daily:
        return Frequency.daily;
      case EventRepeatType.weekly:
        return Frequency.weekly;
      case EventRepeatType.monthly:
        return Frequency.monthly;
      case EventRepeatType.yearly:
        return Frequency.yearly;
      default:
        return Frequency.daily;
    }
  }

  static tz.TZDateTime convertToMyLocalTime(tz.TZDateTime original) {
    return tz.TZDateTime.from(original, tz.local);
  }

  static tz.TZDateTime keepWallTimeInUTC(tz.TZDateTime original) {
    return tz.TZDateTime(
      tz.getLocation('UTC'),
      original.year,
      original.month,
      original.day,
      original.hour,
      original.minute,
      original.second,
      original.millisecond,
      original.microsecond,
    );
  }

  /// Combines two lists of EventModels and deduplicates by uuid + fromDate (same day, local time).
  /// If multiple events have the same uuid and fromDate on the same local day, keep the last one encountered.
  /// If uuid or fromDate is null, skip the event.
  static List<EventModels> combineAndDeduplicate(List<EventModels> a, List<EventModels> b) {
    final combined = [...a, ...b];
    final Map<String, EventModels> map = {};

    for (final event in combined) {
      final uuid = event.uuid;
      final fromDateRaw = event.fromDate;
      if (uuid == null || fromDateRaw == null) {
        // Skip events with null uuid or fromDate (bad data)
        continue;
      }
      DateTime? fromDate;
      try {
        fromDate = DateTime.parse(fromDateRaw).toLocal();
      } catch (_) {
        continue;
      }
      final key = '${uuid}_${fromDate.year}-${fromDate.month}-${fromDate.day}';
      // Always overwrite, so the last event with the same uuid and local day is kept
      map[key] = event;
    }
    return map.values.toList();
  }

  /// Returns a list of events that occur on the given [currentDay].
  static List<EventModels> filterCurrentDayEvents(List<EventModels> events, DateTime currentDay) {
    return events.where((event) => EventUtils.isCurrentDayEvent(event, currentDay)).toList();
  }

  /// Counts the number of all-day events in the given list.
  static int countAllDayEvent(List<EventModels> events) {
    return events.where((event) => EventUtils.isAllDay(event)).length;
  }

  /// Returns true if the event occurs on the given [currentDay].
  static bool isCurrentDayEvent(EventModels event, DateTime currentDay) {
    final startTime = event.fromDate?.toLocalDT;
    final endTime = event.toDate?.toLocalDT;
    if (startTime == null || endTime == null) {
      return false;
    }
    if (_isSameDay(startTime, currentDay) || _isSameDay(endTime, currentDay)) {
      return true;
    }

    int startMilis = startTime.millisecondsSinceEpoch;
    int endMilis = endTime.millisecondsSinceEpoch;
    int currentMilis = currentDay.millisecondsSinceEpoch;

    if (startMilis <= currentMilis && endMilis >= currentMilis) {
      return true;
    }

    return false;
  }

  /// Determines if the event should be considered an all-day event.
  ///
  /// Logic:
  /// 1. If the event's [allDay] property is set to 1, returns true.
  /// 2. If either [fromDate] or [toDate] is missing or cannot be parsed, returns false.
  /// 3. If the event starts and ends on the same day, and starts before 7 AM and ends after 10 PM, returns true.
  /// 4. If the event spans multiple days (start and end are on different days), returns true.
  /// 5. Otherwise, returns false.
  static bool isAllDay(EventModels event) {
    if (event.allDay == 1) return true;
    final startTime = event.fromDate?.toLocalDT;
    final endTime = event.toDate?.toLocalDT;
    if (startTime == null || endTime == null) {
      return false;
    }
    if (_isSameDay(startTime, endTime)) {
      if (startTime.hour < 7 && endTime.hour > 22) {
        return true;
      }
    } else {
      return true;
    }
    return false;
  }

  static bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  /// Returns (startDate, endDate) for all-day events, or null if not all-day.
  static (DateTime, DateTime)? getAllDaySyncedDates({
    required bool isAllDay,
    required DateTime newDateTime,
    DateTime? endDate,
  }) {
    if (!isAllDay) return null;
    final date = DateTime(newDateTime.year, newDateTime.month, newDateTime.day);
    return (date, DateTime(date.year, date.month, date.day, 23, 59));
  }
}

class RepeatTypeAndDays {
  final EventRepeatType type;
  final List<WeekDay> days;
  RepeatTypeAndDays(this.type, this.days);
}
