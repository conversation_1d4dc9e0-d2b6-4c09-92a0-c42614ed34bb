import 'dart:async';

import 'package:flutter/material.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/flash/flash.dart';

// void showToast(
//   String title,
//   String content, {
//   bool? isWarningToast = false,
//   Color? color,
//   String? imagePath,
//   Widget? iconWidget,
//   bool isTopPosition = true,
//   Function()? onTap,
// }) {
//   showFlash(
//     context: Get.context!,
//     duration: const Duration(seconds: 3),
//     // persistent: false,
//     builder: (_, controller) {
//       return Flash(
//         borderRadius: Component.radius.radius8,
//         margin: padding(all: 24),
//         controller: controller,
//         backgroundColor: Colors.white,
//         boxShadows: Component.shadow.toastShadow,
//         barrierDismissible: true,
//         behavior: FlashBehavior.floating,
//         position: isTopPosition ? FlashPosition.top : FlashPosition.bottom,
//         onTap: () async {
//           await controller.dismiss();
//           if (onTap != null) {
//             onTap()!;
//           }
//         },
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Padding(
//               padding: padding(horizontal: 8, vertical: 12),
//               child: IntrinsicHeight(
//                 child: Row(
//                   children: [
//                     Container(
//                       width: width(3),
//                       decoration: BoxDecoration(
//                         borderRadius: Component.radius.customRadius(18),
//                         color:
//                             color ?? (isWarningToast! ? Component.color.secondaryColor : Component.color.successColor),
//                       ),
//                     ),
//                     SizedBox(
//                       width: width(12),
//                     ),
//                     iconWidget ??
//                         SvgImageCustom(
//                             imagePath: imagePath ?? (isWarningToast! ? ImagePaths.warning : ImagePaths.success),
//                             size: 32),
//                     SizedBox(width: width(9)),
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             title,
//                             style: Component.textStyle.mediumSemiBold,
//                             // ? Fix Issue SFA-1137
//                             maxLines: 1,
//                             overflow: TextOverflow.ellipsis,
//                           ),
//                           Text(
//                             content,
//                             style: Component.textStyle.smallMedium.copyWith(color: Component.color.grey400),
//                             maxLines: 2,
//                             overflow: TextOverflow.ellipsis,
//                           ),
//                         ],
//                       ),
//                     ),
//                     SizedBox(
//                       width: width(8),
//                     ),
//                     GestureDetector(
//                       onTap: controller.dismiss,
//                       child: SvgImageCustom(
//                         imagePath: ImagePaths.close,
//                         size: 16,
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       );
//     },
//   );
// }

// void showNotifyToast({required String title, required String content, required Function onTap}) {
//   showFlash(
//     context: Get.context!,
//     duration: const Duration(seconds: 3),
//     builder: (_, controller) {
//       return Flash(
//         borderRadius: Component.radius.radius8,
//         margin: padding(horizontal: 16, vertical: 12),
//         controller: controller,
//         backgroundColor: Colors.white,
//         boxShadows: Component.shadow.toastShadow,
//         barrierDismissible: true,
//         behavior: FlashBehavior.floating,
//         position: FlashPosition.top,
//         onTap: () => {
//           controller.dismiss(),
//           onTap(),
//         },
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Padding(
//               padding: padding(horizontal: 8, vertical: 12),
//               child: IntrinsicHeight(
//                 child: Row(
//                   children: [
//                     Container(
//                       width: width(3),
//                       decoration: BoxDecoration(
//                         borderRadius: Component.radius.customRadius(18),
//                         color: Component.color.primaryColor,
//                       ),
//                     ),
//                     SizedBox(
//                       width: width(12),
//                     ),
//                     Container(
//                       height: width(24),
//                       width: width(24),
//                       decoration: BoxDecoration(
//                         borderRadius: Component.radius.radius100,
//                         color: Component.color.primaryColor,
//                       ),
//                       child: Center(
//                         child: SvgImageCustom(imagePath: ImagePaths.product_dashboard, size: 14),
//                       ),
//                     ),
//                     SizedBox(width: width(9)),
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             title,
//                             style: Component.textStyle.xsSmallBold,
//                             maxLines: 1,
//                             overflow: TextOverflow.ellipsis,
//                           ),
//                           Text(
//                             content,
//                             style: Component.textStyle.exSmallRegular.copyWith(color: Component.color.grey400),
//                             maxLines: 2,
//                             overflow: TextOverflow.ellipsis,
//                           ),
//                         ],
//                       ),
//                     ),
//                     SizedBox(width: width(8)),
//                     GestureDetector(
//                       onTap: controller.dismiss,
//                       child: SvgImageCustom(
//                         imagePath: ImagePaths.close_toast,
//                         size: 16,
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       );
//     },
//   );
// }

enum ToastType { normal, error, success }

class ToastStyle {
  final Duration duration;
  final Color? backgroundColor;
  final Color? textColor;
  final double borderRadius;
  final EdgeInsets? margin;
  final EdgeInsets? paddingValue;
  final double fontSize;
  final FontWeight fontWeight;
  final FlashPosition position;
  final List<BoxShadow>? boxShadows;

  const ToastStyle({
    this.duration = const Duration(seconds: 2),
    this.backgroundColor,
    this.textColor,
    this.borderRadius = 12,
    this.margin,
    this.paddingValue,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w600,
    this.position = FlashPosition.bottom,
    this.boxShadows,
  });
}

// Predefined toast styles
const ToastStyle kToastStyleNormal = ToastStyle(
  backgroundColor: null, // fallback to appTheme.fadeTextColor in showToast
);
const ToastStyle kToastStyleError = ToastStyle(
  backgroundColor: Color(0xFFF44336), // fallback to appTheme.errorV2 if null
);
ToastStyle kToastStyleSuccess = ToastStyle(
  backgroundColor: appTheme.greenV2, // fallback to appTheme.greenV2 if available
  textColor: Colors.white,
);

// Backward compatible simple toast
void showSimpleToast(String content, {ToastStyle? style}) {
  showToast(content, type: ToastType.normal, style: style);
}

void showErrorToast(String content, {ToastStyle? style}) {
  showToast(content, type: ToastType.error, style: style);
}

void showToast(
  String content, {
  ToastType type = ToastType.normal,
  ToastStyle? style,
  Duration? duration,
  Color? backgroundColor,
  Color? textColor,
  double? borderRadius,
  EdgeInsets? margin,
  EdgeInsets? paddingValue,
  double? fontSize,
  FontWeight? fontWeight,
  FlashPosition? position,
  List<BoxShadow>? boxShadows,
  bool? barrierDismissible,
  FlashBehavior? behavior,
  Widget? child,
  VoidCallback? onTap,
}) {
  ToastStyle baseStyle;
  switch (type) {
    case ToastType.error:
      baseStyle = style ?? kToastStyleError;
      break;
    case ToastType.success:
      baseStyle = style ?? kToastStyleSuccess;
      break;
    case ToastType.normal:
      baseStyle = style ?? kToastStyleNormal;
      break;
  }
  final ToastStyle s = ToastStyle(
    duration: (duration != null && duration.inMilliseconds < 0) ? baseStyle.duration : (duration ?? baseStyle.duration),
    backgroundColor: backgroundColor ?? baseStyle.backgroundColor,
    textColor: textColor ?? baseStyle.textColor,
    borderRadius: borderRadius ?? baseStyle.borderRadius,
    margin: margin ?? baseStyle.margin,
    paddingValue: paddingValue ?? baseStyle.paddingValue,
    fontSize: fontSize ?? baseStyle.fontSize,
    fontWeight: fontWeight ?? baseStyle.fontWeight,
    position: position ?? baseStyle.position,
    boxShadows: boxShadows ?? baseStyle.boxShadows,
  );
  final Duration? flashDuration = (duration != null && duration.inMilliseconds < 0) ? null : (duration ?? baseStyle.duration);
  showFlash(
    context: navigatorKey.currentContext!,
    duration: flashDuration, // null for persistent, else as set
    builder: (_, controller) {
      return Flash(
        borderRadius: BorderRadius.circular(s.borderRadius),
        margin: s.margin ?? padding(all: 24),
        controller: controller,
        backgroundColor: s.backgroundColor ?? appTheme.fadeTextColor,
        boxShadows: s.boxShadows ??
            [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                offset: const Offset(2.0, 2.0),
                blurRadius: 4.0,
              ),
            ],
        barrierDismissible: barrierDismissible ?? true,
        behavior: behavior ?? FlashBehavior.floating,
        position: s.position,
        onTap: onTap,
        child: child ??
            Container(
              padding: s.paddingValue ?? padding(horizontal: 38, vertical: 13),
              child: Text(
                content,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: s.fontSize,
                  fontWeight: s.fontWeight,
                  color: s.textColor ?? appTheme.whiteText,
                ),
              ),
            ),
      );
    },
  );
}

/// Shows a persistent toast (manual dismiss) using the global navigator context.
/// 
/// Returns a [Completer] that you can call `complete()` on to dismiss the toast.
/// The toast will remain visible for at least [ToastStyle.duration] (default 2 seconds),
/// even if `complete()` is called earlier. If `complete()` is called before the toast
/// is shown, it will be dismissed as soon as possible after the minimum duration.
Completer<void> showPersistentToast(
  String content, {
  ToastType type = ToastType.normal,
  ToastStyle? style,
  Widget? child,
  VoidCallback? onTap,
  bool? barrierDismissible,
  FlashBehavior? behavior,
  FlashPosition? position,
  List<BoxShadow>? boxShadows,
}) {
  final ToastStyle baseStyle;
  switch (type) {
    case ToastType.error:
      baseStyle = style ?? kToastStyleError;
      break;
    case ToastType.success:
      baseStyle = style ?? kToastStyleSuccess;
      break;
    case ToastType.normal:
      baseStyle = style ?? kToastStyleNormal;
      break;
  }
  final completer = Completer<void>();
  FlashController? flashController;
  bool shouldDismiss = false;
  DateTime? shownAt;

  showFlash(
    context: navigatorKey.currentContext!,
    duration: null, // persistent
    builder: (_, controller) {
      flashController = controller;
      shownAt ??= DateTime.now();
      // If completer already completed, dismiss after baseStyle.duration for smoothness
      if (shouldDismiss && !controller.isDisposed) {
        final elapsed = DateTime.now().difference(shownAt!);
        final remaining = baseStyle.duration - elapsed;
        final delay = remaining > Duration.zero ? remaining : Duration.zero;
        Future.delayed(delay, () {
          if (!controller.isDisposed) controller.dismiss();
        });
      }
      return Flash(
        controller: controller,
        borderRadius: BorderRadius.circular(baseStyle.borderRadius),
        margin: baseStyle.margin ?? padding(all: 24),
        backgroundColor: baseStyle.backgroundColor ?? appTheme.fadeTextColor,
        boxShadows: boxShadows ??
            baseStyle.boxShadows ??
            [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                offset: const Offset(2.0, 2.0),
                blurRadius: 4.0,
              ),
            ],
        barrierDismissible: barrierDismissible ?? true,
        behavior: behavior ?? FlashBehavior.floating,
        position: position ?? baseStyle.position,
        onTap: onTap,
        child: child ??
            Container(
              padding: baseStyle.paddingValue ?? padding(horizontal: 38, vertical: 13),
              child: Text(
                content,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: baseStyle.fontSize,
                  fontWeight: baseStyle.fontWeight,
                  color: baseStyle.textColor ?? appTheme.whiteText,
                ),
              ),
            ),
      );
    },
  ).whenComplete(() {
    if (!completer.isCompleted) completer.complete();
  });

  completer.future.then((_) {
    if (flashController != null && !flashController!.isDisposed) {
      // Ensure min duration
      final elapsed = shownAt != null ? DateTime.now().difference(shownAt!) : Duration.zero;
      final remaining = baseStyle.duration - elapsed;
      final delay = remaining > Duration.zero ? remaining : Duration.zero;
      Future.delayed(delay, () {
        if (flashController != null && !flashController!.isDisposed) {
          flashController!.dismiss();
        }
      });
    } else {
      // Controller not ready yet, mark for dismissal
      shouldDismiss = true;
    }
  });

  return completer;
}
