import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/main.dart';

/// Navigation helper to standardize navigation patterns throughout the app
/// This helper works seamlessly with existing context.pushRoute() calls
/// and provides intelligent tab/stack navigation handling
class NavigationHelper {
  /// Navigate to a specific tab in the main bottom navigation
  /// Use this when you want to switch to a tab from within the app
  static void navigateToTab(BuildContext context, int tabIndex) {
    try {
      final tabRouter = context.tabsRouter;
      tabRouter.setActiveIndex(tabIndex);
    } catch (e) {
      // If not in tab context, this will fail silently
      debugPrint('NavigationHelper: Not in tab context, cannot navigate to tab $tabIndex');
    }
  }

  /// Navigate to Home tab (index 0)
  static void navigateToHomeTab(BuildContext context) {
    navigateToTab(context, TabConstants.homeTab);
  }

  /// Navigate to Calendar tab (index 1)
  static void navigateToCalendarTab(BuildContext context) {
    navigateToTab(context, TabConstants.calendarTab);
  }

  /// Navigate to CheckList tab (index 2)
  static void navigateToCheckListTab(BuildContext context) {
    navigateToTab(context, TabConstants.checkListTab);
  }

  /// Navigate to Profile tab (index 3)
  static void navigateToProfileTab(BuildContext context) {
    navigateToTab(context, TabConstants.profileTab);
  }

  /// Smart navigation that attempts tab navigation first, then falls back to route push
  /// Use this when you want to navigate to a screen that could be either a tab or standalone
  static void smartNavigate(BuildContext context, PageRouteInfo route, {int? tabIndex}) {
    if (tabIndex != null) {
      try {
        final tabRouter = context.tabsRouter;
        tabRouter.setActiveIndex(tabIndex);
        return;
      } catch (e) {
        // Fall through to route push
      }
    }
    context.pushRoute(route);
  }

  /// Navigate to CheckList with smart fallback
  static void navigateToCheckList(BuildContext context) {
    smartNavigate(
      context, 
      const CheckListRoute(), 
      tabIndex: TabConstants.checkListTab,
    );
  }

  /// Navigate to Calendar with smart fallback
  static void navigateToCalendar(BuildContext context) {
    smartNavigate(
      context, 
      const CalendarRoute(), 
      tabIndex: TabConstants.calendarTab,
    );
  }

  /// Check if currently in a tab context
  static bool isInTabContext(BuildContext context) {
    try {
      context.tabsRouter;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get current active tab index, returns -1 if not in tab context
  static int getCurrentTabIndex(BuildContext context) {
    try {
      return context.tabsRouter.activeIndex;
    } catch (e) {
      return -1;
    }
  }

  /// Check if a route is a tab route (doesn't require navigation stack)
  static bool isTabRoute(PageRouteInfo route) {
    final tabRoutes = {
      'HomeRoute',
      'CalendarRoute', 
      'CheckListRoute',
      'ProfileRoute',
    };
    return tabRoutes.contains(route.routeName);
  }

  /// Get tab index for a tab route, returns -1 if not a tab route
  static int getTabIndexForRoute(PageRouteInfo route) {
    switch (route.routeName) {
      case 'HomeRoute':
        return TabConstants.homeTab;
      case 'CalendarRoute':
        return TabConstants.calendarTab;
      case 'CheckListRoute':
        return TabConstants.checkListTab;
      case 'ProfileRoute':
        return TabConstants.profileTab;
      default:
        return -1;
    }
  }

  /// Enhanced smart back navigation that handles both stack and tab contexts
  /// This works automatically without requiring changes to existing code
  static void smartBack(BuildContext context) {
    // Check if we can pop normally (for standalone routes)
    if (context.router.canPop()) {
      context.maybePop();
    } else {
      // We're in a tab context and can't pop
      try {
        final tabRouter = context.tabsRouter;
        // Only navigate to home if we're not already on home tab
        if (tabRouter.activeIndex != TabConstants.homeTab) {
          tabRouter.setActiveIndex(TabConstants.homeTab);
        } else {
          // If we're already on home tab and can't pop, do nothing
          // This prevents infinite loops or unexpected behavior
          debugPrint('NavigationHelper: Already on home tab, no further back navigation available');
        }
      } catch (e) {
        // If we're not in a tab context, try to pop anyway
        context.maybePop();
      }
    }
  }

  /// **COMPATIBILITY LAYER**: Intercept pushRoute calls for tab routes
  /// This method can be used to wrap existing context.pushRoute() calls
  /// without changing the calling code
  static Future<T?> compatiblePushRoute<T extends Object?>(
    BuildContext context, 
    PageRouteInfo route
  ) {
    // If it's a tab route and we're in tab context, use tab navigation
    if (isTabRoute(route) && isInTabContext(context)) {
      final tabIndex = getTabIndexForRoute(route);
      if (tabIndex != -1) {
        navigateToTab(context, tabIndex);
        // Return a completed future to maintain API compatibility
        return Future.value(null);
      }
    }
    
    // Otherwise, use normal route pushing
    return context.pushRoute(route);
  }

  /// **FUTURE ENHANCEMENT**: Auto-detect problematic navigation patterns
  /// This can be used for debugging and monitoring
  static void debugNavigationCall(BuildContext context, PageRouteInfo route) {
    if (isTabRoute(route) && isInTabContext(context)) {
      debugPrint('⚠️  NavigationHelper: Detected pushRoute to tab route ${route.routeName} from tab context. Consider using NavigationHelper.navigateToTab() instead.');
    }
  }

  /// Wait for navigation context to be ready with proper checks
  static Future<bool> waitForNavigationContextReady({
    Duration timeout = const Duration(seconds: 10),
    Duration checkInterval = const Duration(milliseconds: 100),
  }) async {
    final startTime = DateTime.now();
    
    while (DateTime.now().difference(startTime) < timeout) {
      try {
        if (navigatorKey.currentContext != null) {
          final context = navigatorKey.currentContext!;
          
          if (context.mounted) {
            try {
              context.router;
              debugPrint('NavigationHelper: Navigation context is ready and usable');
              return true;
            } catch (e) {
              debugPrint('NavigationHelper: Router not ready yet, waiting...');
            }
          }
        }
        
        await Future.delayed(checkInterval);
      } catch (e) {
        debugPrint('NavigationHelper: Error checking navigation context: $e');
        await Future.delayed(checkInterval);
      }
    }
    
    debugPrint('NavigationHelper: Navigation context not ready after ${timeout.inSeconds} seconds');
    return false;
  }

  /// Safe navigation that waits for context to be ready before attempting navigation
  static Future<bool> safeNavigate(
    PageRouteInfo route, {
    Duration timeout = const Duration(seconds: 10),
    Duration checkInterval = const Duration(milliseconds: 100),
  }) async {
    try {
      final contextReady = await waitForNavigationContextReady(
        timeout: timeout,
        checkInterval: checkInterval,
      );
      
      if (!contextReady) {
        debugPrint('NavigationHelper: Navigation context not ready, cannot navigate to ${route.routeName}');
        return false;
      }
      
      final context = navigatorKey.currentContext!;
      
      if (isTabRoute(route) && isInTabContext(context)) {
        final tabIndex = getTabIndexForRoute(route);
        if (tabIndex != -1) {
          navigateToTab(context, tabIndex);
          return true;
        }
      }
      
      context.pushRoute(route);
      return true;
    } catch (e) {
      debugPrint('NavigationHelper: Error during safe navigation to ${route.routeName}: $e');
      return false;
    }
  }

  /// Safe navigation with fallback - if navigation fails, store for later retry
  static Future<void> safeNavigateWithFallback(
    PageRouteInfo route, {
    Duration timeout = const Duration(seconds: 10),
    Duration checkInterval = const Duration(milliseconds: 100),
    Future<void> Function(Map<String, dynamic>)? storeForLater,
    Map<String, dynamic>? notificationData,
  }) async {
    try {
      final success = await safeNavigate(
        route,
        timeout: timeout,
        checkInterval: checkInterval,
      );
      
      if (!success && storeForLater != null && notificationData != null) {
        await storeForLater(notificationData);
      }
    } catch (e) {
      if (storeForLater != null && notificationData != null) {
        await storeForLater(notificationData);
      }
    }
  }
}
