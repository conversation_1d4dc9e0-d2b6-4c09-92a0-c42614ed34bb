import 'package:device_calendar/device_calendar.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:uuid/uuid.dart';

class CalendarUtils {
  // ====== Constants ======
  static const Uuid _uuid = Uuid();
  // RFC4122 UUID namespace
  // Be careful when changing this, as it will affect the generated UUIDs
  // make duplicate UUIDs if the namespace is changed
  static const String _namespace = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';

  /// Weekday headers for Monday and Sunday start
  static const List<String> daysOfWeekMonday = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
  static const List<String> daysOfWeekSunday = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

  // ====== Calendar Header/Week Utils ======

  /// Returns the days of week header based on week start.
  ///
  /// [weekStartMonday] - true for Monday start, false for Sunday start.
  static List<String> getDaysOfWeek(bool weekStartMonday) =>
      weekStartMonday ? daysOfWeekMonday : daysOfWeekSunday;

  /// Returns the number of leading empty days for a calendar month grid.
  ///
  /// [firstDayOfMonth] is the first day of the month.
  /// [weekStartMonday] is true if week starts on Monday, false if Sunday.
  static int getLeadingEmptyDays(DateTime firstDayOfMonth, bool weekStartMonday) {
    final int weekday = firstDayOfMonth.weekday; // 1=Mon, 7=Sun
    int leading;
    if (weekStartMonday) {
      leading = (weekday - DateTime.monday) % 7;
    } else {
      leading = (weekday - DateTime.sunday) % 7;
    }
    if (leading < 0) leading += 7;
    return leading;
  }

  /// Returns the start and end of the week for a given date, respecting weekStartForMonday.
  ///
  /// [date] - the reference date.
  /// [weekStartForMonday] - true for Monday start, false for Sunday start.
  static List<DateTime> getWeekRange(DateTime date, bool weekStartForMonday) {
    DateTime start, end;
    if (weekStartForMonday) {
      final int daysToSubtract = (date.weekday + 6) % 7; // Monday=1, Sunday=7
      start = date.subtract(Duration(days: daysToSubtract));
      end = start.add(const Duration(days: 6));
    } else {
      final int daysToSubtract = date.weekday % 7; // Sunday=7
      start = date.subtract(Duration(days: daysToSubtract));
      end = start.add(const Duration(days: 6));
    }
    return [start, end];
  }

  // ====== UUID/ID Utilities ======

  /// Generates a consistent UUID v5 from an event ID.
  ///
  /// [sourceId] - the source event ID.
  static String generateEventId(String? sourceId) {
    return _uuid.v5(_namespace, sourceId);
  }

  /// Deduplicate a list of events by eventId or uuid (keep the first occurrence).
  ///
  /// Accepts a list of any event-like objects with either `eventId` or `uuid` property.
  /// [getId] - optional function to extract the ID from the event.
  static List<T> deduplicateEventsById<T>(
    List<T> events, {
    String Function(T)? getId,
  }) {
    final seenIds = <String>{};
    final deduped = <T>[];
    for (final event in events) {
      final id = getId != null ? getId(event) : _getEventIdOrUuid(event);
      if (id.isEmpty) continue;
      if (seenIds.add(id)) {
        deduped.add(event);
      }
    }
    return deduped;
  }

  /// Helper to extract eventId or uuid from dynamic event.
  static String _getEventIdOrUuid(dynamic event) {
    if (event == null) return '';
    if (event is Map && event['eventId'] != null) return event['eventId'] as String;
    if (event is Map && event['uuid'] != null) return event['uuid'] as String;
    try {
      final eventId = event.eventId;
      if (eventId != null && eventId is String && eventId.isNotEmpty) return eventId;
    } catch (_) {}
    try {
      final uuid = event.uuid;
      if (uuid != null && uuid is String && uuid.isNotEmpty) return uuid;
    } catch (_) {}
    return '';
  }

  // ====== Date Range Utilities ======

  /// Returns the default date range for import/delete (from, to).
  ///
  /// The range covers the entire current calendar year (from January 1st to December 31st, UTC).
  static (DateTime from, DateTime to) getDefaultImportRange() {
    final now = DateTime.now().toUtc();
    final from = DateTime.utc(now.year, 1, 1, 0, 0, 0);
    final to = DateTime.utc(now.year, 12, 31, 23, 59, 59);
    return (from, to);
  }

  /// Returns the UTC start and end DateTime for a 3-month range (previous, current, next month) based on the given date.
  ///
  /// For example, if date is 10/06, returns 01/05 to 31/07.
  static List<DateTime> getThreeMonthRangeUtc(DateTime date) {
    final prevMonth = DateTime.utc(date.year, date.month - 1, 1, 0, 0, 0);
    final nextMonthEnd = DateTime.utc(date.year, date.month + 2, 0, 23, 59, 59);
    return [prevMonth, nextMonthEnd];
  }

  /// Returns the UTC start and end DateTime for N months starting from [monthOffset] relative to [date].
  ///
  /// For example, if date is June and monthOffset is 1, and count is 2, returns 01/07 to 31/08.
  static List<DateTime> getMonthRangeUtc(DateTime date, {int monthOffset = 0, int count = 1}) {
    final startMonth = DateTime.utc(date.year, date.month + monthOffset, 1, 0, 0, 0);
    final endMonth = DateTime.utc(date.year, date.month + monthOffset + count, 0, 23, 59, 59);
    return [startMonth, endMonth];
  }

  // ====== Month Range/Prefetch Utilities ======

  /// Returns true if [month] is outside the given 3-month [range] (as returned by getThreeMonthRangeUtc).
  ///
  /// [month] should be any date within the month to check.
  /// The function checks if the entire month is outside the range.
  static bool isMonthOutsideThreeMonthRange(DateTime month, List<DateTime> range) {
    if (range.length != 2) return true;
    final from = range[0];
    final to = range[1];
    final firstDay = DateTime.utc(month.year, month.month, 1);
    final lastDay = DateTime.utc(month.year, month.month + 1, 0, 23, 59, 59);
    return lastDay.isBefore(from) || firstDay.isAfter(to);
  }

  /// Returns true if [month] is outside the previous, current, and next 3-month ranges.
  ///
  /// Used for lazy loading: only fetch if [month] is not in any adjacent 3-month window.
  static bool isMonthOutsideAdjacentThreeMonthRanges(DateTime month, DateTime previousMonth, DateTime currentMonth) {
    final prevRange = getThreeMonthRangeUtc(previousMonth);
    final currRange = getThreeMonthRangeUtc(currentMonth);
    final nextMonth = DateTime.utc(currentMonth.year, currentMonth.month + 1, 1);
    final nextRange = getThreeMonthRangeUtc(nextMonth);
    final inPrev = !isMonthOutsideThreeMonthRange(month, prevRange);
    final inCurr = !isMonthOutsideThreeMonthRange(month, currRange);
    final inNext = !isMonthOutsideThreeMonthRange(month, nextRange);
    return !(inPrev || inCurr || inNext);
  }

  /// Returns true if [month] is the first or last month in the given 3-month [range].
  ///
  /// Used for lazy loading: fetch next/previous range if user navigates to the edge month.
  static bool isMonthAtEdgeOfThreeMonthRange(DateTime month, List<DateTime> range) {
    if (range.length != 2) return false;
    final from = range[0];
    final to = range[1];
    // First and last month numbers in the range
    final firstMonth = from.month;
    final lastMonth = to.month;
    final yearOfFirst = from.year;
    final yearOfLast = to.year;
    // Check if month is the first or last month in the range (handle year wrap)
    final isFirst = month.month == firstMonth && month.year == yearOfFirst;
    final isLast = month.month == lastMonth && month.year == yearOfLast;
    return isFirst || isLast;
  }

  /// Determines if prefetching is needed and returns the direction (next/prev) and the range to fetch.
  ///
  /// Returns null if no prefetch is needed, otherwise returns a map with keys: 'direction', 'from', 'to', 'month'.
  /// [adjacentMonthCount] controls how many months before/after to fetch (default: 2).
  /// If newMonth is immediately before or after oldMonth, prefetches events for newMonth and the next or previous [adjacentMonthCount] months.
  static Map<String, dynamic>? getPrefetchInfo(
    DateTime newMonth,
    DateTime oldMonth, {
    int adjacentMonthCount = 2,
  }) {
    // Calculate the difference in months between newMonth and oldMonth
    final int diff = (newMonth.year - oldMonth.year) * 12 + (newMonth.month - oldMonth.month);

    if (diff > 0) {
      // newMonth is after oldMonth, prefetch newMonth + next [adjacentMonthCount] months
      final from = DateTime.utc(newMonth.year, newMonth.month, 1, 0, 0, 0);
      final to = DateTime.utc(newMonth.year, newMonth.month + adjacentMonthCount, 0, 23, 59, 59);
      return {
        'direction': 'next',
        'month': newMonth,
        'from': from,
        'to': to,
      };
    } else if (diff < 0) {
      // newMonth is before oldMonth, prefetch previous [adjacentMonthCount] months + newMonth
      final prevMonth = DateTime.utc(newMonth.year, newMonth.month - adjacentMonthCount, 1);
      final from = prevMonth;
      final to = DateTime.utc(newMonth.year, newMonth.month + 1, 0, 23, 59, 59);
      return {
        'direction': 'prev',
        'month': prevMonth,
        'from': from,
        'to': to,
      };
    }
    return null;
  }

  // ====== Recurrence Utilities ======

  /// Maps [RecurrenceFrequency] to device_calendar's [Frequency].
  static Frequency? getRecurrenceFrequencyForDeviceCalendar(RecurrenceFrequency? frequency) {
    switch (frequency) {
      case RecurrenceFrequency.Daily:
        return Frequency.daily;
      case RecurrenceFrequency.Weekly:
        return Frequency.weekly;
      case RecurrenceFrequency.Monthly:
        return Frequency.monthly;
      case RecurrenceFrequency.Yearly:
        return Frequency.yearly;
      default:
        return null;
    }
  }

  /// Converts a list of device_calendar's DayOfWeek to a list of WeekDay enum.
  static List<WeekDay>? getByDayOfWeek(List<DayOfWeek>? daysOfWeek) {
    if (daysOfWeek == null || daysOfWeek.isEmpty) return null;
    return daysOfWeek.map((d) => WeekDay.values[d.value]).toList();
  }
}
