import 'package:family_app/data/model/category.dart';
import 'package:family_app/data/repository/ibase_repository.dart';

abstract class ICategoryRepository extends IBaseRepository {
  Future<List<Category>> getAllCategory();
  Future<Category> createCategory(String name);
  Future<void> getCategoryById(String id, Map<String, dynamic> queries);
  Future<void> updateCategory(String id, Map<String, dynamic> body);
  Future<void> deleteCategory(String id);
}
