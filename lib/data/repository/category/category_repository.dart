import 'package:family_app/data/model/category.dart';
import 'package:family_app/data/remote/category_api.dart';
import 'package:family_app/data/repository/category/icategory_repository.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: ICategoryRepository)
class CategoryRepository extends ICategoryRepository {
  final CategoryAPI categoryAPI;

  CategoryRepository({required this.categoryAPI});

  @override
  Future<Category> createCategory(String name) async {
    try {
      final body = <String, dynamic>{'name': name, "color": "#dddd"};
      final result = await categoryAPI.createCategory(body);
      return result.parse(Category.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<void> deleteCategory(String id) {
    // TODO: implement deleteCategory
    throw UnimplementedError();
  }

  @override
  Future<List<Category>> getAllCategory() async {
    try {
      final queries = <String, dynamic>{};
      final result = await categoryAPI.getAllCategory(queries);

      return result.parseList(Category.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<void> getCategoryById(String id, Map<String, dynamic> queries) {
    // TODO: implement getCategoryById
    throw UnimplementedError();
  }

  @override
  Future<void> updateCategory(String id, Map<String, dynamic> body) {
    // TODO: implement updateCategory
    throw UnimplementedError();
  }
}
