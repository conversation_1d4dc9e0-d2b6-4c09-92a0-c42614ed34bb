class CategoryParameter {
  final String name;
  final String planDate;
  final String color;
  final String description;
  final List includedMembers;
  final String categoryId;
  final String familyId;

  CategoryParameter({
    required this.name,
    required this.planDate,
    required this.color,
    required this.description,
    required this.includedMembers,
    required this.categoryId,
    required this.familyId,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      "name": name,
      "plan_date": planDate,
      "color": color,
      "description": description,
      "included_members": includedMembers,
      "category_uuid": categoryId,
      "family_uuid": familyId,
    };
  }
}
