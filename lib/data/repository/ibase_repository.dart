import 'package:dio/dio.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';

abstract class IBaseRepository {
  handleError(error) {
    if (error is DioException) {
      // final exception = error as DioException;
      // if (exception.response?.data is Map && exception.response?.data.containsKey('code')) {
      //   final code = exception.response?.data['code'] ?? 200;
      //   if (code == 401) {
      //     dismissLoading();
      //     locator.get<LocalStorage>().clear();
      //     navigatorKey.currentContext!.pushRoute(const AuthRoute());
      //   }
      // }
    }
  }

  handleChangePasswordError(error) {
    if (error is DioException) {
      if (error.response?.statusCode == 400) {
        final responseData = error.response!.data;
        if (responseData is Map) {
          final dataMessage = responseData['message'];
          if (dataMessage == 'wrong_password') {
            throw WrongPassword();
          }
        }
      }
    }
  }
}
