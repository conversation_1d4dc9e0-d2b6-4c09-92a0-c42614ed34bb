import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/family.dart';
import 'package:family_app/data/model/family_profile.dart';
import 'package:family_app/data/model/interaction_model.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/presign_url.dart';
import 'package:family_app/data/model/privacy_policy.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/model/upcoming.dart';
import 'package:family_app/data/remote/family_api.dart';
import 'package:family_app/data/repository/family/model/change_password_parameter.dart';
import 'package:family_app/data/repository/family/family_exception.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/family/model/invite_parameter.dart';
import 'package:family_app/data/usecase/model/upsert_memory_activity_param.dart';
import 'package:family_app/data/usecase/model/upsert_memory_param.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:injectable/injectable.dart';

@Injectable(as: IFamilyRepository)
class FamilyRepository extends IFamilyRepository {
  final FamilyApi familyApi;

  FamilyRepository({required this.familyApi});

  @override
  Future<FamilyProfile> getProfileById(String familyId) async {
    try {
      final result = await familyApi.getProfileById(familyId);
      return result.parse(FamilyProfile.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Account> updateProfileById(
    String familyId, {
    required String familyName,
    String? description,
    String? birthday,
    int? showYear,
    String? full_name,
  }) async {
    try {
      final bodies = <String, dynamic>{};
      bodies.putIfAbsent('family_name', () => familyName);
      if ((description ?? '').isNotEmpty) {
        bodies.putIfAbsent('description', () => description);
      }
      if ((birthday ?? '').isNotEmpty) {
        bodies.putIfAbsent('birthday', () => birthday);
      }
      if (showYear != null) {
        bodies.putIfAbsent('show_year', () => "$showYear");
      }
      if ((full_name ?? '').isNotEmpty) {
        bodies.putIfAbsent('full_name', () => full_name);
      }

      final result = await familyApi.updateProfileById(familyId, bodies);
      return result.parse(Account.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Account> createProfile({required String familyName, String? description}) async {
    try {
      final bodies = <String, dynamic>{};
      bodies.putIfAbsent('family_name', () => familyName);
      if ((description ?? '').isNotEmpty) {
        bodies.putIfAbsent('description', () => description);
      }
      final result = await familyApi.createProfile(bodies);
      return result.parse(Account.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Family> getMyProfile() async {
    try {
      final result = await familyApi.getMyProfile();
      final family = result.parse(Family.fromJson);
      family.familyUuid = family.uuid;
      return family;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Account> getUserInfo(String userId) async {
    try {
      final result = await familyApi.getUserInfo(userId);
      final user = result.parse(Account.fromJson);
      return user;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Family> getUserProfile(String familyId) async {
    try {
      final result = await familyApi.getUserInFamily(familyId);
      return result.parse(Family.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<Account>> getUserInFamily(String familyId) async {
    try {
      final result = await familyApi.getUserInFamily(familyId);
      return result.parseList(Account.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Account?> invite(InviteParameter parameter) async {
    try {
      final result = await familyApi.invite(parameter.toJson());
      return result.parse(Account.fromJson);
    } catch (e) {
      if (e is DioException) {
        if (e.response?.statusCode == 400 && e.response?.data['message'] == 'cant_invite_myself') {
          throw CantInviteMyselfError();
        }
      }
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<Family> updateFamilyInfo({String? fullName, String? familyName, String? description}) async {
    try {
      final bodies = <String, dynamic>{};
      if ((fullName ?? '').isNotEmpty) {
        bodies.putIfAbsent('full_name', () => fullName);
      }
      if ((familyName ?? '').isNotEmpty) {
        bodies.putIfAbsent('family_name', () => familyName);
      }
      final result = await familyApi.updateFamilyInfo(bodies);
      return result.parse(Family.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<Account>> getFamilyMember({StatusMember? status}) async {
    try {
      final queries = <String, dynamic>{};
      // queries.putIfAbsent('status', () => status.index);
      final result = await familyApi.getFamilyMembers(queries);
      if (result.data == null) {
        return <Account>[];
      }
      return result.parseList(Account.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<Family>> getFamilyBelongTo() async {
    try {
      final result = await familyApi.getFamilyBelong();
      return result.parseList(Family.fromJson);
    } catch (e) {
      return <Family>[];
    }
  }

  @override
  Future<Account> updateMember(String memberId, {String? role, String? relationship, String? nickname}) async {
    try {
      var bodies = <String, dynamic>{};
      if ((role ?? '').isNotEmpty) {
        bodies.putIfAbsent('role', () => role);
      }
      if ((relationship ?? '').isNotEmpty) {
        bodies.putIfAbsent('relationship', () => relationship);
      }
      if ((nickname ?? '').isNotEmpty) {
        bodies.putIfAbsent('nickname', () => nickname);
      }
      final result = await familyApi.updateMember(memberId, bodies);
      return result.parse(Account.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<bool> kickMember(String memberId) async {
    try {
      await familyApi.kickMember(memberId);
      return true;
    } catch (e) {
      handleError(e);
      // rethrow;
      return false;
    }
  }

  @override
  Future<bool> activeFamily(String familyId) async {
    try {
      await familyApi.activeFamily(familyId);
      return true;
    } catch (e) {
      handleError(e);
      // rethrow;
      return false;
    }
  }

  @override
  Future<Upcoming?> getUpcoming(String familyId, {DateTime? from, DateTime? to}) async {
    try {
      final queries = <String, dynamic>{};
      if (from != null) {
        queries.putIfAbsent('from', () => from.toUtc().toIso8601String());
      }
      if (to != null) {
        queries.putIfAbsent('to', () => to.toUtc().toIso8601String());
      }

      queries.putIfAbsent('order_by', () => 'from_date');
      queries.putIfAbsent('ordering', () => 'asc');

      final result = await familyApi.getUpcoming(familyId, queries);
      return result.parse(Upcoming.fromJson);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> deactiveFamily(String familyId) async {
    try {
      await familyApi.deactiveFamily(familyId);
      return true;
    } catch (e) {
      handleError(e);
      // rethrow;
      return false;
    }
  }

  @override
  Future<void> updateNotificationSetting(String fcmToken) async {
    try {
      String osVersion = '';
      String device = '';
      String deviceId = '';
      if (Platform.isAndroid) {
        final deviceInfo = await DeviceInfoPlugin().androidInfo;
        osVersion = deviceInfo.version.release;
        device = deviceInfo.model;
        deviceId = deviceInfo.id;
      } else {
        final deviceInfo = await DeviceInfoPlugin().iosInfo;
        osVersion = deviceInfo.systemVersion;
        device = deviceInfo.model;
        deviceId = deviceInfo.identifierForVendor ?? '';
      }
      await familyApi.pushNotification({'token': fcmToken, 'os': Platform.isIOS ? 'ios' : 'android', 'device': device, 'version': osVersion, 'device_id': deviceId});
    } catch (e) {}
  }

  //XXX: NOT USED ANY MORE
  @override
  Future<bool> updateAvatar(String familyId, {required File file}) async {
    try {
      final body = FormData.fromMap({"file": MultipartFile.fromFileSync(file.path)});
      final resutl = await familyApi.updateAvatar(familyId, body);
      return true;
    } catch (e) {
      handleError(e);
      // rethrow;
      return false;
    }
  }

  @override
  Future<bool> updateAvatarS3(String familyId, {required String fileUuid}) async {
    try {
      final body = FormData.fromMap({"photo_url": fileUuid});
      await familyApi.updateAvatar(familyId, body);
      return true;
    } catch (e) {
      handleError(e);
      // rethrow;
      return false;
    }
  }

  @override
  Future<PrivacyPolicyData> privacy() async {
    try {
      final result = await familyApi.privacyPolicy();
      return result.parse(PrivacyPolicyData.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<PresignUrl> getStoragePresignedUrls(String familyId) async {
    try {
      final result = await familyApi.getStoragePresignedUrls(familyId);
      return result.parse(PresignUrl.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<StorageModel> updateFileInStorage(String storageId, String fileName) async {
    try {
      final formData = FormData.fromMap({"file_name": fileName});
      final result = await familyApi.updateFileInStorage(storageId, formData);
      return result.parse(StorageModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<bool> removeStorageFile(String uuid) async {
    try {
      await familyApi.removeStorageFile(uuid);
      return true;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<StorageModel>> getStorageByFamily(String familyId) async {
    try {
      final result = await familyApi.getStorageFilesByFamily(familyId);
      if (result.data == null) {
        return <StorageModel>[];
      }
      return result.parseList(StorageModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<String?> getStorageById(String familyId) async {
    try {
      final result = await familyApi.getStorageFile(familyId);
      if (result.data == null) return null;
      return result.parse(StorageModel.fromJson).fileUrl;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<MemoryModel>> getMemoryListByFamily(String familyId, {String? type}) async {
    try {
      final queries = <String, String>{};
      if (type != null) {
        queries.putIfAbsent('type', () => type);
      }

      final result = await familyApi.getMemoryListByFamily(familyId, queries);
      if (result.data == null) {
        return <MemoryModel>[];
      }
      return result.parseList(MemoryModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<MemoryModel> attachStorageToActivity(UpsertMemoryParam param) async {
    try {
      final result = await familyApi.attachStorageToMemory(param.toJson());
      return result.parse(MemoryModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<MemoryModel> attachStorageToMemory(UpsertMemoryParam param) async {
    try {
      final result = await familyApi.attachStorageToMemory(param.toJson());
      return result.parse(MemoryModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<MemoryModel> updateMemory(String attachId, UpsertMemoryParam param) async {
    try {
      final result = await familyApi.updateAttachInfo(attachId, param.toJson());
      return result.parse(MemoryModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<bool> deleteMemory(String attachId) async {
    try {
      final result = await familyApi.deleteAttach(attachId);
      return result.code == 200;
    } catch (e) {
      handleError(e);
    }
    return false;
  }

  @override
  Future<InteractionModel> updateMemoryActivity(String memoryId, UpsertMemoryActivityParam param) async {
    try {
      final result = await familyApi.upsertMemoryActivity(memoryId, param.toJson());
      return result.parse(InteractionModel.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<bool> deleteMemoryActivity(String interactionId) async {
    try {
      final result = await familyApi.deleteMemoryActivity(interactionId);
      return result.code == 200;
    } catch (e) {
      handleError(e);
    }
    return false;
  }

  @override
  Future<List<Item>> getTaskDueToday(String familyId) async {
    try {
      DateTime now = DateTime.now();
      DateTime from = DateTime(now.year, now.month, now.day, 0, 0, 0);
      DateTime to = DateTime(now.year, now.month, now.day, 23, 59, 59);

      final queries = <String, dynamic>{};
      queries.putIfAbsent('from', () => from.toUtc().toIso8601String());
      queries.putIfAbsent('to', () => to.toUtc().toIso8601String());

      queries.putIfAbsent('order_by', () => 'due_date');
      queries.putIfAbsent('ordering', () => 'asc');
      queries.putIfAbsent('family_id', () => familyId);

      final result = await familyApi.getItemsAll(queries);

      List<Item> items = result.parseList(Item.fromJson);

      return items;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<Account?> inviteNoParameter() async {
    try {
      final result = await familyApi.inviteNoParameter();
      return result.parse(Account.fromJson);
    } catch (e) {
      if (e is DioException) {
        if (e.response?.statusCode == 400 && e.response?.data['message'] == 'cant_invite_myself') {
          throw CantInviteMyselfError();
        }
      }
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<String> getPhotoUrl(String userUuid, String photoUuid) async {
    final response = await familyApi.getS3Avatar(userUuid, photoUuid);

    logd("repsonse is : ${response.data}");

    return '';
  }

  @override
  Future<bool> removeStorageFromMemory(String attachId) async {
    try {
      await familyApi.removeStorageFromMemory(attachId);
      return true;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<void> changePassword(ChangePasswordParameter param) async {
    try {
      await familyApi.changePassword(param.toMap());
    } catch (e) {
      handleChangePasswordError(e);
      rethrow;
    }
  }

  @override
  Future<Account> updateMemberByFamilyId(String memberId, String familyId, {String? role, String? relationship, String? nickname}) async {
    try {
      var bodies = <String, dynamic>{};
      if ((role ?? '').isNotEmpty) {
        bodies.putIfAbsent('role', () => role);
      }
      if ((relationship ?? '').isNotEmpty) {
        bodies.putIfAbsent('relationship', () => relationship);
      }
      if ((nickname ?? '').isNotEmpty) {
        bodies.putIfAbsent('nickname', () => nickname);
      }
      final result = await familyApi.updateMemberByFamilyId(memberId, familyId, bodies);
      return result.parse(Account.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

}
