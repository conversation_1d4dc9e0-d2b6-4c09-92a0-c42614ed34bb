import 'dart:io';

import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/family.dart';
import 'package:family_app/data/model/family_profile.dart';
import 'package:family_app/data/model/interaction_model.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/presign_url.dart';
import 'package:family_app/data/model/privacy_policy.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/model/upcoming.dart';
import 'package:family_app/data/repository/family/model/change_password_parameter.dart';
import 'package:family_app/data/repository/family/model/invite_parameter.dart';
import 'package:family_app/data/repository/ibase_repository.dart';
import 'package:family_app/data/usecase/model/upsert_memory_activity_param.dart';
import 'package:family_app/data/usecase/model/upsert_memory_param.dart';

abstract class IFamilyRepository extends IBaseRepository {
  Future<Account> getUserInfo(String userId);
  Future<FamilyProfile> getProfileById(String familyId);
  Future<Account> updateProfileById(String familyId, {required String familyName, String? description, String? birthday, int? showYear, String? full_name});
  Future<Account> createProfile({required String familyName, String? description});
  Future<Family> getMyProfile();
  Future<Family> getUserProfile(String familyId);
  Future<List<Account>> getUserInFamily(String familyId);
  Future<Family> updateFamilyInfo({String? fullName, String? familyName});
  Future<Account?> invite(InviteParameter parameter);
  Future<Account?> inviteNoParameter();
  Future<List<Account>> getFamilyMember({StatusMember? status});
  Future<List<Family>> getFamilyBelongTo();
  Future<Account> updateMember(String memberId, {String? role, String? relationship});
  Future<bool> kickMember(String memberId);
  Future<bool> activeFamily(String familyId);
  Future<bool> deactiveFamily(String familyId);
  Future<Upcoming?> getUpcoming(String familyId, {DateTime? from, DateTime? to});
  Future<void> updateNotificationSetting(String fcmToken);
  Future<bool> updateAvatar(String familyId, {required File file});
  Future<bool> updateAvatarS3(String familyId, {required String fileUuid});
  Future<PrivacyPolicyData> privacy();

  Future<PresignUrl> getStoragePresignedUrls(String familyId);
  Future<StorageModel> updateFileInStorage(String storageId, String fileName);
  Future<List<StorageModel>> getStorageByFamily(String familyId);
  Future<String?> getStorageById(String familyId);
  Future<List<MemoryModel>> getMemoryListByFamily(String familyId, {String? type});
  Future<MemoryModel> attachStorageToMemory(UpsertMemoryParam param);
  Future<MemoryModel> updateMemory(String attachId, UpsertMemoryParam param);
  Future<bool> deleteMemory(String attachId);
  Future<InteractionModel> updateMemoryActivity(String memoryId, UpsertMemoryActivityParam param);
  Future<bool> deleteMemoryActivity(String interactionId);
  Future<List<Item>> getTaskDueToday(String familyId);

  Future<String> getPhotoUrl(String userUuid, String photoUuid);

  Future<MemoryModel> attachStorageToActivity(UpsertMemoryParam param);
  Future<bool> removeStorageFromMemory(String attachId);

  Future<bool> removeStorageFile(String uuid);

  Future<void> changePassword(ChangePasswordParameter param);

  Future<Account> updateMemberByFamilyId(String memberId, String familyId, {String? role, String? relationship, String? nickname});

}
