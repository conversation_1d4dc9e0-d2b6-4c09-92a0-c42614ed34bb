import 'package:json_annotation/json_annotation.dart';

part 'invite_parameter.g.dart';

@JsonSerializable()
class InviteParameter {
  @Json<PERSON>ey(name: 'full_name')
  final String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email')
  final String? email;
  @<PERSON><PERSON><PERSON>ey(name: 'relationship')
  final String? relations;
  @<PERSON>son<PERSON>ey(name: 'role')
  final String? role;
  @<PERSON>son<PERSON>ey(name: 'family_id')
  final String? familyId;

  InviteParameter({
     this.name,
     this.email,
     this.relations,
     this.role,
     this.familyId,
  });

  Map<String, dynamic> toJson() => _$InviteParameterToJson(this);
}
