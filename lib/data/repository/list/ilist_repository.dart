import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/list_log_item.dart';
import 'package:family_app/data/repository/ibase_repository.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:family_app/data/usecase/model/upsert_list_item_param.dart';

abstract class IListRepository extends IBaseRepository {
  Future<ListItem?> createList(UpsertListItemParam parameter);
  Future<ListItem?> updateList(String id, UpsertListItemParam parameter);
  Future<bool> deleteList(String id);
  Future<List<ListItem>> getListByFamilyId(String familyId);
  Future<ListItem?> getListDetail(String id);

  Future<Item?> createItemInList(UpsertItemParam param);
  Future<List<Item>> getAllItemInList(String listId);
  Future<Item?> getItemDetail(String id);
  Future<List<ListLog>> getAllItemLogInList(String listId);
  Future<bool> deleteItemInList(String id);
  Future<Item?> updateItemInList(String id, UpsertItemParam param);
  Future<Item?> changeStatusItemInList(String id, int status);

}
