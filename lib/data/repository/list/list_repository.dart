import 'dart:developer';

import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/list_log_item.dart';
import 'package:family_app/data/remote/list_api.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:family_app/data/usecase/model/upsert_list_item_param.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: IListRepository)
class ListRepository extends IListRepository {
  final ListAPI listAPI;

  ListRepository({required this.listAPI});

  @override
  Future<ListItem?> createList(UpsertListItemParam parameter) async {
    try {
      final result = await listAPI.createList(parameter.toJson());
      return result.parse(ListItem.fromJson);
    } catch (w) {
      handleError(w);
    }
    return null;
  }

  @override
  Future<bool> deleteList(String id) async {
    try {
      if (id.isEmpty) return false;
      await listAPI.deleteList(id);
      return true;
    } catch (w) {
      handleError(w);
    }
    return false;
  }

  @override
  Future<ListItem?> updateList(String id, UpsertListItemParam parameter) async {
    try {
      final result = await listAPI.updateList(id, parameter.toJson());
      return result.parse(ListItem.fromJson);
    } catch (w) {
      handleError(w);
    }
    return null;
  }

  @override
  Future<List<ListItem>> getListByFamilyId(String familyId) async {
    try {
      final queries = <String, dynamic>{};

      queries.putIfAbsent('order_by', () => 'updated_at');
      queries.putIfAbsent('ordering', () => 'desc');

      final result = await listAPI.getListByFamily(familyId, queries);
      log("getListByFamilyId result: ${result.data}");
      return result.parseList(ListItem.fromJson);
    } catch (e) {
      handleError(e);
      return [];
    }
  }

  @override
  Future<Item?> createItemInList(UpsertItemParam param) async {
    try {
      final result = await listAPI.createItemInList(param.toJson());
      return result.parse(Item.fromJson);
    } catch (w) {
      handleError(w);
    }
    return null;
  }

  @override
  Future<bool> deleteItemInList(String id) async {
    try {
      if (id.isEmpty) return false;
      await listAPI.deleteItemInList(id);
      return true;
    } catch (w) {
      handleError(w);
    }
    return false;
  }

  @override
  Future<List<Item>> getAllItemInList(String listId) async {
    try {
      final result = await listAPI.getAllItemInList({"list_id": listId});
      return result.parseList(Item.fromJson);
    } catch (e) {
      handleError(e);
      return [];
    }
  }

  @override
  Future<List<ListLog>> getAllItemLogInList(String listId) async {
    try {
      final result = await listAPI.getAllItemLogInList(listId);
      return result.parseList(ListLog.fromJson);
    } catch (e) {
      handleError(e);
      return [];
    }
  }

  @override
  Future<Item?> getItemDetail(String id) async {
    try {
      return listAPI.getItemDetail(id).then((value) => value.parse(Item.fromJson));
    } catch (e) {
      handleError(e);
      return null;
    }
  }

  @override
  Future<Item?> changeStatusItemInList(String id, int status) async {
    try {
      final result = await listAPI.updateItemInList(id, {'status': status});
      return result.parse(Item.fromJson);
    } catch (w) {
      handleError(w);
      return null;
    }
  }

  @override
  Future<Item?> updateItemInList(String id, UpsertItemParam param) async {
    try {
      print(" 11 update item: ${param}");
      final result = await listAPI.updateItemInList(id, param.toJson());
      return result.parse(Item.fromJson);
    } catch (w) {
      handleError(w);
      return null;
    }
  }

  @override
  Future<ListItem?> getListDetail(String id) async {
    try {
      final result = await listAPI.getListDetail(id);

      print("getListDetail result: ${result}");

      return result.parse(ListItem.fromJson);
    } catch (e) {
      handleError(e);
      return null;
    }
  }

}
