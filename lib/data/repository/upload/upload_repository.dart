import 'dart:io';

import 'package:dio/dio.dart';
import 'package:family_app/config/service/provider/s3_dio_provider.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:injectable/injectable.dart';

@Injectable(as: IUploadRepository)
class UploadRepository extends IUploadRepository {
  final S3DioProvider s3DioProvider;
  UploadRepository({required this.s3DioProvider});

  @override
  Future<bool> delete(String path) {
    // TODO: implement delete
    throw UnimplementedError();
  }

  @override
  Future<String> uploadImage(String path, File file, {String? fileName}) {
    // TODO: implement uploadImage
    throw UnimplementedError();
  }

  @override
  Future<String> uploadVideo(String path, File file, {String? fileName}) {
    // TODO: implement uploadVideo
    throw UnimplementedError();
  }

  @override
  Future<bool> uploadFileToS3(String presignedUrl, File file) async {
    final contentLength = await file.length();
    final dio = s3DioProvider.dio;
    try {
      final response = await dio.put(
        presignedUrl, // Full URL
        data: file.openRead(), // File content
        options: Options(
          headers: {
            Headers.contentLengthHeader: contentLength,
          },
        ),
      );
      AppLogger.d('Upload status code: ${response.statusCode}');
      return response.statusCode == 200;
    } catch (e) {
      AppLogger.e('Failed to upload: $e');
      handleError(e);
    }
    return false;
  }
}
