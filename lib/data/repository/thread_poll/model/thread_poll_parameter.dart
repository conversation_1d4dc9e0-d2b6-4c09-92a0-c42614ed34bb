import 'package:json_annotation/json_annotation.dart';

part 'thread_poll_parameter.g.dart';

@JsonSerializable()
class ThreadPollParameter {
  // Poll detail
  final String? name;
  @Json<PERSON>ey(name: 'thread_id')
  final String? threadId;
  final List<String>? items;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'from_date')
  final String? fromDate;
  @Json<PERSON>ey(name: 'to_date')
  final String? toDate;
  // Vote a poll
  @JsonKey(name: 'poll_items')
  final List<String>? pollItems;
  @JsonKey(name: 'multiple')
  final bool? multiple;

  ThreadPollParameter({this.pollItems, this.name, this.threadId, this.items, this.fromDate, this.toDate, this.multiple});

  factory ThreadPollParameter.fromJson(Map<String, dynamic> json) => _$ThreadPollParameterFromJson(json);

  Map<String, dynamic> toJson() => _$ThreadPollParameterToJson(this);
}
