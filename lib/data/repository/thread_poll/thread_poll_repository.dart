import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:injectable/injectable.dart';

import '../../remote/thread_poll_api.dart';
import 'ithread_poll_repository.dart';
import 'model/thread_poll_parameter.dart';

@LazySingleton(as: IThreadPollRepository)
class ThreadPollRepository extends IThreadPollRepository {
  final ThreadPollAPI threadPollAPI;

  ThreadPollRepository({required this.threadPollAPI});

  @override
  Future<ThreadMessageRelatedData> threadPollCreate(ThreadPollParameter parameter) async {
    try {
      final result = await threadPollAPI.threadPollCreate(parameter.toJson());
      AppLogger.d('result create poll: ${result.toJson()}');
      return result.parse(ThreadMessageRelatedData.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<ThreadMessageRelatedData> threadPollVote(String pollId, ThreadPollParameter parameter) async {
    try {
      final result = await threadPollAPI.threadPollVote(pollId, parameter.toJson());
      return result.parse(ThreadMessageRelatedData.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }
}
