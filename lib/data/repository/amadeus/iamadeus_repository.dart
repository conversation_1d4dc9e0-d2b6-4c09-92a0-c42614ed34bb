import 'package:family_app/data/model/airline_model.dart';
import 'package:family_app/data/model/amadeus/authorization.dart';
import 'package:family_app/data/model/amadeus/city.dart';
import 'package:family_app/data/model/amadeus/flight_offer_model.dart';
import 'package:family_app/data/model/amadeus/flight_order_model.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/data/model/amadeus/hotel_rating.dart';
import 'package:family_app/data/model/flight_offer_model.dart';
import 'package:family_app/data/usecase/model/booking_flight_param.dart';
import 'package:family_app/data/usecase/model/booking_hotel_param.dart';

import '../ibase_repository.dart';

const List<String> AmadeusSupportAmenities = [
  "SWIMMING_POOL",
  "SPA",
  "FITNESS_CENTER",
  "AIR_CONDITIONING",
  "RESTAURANT",
  "PARKING",
  "PETS_ALLOWED",
  "AIRPORT_SHUTTLE",
  "BUSINESS_CENTER",
  "DISABLED_FACILITIES",
  "WIFI",
  "MEETING_ROOMS",
  "NO_KID_ALLOWED",
  "TENNIS",
  "GOLF",
  "KITCHEN",
  "ANIMAL_WATCHING",
  "BABY-SITTING",
  "BEACH",
  "CASINO",
  "JACUZZI",
  "SAUNA",
  "SOLARIUM",
  "MASSAGE",
  "VALET_PARKING",
  "BAR or LOUNGE",
  "KIDS_WELCOME",
  "NO_PORN_FILMS",
  "MINIBAR",
  "TELEVISION",
  "WI-FI_IN_ROOM",
  "ROOM_SERVICE",
  "GUARDED_PARKG",
  "SERV_SPEC_MENU"
];

abstract class IAmadeusRepository extends IBaseRepository {

  Future<AmadeusAuth> oauth2();

  ///CITY
  Future<List<AmadeusCity>> getCityByName(String keyword, String countryCode);

  Future<List<AmadeusHotelModel>> getHotelByCity(String cityCode,
      {List<int>? ratings, List<String>? amenities});

  Future<List<AmadeusHotelModel>> getHotelByIds(String hotelIds);

  Future<List<AMAHotelRatingModel>> getHotelRating(String hotelIds);

  Future<AmadeusHotelOfferResponse?> getHotelOffers(
    String hotelIds,
    int adults, {
    String? checkInDate,
    String? checkOutDate,
    int? roomQuantity,
    String? rateCode,
    String? currencyCode,
  });

  Future<dynamic> bookHotel(BookingHotelParam param);

  ///FLIGHT
  Future<AmaFlightOfferResponse> getFlightOffers(
      String originLocationCode,
      String destinationLocationCode,
      String departureDate,
      int adults, {
        String? returnDate,
        int max = 10,
        bool nonStop = false,
      });

  Future<List<AirlineModel>> getAirlines(String airlineCodes);


  Future<AmaFlightOfferData> priceFlightOffers(FlightOfferModel offer);

  Future<AmaFlightOrderModel> bookFlight(BookingFlightParam param);

}
