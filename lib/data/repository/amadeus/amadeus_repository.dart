import 'package:dio/dio.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/airline_model.dart';
import 'package:family_app/data/model/amadeus/authorization.dart';
import 'package:family_app/data/model/amadeus/city.dart';
import 'package:family_app/data/model/amadeus/flight_offer_model.dart';
import 'package:family_app/data/model/amadeus/flight_order_model.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/data/model/amadeus/hotel_rating.dart';
import 'package:family_app/data/model/flight_offer_model.dart';
import 'package:family_app/data/remote/amadeus_api.dart';
import 'package:family_app/data/usecase/model/booking_flight_param.dart';
import 'package:family_app/data/usecase/model/booking_hotel_param.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:injectable/injectable.dart';

import 'iamadeus_repository.dart';

@LazySingleton(as: IAmadeusRepository)
class AmadeusRepository extends IAmadeusRepository {
  final String _tag = 'AmadeusRepository';
  final AmadeusAPI amadeusAPI;
  final LocalStorage localStorage;

  AmadeusRepository({required this.amadeusAPI, required this.localStorage});

  @override
  Future<AmadeusAuth> oauth2() async {
    try {
      logd(
          'AmadeusRepository.oauth2 called: ${AppConfig.AMADEUS_API_KEY}, url: ${AppConfig.AMADEUS_API_URL}, secret: ${AppConfig.AMADEUS_API_SECRET}',
          tag: _tag);

      final body = <String, dynamic>{};
      body.putIfAbsent('grant_type', () => 'client_credentials');
      body.putIfAbsent('client_id', () => AppConfig.AMADEUS_API_KEY);
      body.putIfAbsent('client_secret', () => AppConfig.AMADEUS_API_SECRET);
      final result = await amadeusAPI.oauth2(body);
      if (result.accessToken == null) {
        throw Exception('Amadeus access token is null');
      }
      await Future.wait([
        localStorage.cacheAmadeusAccessToken(result.accessToken!),
        localStorage.cacheAmadeusTokenExpired(DateTime.now()
            .add(Duration(seconds: result.expiresIn ?? 0))
            .toIso8601String())
      ]);

      return result;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  bool _isTokenExpired(String? tokenExpired) {
    if (tokenExpired == null) return true;
    final expiredDate = DateTime.parse(tokenExpired);
    return expiredDate.isBefore(DateTime.now());
  }

  _checkAndRefreshToken() async {
    final token = localStorage.amadeusAccessToken;
    final tokenExpired = localStorage.amadeusTokenExpired;

    logd("_checkAndRefreshToken token: $token, tokenExpired: $tokenExpired",
        tag: _tag);

    if (token == null || token.isEmpty || _isTokenExpired(tokenExpired)) {
      await oauth2();
    }
  }

  @override
  Future<List<AmadeusCity>> getCityByName(
      String keyword, String countryCode) async {
    try {
      await _checkAndRefreshToken();
      logd('getCityByName keyword: $keyword, countryCode: $countryCode',
          tag: _tag);
      final result = await amadeusAPI.getCityByName(keyword, countryCode, 10);
      return result.parseList(AmadeusCity.fromJson);
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<AmadeusHotelModel>> getHotelByCity(
    String cityCode, {
    List<int>? ratings,
    List<String>? amenities,
  }) async {
    try {
      await _checkAndRefreshToken();
//      logd(
//          'getHotelByCity cityCode: $cityCode, ratings: $ratings, amenities: $amenities, ignoring Ratings for now',
//          tag: _tag);
//
//      final result = await amadeusAPI.getHotelByCity(cityCode,
//          ratings: ratings != null && ratings.isNotEmpty
//              ? "[${ratings.join(",")}]"
//              : null,
//          amenities: amenities != null && amenities.isNotEmpty
//              ? "[${amenities.join(",")}]"
//              : null);

      final result = await amadeusAPI.getHotelByCity(cityCode,
          ratings: null, //ignore rating for now
          amenities: null //ignore amenities for now
          );

      if (result.data == null || result.data.isEmpty || result.data is! List) {
        return [];
      }
      List<AmadeusHotelModel> listHotel = [];
      for (var e in result.data) {
        if (e is Map<String, dynamic>) {
          listHotel.add(AmadeusHotelModel.fromJson(e));
        }
      }
      return listHotel;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<AmadeusHotelModel>> getHotelByIds(String hotelIds) async {
    try {
      await _checkAndRefreshToken();
      logd('getHotelByIds hotelIds: $hotelIds', tag: _tag);
      final result = await amadeusAPI.getHotelByIds(hotelIds);
      if (result.data == null || result.data.isEmpty || result.data is! List) {
        return [];
      }
      List<AmadeusHotelModel> listHotel = [];
      for (var e in result.data) {
        if (e is Map<String, dynamic>) {
          listHotel.add(AmadeusHotelModel.fromJson(e));
        }
      }
      return listHotel;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<AMAHotelRatingModel>> getHotelRating(String hotelIds) async {
    try {
      await _checkAndRefreshToken();
      logd('getHotelRating hotelId: $hotelIds', tag: _tag);
      final result = await amadeusAPI.getHotelRatings(hotelIds);
      if (result.data.isEmpty) {
        return [];
      }
      return result.data;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<AmadeusHotelOfferResponse?> getHotelOffers(
    String hotelIds,
    int adults, {
    String? checkInDate,
    String? checkOutDate,
    int? roomQuantity,
    String? rateCode,
    String? currencyCode,
  }) async {
    try {
      await _checkAndRefreshToken();
      logd('getHotelOffers hotelIds: $hotelIds, adults: $adults', tag: _tag);
      final result = await amadeusAPI.getHotelOffers(hotelIds, adults,
          checkInDate: checkInDate,
          checkOutDate: checkOutDate,
          roomQuantity: roomQuantity,
          rateCode: rateCode,
          currencyCode: currencyCode);
      // if (result.data == null || result.data.isEmpty || result.data is! List) {
      //   return [];
      // }
      // List<AmadeusHotelOfferData> listHotel = [];
      // for(var e in result.data) {
      //   if (e is Map<String, dynamic>) {
      //     listHotel.add(AmadeusHotelOfferData.fromJson(e));
      //   }
      // }
      return result;
    } catch (e) {
      // handleError(e);
      logd("getHotelOffers error: $e", tag: _tag);
      if (e is DioException && e.response != null && e.response!.data != null) {
        var data = e.response!.data;
        return AmadeusHotelOfferResponse(errors: data['errors'] ?? []);
      }
      return null;
      // rethrow;
    }
  }

  @override
  Future<dynamic> bookHotel(BookingHotelParam param) async {
    try {
      await _checkAndRefreshToken();
      final result = await amadeusAPI.bookHotel(param.toAmadeusJson());
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<AmaFlightOfferResponse> getFlightOffers(
    String originLocationCode,
    String destinationLocationCode,
    String departureDate,
    int adults, {
    String? returnDate,
    int max = 10,
    bool nonStop = false,
  }) async {
    try {
      await _checkAndRefreshToken();
      logd('getFlightOffers origin: $originLocationCode, destination: $destinationLocationCode, departure: $departureDate, adults: $adults', tag: _tag);
      final result = await amadeusAPI.getFlightOffers(
        originLocationCode,
        destinationLocationCode,
        departureDate,
        adults,
        returnDate: returnDate,
        max: max,
        nonStop: nonStop,
      );
      return result;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }

  @override
  Future<List<AirlineModel>> getAirlines(String airlineCodes) async {
    try {
      await _checkAndRefreshToken();
      logd('getAirlines airlineCodes: $airlineCodes', tag: _tag);
      final result = await amadeusAPI.getAirlines(airlineCodes);
      if (result.data == null || result.data.isEmpty || result.data is! List) {
        return [];
      }
      List<AirlineModel> listAirlines = [];
      for (var e in result.data) {
        if (e is Map<String, dynamic>) {
          listAirlines.add(AirlineModel.fromJson(e));
        }
      }
      return listAirlines;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }


  @override
  Future<AmaFlightOfferData> priceFlightOffers(FlightOfferModel offer) async {
    try {
      await _checkAndRefreshToken();
      var params = {
        "data": {
          "type": "flight-offers-pricing",
          "flightOffers": [offer.toJson()],
        }
      };
      final result = await amadeusAPI.priceFlightOffers(params);
      return AmaFlightOfferData.fromJson(result.data);
    } catch (e) {
      if(e is DioException && e.response != null && e.response!.data != null) {
        var data = e.response!.data;
        if (data['errors'] != null) {
          throw Exception(data['errors']);
        }
      }
      rethrow;
    }
  }

  @override
  Future<AmaFlightOrderModel> bookFlight(BookingFlightParam param) async {
    try {
      await _checkAndRefreshToken();
      final result = await amadeusAPI.bookFlight({
        "data": param.toAmadeusJson(),
      });

      return AmaFlightOrderModel.fromJson(result.data);;
    } catch (e) {
    if (e is DioException && e.response != null && e.response!.data != null) {
        var data = e.response!.data;
        if (data['errors'] != null) {
          throw Exception(data['errors']);
        }
      }
      rethrow;
    }
  }
}
