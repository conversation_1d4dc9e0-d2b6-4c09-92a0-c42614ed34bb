import 'dart:io' show Platform;

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/data/remote/premium_api.dart';
import 'package:injectable/injectable.dart';

import 'ipremium_repository.dart';

@LazySingleton(as: IPremiumRepository)
class PremiumRepository extends IPremiumRepository {
  final PremiumAPI _api;

  PremiumRepository(this._api);

  @override
  Future<int> getPremiumExpiresAt() async {
    try {
      final result = await _api.getIsPremium();

      return result.parse(
        (v) {
          final expiresAt = DateTime.tryParse('${v['expired_at']}')?.millisecondsSinceEpoch;

          if (expiresAt == null) throw 'an_error_occurred_text'.tr();

          return expiresAt;
        },
      );
    } catch (e) {
      handleError(e);

      rethrow;
    }
  }

  @override
  Future<int> validatePurchase(String id) async {
    try {
      final result = await _api.validatePurchase(id, {
        'platform': Platform.isIOS
            ? 'ios'
            : Platform.isAndroid
                ? 'android'
                : ''
      });

      final r = result.parse((v) => DateTime.tryParse('${v['expiresDate']}')?.millisecondsSinceEpoch);

      if (r == null) throw 'an_error_occurred_text'.tr();

      return r;
    } catch (e) {
      handleError(e);
      rethrow;
    }
  }
}
