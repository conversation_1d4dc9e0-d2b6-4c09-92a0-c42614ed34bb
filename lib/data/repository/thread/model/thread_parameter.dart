import 'package:json_annotation/json_annotation.dart';

part 'thread_parameter.g.dart';

@JsonSerializable()
class ThreadParameter {
  @Json<PERSON>ey(name: 'family_id')
  String? familyId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  String? name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'members')
  List<String> members;

  String? color;
  String? image;
  List<dynamic>? setting;

  ThreadParameter({
    this.familyId,
    this.name,
    this.members = const [],
    this.color,
    this.image,
    this.setting,
  });

  factory ThreadParameter.fromJson(Map<String, dynamic> json) => _$ThreadParameterFromJson(json);
  Map<String, dynamic> toJson() => _$ThreadParameterToJson(this);


}