import 'package:json_annotation/json_annotation.dart';

part 'message_parameter.g.dart';

@JsonSerializable()
class MessageParameter {
  @Json<PERSON>ey(name: 'message')
  String? message;

  @Json<PERSON>ey(name: 'message_type')
  String? messageType;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_type_id')
  String? messageTypeId;
  
  @<PERSON><PERSON><PERSON>ey(name: 'data')
  String? data;


  MessageParameter({
    this.message,
    this.messageType,
    this.messageTypeId,
    this.data
  });

  factory MessageParameter.fromJson(Map<String, dynamic> json) => _$MessageParameterFromJson(json);

  Map<String, dynamic> toJson() => _$MessageParameterToJson(this);


}