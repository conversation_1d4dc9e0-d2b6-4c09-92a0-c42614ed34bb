class InvalidUserNameOrPassword implements Exception {}

class EmailCantCheck implements Exception {}

class VerifyCodeError implements Exception {}

class EmailNotAvailableError implements Exception {}

class EmailNotActiveError implements Exception {
  final String uuid;

  const EmailNotActiveError(this.uuid);
}

class InvalidToken implements Exception {}

class WrongPassword implements Exception {}
