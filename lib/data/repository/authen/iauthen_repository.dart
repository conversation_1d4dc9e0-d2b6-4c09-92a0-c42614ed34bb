import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/repository/authen/model/sign_up_parameter.dart';
import 'package:family_app/data/repository/ibase_repository.dart';
import 'package:family_app/data/usecase/sign_in_usecase.dart';

abstract class IAuthenRepository extends IBaseRepository {
  Future<Account> login(SignInParameter parameter);
  Future<Account> loginWithGoogle();
  Future<void> signOutWithGoogle();
  Future<Account> loginWithApple();
  Future<Account> loginWithFacebook();
  Future<void> logout();
  Future<void> forgotPass(String email);
  Future<Account?> signUp(SignUpParameter parameter);
  Future<bool> verifyEmail(String uuid);
  Future<void> verifyOTP(String email, String otp);
}
