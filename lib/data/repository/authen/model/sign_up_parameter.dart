// ignore_for_file: public_member_api_docs, sort_constructors_first

class SignUpParameter {
  final String email;
  final String password;
  final String fullname;

  SignUpParameter({
    required this.email,
    required this.password,
    required this.fullname,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'email': email,
      'password': password,
      'full_name': fullname,
    };
  }
}
