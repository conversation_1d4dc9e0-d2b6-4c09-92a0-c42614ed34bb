import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:injectable/injectable.dart';

import 'base_usecase.dart';

class AppleSignInParameter {
  final bool isLoggedIn;

  AppleSignInParameter({
    this.isLoggedIn = false,
  });
}

class AppleOauthParameter {
  final String idToken;
  final String? givenName;
  final String? familyName;
  final String? email;
  final String authorizationCode;
  final String? state;


  AppleOauthParameter({
    required this.idToken,
    this.givenName,
    this.familyName,
    this.email,
    required this.authorizationCode,
    this.state,
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'idToken': idToken,
      'givenName': givenName,
      'familyName': familyName,
      'email': email,
      'authorizationCode': authorizationCode,
      'state': state,
    };
  }
}

@lazySingleton
class AppleSignInUsecase extends BaseUseCaseNoResult<AppleSignInParameter> {
  final IAuthenRepository authenRepository;
  final LocalStorage localStorage;
  final AccountService accountService;

  AppleSignInUsecase({
    required this.authenRepository,
    required this.localStorage,
    required this.accountService,
  });

  @override
  Future<void> call(AppleSignInParameter param) async {
    try {
      final account = await authenRepository.loginWithApple();
      await Future.wait([
        localStorage.cacheUserID(account.uuid ?? ''),
        localStorage.cacheAccessToken(account.token ?? ''),
        localStorage.cacheEmail(account.email ?? ''),
        localStorage.cacheLoggedIn(param.isLoggedIn),
      ]);
      await accountService.initMyProfile();
    } catch (e) {
      rethrow;
    }
  }
}
