import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:injectable/injectable.dart';

import 'base_usecase.dart';

class GoogleSignInParameter {
  final bool isLoggedIn;

  GoogleSignInParameter({
    this.isLoggedIn = false,
  });
}

class GoogleOauthParameter {
  final String idToken;

  GoogleOauthParameter({
    required this.idToken,
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'idToken': idToken,
    };
  }
}

@lazySingleton
class GoogleSignInUsecase extends BaseUseCaseNoResult<GoogleSignInParameter> {
  final IAuthenRepository authenRepository;
  final LocalStorage localStorage;
  final AccountService accountService;

  GoogleSignInUsecase({
    required this.authenRepository,
    required this.localStorage,
    required this.accountService,
  });

  @override
  Future<void> call(GoogleSignInParameter param) async {
    try {
      final account = await authenRepository.loginWithGoogle();
      await Future.wait([
        localStorage.cacheUserID(account.uuid ?? ''),
        localStorage.cacheAccessToken(account.token ?? ''),
        localStorage.cacheEmail(account.email ?? ''),
        localStorage.cacheLoggedIn(param.isLoggedIn),
      ]);
      await accountService.initMyProfile();
    } catch (e) {
      rethrow;
    }
  }
}
