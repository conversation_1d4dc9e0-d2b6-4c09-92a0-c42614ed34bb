import 'package:dartx/dartx.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/category.dart';
import 'package:family_app/data/repository/category/icategory_repository.dart';

mixin CategoryMixin {
  final ICategoryRepository categoryRepository = locator.get();

  Future<Category> getCategory(ListType type) async {
    final listCategories = await categoryRepository.getAllCategory();
    print('listCategories: $listCategories');
    final category = listCategories.firstOrNullWhere((element) => element.name == type.name);
    if (category == null) {
      final newCategory = await categoryRepository.createCategory(type.name);
      return newCategory;
    }
    return category;
  }
}
