import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:injectable/injectable.dart';

class UpsertItemUSParam {
  final List<UpsertItemParam>? oldItems;
  final List<UpsertItemParam> newItems;

  UpsertItemUSParam({this.oldItems, this.newItems = const []});
}

@lazySingleton
class UpsertItemUsecase extends BaseUseCase<List<Item>, UpsertItemUSParam> {
  final IListRepository listRepository;

  UpsertItemUsecase({required this.listRepository});

  @override
  Future<List<Item>> call(UpsertItemUSParam param) async {
    try {
      final removeId = <String?>[];

      // Check if there are old items to process
      if ((param.oldItems ?? []).isNotEmpty) {
        // Extract item IDs from old and new items
        final oldItemsId = param.oldItems?.map((e) => e.itemId).toList() ?? <String>[];
        final newItemsId = param.newItems.map((e) => e.itemId).toList();

        // Identify items to be removed
        removeId.addAll(oldItemsId.where((element) => !newItemsId.contains(element)).toList());

        // Remove items that are no longer needed
        if (removeId.isNotEmpty) {
          await Future.wait(removeId.map((id) => listRepository.deleteItemInList(id ?? '')));
        }
      }

      // Process new items: update existing ones or create new ones
      final items = await Future.wait(param.newItems.map((e) async {
        late Item? item;

        if ((e.itemId ?? '').isNotEmpty) {
          item = await listRepository.updateItemInList(e.itemId!, e);
        } else {
          item = await listRepository.createItemInList(e);
        }
        return item;
      }));

      // Return the list of processed items
      return items.map((e) => e!).toList();
    } catch (e) {
      // Handle any errors by returning an empty list
      return <Item>[];
    }
  }
}
