import 'package:collection/collection.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/amadeus/hotel_rating.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/amadeus/iamadeus_repository.dart';
import 'package:family_app/data/usecase/model/booking_hotel_param.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

enum HotelBookingProvider { amadeus, agoda }

@lazySingleton
class HotelBookingUseCase {
  // This class will contain methods related to hotel booking operations
  // such as searching for hotels, booking a room, and managing bookings.
  HotelBookingProvider provider = HotelBookingProvider.amadeus;

  Future<List<HotelModel>> searchHotels(
    String location, {
    List<int>? ratings,
    List<String>? amenities,
  }) async {
    if (provider == HotelBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      List<String> supportAmenities = [];
      for (String amenity in amenities ?? []) {
        var amaValue = AmadeusSupportAmenities.firstWhereOrNull(
          (element) => element.toLowerCase().contains(amenity),
        );
        if (amaValue != null) {
          supportAmenities.add(amaValue);
        }
      }

      List<HotelModel> hotels = await repository.getHotelByCity(
        location,
        ratings: ratings,
        amenities: supportAmenities.isNotEmpty ? supportAmenities : null,
      );

      //remote name contains test
      if (!kDebugMode) {
        hotels = hotels
            .where((h) => !h.name!.toLowerCase().contains('test'))
            .toList();
      }

      return hotels;
    } else if (provider == HotelBookingProvider.agoda) {
      // Call Agoda API to search hotels
      // return await agodaRepository.searchHotels(location);
    }
    return []; // Return a list of HotelModel objects
  }

  Future<HotelModel?> getHotelById(String hotelId) async {
    if (provider == HotelBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      var hotels = await repository.getHotelByIds("[$hotelId]");
      return hotels.first;
    } else if (provider == HotelBookingProvider.agoda) {
      // Call Agoda API to get hotel by ID
      // return await agodaRepository.getHotelById(hotelId);
    }
    return null; // Return a list of HotelModel objects
  }

  Future<List<HotelOfferModel>> getHotelOffers(
    String hotelId,
    int adults, {
    String? checkInDate,
    String? checkOutDate,
    int roomQuantity = 1,
  }) async {
    if (provider == HotelBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      var offerResp = await repository.getHotelOffers(
        "[$hotelId]",
        adults,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        roomQuantity: roomQuantity,
      );
      if (offerResp?.data.isNotEmpty == true) {
        return offerResp!.data.first.offers;
      } else if (offerResp?.errors.isNotEmpty == true) {
        var error = offerResp!.errors[0]['title'];
        throw Exception(error);
      }

      return [];
    } else if (provider == HotelBookingProvider.agoda) {
      // Call Agoda API to get hotel offers
      // return await agodaRepository.getHotelOffers(hotelId, checkIn, checkOut);
    }
    return []; // Return a list of HotelOfferModel objects
  }

  Future<Map<String, dynamic>> bookHotel(BookingHotelParam param) async {
    if (provider == HotelBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      try {
        var resp = await repository.bookHotel(param);
        return {
          'success': true,
          'data': resp,
        };
      } catch (e) {
        return {
          'success': false,
          'error': e.toString(),
        };
      }
    } else if (provider == HotelBookingProvider.agoda) {
      // Call Agoda API to book hotel
      // return await agodaRepository.bookHotel(param);
    }
    return {
      'success': false,
      'error': 'Booking provider not supported',
    };
  }

  Future<double> getHotelRating(String hotelId) async {
    if (provider == HotelBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      var resp = await repository.getHotelRating("[$hotelId]");
      var rating = resp.firstWhereOrNull((r) => r.hotelId == hotelId);
      return rating?.overallRating.toDouble() ?? 0.0;
    } else if (provider == HotelBookingProvider.agoda) {
      // Call Agoda API to get hotel rating
      // return await agodaRepository.getHotelRating(hotelId);
    }
    return 0; // Return a list of AMAHotelRatingModel objects
  }

  Future<void> cancelBooking(String bookingId) async {
    // Logic to cancel a hotel booking
  }

  /// Search hotels based on hotel preferences and dates
  /// This method now passes ratings and amenities directly to the API for filtering
  Future<List<HotelModel>> searchHotelsByPreferences(
    HotelPreferences hotelPreferences,
    DateTime checkInDate,
    DateTime checkOutDate,
  ) async {
    try {
      // Use the location from hotel preferences, fallback to a default if not provided
      String location = hotelPreferences.location ?? '';

      if (location.isEmpty) {
        // If no location is provided, return empty list
        AppLogger.w('No location provided in hotel preferences');
        return [];
      }

      // Prepare ratings parameter for API
      List<int>? ratings;
      if (hotelPreferences.starRating != null) {
        // If a specific star rating is provided, search for that rating and above
        ratings = [];
        for (int i = hotelPreferences.starRating!; i <= 5; i++) {
          ratings.add(i);
        }
        AppLogger.d(
            'Searching for hotels with ratings: $ratings (${hotelPreferences.starRating}+ stars)');
      }

      // Prepare amenities parameter for API
      List<String>? amenities = hotelPreferences.amenities;
      if (amenities != null && amenities.isNotEmpty) {
        AppLogger.d('Searching for hotels with amenities: $amenities');
      }

      // Search hotels by location with ratings and amenities filtering
      List<HotelModel> hotels = await searchHotels(
        location,
        ratings: ratings,
        amenities: amenities,
      );

      AppLogger.d(
          'Found ${hotels.length} hotels for location: $location with preferences applied');

      // Apply additional client-side filtering for preferences not supported by API
      List<HotelModel> filteredHotels =
          _filterHotelsByPreferences(hotels, hotelPreferences);

      AppLogger.d(
          'After additional filtering: ${filteredHotels.length} hotels');
      return filteredHotels;
    } catch (e) {
      // Log error and return empty list
      AppLogger.e('Error searching hotels by preferences: $e');
      return [];
    }
  }

  /// Client-side filtering for preferences not supported by the API
  /// This provides additional filtering on top of API-level filtering
  List<HotelModel> _filterHotelsByPreferences(
    List<HotelModel> hotels,
    HotelPreferences preferences,
  ) {
    List<HotelModel> filteredHotels = List.from(hotels);

    // Apply budget filtering if specified
    if (preferences.budget != null) {
      Budget budget = preferences.budget!;
      if (budget.min != null || budget.max != null) {
        AppLogger.d(
            'Applying budget filter: min=${budget.min}, max=${budget.max}');
        // Note: This is a placeholder since HotelModel doesn't have price info
        // In a real implementation, you might need to fetch hotel offers to get pricing
        // For now, we'll just log the intent and keep all hotels
        AppLogger.d(
            'Budget filtering not implemented - requires hotel offers data');
      }
    }

    // Apply room type filtering if specified
    if (preferences.roomType != null && preferences.roomType!.isNotEmpty) {
      AppLogger.d('Room type preference: ${preferences.roomType}');
      // Note: This is a placeholder since basic hotel search doesn't include room types
      // Room type filtering would typically be done at the hotel offers level
      AppLogger.d(
          'Room type filtering not implemented - requires hotel offers data');
    }

    // Apply guest count validation if specified
    if (preferences.numberOfGuests != null) {
      AppLogger.d('Number of guests preference: ${preferences.numberOfGuests}');
      // Note: This would typically be validated against room capacity in hotel offers
      AppLogger.d(
          'Guest count validation not implemented - requires hotel offers data');
    }

    AppLogger.d(
        'Client-side filtering completed: ${filteredHotels.length} hotels remain');
    return filteredHotels;
  }
}
