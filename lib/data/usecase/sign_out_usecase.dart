import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/socket/base_socket_service.dart';
import 'package:family_app/config/service/notification/notification_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class SignOutUsecase extends BaseNormalUseCase {
  final IAuthenRepository authenRepository;
  final LocalStorage localStorage;
  final BaseSocketService socketService;
  final AccountService accountService;
  final NotificationService notificationService;

  SignOutUsecase({
    required this.authenRepository,
    required this.localStorage,
    required this.socketService,
    required this.accountService,
    required this.notificationService,
  });

  /// Signs out the user, clears local data, and deletes the FCM token.
  /// If backend support for FCM token revocation is added, enable the API call below.
  @override
  Future<void> call() async {
    try {
      // Delete FCM token locally to prevent further push notifications.
      await notificationService.deleteFcmToken();
      // TODO: When backend supports FCM token revocation, enable this:
      // final fcmToken = await notificationService.getFcmToken();
      // if (fcmToken != null) {
      //   await authenRepository.revokeFcmToken(fcmToken);
      // }
      await authenRepository.signOutWithGoogle();
      await authenRepository.logout();
      accountService.logout();
      socketService.onDisconnect();
      localStorage.clear();
    } catch (e) {
      rethrow;
    }
  }
}
