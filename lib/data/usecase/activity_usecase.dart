import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:family_app/data/usecase/mixin/category_mixin.dart';
import 'package:family_app/data/usecase/mixin/remove_empty_string.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/data/usecase/upsert_activity_item_usecase.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class ActivityUsecase extends BaseUseCase<ActivityModel, CreateActivityParameter>
    with RemoveEmptyString, CategoryMixin {
  final IActivityRepository activityRepository;
  final UpsertActivityItemUsecase upsertActivityItemUsecase;

  ActivityUsecase({required this.activityRepository, required this.upsertActivityItemUsecase});

  @override
  Future<ActivityModel> call(CreateActivityParameter param) async {
    try {
      final category = await getCategory(param.type);
      print("activity category: $category");
      final newParam = param.copyWith(
        activityType: category.uuid,
        includedEvents: param.includedEvents,
        includedMembers: removeEmptyString(param.includedMembers ?? []),
        includedLists: removeEmptyString(param.includedLists ?? []),
      );

      late ActivityModel? result;
      if ((param.uuid ?? '').isNotEmpty) {
        result = await activityRepository.updateActivity(param.uuid!, newParam);
      } else {
        result = await activityRepository.createActivity(newParam);
      }

      if (locator.isRegistered<CalendarCubit>()) {
        final calendarCubit = locator.get<CalendarCubit>();
        calendarCubit.onFetchEvent();
      }
      if (locator.isRegistered<MainCubit>()) {
        final mainCubit = locator.get<MainCubit>();
        mainCubit.updateActivityInListItem(newParam.includedLists ?? [], result);
        if (newParam.oldListIds.isNotEmpty) {
          final deleteId = param.oldListIds.where((e) => !param.includedLists!.contains(e)).toList();
          mainCubit.onRemoveActivityIdInList(deleteId);
        }
      }
      return result;
    } catch (e) {
      rethrow;
    }
  }
}
