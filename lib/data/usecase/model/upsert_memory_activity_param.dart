import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

part 'upsert_memory_activity_param.g.dart';

@JsonSerializable()
class UpsertMemoryActivityParam {
	@Json<PERSON>ey(name: 'activity_type') 
	final String? activityType;
	@JsonKey(name: 'activity_content') 
	final String? activityContent;
	@JsonKey(name: 'extra_data') 
	final String? extraData;

	const UpsertMemoryActivityParam({
		this.activityType, 
		this.activityContent, 
		this.extraData, 
	});

	@override
	String toString() {
		return 'UpsertMemoryActivityParam(activityType: $activityType, activityContent: $activityContent, extraData: $extraData)';
	}

	factory UpsertMemoryActivityParam.fromJson(Map<String, dynamic> json) {
		return _$UpsertMemoryActivityParamFromJson(json);
	}

	Map<String, dynamic> toJson() => _$UpsertMemoryActivityParamToJson(this);

	UpsertMemoryActivityParam copyWith({
		String? activityType,
		String? activityContent,
		String? extraData,
	}) {
		return UpsertMemoryActivityParam(
			activityType: activityType ?? this.activityType,
			activityContent: activityContent ?? this.activityContent,
			extraData: extraData ?? this.extraData,
		);
	}

	@override
	bool operator ==(Object other) {
		if (identical(other, this)) return true;
		if (other is! UpsertMemoryActivityParam) return false;
		final mapEquals = const DeepCollectionEquality().equals;
		return mapEquals(other.toJson(), toJson());
	}

	@override
	int get hashCode =>
			activityType.hashCode ^
			activityContent.hashCode ^
			extraData.hashCode;
}
