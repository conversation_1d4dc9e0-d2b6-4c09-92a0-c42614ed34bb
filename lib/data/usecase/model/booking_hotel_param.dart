import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/data/model/booking_personal_info.dart';
import 'package:family_app/data/model/hotel_model.dart';

import '../../model/card_info.dart';

class BookingHotelParam {
  BookingPersonalInfo personalInfo;
  CardInfo cardInfo;
  HotelOfferModel offer;

  BookingHotelParam({
    required this.personalInfo,
    required this.cardInfo,
    required this.offer,
  });

  Map<String, dynamic> toAmadeusJson() {
    Map<String, dynamic> json = {
      "type": "hotel-order",
      "travelAgent": {
        "contact": {
          "email": personalInfo.email,
        }
      },
      "payment": cardInfo.toAmadeusJson(),
    };
    var guests = <BookingPersonalInfo>[];
    var roomAssociations = <Map<String, dynamic>>[];
    for (var i = 1; i <= offer.quantity; i++) {
      var tmpPersonal = personalInfo.copyWith(id: guests.length + 1);
      guests.add(tmpPersonal);
      roomAssociations.add({
        "guestReferences": [
          {"guestReference": "${tmpPersonal.id}"}
        ],
        "hotelOfferId": offer.id,
      });
    }
    json["roomAssociations"] = roomAssociations;
    json["guests"] = guests.map((e) => e.toAmadeusJson()).toList();
    return {"data": json};
  }
}
