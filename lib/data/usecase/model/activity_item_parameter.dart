import 'package:json_annotation/json_annotation.dart';

part 'activity_item_parameter.g.dart';

@JsonSerializable()
class ActivityItemParameter {
  @JsonKey(name: 'list_id')
  final String? listId;

  ActivityItemParameter({this.listId});

  factory ActivityItemParameter.fromJson(Map<String, dynamic> json) =>
      _$ActivityItemParameterFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityItemParameterToJson(this);

  ActivityItemParameter copyWith({
    String? listId,
  }) {
    return ActivityItemParameter(
      listId: listId ?? this.listId,
    );
  }
}
