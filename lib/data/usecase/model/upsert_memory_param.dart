import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

part 'upsert_memory_param.g.dart';

@JsonSerializable()
class UpsertMemoryParam {
  @Json<PERSON>ey(name: 'uuid')
  final String? uuid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'caption')
  final String? caption;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'activity_id')
  final String? activityId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'attachment_type')
  final String? attachmentType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_id')
  final List<String>? fileId;
  @J<PERSON><PERSON><PERSON>(name: 'family_id')
  final String? familyId;

  const UpsertMemoryParam({
    this.uuid,
    this.caption,
    this.activityId,
    this.attachmentType,
    this.fileId,
    this.familyId,
  });

  @override
  String toString() {
    return 'UpsertMemoryParam(uuid: $uuid, caption: $caption, activityId: $activityId, attachmentType: $attachmentType, fileId: $fileId, familyId: $familyId)';
  }

  factory UpsertMemoryParam.fromJson(Map<String, dynamic> json) {
    return _$UpsertMemoryParamFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UpsertMemoryParamToJson(this);

  UpsertMemoryParam copyWith({
    String? uuid,
    String? caption,
    String? activityId,
    String? attachmentType,
    List<String>? fileId,
    String? familyId,
  }) {
    return UpsertMemoryParam(
      uuid: uuid ?? this.uuid,
      caption: caption ?? this.caption,
      activityId: activityId ?? this.activityId,
      attachmentType: attachmentType ?? this.attachmentType,
      fileId: fileId ?? this.fileId,
      familyId: familyId ?? this.familyId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! UpsertMemoryParam) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      uuid.hashCode ^
      caption.hashCode ^
      activityId.hashCode ^
      attachmentType.hashCode ^
      fileId.hashCode ^
      familyId.hashCode;
}
