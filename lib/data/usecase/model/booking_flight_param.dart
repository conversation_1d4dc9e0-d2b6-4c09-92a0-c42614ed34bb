import 'package:family_app/data/model/flight_offer_model.dart';

class BookingFlightParam {
  final List<FlightOfferModel> flightOffers;
  final List<BookingFlightPassenger> travelers;

  BookingFlightParam({
    required this.flightOffers,
    required this.travelers,
  });

  Map<String, dynamic> toAmadeusJson() {
    return {
      'type': 'flight-order',
      'flightOffers': flightOffers.map((offer) => offer.toJson()).toList(),
      'travelers': travelers.map((traveler) => traveler.toAmadeusJson()).toList(),
    };
  }

  BookingFlightParam copyWith({
    List<FlightOfferModel>? flightOffers,
    List<BookingFlightPassenger>? travelers,
  }) {
    return BookingFlightParam(
      flightOffers: flightOffers ?? this.flightOffers,
      travelers: travelers ?? this.travelers,
    );
  }

}

class BookingFlightPassenger {
  String id;
  String dateOfBirth;
  String firstName;
  String lastName;
  String gender;
  String emailAddress;

  // Optional fields for contact information
  String phoneCountryCallingCode;
  String phoneNumber;

  BookingFlightPassenger({required this.id,
    required this.dateOfBirth,
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.emailAddress,
    required this.phoneCountryCallingCode,
    required this.phoneNumber});



//         "id": "1",
//         "dateOfBirth": "1982-01-16",
//         "name": {
//           "firstName": "JORGE",
//           "lastName": "GONZALES"
//         },
//         "gender": "MALE",
//         "contact": {
//           "emailAddress": "<EMAIL>",
//           "phones": [
//             {
//               "deviceType": "MOBILE",
//               "countryCallingCode": "34",
//               "number": "*********"
//             }
//           ]
//         }

Map<String, dynamic> toAmadeusJson() {
  return {
    'id': id,
    'dateOfBirth': dateOfBirth,
    'name': {
      'firstName': firstName,
      'lastName': lastName,
    },
    'gender': gender,
    'contact': {
      'emailAddress': emailAddress, // Email can be added later
      'phones': [
        {
          'deviceType': 'MOBILE',
          'countryCallingCode': phoneCountryCallingCode, // Country code can be added later
          'number': phoneNumber , // Phone number can be added later
        }
      ],
    },
  };
}}
