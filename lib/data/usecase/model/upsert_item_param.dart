// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

part 'upsert_item_param.g.dart';

@JsonSerializable()
class UpsertItemParam {
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? itemId;
  @Json<PERSON><PERSON>(name: 'family_uuid')
  final String? familyUuid;
  @Json<PERSON>ey(name: 'list_uuid')
  final String? listUuid;
  final String? name;
  final String? description;

  @Json<PERSON>ey(name: 'time_zone')
  final String? timeZone;
  final int? status;

  @Json<PERSON>ey(name: 'due_date')
  final String due_date;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_time')
  final String? notificationTime;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'included_members')
  final List<String> includedMembers;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'assignment')
  final List<String> assignment;

  @Json<PERSON>ey(name: 'point', defaultValue: 0)
  final int point;

  String listCategory = '';

  UpsertItemParam(
      {this.itemId,
      this.familyUuid,
      this.listUuid,
      this.name,
      this.description,
      this.timeZone,
      this.notificationTime,
      this.status,
      this.point = 0,
      this.due_date = '',
      this.includedMembers = const [],
      this.assignment = const [],
      this.listCategory = ''});

  factory UpsertItemParam.fromJson(Map<String, dynamic> json) => _$UpsertItemParamFromJson(json);

  Map<String, dynamic> toJson() => _$UpsertItemParamToJson(this);

  Map<String, dynamic> toUpsertActivityItemParam() {
    return {
      'family_uuid': familyUuid,
      'list_uuid': listUuid,
      'name': name,
      'description': description,
      'time_zone': timeZone,
      'status': status,
      'due_date': due_date,
      'notification_time': notificationTime,
      'included_members': includedMembers,
      'assignment': assignment,
    };
  }

  UpsertItemParam copyWith(
      {String? itemId,
      String? familyUuid,
      String? listUuid,
      String? name,
      List<String>? includedMembers,
      List<String>? assignment,
      int? status,
      int? point,
      String? timeZone,
      String? description,
      String? due_date,
      String? notificationTime,
      String? listCategory}) {
    return UpsertItemParam(
      itemId: itemId ?? this.itemId,
      familyUuid: familyUuid ?? this.familyUuid,
      listUuid: listUuid ?? this.listUuid,
      name: name ?? this.name,
      point: point ?? this.point,
      status: status ?? this.status,
      timeZone: timeZone ?? this.timeZone,
      due_date: due_date ?? this.due_date,
      description: description ?? this.description,
      notificationTime: notificationTime ?? this.notificationTime,
      listCategory: listCategory ?? this.listCategory,
      includedMembers: includedMembers ?? this.includedMembers,
      assignment: assignment ?? this.assignment,
    );
  }

  @override
  String toString() {
    return "UpsertItemParam(familyUuid: $familyUuid, listUuid: $listUuid, name: $name, description: $description, timeZone: $timeZone, status: $status, due_date: $due_date, notificationTime: $notificationTime)";
  }
}
