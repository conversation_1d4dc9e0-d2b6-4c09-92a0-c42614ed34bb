import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/category.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:json_annotation/json_annotation.dart';

part 'upsert_list_item_param.g.dart';

@JsonSerializable()
class UpsertListItemParam {
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? uuid;

  final String name;
  @Json<PERSON>ey(name: 'plan_date')
  final String? planDate;
  final String color;
  final String description;
  @Json<PERSON>ey(name: 'included_members')
  final List<String> includedMembers;
  @J<PERSON><PERSON>ey(name: 'assignment')
  final List<String> assignment;
  int? point;


  @Json<PERSON>ey(name: 'category_id') //list_uuid
  final String categoryId;
  @Json<PERSON><PERSON>(name: 'family_uuid')
  final String familyId;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ListType type;
  @Json<PERSON>ey(includeFromJson: false, includeToJson: false)
  final List<UpsertItemParam> items;

  @JsonKey(includeFromJson: false, includeToJson: false)
  final List<UpsertItemParam>? oldItems;

  @JsonKey(name: 'activity_id')
  final String? activityId;

  UpsertListItemParam(
      {required this.name, this.planDate,
      required this.color,
      required this.description,
      required this.includedMembers,
      required this.assignment,
      required this.familyId,
      this.point,
      this.activityId,
      this.categoryId = '',
      this.uuid,
      this.oldItems,
      this.items = const [],
      this.type = ListType.Shopping});

  factory UpsertListItemParam.fromJson(Map<String, dynamic> json) => _$UpsertListItemParamFromJson(json);

  Map<String, dynamic> toJson() => _$UpsertListItemParamToJson(this);

  UpsertListItemParam copyWith({
    String? name,
    String? planDate,
    String? color,
    String? description,
    List<String>? assignment,
    List<String>? includedMembers,
    int? point,
    String? categoryId,
    String? familyId,
    ListType? type,
    List<Category>? categories,
    List<UpsertItemParam>? items,
    List<UpsertItemParam>? oldItems,
    String? activityId,
  }) {
    return UpsertListItemParam(
      name: name ?? this.name,
      planDate: planDate ?? this.planDate,
      color: color ?? this.color,
      description: description ?? this.description,
      assignment: assignment ?? this.assignment,
      includedMembers: includedMembers ?? this.includedMembers,
      categoryId: categoryId ?? this.categoryId,
      familyId: familyId ?? this.familyId,
      point: point ?? this.point,
      type: type ?? this.type,
      items: items ?? this.items,
      oldItems: oldItems ?? this.oldItems,
      activityId: activityId ?? this.activityId,
    );
  }
}
