// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:injectable/injectable.dart';

class SignInParameter {
  final String email;
  final String password;
  final bool isLoggedIn;

  SignInParameter({
    required this.email,
    required this.password,
    this.isLoggedIn = false,
  });

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'email': email,
      'password': password,
    };
  }
}

@lazySingleton
class SignInUsecase extends BaseUseCaseNoResult<SignInParameter> {
  final IAuthenRepository authenRepository;
  final LocalStorage localStorage;
  final AccountService accountService;
  final IFamilyRepository familyRepository;

  SignInUsecase({
    required this.authenRepository,
    required this.localStorage,
    required this.accountService,
    required this.familyRepository,
  });

  @override
  Future<void> call(SignInParameter param) async {
    try {
      final account = await authenRepository.login(param);
      await Future.wait([
        localStorage.cacheUserID(account.uuid ?? ''),
        localStorage.cacheAccessToken(account.token ?? ''),
        localStorage.cacheLoggedIn(param.isLoggedIn),
        localStorage.cacheEmail(param.email),
        localStorage.cachePassword(param.password),
      ]);
      await accountService.initMyProfile();
    } catch (e) {
      rethrow;
    }
  }
}
