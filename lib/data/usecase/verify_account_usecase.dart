import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class VerifyAccountUsecase extends BaseResultUseCase<bool> {
  final AccountService accountService;

  VerifyAccountUsecase({
    required this.accountService,
  });

  @override
  Future<bool> call() async {
    try {
      final account = accountService.account;
      if ((account?.familyName ?? '').isEmpty &&
          (accountService.myActiveFamily.value == null || accountService.myFamilyBelong.value.isEmpty)) {
        return false;
      } else {
        return true;
      }
    } catch (e) {
      return false;
    }
  }
}
