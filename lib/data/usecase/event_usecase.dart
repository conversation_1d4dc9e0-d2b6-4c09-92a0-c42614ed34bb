import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class EventUsecase extends BaseUseCase<EventModels, EventParameter> {
  final IEventRepository eventRepository;

  EventUsecase({required this.eventRepository});

  @override
  Future<EventModels> call(EventParameter param) async {
    try {
      late EventModels? result;

      if (param.uuid.isNotEmpty) {
        result = await eventRepository.updateEvent(param.uuid, param);
      } else {
        result = await eventRepository.createEvent(param);
      }
      return result;
    } catch (e) {
      rethrow;
    }
  }
}
