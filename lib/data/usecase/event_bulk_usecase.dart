import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class EventBulkUsecase extends BaseUseCase<void, List<EventParameter>> {
  final IEventRepository eventRepository;

  EventBulkUsecase({required this.eventRepository});

  @override
  Future<void> call(List<EventParameter> param) async {
    if (param.isEmpty) return;
    await eventRepository.bulkEvent(param);
  }
}
