import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/airline_model.dart';
import 'package:family_app/data/model/flight_offer_model.dart';
import 'package:family_app/data/model/amadeus/flight_offer_model.dart';
import 'package:family_app/data/repository/amadeus/iamadeus_repository.dart';
import 'package:family_app/data/usecase/model/booking_flight_param.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:injectable/injectable.dart';

enum FlightBookingProvider { amadeus, agoda }

@lazySingleton
class FlightBookingUseCase {
  // This class will contain methods related to flight booking operations.
  // For example, searching for flights, booking a flight, and managing bookings.

  FlightBookingProvider provider = FlightBookingProvider.amadeus;
  Map<String, AirlineModel> airlines = {};

  Future<List<AirlineModel>> findAirlines(List<String> airlineCodes) async {
    // Logic to find and return a list of airlines.
    if (provider == FlightBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      List<AirlineModel> result = [];
      List<String> requestCodes = [];
      // Check if the airlines are already cached
      for (var code in airlineCodes) {
        if (airlines.containsKey(code)) {
          result.add(airlines[code]!);
        } else {
          requestCodes.add(code);
        }
      }
      if (result.isNotEmpty) {
        return result;
      }

      var resp = await repository.getAirlines("[${requestCodes.join(",")}]");
      for (var airline in resp) {
        if (airline.iataCode == null) continue;
        airlines[airline.iataCode!] = airline;
        result.add(airline);
      }
      return result;
    } else if (provider == FlightBookingProvider.agoda) {
      // Call Agoda API to get airlines
      // return agodaAPI.getAirlines();
    }
    return [];
  }

  /// Search for flights with optional sorting by price or number of transits (stops).
  /// [sortBy]: 'price' or 'transit' (null for no sorting)
  /// [ascending]: true for ascending order, false for descending (default: true)
  Future<List<FlightOfferModel>> searchFlights(
    String origin,
    String destination,
    DateTime date,
    int adults, {
    List<String>? airlineCodesP,
    DateTime? returnDate,
    int? children,
    String? sortBy, // 'price' or 'transit'
    bool ascending = true,
  }) async {

     

    // Logic to search for flights based on origin, destination, and date.
    if (provider == FlightBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      var resp = await repository.getFlightOffers(
          origin,
          destination,
          date.yyyy_MM_dd, // Format date to YYYY-MM-DD
          1, //adults,
          returnDate: returnDate?.yyyy_MM_dd,
          max: 250); //get max and filter later 

      List<FlightOfferModel> offers = resp.data;
      logd("Got11: ${offers.length} offer ");

      //TODO:Filter by airlineCodes if not NULL
      if (airlineCodesP != null && airlineCodesP.isNotEmpty) {
        offers = offers.where((offer) {
          if (offer is AmaFlightOfferModel) {
            // Check all segments in all itineraries
            return offer.itineraries.any((itin) => itin.segments.any((seg) => airlineCodesP.contains(seg.carrierCode)));
          }
          return false;
        }).toList();

        logd("after filtering : ${offers.length} offer ");
      }

     

      // --- New: Sorting logic ---
      if (sortBy != null) {
        if (sortBy == 'price') {
          offers.sort((a, b) => ascending ? a.price.compareTo(b.price) : b.price.compareTo(a.price));
        } else if (sortBy == 'transit') {
          int getTotalStops(FlightOfferModel offer) {
            if (offer is AmaFlightOfferModel) {
              // Sum all segments' stops in all itineraries
              return offer.itineraries.fold(
                  0,
                  (total, itin) =>
                      total + itin.segments.fold(0, (segTotal, seg) => segTotal + (seg.numberOfStops ?? 0)));
            }
            return 0;
          }

          offers.sort((a, b) =>
              ascending ? getTotalStops(a).compareTo(getTotalStops(b)) : getTotalStops(b).compareTo(getTotalStops(a)));
        } else if (sortBy == 'duration') {
          int getTotalDuration(FlightOfferModel offer) {
            if (offer is AmaFlightOfferModel) {
              // Sum all segments' durations in all itineraries
              int totalDuration = offer.itineraries.fold(
                0,
                (total, itin) =>
                    total +
                    itin.segments.fold(
                      0,
                      (segTotal, seg) {
                        int minutes = parseIsoDurationToMinutes(seg.duration);
                        if (minutes == 0 && seg.duration != null) {
                          logd("Warning: Could not parse duration '${seg.duration}' for segment in offer ${offer.id}");
                        }
                        return segTotal + minutes;
                      },
                    ),
              );
              // logd("Offer ${offer.id} total duration: $totalDuration minutes");
              return totalDuration;
            }
            return 0;
          }

          // // Log all durations before sorting
          // for (var offer in offers) {
          //   getTotalDuration(offer);
          // }

          offers.sort((a, b) => ascending
              ? getTotalDuration(a).compareTo(getTotalDuration(b))
              : getTotalDuration(b).compareTo(getTotalDuration(a)));

          // for (var offer in offers) {
          //   logd("Sorted Offer ${offer.id} total duration: ${getTotalDuration(offer)} minutes");
          // }
        }
      }
      // --- End sorting logic ---

      return offers;
    } else if (provider == FlightBookingProvider.agoda) {
      // Call Agoda API to search flights
      // return agodaAPI.searchHlights(origin, destination, date);
    }

    return [];
  }

  /// Helper to parse ISO 8601 duration (e.g., 'PT2H30M') to minutes
  int parseIsoDurationToMinutes(String? duration) {
    if (duration == null) return 0;
    final regex = RegExp(r'PT(?:(\d+)H)?(?:(\d+)M)?');
    final match = regex.firstMatch(duration);
    if (match == null) return 0;
    final hours = int.tryParse(match.group(1) ?? '0') ?? 0;
    final minutes = int.tryParse(match.group(2) ?? '0') ?? 0;
    return hours * 60 + minutes;
  }

  Future<void> bookFlight(BookingFlightParam bookingParam) async {
    // Logic to book a flight using the booking parameters.
    // This could involve calling an API to confirm the booking and handle payment.
    if (provider == FlightBookingProvider.amadeus) {
      IAmadeusRepository repository = locator.get();
      try {
        var resp = await repository.priceFlightOffers(bookingParam.flightOffers.first);
        bookingParam = bookingParam.copyWith(
          flightOffers: resp.flightOffers,
        );
        var order = await repository.bookFlight(bookingParam);
        print("Flight booked successfully ${order.id}." );
      } catch (e) {
        print("Error booking flight: $e");
        throw e; // Handle the error appropriately, maybe rethrow or log it.
      }
    } else if (provider == FlightBookingProvider.agoda) {
      // Call Agoda API to book flight
      // agodaAPI.bookFlight(bookingParam);
    }
  }

  void cancelBooking(String bookingId) {
    // Logic to cancel a flight booking using the booking ID.
  }
}
