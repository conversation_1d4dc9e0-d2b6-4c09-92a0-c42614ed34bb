import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:family_app/data/usecase/mixin/category_mixin.dart';
import 'package:family_app/data/usecase/model/upsert_list_item_param.dart';
import 'package:family_app/data/usecase/upsert_item_usecase.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class UpsertListItemUsecase extends BaseUseCase<ListItem, UpsertListItemParam> with CategoryMixin {
  final IListRepository listRepository;
  final UpsertItemUsecase upsertItemUsecase;

  UpsertListItemUsecase({
    required this.listRepository,
    required this.upsertItemUsecase,
  });

  //XXX: this api returns a LIST object ( but the name is confusing)
  // if the uuid is EMPTY, then create a new list (with items array) and return the list object
  // if the uuid is NOT EMPTY, then update the list object and return the list object
  // in both cases, the return is a LIST object
  @override
  Future<ListItem> call(UpsertListItemParam param) async {
    try {
      /*  Call api to getCategory , then update or create a new list. */
      final category = await getCategory(param.type);
      print("11 category: $category");
      final newParam = param.copyWith(categoryId: category.uuid);
      late ListItem? result;
      if ((param.uuid ?? '').isNotEmpty) {
        print(">>>> UPDATE LIST uuid: ${param.uuid} . familyId: ${param.familyId}");
        result = await listRepository.updateList(param.uuid!, newParam);
      } else {
        print(">>>> Create LIST, familyId: ${param.familyId}");
        result = await listRepository.createList(newParam);
      }
      if (result == null) {
        throw Exception('Failed to create list item');
      }

      /*This function is responsible for updating or creating items in a list repository and removing items that are no longer needed. XXX: WTF .. why ?  */
      final itemsInList = await upsertItemUsecase.call(UpsertItemUSParam(
        oldItems: param.oldItems,
        newItems: param.items
            .map((e) => e.copyWith(
                listUuid: result?.uuid ?? '',
                familyUuid: param.familyId,
                due_date:  e.due_date.isNotEmpty  ? e.due_date :  param.planDate,
                includedMembers: param.includedMembers,
                assignment: e.assignment.isNotEmpty ? e.assignment : param.assignment,
                listCategory: param.type.typeStr()))
            .toList(),
      ));
      return result.copyWith(items: itemsInList);
    } catch (e) {
      rethrow;
    }
  }
}
