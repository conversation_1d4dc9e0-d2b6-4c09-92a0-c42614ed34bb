import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/base_usecase.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:injectable/injectable.dart';

class UpsertActivityItemUSParam {
  final List<UpsertItemParam>? oldItems;
  final List<UpsertItemParam> newItems;

  UpsertActivityItemUSParam({this.oldItems, this.newItems = const []});
}

@lazySingleton
class UpsertActivityItemUsecase extends BaseUseCase<List<Item>, UpsertActivityItemUSParam> {
  final IActivityRepository activityRepository;

  UpsertActivityItemUsecase({required this.activityRepository});

  @override
  Future<List<Item>> call(UpsertActivityItemUSParam param) async {
    try {
      final removeId = <String?>[];
      if ((param.oldItems ?? []).isNotEmpty) {
        final oldItemsId = param.oldItems?.map((e) => e.itemId).toList() ?? <String>[];
        final newItemsId = param.newItems.map((e) => e.itemId).toList();
        removeId.addAll(oldItemsId.where((element) => !newItemsId.contains(element)).toList());
        if (removeId.isNotEmpty) {
          await Future.wait(removeId.map((id) => activityRepository.deleteItemInActivity(id ?? '')));
        }
      }
      final items = await Future.wait(param.newItems.map((e) async {
        late Item? item;
        if ((e.itemId ?? '').isNotEmpty) {
          item = await activityRepository.updateItemInActivity(e.itemId!, e);
        } else {
          item = await activityRepository.createItemInActivity(e);
        }
        return item;
      }));

      return items.map((e) => e!).toList();
    } catch (e) {
      return <Item>[];
    }
  }
}
