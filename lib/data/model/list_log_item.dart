import 'dart:convert';

import 'package:family_app/data/model/account.dart';
import 'package:json_annotation/json_annotation.dart';

part 'list_log_item.g.dart';

@JsonSerializable()
class ListLog {
  String? uuid;
  @Json<PERSON>ey(name: 'created_at')
  String? createdAt;
  @Json<PERSON>ey(name: 'plan_uuid')
  String? planUuid;
  @J<PERSON><PERSON><PERSON>(name: 'item_uuid')
  String? itemUuid;
  @Json<PERSON>ey(name: 'action_name')
  String? actionName;
  String? name;
  @Json<PERSON>ey(name: 'action_by')
  String? actionBy;
  @Json<PERSON>ey(name: 'action_by_log', fromJson: parseFromActionByLog)
  Account? actionByLog;
  @J<PERSON><PERSON><PERSON>(name: 'list_uuid')
  String? listUuid;

  ListLog(
      {this.uuid,
      this.createdAt,
      this.planUuid,
      this.itemUuid,
      this.actionName,
      this.name,
      this.actionBy,
      this.actionByLog,
      this.listUuid});

  factory ListLog.fromJson(Map<String, dynamic> json) => _$ListLogFromJson(json);

  Map<String, dynamic> toJson() => _$ListLogToJson(this);

  static parseFromActionByLog(dynamic json) {
    if (json is String) {
      final map = jsonDecode(json) as Map<String, dynamic>;
      return Account.fromJson(map);
    } else if (json is Map<String, dynamic>) {
      return Account.fromJson(json);
    } else {
      return null;
    }
  }
}
