import 'package:equatable/equatable.dart';

class BookingPersonalInfo extends Equatable {
  int id;
  String? firstName;
  String? lastName;
  String? email;
  String? phone;

  bool isChildren;
  bool expanded;
  BookingPersonalInfo({
    required this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.isChildren = false,
    this.expanded = false,
  });


  @override
  List<Object?> get props => [
        id,
        firstName,
        lastName,
        email,
        phone,
        isChildren,
        expanded,
      ];


  Map<String, dynamic> toAmadeusJson() {
    return {
      "tid": id,
      // "title": "MR",
      "firstName": firstName,
      "lastName": lastName,
      "phone": phone,
      "email": email,
    };
  }

  BookingPersonalInfo copyWith({
    int? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    bool? isChildren,
    bool? expanded,
  }) {
    return BookingPersonalInfo(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      isChildren: isChildren ?? this.isChildren,
      expanded: expanded ?? this.expanded,
    );
  }




}