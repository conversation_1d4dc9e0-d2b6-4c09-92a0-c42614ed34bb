import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:json_annotation/json_annotation.dart';

abstract class HotelModel {
  String? name;
  String? imageURL;

  String? city, country;

  String? get id => null;

  set id(String? value) {
    // This setter is intentionally left empty as HotelModel does not have an id.
  }

  double? overallRating;

  HotelModel({this.name, this.imageURL});

  Future<String> fetchImage() async {
    //  String searchString = "hotel $name, $city, $country";
//    imageURL = await provider.fetchImageUrl(searchString);
    logd("Hotel model : disable search image");

    return imageURL ?? "";
  }

  Future<double> fetchRating() async {
    return 0.0;
  }
}

// Generic HotelModel implementation that can be created from HotelBookingModel
class GenericHotelModel extends HotelModel {
  String? _id;
  String? _name;
  String? _imageURL;
  String? _city;
  String? _country;
  double? _overallRating;

  GenericHotelModel({
    String? id,
    String? name,
    String? imageURL,
    String? city,
    String? country,
    double? overallRating,
  }) : super(name: name, imageURL: imageURL) {
    _id = id;
    _name = name;
    _imageURL = imageURL;
    _city = city;
    _country = country;
    _overallRating = overallRating;
  }

  @override
  String? get id => _id;

  @override
  set id(String? value) {
    _id = value;
  }

  @override
  String? get name => _name;

  @override
  set name(String? value) {
    _name = value;
  }

  @override
  String? get imageURL => _imageURL;

  @override
  set imageURL(String? value) {
    _imageURL = value;
  }

  @override
  String? get city => _city;

  @override
  set city(String? value) {
    _city = value;
  }

  @override
  String? get country => _country;

  @override
  set country(String? value) {
    _country = value;
  }

  @override
  double? get overallRating => _overallRating;

  @override
  set overallRating(double? value) {
    _overallRating = value;
  }

  // Factory constructor to create from HotelBookingModel
  factory GenericHotelModel.fromHotelBookingModel(dynamic hotelBooking) {
    if (hotelBooking == null) {
      return GenericHotelModel();
    }

    // Extract location information
    String? city;
    String? country;

    if (hotelBooking.location != null && hotelBooking.location!.isNotEmpty) {
      final locationParts = hotelBooking.location!.split(',');
      if (locationParts.length >= 2) {
        city = locationParts[0].trim();
        country = locationParts[1].trim();
      } else {
        city = hotelBooking.location;
      }
    }

    return GenericHotelModel(
      id: hotelBooking.hotelId,
      name: hotelBooking.hotelName,
      imageURL: hotelBooking.imageUrl,
      city: city,
      country: country,
    );
  }
}

abstract class HotelOfferModel {
  String? id;
  String? checkInDate;
  String? checkOutDate;
  String? imageURL;

  @JsonKey(includeFromJson: false, includeToJson: false)
  int quantity = 0;

  double get price =>
      0; // Placeholder for price, should be overridden in subclasses
  String? get currency =>
      "USD"; // Placeholder for currency, should be overridden in subclasses
  String? get description =>
      null; // Placeholder for description, should be overridden in subclasses

  int get adults => 1;
  int get children => 0;
  String? get beds => null;

  HotelOfferModel({this.id, this.checkInDate, this.checkOutDate});

  Future<String> fetchRoomImage(String roomSearch) async {
    imageURL = await provider.fetchImageUrl(roomSearch);
    return imageURL ?? "";
  }
}
