import 'package:json_annotation/json_annotation.dart';

part 'file_model.g.dart';

@JsonSerializable()
class FileModel {
  @Json<PERSON>ey(name: 'file_uuid')
  final String? fileUuid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_name')
  final String fileName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_url')
  final String fileUrl;
  @Json<PERSON>ey(name: 'file_type')
  final String? fileType;
  @JsonKey(name: 'file_size')
  final String? fileSize;

  const FileModel({
    required this.fileUuid,
    required this.fileName,
    required this.fileUrl,
    this.fileType,
    this.fileSize,
  });

  factory FileModel.fromJson(Map<String, dynamic> json) => _$FileModelFromJson(json);
  Map<String, dynamic> toJson() => _$FileModelToJson(this);
}
