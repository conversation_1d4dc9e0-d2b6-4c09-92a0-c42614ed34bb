import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'hotel_booking_model.g.dart';

@JsonSerializable()
class HotelBookingModel {
  String? hotelId;
  String? hotelName;
  String? imageUrl;
  String? location;
  String? provider;
  String? checkInDate;
  String? checkOutDate;

  List<dynamic>? bookingResults;

  HotelBookingModel({
    this.hotelId,
    this.hotelName,
    this.imageUrl,
    this.location,
    this.provider,
    this.checkInDate,
    this.checkOutDate,
    this.bookingResults,
  });

  factory HotelBookingModel.fromJson(Map<String, dynamic> json) =>
      _$HotelBookingModelFromJson(json);

  Map<String, dynamic> toJson() => _$HotelBookingModelToJson(this);

  HotelModel toHotelModel() {
    // Extract location information
    String? city;
    String? country;

    if (location != null && location!.isNotEmpty) {
      final locationParts = location!.split(',');
      if (locationParts.length >= 2) {
        city = locationParts[0].trim();
        country = locationParts[1].trim();
      } else {
        city = location;
      }
    }

    return GenericHotelModel(
      id: hotelId,
      name: hotelName,
      imageURL: imageUrl,
      city: city,
      country: country,
    );
  }
}
