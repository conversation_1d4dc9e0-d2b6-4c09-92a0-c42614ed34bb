import 'package:json_annotation/json_annotation.dart';

part 'presign_url.g.dart';

@JsonSerializable()
class PresignUrl {
  @Json<PERSON>ey(name: 'url')
  String? url;
  @<PERSON><PERSON><PERSON>ey(name: 'url_md')
  String? urlMd;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'url_sm')
  String? urlSm;
  @J<PERSON><PERSON>ey(name: 'uuid')
  String? uuid;
  int? status;

  PresignUrl({
    this.url,
    this.urlMd,
    this.urlSm,
    this.uuid,
  });

  factory PresignUrl.fromJson(Map<String, dynamic> json) =>
      _$PresignUrlFromJson(json);

  Map<String, dynamic> toJson() => _$PresignUrlToJson(this);
}
