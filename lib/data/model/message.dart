// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:io';

import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/message_data.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:family_app/data/usecase/model/upsert_list_item_param.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/ai_butler/model/message_model.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:json_annotation/json_annotation.dart';

part 'message.g.dart';

@JsonSerializable()
class Message {
  final String? file;
  final String? command;
  final String? message;
  final MessageData? data;
  final List<String> type;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final File? imageFile;

  Message({
    this.file,
    this.command,
    this.message,
    this.data,
    this.type = const [],
    this.imageFile,
  });

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);

  Map<String, dynamic> toJson() => _$MessageToJson(this);

  Message copyWith({
    String? file,
    String? command,
    String? message,
    List<String>? type,
    MessageData? data,
    File? imageFile,
  }) {
    return Message(
      file: file ?? this.file,
      command: command ?? this.command,
      message: message ?? this.message,
      type: type ?? this.type,
      data: data ?? this.data,
      imageFile: imageFile ?? this.imageFile,
    );
  }
}

extension MessageExtension on Message {
  MessageModel get messageModel => MessageModel(
        content: message ?? '',
        isAI: SocketEvent.isAiMess(command ?? ''),
        message: this,
        file: file ?? '',
      );

  CreateActivityParameter get createActivityParameter {
    final accountService = locator.get<AccountService>();
    return CreateActivityParameter(
      name: data?.name ?? '',
      description: data?.description ?? '',
      fromDate: data?.fromDate ?? '',
      toDate: data?.toDate ?? '',
      color: appTheme.primaryColor.text,
      caption: '',
      includedMembers: (data?.members?.map((e) => e.userId ?? '').toList() ?? <String>[])
        ..removeWhere((element) => element.isEmpty),
      includedEvents: [],
      includedLists: [],
      familyId: accountService.familyId,
      activityType: ListType.Trip.name,
    );
  }

  UpsertListItemParam get upsertListItemParam {
    final accountService = locator.get<AccountService>();
    return UpsertListItemParam(
      type: ListType.Shopping,
      name: data?.name ?? '',
      description: data?.description ?? '',
      planDate: data?.fromDate ?? '',
      color: appTheme.primaryColor.text,
      items: data?.items?.map((e) => UpsertItemParam(name: e.name, status: e.isDone ? 1 : 0)).toList() ??
          <UpsertItemParam>[],
      includedMembers: (data?.members?.map((e) => e.userId ?? '').toList() ?? <String>[])
        ..removeWhere((element) => element.isEmpty),
      assignment: (data?.members?.map((e) => e.userId ?? '').toList() ?? <String>[])
        ..removeWhere((element) => element.isEmpty),
      familyId: accountService.familyId,
    );
  }

  EventParameter get eventParameter {
    final accountService = locator.get<AccountService>();
    return EventParameter(
      uuid: data?.uuid ?? '',
      name: data?.name ?? '',
      description: data?.description ?? '',
      fromDate: data?.fromDate ?? '',
      toDate: data?.toDate ?? '',
      color: appTheme.primaryColor.text,
      caption: '',
      familyId: accountService.familyId,
      activityId: '',
      notificationStatus: '',
      notificationTime: data?.notificationTime ?? '',
    );
  }
}
