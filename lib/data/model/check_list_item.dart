// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';

class CheckListItem {
  String? id;
  String? name;
  bool? isDone;
  String? dueDate;

  List<Account>? includedMembers;
  List<Account>? assignment;

  CheckListItem({
    this.name,
    this.isDone,
    this.id,
    this.dueDate,
    this.assignment,
    this.includedMembers,
  });
}

extension CheckListItemExtension on CheckListItem {
  UpsertItemParam get param => UpsertItemParam(
        itemId: id,
        name: name ?? '',
        status: (isDone ?? false) ? 1 : 0,
        due_date: dueDate ?? '',
        includedMembers: includedMembers?.map((e) => e.familyMemberUuid ?? '').toList() ?? [],
        assignment: assignment?.map((e) => e.familyMemberUuid ?? '').toList() ?? [],
      );
}
