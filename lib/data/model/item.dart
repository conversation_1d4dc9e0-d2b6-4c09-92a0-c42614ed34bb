import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:json_annotation/json_annotation.dart';

part 'item.g.dart';

@JsonSerializable()
class Item {
  int? i;
  String? uuid;
  int? status;
  @J<PERSON><PERSON><PERSON>(name: 'family_uuid')
  String? familyUuid;
  @Json<PERSON><PERSON>(name: 'plan_uuid')
  String? planUuid;
  @Json<PERSON>ey(name: 'list_uuid')
  String? listUuid;
  @Json<PERSON>ey(name: 'created_by')
  String? createdBy;
  String? name;
  String? description;
  @Json<PERSON>ey(name: 'due_date_utc')
  String? due_date;

  @Json<PERSON><PERSON>(name: 'time_zone')
  String? timezone;

  @Json<PERSON>ey(name: "category_id")
  String? listCategory;
  @Json<PERSON>ey(name: 'notification_time')
  String? notificationTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_time_utc')
  String? notificationTimeUTC;

  String? listName;
  @Json<PERSON><PERSON>(name: 'parent_name')
  String? parentName;

  bool get isDone => status == 1;

  //assignees of the item ~ same as number of members in the family
  @JsonKey(name: 'included_members', fromJson: parseIncludeMember)
  List<Account>? includedMembers;

  @JsonKey(name: 'assignment', fromJson: parseIncludeMember)
  List<Account>? assignment;

  @JsonKey(name: 'point', defaultValue: 0)
  int point;

  // List<Account>? assignees;

  Item(
      {this.i,
      this.uuid,
      this.status,
      this.familyUuid,
      this.planUuid,
      this.listUuid,
      this.createdBy,
      this.name,
      this.point = 0,
      this.description,
      this.due_date,
      this.timezone,
      this.assignment,
      this.includedMembers,
      this.listCategory});

  factory Item.fromJson(Map<String, dynamic> json) => _$ItemFromJson(json);

  Map<String, dynamic> toJson() => _$ItemToJson(this);

  static parseIncludeMember(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <Account>[];
    }

    if (json is String) {
      List<String> ids = json.split(',');
      return ids.map((id) => Account(uuid: id, familyMemberUuid: id)).toList();
    }

    return (json as List<dynamic>).map((e) {
      var res = Account.fromJson(e);
      res.familyMemberUuid ??= res.uuid;
      return res;
    }).toList();
  }

  @override
  String toString() {
    return 'Item{i: $i, uuid: $uuid,  listUuid: $listUuid, , name: $name, description: $description, due_date: $due_date, category: $listCategory}';
  }

  bool isOwner(String currentUserId) {
    if (createdBy == null || createdBy!.isEmpty) {
      return false;
    }
    return createdBy == currentUserId;
  }

  bool isAssignee(String currentUserId) {
    if (assignment == null || assignment!.isEmpty) {
      return false;
    }
    return assignment!.any((element) => element.familyMemberUuid == currentUserId);
  }
}

extension ItemExtension on Item {
  CheckListItem get checkListItem => CheckListItem(
        id: uuid,
        isDone: status == 1,
        name: name,
        dueDate: due_date,
        includedMembers: includedMembers,
        assignment: assignment,
      );

  UpsertItemParam get param => UpsertItemParam(
        itemId: uuid,
        status: status,
        familyUuid: familyUuid,
        listUuid: listUuid,
        description: description,
        timeZone: timezone,
        notificationTime: notificationTime,
        due_date: due_date ?? '',
        point: point,
        includedMembers: includedMembers?.map((e) => e.familyMemberUuid ?? '').toList() ?? [],
      );

  UpsertItemParam get duplicateParam => UpsertItemParam(
        name: name ?? '',
        status: status,
        familyUuid: familyUuid,
        listUuid: listUuid,
        description: description,
        timeZone: timezone,
        notificationTime: notificationTime,
        due_date: due_date ?? '',
        point: point,
        assignment: assignment?.map((e) => e.familyMemberUuid ?? '').toList() ?? [],
        includedMembers: includedMembers?.map((e) => e.familyMemberUuid ?? '').toList() ?? [],
      );
}
