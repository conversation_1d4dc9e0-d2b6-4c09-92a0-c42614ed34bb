import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:json_annotation/json_annotation.dart';

// Contains the definitions of LIST and EVENT created by AI

part 'ai_model.g.dart';

@JsonSerializable(explicitToJson: true)
class FlList {
  @Json<PERSON>ey(name: 'name')
  String name;

  // @J<PERSON><PERSON><PERSON>(name: 'progress')
  // int? progress;

  @Json<PERSON>ey(name: 'list_type')
  String categoryId; // shopping or todo
  @Json<PERSON>ey(name: 'due_date')
  String planDateUtc;

  @Json<PERSON>ey(name: 'description')
  String description;
  // @Json<PERSON>ey(name: 'included_members', fromJson: _fromJsonAccountList)
  // List<Account>? includedMembers;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'items')
  List<Item> items;
  // @Json<PERSON>ey(name: 'total_items')
  // int totalItems;

  FlList(
      {required this.name,
      required this.categoryId,
      required this.planDateUtc,
      required this.description,
      required this.items});

  factory FlList.fromJson(Map<String, dynamic> json) => _$FlListFromJson(json);
  Map<String, dynamic> toJson() => _$FlListToJson(this);

  // Method to convert  to Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'category_id': categoryId,
      'plan_date_utc': planDateUtc,
      'description': description,
      'items': items,
      'total_items': items.length
    };
  }

  @override
  String toString() {
    return 'List: {name: $name,  category_id: $categoryId, plan_date_utc: $planDateUtc, description: $description,   items: $items, total_items: ${items.length}}';
  }

  UpsertListItemParameter toUpsertListItemParameter() {
    final newList = ListItem(
        name: name,
        categoryId: categoryId,
        planDate: planDateUtc,
        description: description,
        items: items);

    final listType =
        categoryId == 'shopping' ? ListType.Shopping : ListType.Todo;

    return UpsertListItemParameter(type: listType, listItem: newList);
  }
}

/*   "ai_event": {
flutter: │     "name": "Dinner with family",
flutter: │     "from_date": "2025-02-21T12:00:00.000Z",
flutter: │     "to_date": "2025-02-21T14:00:00.000Z",
flutter: │     "description": "Family dinner at Pizza 4P's",
flutter: │     "attendance": ["wife", "kid"],
flutter: │     "venue": "Pizza 4P's",
flutter: │     "notification_status": "pending",
flutter: │     "notification_time": "2025-02-21T11:00:00.000Z",
flutter: │     "gps": [10.7763777, 106.6969173]
flutter: │   }*/
@JsonSerializable(explicitToJson: true)
class FlEvent {
  @JsonKey(name: 'name')
  String name;

  @JsonKey(name: 'activity_id')
  String?
      activityId; // activity_id is the id of the activity that the event is associated with
  @JsonKey(name: 'list_id')
  String? listId;
  @JsonKey(name: 'family_id')
  String? familyId;
  @JsonKey(name: 'description')
  String description;
  @JsonKey(name: 'from_date')
  String fromDate;
  @JsonKey(name: 'to_date')
  String toDate;
  // @JsonKey(name: 'color')
  // String color;
  @JsonKey(name: 'notification_status')
  String? notificationStatus;
  @JsonKey(name: 'notification_time')
  String? notificationTime;

  @JsonKey(name: 'time_zone')
  String? timeZone;

  FlEvent(
      {required this.name,
      required this.activityId,
      required this.description,
      required this.fromDate,
      required this.toDate,
      this.notificationStatus,
      this.notificationTime,
      this.listId,
      this.familyId,
      this.timeZone});

  factory FlEvent.fromJson(Map<String, dynamic> json) =>
      _$FlEventFromJson(json);
  Map<String, dynamic> toJson() => _$FlEventToJson(this);

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'activity_id': activityId,
      'list_id': listId,
      'family_id': familyId,
      'description': description,
      'from_date': fromDate,
      'to_date': toDate,
      'notification_status': notificationStatus,
      'notification_time_utc': notificationTime,
      'time_zone': timeZone
    };
  }

  @override
  String toString() {
    return 'Event: {name: $name, activity_id: $activityId, list_id: $listId, family_id: $familyId, description: $description, from_date_utc: $fromDate, to_date_utc: $toDate, notification_status: $notificationStatus, notification_time_utc: $notificationTime, time_zone: $timeZone}';
  }

  EventModels toEventModels() {
    return EventModels(
      name: name,
      activityId: activityId,
      description: description,
      fromDate: fromDate,
      toDate: toDate,
      notificationStatus: notificationStatus,
      notificationTime: notificationTime,
      familyId: familyId,
    );
  }
}


/*
{
            "uuid": "ac9135d7-8fe9-41f8-96cc-0d587384c03e",
            "status": 0,
            "created_at": "2025-02-20 08:35:47",
            "updated_at": "2025-02-20 08:35:47",
            "created_by": "fb092da0-a37b-49da-b478-092276c30708",
            "activity_id": "",
            "list_id": "",
            "family_id": "fb934e19-b9e1-4b00-8029-7e25c0b580de",
            "name": "",
            "description": "Dinner with family",
            "from_date_utc": "2025-02-28T11:00:00.000Z",
            "to_date_utc": "2025-02-28T12:59:00.000Z",
            "color": "#FDC14E",
            "notification_status": "",
            "notification_time_utc": "",
            "members": [
                {
                    "uuid": "fb092da0-a37b-49da-b478-092276c30708",
                    "email": "<EMAIL>",
                    "full_name": "phung_test",
                    "family_name": "Default Family",
                    "color": "",
                    "photo_url": "8b810c9f-80ff-458b-b600-8f97f45a7efa/profile.jpg"
                },
                {
                    "uuid": "74bb352d-95fc-4b8e-8e8b-b650bdac42d5",
                    "email": "<EMAIL>",
                    "full_name": "Feynman",
                    "family_name": "Feynman",
                    "color": "",
                    "photo_url": ""
                },
                {
                    "uuid": "30ff5998-0069-42ba-aae8-eaae45d6ea6e",
                    "email": "<EMAIL>",
                    "full_name": "Winkey one",
                    "family_name": "Winkey one",
                    "color": "",
                    "photo_url": ""
                },
                {
                    "uuid": "fb934e19-b9e1-4b00-8029-7e25c0b580de",
                    "email": "<EMAIL>",
                    "full_name": "Winkey Wong",
                    "family_name": "Winkey Wong",
                    "color": "",
                    "photo_url": "3ba90fdd-dee0-4669-88d1-c85a8baa9477"
                }
            ],
            "role": "",
            "time_zone": ""
        }


        */