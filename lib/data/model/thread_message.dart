import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'thread_message.g.dart';

@JsonSerializable()
class ThreadMessage {
  String? uuid;
  int? status;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  String? createdAt;
  @Json<PERSON><PERSON>(name: 'thread_id')
  String? threadId;
  @Json<PERSON>ey(name: 'user_id')
  String? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_log')
  String? userLog;
  String? message;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'message_type')
  String? messageType;
  @J<PERSON><PERSON><PERSON>(name: 'message_type_id')
  String? messageTypeId;
  @J<PERSON><PERSON><PERSON>(name: 'message_status')
  String? messageStatus;
  @J<PERSON><PERSON>ey(name: 'extra_data')
  String? extraData;
  List<dynamic>? activities;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'related_data')
  ThreadMessageRelatedData? relatedData;

  ThreadMessage({
    this.uuid,
    this.status,
    this.createdAt,
    this.threadId,
    this.userId,
    this.userLog,
    this.message,
    this.messageType,
    this.messageTypeId,
    this.messageStatus,
    this.extraData,
    this.activities,
    this.relatedData,
  });

  factory ThreadMessage.fromJson(Map<String, dynamic> json) {
    // Always pass messageType to relatedData for type-aware deserialization
    final messageType = json['message_type'] as String?;
    var relatedDataJson = json['related_data'];
    if (relatedDataJson is String) {
      try {
        relatedDataJson =
            relatedDataJson.isNotEmpty ? jsonDecode(relatedDataJson) : null;
      } catch (_) {
        relatedDataJson = null;
      }
    }
    final relatedData = relatedDataJson != null
        ? ThreadMessageRelatedData.fromJson(relatedDataJson,
            messageType: messageType)
        : null;
    return ThreadMessage(
      uuid: json['uuid'] as String?,
      status: json['status'] as int?,
      createdAt: json['created_at'] as String?,
      threadId: json['thread_id'] as String?,
      userId: json['user_id'] as String?,
      userLog: json['user_log'] as String?,
      message: json['message'] as String?,
      messageType: messageType,
      messageTypeId: json['message_type_id'] as String?,
      messageStatus: json['message_status'] as String?,
      extraData: json['extra_data'] as String?,
      activities: json['activities'] as List<dynamic>?,
      relatedData: relatedData,
    );
  }

  Map<String, dynamic> toJson() => _$ThreadMessageToJson(this);
}
