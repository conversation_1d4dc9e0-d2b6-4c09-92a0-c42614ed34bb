enum FLCalendarView {
  agenda,
  day,
  week,
  month,
}

const List<String> kDaysOfWeekMondayStart = ["<PERSON>", "<PERSON><PERSON>", "We<PERSON>", "<PERSON>hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"];
const List<String> kDaysOfWeekSundayStart = ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "We<PERSON>", "<PERSON>hu", "Fri", "Sat"];

// Used to identify default device calendars by name
const List<String> kDefaultCalendarNames = ['account_name_local'];

// Used to group calendars by accountName in UI
const String kCalendarGroupOther = 'Other';

// Display name for default calendar group
const String kCalendarGroupDefaultDisplay = 'Default';
