import 'package:device_calendar/device_calendar.dart';
import 'package:family_app/data/model/event.dart';

class DeviceEventModel extends EventModels {
  String calendarId;
  String deviceEventId;
  Event event;

  DeviceEventModel({
    required this.calendarId,
    required this.deviceEventId,
    required this.event,
    super.status,
    super.createdAt,
    super.createdBy,
    super.activityId,
    super.listId,
    super.familyId,
    super.name,
    super.description,
    super.fromDate,
    super.toDate,
    super.color,
    super.notificationStatus,
    super.notificationTime,
    super.activity,
    super.memberIds,
    super.members,
  });
}