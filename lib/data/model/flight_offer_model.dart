import 'package:family_app/data/model/airline_model.dart';
import 'package:json_annotation/json_annotation.dart';

abstract class FlightOfferModel {
  String? id;

  @JsonKey(includeFromJson: false, includeToJson: false)
  double get price => 0; 
  @Json<PERSON>ey(includeFromJson: false, includeToJson: false)
  String? get currency => "USD";
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get description => null;
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get route => null;
  @JsonKey(includeFromJson: false, includeToJson: false)
  int get adults => 1;
  @J<PERSON><PERSON><PERSON>(includeFromJson: false, includeToJson: false)
  int get children => 0;


  @Json<PERSON><PERSON>(includeFromJson: false, includeToJson: false)
  List<String> get airlinesCode => [];
  @Json<PERSON>ey(includeFromJson: false, includeToJson: false)
  List<AirlineModel> airlines = [];




  FlightOfferModel({this.id});

  Map<String, dynamic> toJson() {
    throw UnimplementedError('toJson() must be implemented in subclasses');
  }


}