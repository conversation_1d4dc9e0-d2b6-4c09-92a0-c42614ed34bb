class CardInfo {
  String? cardNumber;
  String? cardHolderName;
  String? expiryDate;
  String? cvc;

  CardInfo({
    this.cardNumber,
    this.cardHolderName,
    this.expiryDate,
    this.cvc,
  });

  Map<String, dynamic> toJson() {
    return {
      'cardNumber': cardNumber,
      'cardHolderName': cardHolderName,
      'expiryDate': expiryDate,
      'cvc': cvc,
    };
  }

  Map<String, dynamic> toAmadeus<PERSON>son() {
    return {
      'method': "CREDIT_CARD",
      "paymentCard": {
        'paymentCardInfo': {
          'vendorCode': getCardType(cardNumber ?? ''),
          'cardNumber': cardNumber,
          'expiryDate': expiryDate,
          'holderName': cardHolderName,
        }
      },
    };
  }

  String getCardType(String cardNumber) {
    final cleaned = cardNumber.replaceAll(RegExp(r'\D'), '');

    final visaRegex = RegExp(r'^4[0-9]{12}(?:[0-9]{3})?(?:[0-9]{3})?$');
    final mastercardRegex = RegExp(
        r'^(5[1-5][0-9]{14}|2(2[2-9][0-9]{12}|[3-6][0-9]{13}|7[01][0-9]{12}|720[0-9]{12}))$'
    );

    if (visaRegex.hasMatch(cleaned)) {
      return 'VI';
    } else if (mastercardRegex.hasMatch(cleaned)) {
      return 'MS';
    } else {
      return 'Unknown';
    }
  }

  factory CardInfo.fromJson(Map<String, dynamic> json) {
    return CardInfo(
      cardNumber: json['cardNumber'],
      cardHolderName: json['cardHolderName'],
      expiryDate: json['expiryDate'],
      cvc: json['cvc'],
    );
  }


}