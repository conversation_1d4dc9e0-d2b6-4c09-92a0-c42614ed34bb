import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'repeat_config_model.g.dart';

/// Frequency enum: DAILY, WEEKLY, MONTHLY, YEARLY
@JsonEnum()
enum Frequency {
  @JsonValue('DAILY')
  daily,

  @JsonValue('WEEKLY')
  weekly,

  @JsonValue('MONTHLY')
  monthly,

  @JsonValue('YEARLY')
  yearly,
}

/// Day enum: SU, MO, TU, ...
@JsonEnum()
enum WeekDay {
  @JsonValue('SU')
  sunday,

  @JsonValue('MO')
  monday,

  @JsonValue('TU')
  tuesday,

  @JsonValue('WE')
  wednesday,

  @JsonValue('TH')
  thursday,

  @JsonValue('FR')
  friday,

  @JsonValue('SA')
  saturday,
}

extension WeekDayExt on WeekDay {
  String get displayName {
    switch (this) {
      case WeekDay.sunday:
        return 'SU';
      case WeekDay.monday:
        return 'MO';
      case WeekDay.tuesday:
        return 'TU';
      case WeekDay.wednesday:
        return 'WE';
      case WeekDay.thursday:
        return 'TH';
      case WeekDay.friday:
        return 'FR';
      case WeekDay.saturday:
        return 'SA';
    }
  }



}

@JsonSerializable(explicitToJson: true)
class RepeatConfig {
  final Frequency frequency;
  final int? interval;
  final DateTime? until;
  final int? count;

  int? reminder;

  // For WEEKLY
  @JsonKey(name: 'by_day_of_week')
  final List<WeekDay>? byDayOfWeek;

  // For MONTHLY
  @JsonKey(name: 'by_month_day')
  final List<int>? byMonthDay;
  @JsonKey(name: 'by_day_of_week_monthly')
  final List<DayOfWeekMonthly>? byDayOfWeekMonthly;

  // For YEARLY
  @JsonKey(name: 'by_month')
  final List<int>? byMonth;
  @JsonKey(name: 'by_month_day_yearly')
  final List<int>? byMonthDayYearly;
  @JsonKey(name: 'by_day_of_week_yearly')
  final List<DayOfWeekYearly>? byDayOfWeekYearly;

  RepeatConfig({
    required this.frequency,
    this.interval,
    this.until,
    this.count,
    this.byDayOfWeek,
    this.byMonthDay,
    this.byDayOfWeekMonthly,
    this.byMonth,
    this.byMonthDayYearly,
    this.byDayOfWeekYearly,
  });

  factory RepeatConfig.fromJson(Map<String, dynamic> json) =>
      _$RepeatConfigFromJson(json);
  Map<String, dynamic> toJson() => _$RepeatConfigToJson(this);

  String toJSONString() {
    return jsonEncode(toJson());
  }
}

@JsonSerializable()
class DayOfWeekMonthly {
  final WeekDay day;
  final int ordinal;

  DayOfWeekMonthly({required this.day, required this.ordinal});

  factory DayOfWeekMonthly.fromJson(Map<String, dynamic> json) =>
      _$DayOfWeekMonthlyFromJson(json);
  Map<String, dynamic> toJson() => _$DayOfWeekMonthlyToJson(this);
}

@JsonSerializable()
class DayOfWeekYearly {
  final WeekDay day;
  final int ordinal;
  final int month;

  DayOfWeekYearly({
    required this.day,
    required this.ordinal,
    required this.month,
  });

  factory DayOfWeekYearly.fromJson(Map<String, dynamic> json) =>
      _$DayOfWeekYearlyFromJson(json);
  Map<String, dynamic> toJson() => _$DayOfWeekYearlyToJson(this);
}
