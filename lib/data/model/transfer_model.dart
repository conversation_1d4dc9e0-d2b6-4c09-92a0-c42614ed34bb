import 'package:collection/collection.dart';
import 'package:family_app/data/model/file_model.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:logger/logger.dart';

part 'transfer_model.g.dart';

@JsonSerializable()
class TransferModel {
  final String? type;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ticket_no')
  final String? ticketNo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'from_location')
  final String? fromLocation;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'from_time')
  final String? fromTime;
  @Json<PERSON>ey(name: 'to_location')
  final String? toLocation;
  @Json<PERSON>ey(name: 'to_time')
  final String? toTime;
  final double? price;
  final String? currency;
  final int? quantity;
  final String? note;
  final List<FileModel>? attachments;

  //similar to the one in Activity 
  final String? transportCode; 

  // optional, if available just use this to show the duration , Flight time accross timezone is tricky to cal, we'll use the value from flight search .
  final int? duration; 

  const TransferModel({
    this.type,
    this.ticketNo,
    this.fromLocation,
    this.fromTime,
    this.toLocation,
    this.toTime,
    this.price,
    this.currency,
    this.quantity,
    this.note,
    this.attachments,
    this.duration,
    this.transportCode, 
  });

  @override
  String toString() {
    return 'TransferModel(type: $type, ticketNo: $ticketNo, fromLocation: $fromLocation, fromTime: $fromTime, toLocation: $toLocation, toTime: $toTime, price: $price, currency: $currency, quantity: $quantity, note: $note, attachments: $attachments)';
  }

  factory TransferModel.fromJson(Map<String, dynamic> json) {
    return _$TransferModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$TransferModelToJson(this);

  TransferModel copyWith({
    String? type,
    String? ticketNo,
    String? fromLocation,
    String? fromTime,
    String? toLocation,
    String? toTime,
    double? price,
    String? currency,
    int? quantity,
    String? note,
    List<FileModel>? attachments,
  }) {
    return TransferModel(
      type: type ?? this.type,
      ticketNo: ticketNo ?? this.ticketNo,
      fromLocation: fromLocation ?? this.fromLocation,
      fromTime: fromTime ?? this.fromTime,
      toLocation: toLocation ?? this.toLocation,
      toTime: toTime ?? this.toTime,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      quantity: quantity ?? this.quantity,
      note: note ?? this.note,
      attachments: attachments ?? this.attachments,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! TransferModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      type.hashCode ^
      ticketNo.hashCode ^
      fromLocation.hashCode ^
      fromTime.hashCode ^
      toLocation.hashCode ^
      toTime.hashCode ^
      price.hashCode ^
      currency.hashCode ^
      quantity.hashCode ^
      note.hashCode ^
      attachments.hashCode;

  String getDuration() {
    if (duration != null) {
      final hours = duration! ~/ 60;
      final minutes = duration! % 60;
      return '	${hours}h ${minutes}m';
    }
    if (fromTime == null || toTime == null || fromTime!.isEmpty || toTime!.isEmpty) {
      return '';
    }
    final from = fromTime!.toLocalDT; // XXXX toLocalDT is wrong
    final to = toTime!.toLocalDT; // XXXX toLocalDT is wrong;
    final durationDiff = to.difference(from);
    final hours = durationDiff.inHours;
    final minutes = durationDiff.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  String getFromDate() {
    if (fromTime == null || fromTime!.isEmpty) {
      return '';
    }
    return DateFormat('EEE dd MMM').format(fromTime!.toDateTime());
  }

  String getToDate() {
    if (toTime == null || toTime!.isEmpty) {
      return '';
    }
    return DateFormat('EEE dd MMM').format(toTime!.toDateTime());
  }

  String getFromTime() {
    if (fromTime == null || fromTime!.isEmpty) {
      return '';
    }
    return fromTime!.toDateTime().HH_mm;
  }

  String getToTime() {
    if (toTime == null || toTime!.isEmpty) {
      return '';
    }
    return toTime!.toDateTime().HH_mm;
  }
}
