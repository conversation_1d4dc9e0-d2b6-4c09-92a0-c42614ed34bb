class Holiday {
  final String name;
  final DateTime date;
  final HolidayType type;

  const Holiday({
    required this.name,
    required this.date,
    this.type = HolidayType.national,
  });

  @override
  String toString() => 'Holiday(name: $name, date: $date, type: $type)';
}

enum HolidayType {
  national,    // National holidays
  religious,   // Religious observances
  observance   // Other general observances
}
