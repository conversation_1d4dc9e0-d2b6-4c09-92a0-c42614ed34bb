// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/data/model/check_list_item.dart';
import 'package:json_annotation/json_annotation.dart';

part 'message_data.g.dart';

@JsonSerializable()
class MessageData {
  String? type;
  String? name;
  String? description;
  @JsonKey(name: 'from_date')
  String? fromDate;
  @JsonKey(name: 'to_date')
  String? toDate;
  String? uuid;
  List<MemberMessage>? members;
  String? notificationTime;
  List<ItemMessage>? items;

  MessageData(
      {this.type,
      this.name,
      this.description,
      this.fromDate,
      this.uuid,
      this.toDate,
      this.members,
      this.notificationTime,
      this.items});

  factory MessageData.fromJson(Map<String, dynamic> json) => _$MessageDataFromJson(json);

  Map<String, dynamic> toJson() => _$MessageDataToJson(this);
}

@JsonSerializable()
class MemberMessage {
  @J<PERSON><PERSON>ey(name: 'user_id')
  String? userId;
  String? name;
  String? description;

  MemberMessage({
    this.userId,
    this.name,
    this.description,
  });

  factory MemberMessage.fromJson(Map<String, dynamic> json) => _$MemberMessageFromJson(json);

  Map<String, dynamic> toJson() => _$MemberMessageToJson(this);
}

@JsonSerializable()
class ItemMessage {
  String? id;
  String? name;
  String? status;

  bool get isDone => status == 'done';

  ItemMessage({this.id, this.name, this.status});

  factory ItemMessage.fromJson(Map<String, dynamic> json) => _$ItemMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ItemMessageToJson(this);
}

extension MessageItemExtension on ItemMessage {
  CheckListItem get checkListItem => CheckListItem(
        name: name ?? '',
        isDone: isDone,
      );
}
