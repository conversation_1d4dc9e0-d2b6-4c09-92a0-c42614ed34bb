import 'package:json_annotation/json_annotation.dart';

part 'privacy_policy.g.dart';

@JsonSerializable()
class PrivacyPolicyData {
  @J<PERSON><PERSON><PERSON>(name: '_')
  int? underscore;

  String? uuid;
  int? status;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'config_key')
  String? configKey;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  String? createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  String? updatedAt;

  @Json<PERSON>ey(name: 'config_value')
  String? configValue;

  PrivacyPolicyData({
    this.underscore,
    this.uuid,
    this.status,
    this.configKey,
    this.createdAt,
    this.updatedAt,
    this.configValue,
  });

  factory PrivacyPolicyData.fromJson(Map<String, dynamic> json) => _$PrivacyPolicyDataFromJson(json);

  Map<String, dynamic> toJson() => _$PrivacyPolicyDataToJson(this);
}
