import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/item.dart';
import 'package:json_annotation/json_annotation.dart';

part 'list_item.g.dart';

@JsonSerializable()
class ListItem {
  String? uuid;
  int? status;
  @Json<PERSON>ey(name: 'created_at')
  String? createdAt;
  @Json<PERSON><PERSON>(name: 'family_uuid')
  String? familyUuid;
  @Json<PERSON>ey(name: 'category_id') // "shopping", "todo"
  String? categoryId;
  String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'plan_date_utc')
  String? planDate;
  @J<PERSON><PERSON><PERSON>(name: 'color')
  String? color;
  @Json<PERSON>ey(name: 'description')
  String? description;
  @Json<PERSON>ey(name: 'included_members', fromJson: parseIncludeMember)
  List<Account>? includedMembers;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'assignment', fromJson: parseIncludeMember)
  List<Account>? assignment;
  @Json<PERSON>ey(name: 'created_by')
  String? createdBy;
  // @Json<PERSON>ey(includeFromJson: false, includeToJson: false)
  @Json<PERSON>ey(name: 'items')
  List<Item>? items;

  @JsonKey(name: 'activity_id')
  String? activityId;
  @JsonKey(includeFromJson: false, includeToJson: false)
  ActivityModel? activity;

  @JsonKey(defaultValue: 0)
  num point;

  ListItem(
      {this.uuid,
      this.status,
      this.createdAt,
      this.familyUuid,
      this.name,
      this.planDate,
      this.point = 0,
      this.color,
      this.description,
      this.assignment,
      this.includedMembers,
      this.createdBy,
      this.categoryId,
      this.activity,
      this.activityId,
      this.items});

  factory ListItem.fromJson(Map<String, dynamic> json) => _$ListItemFromJson(json);

  Map<String, dynamic> toJson() => _$ListItemToJson(this);

  static parseIncludeMember(dynamic json) {
    if (json == null || (json is String && json.isEmpty)) {
      return <Account>[];
    }

    if (json is String) {
      List<String> ids = json.split(',');
      return ids.map((id) => Account(familyMemberUuid: id, uuid: id)).toList();
    }

    return (json as List<dynamic>).map((e) {
      var res = Account.fromJson(e as Map<String, dynamic>);
      res.familyMemberUuid ??= res.uuid;
      return res;
    }).toList();
  }

  ListItem copyWith({
    String? uuid,
    int? status,
    String? createdAt,
    String? familyUuid,
    String? listUuid,
    String? name,
    String? planDate,
    String? color,
    String? description,
    int? point,
    List<Account>? assignment,
    List<Account>? includedMembers,
    String? createdBy,
    List<Item>? items,
    String? categoryId,
    String? activityId,
    ActivityModel? activity,
  }) {
    return ListItem(
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      familyUuid: familyUuid ?? this.familyUuid,
      name: name ?? this.name,
      planDate: planDate ?? this.planDate,
      color: color ?? this.color,
      description: description ?? this.description,
      point: point ?? this.point,
      assignment: assignment ?? this.assignment,
      includedMembers: includedMembers ?? this.includedMembers,
      createdBy: createdBy ?? this.createdBy,
      items: items ?? this.items,
      categoryId: categoryId ?? this.categoryId,
      activityId: activityId ?? this.activityId,
      activity: activity ?? this.activity,
    );
  }

  void setActivity(ActivityModel? model) {
    activity = model;
    activityId = model?.uuid;
  }

  @override
  String toString() {
    return 'ListItem(uuid: $uuid, name: $name, planDate: $planDate, description: $description,items: $items, category: $categoryId)';
  }
}

extension ListItemExtension on ListItem {
  ListType get listType {
    if (categoryId == ListType.Shopping.typeStr()) {
      return ListType.Shopping;
    } else {
      return ListType.Todo;
    }
  }
}
