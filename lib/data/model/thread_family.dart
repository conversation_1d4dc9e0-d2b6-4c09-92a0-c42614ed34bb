import 'package:json_annotation/json_annotation.dart';

part 'thread_family.g.dart';

@JsonSerializable()
class ThreadFamily {
  String? uuid;
  int? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'family_id')
  String? familyId;
  String? name;
  List<Members>? members;
  String? color;
  String? image;
  List<dynamic>? setting;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latest_message')
  String? latestMessage;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latest_message_text')
  String? latestMessageText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latest_message_time')
  String? latestMessageTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latest_message_user_id')
  String? latestMessageUserId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latest_message_user_name')
  String? latestMessageUserName;
  int? unread;

  ThreadFamily({
    this.uuid,
    this.status,
    this.familyId,
    this.name,
    this.members,
    this.color,
    this.image,
    this.setting,
    this.latestMessage,
    this.latestMessageText,
    this.latestMessageTime,
    this.latestMessageUserId,
    this.latestMessageUserName,
    this.unread = 0,
  });

  factory ThreadFamily.fromJson(Map<String, dynamic> json) => _$ThreadFamilyFromJson(json);

  Map<String, dynamic> toJson() => _$ThreadFamilyToJson(this);

  ThreadFamily copyWith({
    String? uuid,
    int? status,
    String? familyId,
    String? name,
    List<Members>? members,
    String? color,
    String? image,
    List<dynamic>? setting,
    String? latestMessage,
    String? latestMessageText,
    String? latestMessageTime,
    String? latestMessageUserId,
    String? latestMessageUserName,
    int? unread,
  }) {
    return ThreadFamily(
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      familyId: familyId ?? this.familyId,
      name: name ?? this.name,
      members: members ?? this.members,
      color: color ?? this.color,
      image: image ?? this.image,
      setting: setting ?? this.setting,
      latestMessage: latestMessage ?? this.latestMessage,
      latestMessageText: latestMessageText ?? this.latestMessageText,
      latestMessageTime: latestMessageTime ?? this.latestMessageTime,
      latestMessageUserId: latestMessageUserId ?? this.latestMessageUserId,
      latestMessageUserName: latestMessageUserName ?? this.latestMessageUserName,
      unread: unread ?? this.unread,
    );
  }
}

class Members {
  String? uuid;
  String? email;
  String? fullName;
  String? familyName;
  String? color;
  String? photoUrl;

  Members({this.uuid, this.email, this.fullName, this.familyName, this.color, this.photoUrl});

  Members.fromJson(Map<String, dynamic> json) {
    uuid = json['uuid'];
    email = json['email'];
    fullName = json['full_name'];
    familyName = json['family_name'];
    color = json['color'];
    photoUrl = json['photo_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['uuid'] = this.uuid;
    data['email'] = this.email;
    data['full_name'] = this.fullName;
    data['family_name'] = this.familyName;
    data['color'] = this.color;
    data['photo_url'] = this.photoUrl;
    return data;
  }
}
