import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:json_annotation/json_annotation.dart';

part 'upcoming.g.dart';

class DummyItem {
  final String name;
  final String description;
  final String color;
  final String planDate;
  final List<String> includedMembers;
  final String category;
  final int progress;

  DummyItem({
    required this.name,
    required this.description,
    required this.color,
    required this.planDate,
    required this.includedMembers,
    required this.category,
    required this.progress,
  });
}

@JsonSerializable()
class Upcoming {
  List<ActivityModel> activity;
  List<EventModels> event;
  List<ListItem> list;

// will be replaced by real data from API

  final List<DummyItem> dummyList = [
    DummyItem(
        name: 'Purchase Fabric 2nd time',
        description: 'Visit Fabric Store',
        color: '0xFFE0E0E0',
        planDate: '10 Jan',
        includedMembers: ['<PERSON>', '<PERSON>'],
        category: 'Shopping',
        progress: 60),
    DummyItem(
        name: 'Complete Project Report',
        description: 'Finalize & Submit Report',
        color: '0xFFE0E0E0',
        planDate: '5 Jan',
        includedMembers: ['<PERSON>', '<PERSON>e', '<PERSON>'],
        category: 'To Do',
        progress: 40),
    DummyItem(
        name: 'Prepare Presentation Slides',
        description: 'Slides for Upcoming Meeting',
        color: '0xFFE0E0E0',
        planDate: '6 Jan',
        includedMembers: ['John Doe'],
        category: 'Work',
        progress: 10),
    DummyItem(
        name: 'Family Dinner',
        description: 'Dinner at Restaurant',
        color: '0xFFE0E0E0',
        planDate: '24 Jan',
        includedMembers: ['John Doe', 'Jane Doe', 'John Smith'],
        category: 'Event',
        progress: 100),
  ];

  //int get count => activity.length + event.length + list.length;
  int get count => 3; //list.count;

  Upcoming({
    this.activity = const [],
    this.event = const [],
    this.list = const [],
  });

  factory Upcoming.fromJson(Map<String, dynamic> json) => _$UpcomingFromJson(json);
  Map<String, dynamic> toJson() => _$UpcomingToJson(this);

  @override
  String toString() {
    return 'Upcoming(activity count: ${activity.length}, event count: ${event.length}, list count: ${list.length}, dummyList count: ${dummyList.length}, count: $count)';
  }
}
