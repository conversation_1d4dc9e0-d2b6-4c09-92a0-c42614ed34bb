import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

part 'storage_model.g.dart';

@JsonSerializable()
class StorageModel {
  @Json<PERSON>ey(name: 'uuid')
  final String? uuid;
  @J<PERSON><PERSON><PERSON>(name: 'status')
  final int? status;
  @Json<PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'deleted_at')
  final dynamic deletedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'family_id')
  final String? familyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_name')
  final String? fileName;
  @Json<PERSON>ey(name: 'file_size')
  final String? fileSize;
  @Json<PERSON>ey(name: 'file_path')
  final String? filePath;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_url')
  final String? fileUrl;
  @<PERSON>son<PERSON><PERSON>(name: 'file_url_sm')
  final String? fileUrlSm;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'file_url_md')
  final String? fileUrlMd;
  @J<PERSON>K<PERSON>(name: 'extra_data')
  final String? extraData;

  const StorageModel({
    this.uuid,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.familyId,
    this.userId,
    this.fileName,
    this.fileSize,
    this.filePath,
    this.fileUrl,
    this.fileUrlSm,
    this.fileUrlMd,
    this.extraData,
  });

  @override
  String toString() {
    return 'Storage(uuid: $uuid, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, deletedAt: $deletedAt, familyId: $familyId, userId: $userId, fileName: $fileName, fileSize: $fileSize, filePath: $filePath, fileUrl: $fileUrl, fileUrlSm: $fileUrlSm, fileUrlMd: $fileUrlMd, extraData: $extraData)';
  }

  factory StorageModel.fromJson(Map<String, dynamic> json) {
    return _$StorageModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$StorageModelToJson(this);

  StorageModel copyWith({
    String? uuid,
    int? status,
    String? createdAt,
    String? updatedAt,
    dynamic deletedAt,
    String? familyId,
    String? userId,
    String? fileName,
    String? fileSize,
    String? filePath,
    String? fileUrl,
    String? fileUrlSm,
    String? fileUrlMd,
    String? extraData,
  }) {
    return StorageModel(
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      familyId: familyId ?? this.familyId,
      userId: userId ?? this.userId,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      filePath: filePath ?? this.filePath,
      fileUrl: fileUrl ?? this.fileUrl,
      fileUrlSm: fileUrlSm ?? this.fileUrlSm,
      fileUrlMd: fileUrlMd ?? this.fileUrlMd,
      extraData: extraData ?? this.extraData,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! StorageModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      uuid.hashCode ^
      status.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      deletedAt.hashCode ^
      familyId.hashCode ^
      userId.hashCode ^
      fileName.hashCode ^
      fileSize.hashCode ^
      filePath.hashCode ^
      fileUrl.hashCode ^
      fileUrlSm.hashCode ^
      fileUrlMd.hashCode ^
      extraData.hashCode;
}
