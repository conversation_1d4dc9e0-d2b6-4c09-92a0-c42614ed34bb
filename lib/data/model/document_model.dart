import 'file_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'document_model.g.dart';

@JsonSerializable()
class DocumentModel {
  final String title;
  final String description;
  final List<FileModel> files;

  const DocumentModel({
    required this.title,
    this.description = '',
    required this.files,
  });
  

  DocumentModel copyWith({
    String? title,
    String? description,
    List<FileModel>? files,
  }) {
    return DocumentModel(
      title: title ?? this.title,
      description: description ?? this.description,
      files: files ?? this.files,
    );
  }
  
  // Use the generated fromJson method
  factory DocumentModel.fromJson(Map<String, dynamic> json) => _$DocumentModelFromJson(json);

  // Use the generated toJson method
  Map<String, dynamic> toJson() => _$DocumentModelToJson(this);

}
