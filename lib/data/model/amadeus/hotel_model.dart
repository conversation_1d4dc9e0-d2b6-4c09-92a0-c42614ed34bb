import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/amadeus/iamadeus_repository.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:json_annotation/json_annotation.dart';

import '../hotel_model.dart';
import 'hotel_offer_model.dart';

///{
///             "chainCode": "AA",
///             "iataCode": "BLR",
///             "dupeId": 501381260,
///             "name": "HOTEL RAMANASHREE BRUNTON",
///             "hotelId": "AABLRHRB",
///             "geoCode": {
///                 "latitude": 12.97228,
///                 "longitude": 77.61508
///             },
///             "address": {
///                 "countryCode": "IN"
///             },
///             "lastUpdate": "2023-06-15T11:05:43"
///}

part 'hotel_model.g.dart';

@JsonSerializable()
class AmadeusHotelModel extends HotelModel {

  String? hotelId;

  @JsonKey(includeFromJson: false)
  @override
  get id => hotelId;

  @override
  set id(String? value) {
    hotelId = value;
  }


  String? chainCode, iataCode, lastUpdate;
  @JsonKey(fromJson: _parseDupId)
  int? dupeId;

  String? cityCode;

  @JsonKey(includeFromJson: false, includeToJson: false)
  List<AmadeusHotelOfferModel> offers;



  AmadeusHotelModel(
      {this.chainCode,
      this.iataCode,
      this.dupeId,
      this.hotelId,
      super.name,
      this.cityCode,
      this.offers = const [],
      this.lastUpdate});

  factory AmadeusHotelModel.fromJson(Map<String, dynamic> json) => _$AmadeusHotelModelFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusHotelModelToJson(this);

  static int? _parseDupId(dynamic value) {
    if (value is String) {
      return int.tryParse(value);
    } else if (value is num) {
      return value.toInt();
    }
    return null;
  }

  @override
  Future<double> fetchRating() async {
    if(hotelId == null) {
      return 0.0; // Return 0 if hotelId is not set
    }
    final HotelBookingUseCase usecase = locator.get<HotelBookingUseCase>();
    overallRating =  await  usecase.getHotelRating(hotelId!);
    return overallRating ?? 0;
  }
}

@JsonSerializable()
class AmadeusGeoCode {
  double? latitude, longitude;

  AmadeusGeoCode({this.latitude, this.longitude});

  factory AmadeusGeoCode.fromJson(Map<String, dynamic> json) => _$AmadeusGeoCodeFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusGeoCodeToJson(this);
}

@JsonSerializable()
class AmadeusAddress {
  String? countryCode;

  AmadeusAddress({this.countryCode});

  factory AmadeusAddress.fromJson(Map<String, dynamic> json) => _$AmadeusAddressFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusAddressToJson(this);
}
