
//    "type": "amadeusOAuth2Token",
//     "username": "maith<PERSON><PERSON><EMAIL>",
//     "application_name": "Family Link for Dev",
//     "client_id": "t3nw6nnHgW0AgPhJ53XWhLTt7rFh87ao",
//     "token_type": "Bearer",
//     "access_token": "EQhB4UdquQEuORSzmQVJ6KAaC5Ps",
//     "expires_in": 1799,
//     "state": "approved",
//     "scope": "
import 'package:json_annotation/json_annotation.dart';

part 'authorization.g.dart';

@JsonSerializable()
class AmadeusAuth {
  String? type;
  String? username;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'application_name')
  String? applicationName;
  @JsonKey(name: 'client_id')
  String? clientId;
  @Json<PERSON>ey(name: 'token_type')
  String? tokenType;
  @JsonKey(name: 'access_token')
  String? accessToken;
  @J<PERSON><PERSON><PERSON>(name: 'expires_in')
  int? expiresIn;
  String? state;
  String? scope;

  AmadeusAuth({
    this.type,
    this.username,
    this.applicationName,
    this.clientId,
    this.tokenType,
    this.accessToken,
    this.expiresIn,
    this.state,
    this.scope,
  });

  factory AmadeusAuth.fromJson(Map<String, dynamic> json) =>
      _$AmadeusAuthFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusAuthToJson(this);

}