import 'package:json_annotation/json_annotation.dart';

part 'hotel_rating.g.dart';

@JsonSerializable()
class AMAHotelRatingResponse {
  List<AMAHotelRatingModel> data;
  List<dynamic> warnings;
  List<dynamic> errors;

  AMAHotelRatingResponse({
    this.data = const [],
    this.warnings = const [],
    this.errors = const [],
  });

  factory AMAHotelRatingResponse.fromJson(Map<String, dynamic> json) =>
      _$AMAHotelRatingResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AMAHotelRatingResponseToJson(this);
}

@JsonSerializable()
class AMAHotelRatingModel {
  String? hotelId;
  int overallRating;
  int numberOfReviews;
  int numberOfRatings;

  AMAHotelRatingModel({
    this.hotelId,
    this.overallRating = 0,
    this.numberOfReviews = 0,
    this.numberOfRatings = 0,
  });

  factory AMAHotelRatingModel.fromJson(Map<String, dynamic> json) =>
      _$AMAHotelRatingModelFromJson(json);

  Map<String, dynamic> toJson() => _$AMAHotelRatingModelToJson(this);
}
