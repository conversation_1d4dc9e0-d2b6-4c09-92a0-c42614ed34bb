import 'package:family_app/data/model/flight_offer_model.dart';
import 'package:json_annotation/json_annotation.dart';

import 'flight_offer_model.dart';

part 'flight_order_model.g.dart';

@JsonSerializable()
class AmaFlightOrderModel extends FlightOfferModel {
  String? type;
  String? queuingOfficeId;
  List<Map<String, dynamic>> associatedRecords;
  @JsonKey(fromJson: fromFlightOffers, toJson: toFlightOffers)
  List<AmaFlightOfferModel> flightOffers;
  List<Map<String, dynamic>> travelers;
  Map<String, dynamic> ticketingAgreement;
  List<Map<String, dynamic>> automatedProcess;

  AmaFlightOrderModel(
      {this.type,
      super.id,
      this.queuingOfficeId,
      this.associatedRecords = const [],
      this.flightOffers = const [],
      this.travelers = const [],
      this.ticketingAgreement = const {},
      this.automatedProcess = const []});

  factory AmaFlightOrderModel.fromJson(Map<String, dynamic> json) => _$AmaFlightOrderModelFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AmaFlightOrderModelToJson(this);


}

@JsonSerializable()
class AmaFlightOrderResponse {
  AmaFlightOrderModel data;
  Map<String, dynamic> dictionaries;
  AmaFlightOrderResponse({required this.data, this.dictionaries = const {}});
}

//{
//     "type": "flight-order",
//     "id": "eJzTd9f3N3H2c%2FUGAAraAko%3D",
//     "queuingOfficeId": "NCE4D31SB",
//     "associatedRecords": [
//       {
//         "reference": "O4CNEK",
//         "creationDate": "2025-05-30T10:35:00.000",
//         "originSystemCode": "GDS",
//         "flightOfferId": "1"
//       }
//     ],
//     "flightOffers": [
//       {
//         "type": "flight-offer",
//         "id": "1",
//         "source": "GDS",
//         "nonHomogeneous": false,
//         "lastTicketingDate": "2025-06-13",
//         "itineraries": [
//           {
//             "segments": [
//               {
//                 "departure": {
//                   "iataCode": "CDG",
//                   "at": "2025-06-13T15:25:00"
//                 },
//                 "arrival": {
//                   "iataCode": "HEL",
//                   "at": "2025-06-13T19:20:00"
//                 },
//                 "carrierCode": "6X",
//                 "number": "3618",
//                 "aircraft": {
//                   "code": "733"
//                 },
//                 "duration": "PT2H55M",
//                 "id": "18",
//                 "numberOfStops": 0,
//                 "co2Emissions": [
//                   {
//                     "weight": 175,
//                     "weightUnit": "KG",
//                     "cabin": "ECONOMY"
//                   }
//                 ]
//               },
//               {
//                 "departure": {
//                   "iataCode": "HEL",
//                   "at": "2025-06-14T17:30:00"
//                 },
//                 "arrival": {
//                   "iataCode": "ICN",
//                   "at": "2025-06-15T08:20:00"
//                 },
//                 "carrierCode": "6X",
//                 "number": "3605",
//                 "aircraft": {
//                   "code": "733"
//                 },
//                 "duration": "PT8H50M",
//                 "id": "19",
//                 "numberOfStops": 0,
//                 "co2Emissions": [
//                   {
//                     "weight": 295,
//                     "weightUnit": "KG",
//                     "cabin": "ECONOMY"
//                   }
//                 ]
//               }
//             ]
//           }
//         ],
//         "price": {
//           "currency": "EUR",
//           "total": "144.98",
//           "base": "62.00",
//           "fees": [
//             {
//               "amount": "0.00",
//               "type": "TICKETING"
//             },
//             {
//               "amount": "0.00",
//               "type": "SUPPLIER"
//             },
//             {
//               "amount": "0.00",
//               "type": "FORM_OF_PAYMENT"
//             }
//           ],
//           "grandTotal": "144.98",
//           "billingCurrency": "EUR"
//         },
//         "pricingOptions": {
//           "fareType": [
//             "PUBLISHED"
//           ],
//           "includedCheckedBagsOnly": true
//         },
//         "validatingAirlineCodes": [
//           "6X"
//         ],
//         "travelerPricings": [
//           {
//             "travelerId": "1",
//             "fareOption": "STANDARD",
//             "travelerType": "ADULT",
//             "price": {
//               "currency": "EUR",
//               "total": "144.98",
//               "base": "62.00",
//               "taxes": [
//                 {
//                   "amount": "22.30",
//                   "code": "FR"
//                 },
//                 {
//                   "amount": "40.00",
//                   "code": "O4"
//                 },
//                 {
//                   "amount": "13.95",
//                   "code": "QX"
//                 },
//                 {
//                   "amount": "5.83",
//                   "code": "WL"
//                 },
//                 {
//                   "amount": "0.90",
//                   "code": "XU"
//                 }
//               ],
//               "refundableTaxes": "82.98"
//             },
//             "fareDetailsBySegment": [
//               {
//                 "segmentId": "18",
//                 "cabin": "ECONOMY",
//                 "fareBasis": "YCNV1",
//                 "class": "Y",
//                 "includedCheckedBags": {
//                   "quantity": 1
//                 }
//               },
//               {
//                 "segmentId": "19",
//                 "cabin": "ECONOMY",
//                 "fareBasis": "YCNV1",
//                 "class": "Y",
//                 "includedCheckedBags": {
//                   "quantity": 1
//                 }
//               }
//             ]
//           }
//         ]
//       }
//     ],
//     "travelers": [
//       {
//         "id": "1",
//         "dateOfBirth": "2000-01-01",
//         "gender": "MALE",
//         "name": {
//           "firstName": "Passenger",
//           "lastName": "Bot"
//         },
//         "contact": {
//           "purpose": "STANDARD",
//           "phones": [
//             {
//               "deviceType": "MOBILE",
//               "countryCallingCode": "82",
//               "number": "12345670"
//             }
//           ],
//           "emailAddress": "<EMAIL>"
//         }
//       }
//     ],
//     "ticketingAgreement": {
//       "option": "CONFIRM"
//     },
//     "automatedProcess": [
//       {
//         "code": "IMMEDIATE",
//         "queue": {
//           "number": "0",
//           "category": "0"
//         },
//         "officeId": "NCE4D31SB"
//       }
//     ]
//   },
//   "dictionaries": {
//     "locations": {
//       "ICN": {
//         "cityCode": "SEL",
//         "countryCode": "KR"
//       },
//       "CDG": {
//         "cityCode": "PAR",
//         "countryCode": "FR"
//       },
//       "HEL": {
//         "cityCode": "HEL",
//         "countryCode": "FI"
//       }
//     }
//   }