import 'package:json_annotation/json_annotation.dart';

import '../airline_model.dart';
import '../flight_offer_model.dart';

part 'flight_offer_model.g.dart';

@JsonSerializable()
class AmaFlightOfferResponse {
  Map<String, dynamic> meta;
  List<AmaFlightOfferModel> data;
  List<dynamic> warnings;
  List<dynamic> errors;
  Map<String, dynamic> dictionaries;

  AmaFlightOfferResponse({
    this.meta = const {},
    this.data = const [],
    this.warnings = const [],
    this.errors = const [],
    this.dictionaries = const {},
  });

  factory AmaFlightOfferResponse.fromJson(Map<String, dynamic> json) => _$AmaFlightOfferResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AmaFlightOfferResponseToJson(this);
}

@JsonSerializable()
class AmaFlightOfferData {

  @JsonKey(fromJson: fromFlightOffers, toJson: toFlightOffers)
  List<AmaFlightOfferModel> flightOffers;
  Map<String, dynamic> bookingRequirements;

  AmaFlightOfferData(this.flightOffers, this.bookingRequirements);

  factory AmaFlightOfferData.fromJson(Map<String, dynamic> json) => _$AmaFlightOfferDataFromJson(json);

  Map<String, dynamic> toJson() => _$AmaFlightOfferDataToJson(this);




}

@JsonSerializable()
class AmaFlightOfferModel extends FlightOfferModel {
  String? type;
  String? source;
  bool? instantTicketingRequired;
  bool? nonHomogeneous;
  bool? oneWay;
  bool? isUpsellOffer;
  String? lastTicketingDate;
  String? lastTicketingDateTime;
  int numberOfBookableSeats;
  @JsonKey(fromJson: _fromItineraries, toJson: _toItineraries)
  List<AmaFlightOfferItinerary> itineraries;
  @JsonKey(name: 'price', fromJson: _amaFlightOfferPriceFromJson, toJson: _amaFlightOfferPriceToJson)
  AmaFlightOfferPrice? priceObj;
  Map<String, dynamic> pricingOptions;
  List<String> validatingAirlineCodes;
  List<Map<String, dynamic>> travelerPricings;

  @override
  get price => priceObj?.total ?? 0.0;

  @override
  get currency => priceObj?.currency ?? 'USD';

  @override
  List<AirlineModel> airlines = [];

  @override
  List<String> get airlinesCode => validatingAirlineCodes;

  AmaFlightOfferModel({
    super.id,
    this.type,
    this.source,
    this.instantTicketingRequired,
    this.nonHomogeneous,
    this.oneWay,
    this.isUpsellOffer,
    this.lastTicketingDate,
    this.lastTicketingDateTime,
    this.numberOfBookableSeats = 0,
    this.itineraries = const [],
    this.travelerPricings = const [],
    this.priceObj,
    this.pricingOptions = const {},
    this.validatingAirlineCodes = const [],
  });

  factory AmaFlightOfferModel.fromJson(Map<String, dynamic> json) => _$AmaFlightOfferModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AmaFlightOfferModelToJson(this);

  static _fromItineraries(dynamic json) {
    if (json is List) {
      return json.map((e) => AmaFlightOfferItinerary.fromJson(e)).toList();
    } else if (json is Map<String, dynamic>) {
      return [AmaFlightOfferItinerary.fromJson(json)];
    }
    return [];
  }

  static _toItineraries(List<AmaFlightOfferItinerary> itineraries) {
    return itineraries.map((e) => e.toJson()).toList();
  }

}

@JsonSerializable()
class AmaFlightOfferItinerary {
  String? duration;
  @JsonKey(fromJson: _fromSegments, toJson: _toSegments)
  List<AmaFlightSegment> segments;

  AmaFlightOfferItinerary({
    this.duration,
    this.segments = const [],
  });

  factory AmaFlightOfferItinerary.fromJson(Map<String, dynamic> json) => _$AmaFlightOfferItineraryFromJson(json);

  Map<String, dynamic> toJson() => _$AmaFlightOfferItineraryToJson(this);

  static _fromSegments(dynamic json) {
    if (json is List) {
      return json.map((e) => AmaFlightSegment.fromJson(e)).toList();
    } else if (json is Map<String, dynamic>) {
      return [AmaFlightSegment.fromJson(json)];
    }
    return [];
  }

  static _toSegments(List<AmaFlightSegment> segments) {
    return segments.map((e) => e.toJson()).toList();
  }
}

@JsonSerializable()
class AmaFlightSegment {
  @JsonKey(fromJson: _amaAirportFromJSON, toJson: _amaAirportToJSON)
  final AmaAirport? departure;
  @JsonKey(fromJson: _amaAirportFromJSON, toJson: _amaAirportToJSON)
  final AmaAirport? arrival;
  final String? carrierCode;
  final String? number;
  final Map<String, dynamic> aircraft;
  final Map<String, dynamic> operating;
  final String? duration;
  final String? id;
  final int? numberOfStops;
  final bool? blacklistedInEU;

  AmaFlightSegment({
    this.departure,
    this.arrival,
    this.carrierCode,
    this.number,
    this.aircraft = const {},
    this.operating = const {},
    this.duration,
    this.id,
    this.numberOfStops,
    this.blacklistedInEU,
  });

  factory AmaFlightSegment.fromJson(Map<String, dynamic> json) => _$AmaFlightSegmentFromJson(json);

  Map<String, dynamic> toJson() => _$AmaFlightSegmentToJson(this);

  static AmaAirport? _amaAirportFromJSON(dynamic json) {
    if (json is Map<String, dynamic>) {
      return AmaAirport.fromJson(json);
    }
    return null;
  }

  static Map<String, dynamic>? _amaAirportToJSON(AmaAirport? airport) {
    if (airport != null) {
      return airport.toJson();
    }
    return null;
  }
}

@JsonSerializable()
class AmaAirport {
  final String iataCode;
  final String at;

  AmaAirport({required this.iataCode, required this.at});

  factory AmaAirport.fromJson(Map<String, dynamic> json) => _$AmaAirportFromJson(json);

  Map<String, dynamic> toJson() => _$AmaAirportToJson(this);
}

@JsonSerializable()
class AmaFlightOfferPrice {
  final String? currency;
  @JsonKey(fromJson: _parseDouble)
  final double total;
  @JsonKey(fromJson: _parseDouble)
  final double base;
  final List<dynamic> fees;
  final List<dynamic> additionalServices;
  @JsonKey(fromJson: _parseDouble)
  final double grandTotal;

  AmaFlightOfferPrice({
    this.currency,
    this.total = 0.0,
    this.base = 0.0,
    this.fees = const [],
    this.additionalServices = const [],
    this.grandTotal = 0.0,
  });

  factory AmaFlightOfferPrice.fromJson(Map<String, dynamic> json) => _$AmaFlightOfferPriceFromJson(json);

  Map<String, dynamic> toJson() => _$AmaFlightOfferPriceToJson(this);
}

@JsonSerializable()
class AmaTravelerPricing {
  final String? travelerId;
  final String? fareOption;
  final String? travelerType;
  @JsonKey(name: 'price', fromJson: _amaFlightOfferPriceFromJson, toJson: _amaFlightOfferPriceToJson)
  final AmaFlightOfferPrice? price;
  final List<AmaFareDetailsBySegment> fareDetailsBySegment;

  AmaTravelerPricing({
    this.travelerId,
    this.fareOption,
    this.travelerType,
    this.price,
    this.fareDetailsBySegment = const [],
  });

  factory AmaTravelerPricing.fromJson(Map<String, dynamic> json) => _$AmaTravelerPricingFromJson(json);

  Map<String, dynamic> toJson() => _$AmaTravelerPricingToJson(this);
}

@JsonSerializable()
class AmaFareDetailsBySegment {
  final String? segmentId;
  final String? cabin;
  final String? fareBasis;
  @JsonKey(name: 'class')
  final String? segmentClass;
  final Map<String, dynamic>? includedCheckedBags;

  AmaFareDetailsBySegment({
    this.segmentId,
    this.cabin,
    this.fareBasis,
    this.segmentClass,
    this.includedCheckedBags = const {},
  });

  factory AmaFareDetailsBySegment.fromJson(Map<String, dynamic> json) => _$AmaFareDetailsBySegmentFromJson(json);
}

double _parseDouble(dynamic value) {
  if (value is String) {
    return double.tryParse(value) ?? 0.0;
  } else if (value is num) {
    return value.toDouble();
  }
  return 0;
}

AmaFlightOfferPrice? _amaFlightOfferPriceFromJson(dynamic json) {
  if (json is Map<String, dynamic>) {
    return AmaFlightOfferPrice.fromJson(json);
  }
  return null;
}

Map<String, dynamic>? _amaFlightOfferPriceToJson(AmaFlightOfferPrice? price) {
  if (price != null) {
    return price.toJson();
  }
  return null;
}

fromFlightOffers(dynamic json) {
  if (json is List) {
    return json.map((e) => AmaFlightOfferModel.fromJson(e)).toList();
  } else if (json is Map<String, dynamic>) {
    return [AmaFlightOfferModel.fromJson(json)];
  }
  return [];
}

toFlightOffers(List<AmaFlightOfferModel> flightOffers) {
  return flightOffers.map((e) => e.toJson()).toList();
}
