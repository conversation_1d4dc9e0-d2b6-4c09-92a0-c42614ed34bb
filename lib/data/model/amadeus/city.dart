//"type": "location",
//"subType": "city",
//"name": "Ho Chi Minh City",
//"iataCode": "SGN",

import 'package:json_annotation/json_annotation.dart';

part 'city.g.dart';

@JsonSerializable()
class AmadeusCity {
  String? type;
  String? subType;
  String? name;
  String? iataCode;


  AmadeusCity({
    this.type,
    this.subType,
    this.name,
    this.iataCode,
  });

  factory AmadeusCity.fromJson(Map<String, dynamic> json) =>
      _$AmadeusCityFromJson(json);

  Map<String, dynamic> toJson() => _$AmadeusCityToJson(this);

}