// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

part 'family.g.dart';

@JsonSerializable()
class Family {
  @JsonKey(name: 'created_at')
  String? createdAt;
  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  String? updatedAt;
  String? uuid;
  @Json<PERSON>ey(name: 'family_uuid')
  String? familyUuid;
  @Json<PERSON><PERSON>(name: 'family_member_uuid')
  String? familyMemberUuid;
  String? email;
  String? relationship;
  int? status;
  String? role;
  @J<PERSON><PERSON><PERSON>(name: 'full_name')
  String? fullName;
  // @Json<PERSON><PERSON>(name: 'photo_url')
  // String? photoUuid; //photo_url contains the uuid of the photo
  @<PERSON><PERSON><PERSON><PERSON>(name: 'invited_by')
  String? invitedBy;
  @JsonKey(name: 'invited_at')
  String? invitedAt;
  @Json<PERSON>ey(name: 'accepted_at')
  String? acceptedAt;
  @J<PERSON><PERSON><PERSON>(name: 'rejected_at')
  String? rejectedAt;
  @J<PERSON><PERSON><PERSON>(name: 'is_active')
  int? isActive;
  String? note;
  @Json<PERSON><PERSON>(name: 'family_name')
  String? familyName;
  @Json<PERSON>ey(name: 'members')
  int? members;
  @Json<PERSON>ey(name: 'active_family')
  String? activeFamily;

  @JsonKey(name: 'active_family_name')
  String? activeFamilyName;

  @JsonKey(name: 'birthday')
  String? birthday;

  @JsonKey(name: 'show_year')
  int? showYear;

  @JsonKey(name: 'photo_url')
  String? photoUrl;

  @JsonKey(name: 'description')
  String? description;

  Family(
      {this.createdAt,
      this.updatedAt,
      this.uuid,
      this.familyUuid,
      this.familyMemberUuid,
      this.email,
      this.relationship,
      this.status,
      this.role,
      this.fullName,
      this.photoUrl,
      this.invitedBy,
      this.invitedAt,
      this.acceptedAt,
      this.rejectedAt,
      this.isActive,
      this.familyName,
      this.members,
      this.activeFamily,
      this.activeFamilyName,
      this.birthday,
      this.showYear,
      this.note,
      this.description});

  factory Family.fromJson(Map<String, dynamic> json) => _$FamilyFromJson(json);

  Map<String, dynamic> toJson() => _$FamilyToJson(this);

  Family copyWith({
    String? createdAt,
    String? updatedAt,
    String? uuid,
    String? familyUuid,
    String? familyMemberUuid,
    String? email,
    String? relationship,
    int? status,
    String? role,
    String? fullName,
    String? photoUrl,
    String? invitedBy,
    String? invitedAt,
    String? acceptedAt,
    String? rejectedAt,
    int? isActive,
    String? note,
    String? familyName,
    int? members,
    String? activeFamily,
    String? activeFamilyName,
    String? birthday,
    int? showYear,
    String? description,
  }) {
    return Family(
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      uuid: uuid ?? this.uuid,
      familyUuid: familyUuid ?? this.familyUuid,
      familyMemberUuid: familyMemberUuid ?? this.familyMemberUuid,
      email: email ?? this.email,
      relationship: relationship ?? this.relationship,
      status: status ?? this.status,
      role: role ?? this.role,
      fullName: fullName ?? this.fullName,
      photoUrl: photoUrl ?? this.photoUrl,
      invitedBy: invitedBy ?? this.invitedBy,
      invitedAt: invitedAt ?? this.invitedAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      rejectedAt: rejectedAt ?? this.rejectedAt,
      isActive: isActive ?? this.isActive,
      note: note ?? this.note,
      familyName: familyName ?? this.familyName,
      members: members ?? this.members,
      activeFamily: activeFamily ?? this.activeFamily,
      activeFamilyName: activeFamilyName ?? this.activeFamilyName,
      birthday: birthday ?? this.birthday,
      showYear: showYear ?? this.showYear,
      description: description ?? this.description,
    );
  }

  String get firstName {
    if (fullName == null || fullName!.isEmpty) return '';
    final parts = fullName!.split(' ');
    return parts.isNotEmpty ? parts.first : '';
  }

  String get lastName {
    if (fullName == null || fullName!.isEmpty) return '';
    final parts = fullName!.split(' ');
    return parts.length > 1 ? parts.sublist(1).join(' ') : '';
  }

  String toString() {
    return 'Profile (family) {  uuid: $uuid, familyUuid: $familyUuid,   email: $email,  role: $role, fullName: $fullName,    isActive: $isActive,  familyName: $familyName, members: $members, activeFamily: $activeFamily} \n';
  }
}
