import 'package:json_annotation/json_annotation.dart';

/// type : "airline"
/// iataCode : "BA"
/// icaoCode : "BAW"
/// businessName : "BRITISH AIRWAYS"
/// commonName : "BRITISH A/W"
///
part 'airline_model.g.dart';

@JsonSerializable()
class AirlineModel {
  AirlineModel({
    this.type,
    this.iataCode,
    this.icaoCode,
    this.businessName,
    this.commonName,
  });

  String? type;
  String? iataCode;
  String? icaoCode;
  String? businessName;
  String? commonName;

  factory AirlineModel.fromJson(Map<String, dynamic> json) =>
      _$AirlineModelFromJson(json);

  Map<String, dynamic> toJson() => _$AirlineModelToJson(this);


}
