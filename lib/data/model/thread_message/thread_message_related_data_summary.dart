import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'thread_message_related_data_summary.g.dart';

@JsonSerializable()
class ThreadMessageRelatedDataSummary {
  final Map<String, int> votes;
  final int total;

  ThreadMessageRelatedDataSummary({
    required this.votes,
    required this.total,
  });

  factory ThreadMessageRelatedDataSummary.fromJson(dynamic summaryJson) {
    if (summaryJson == null || (summaryJson is String && summaryJson.isEmpty)) {
      return ThreadMessageRelatedDataSummary(votes: {}, total: 0);
    }

    try {
      Map<String, dynamic> decoded;
      if (summaryJson is String) {
        decoded = jsonDecode(summaryJson);
      } else if (summaryJson is Map<String, dynamic>) {
        decoded = summaryJson;
      } else if (summaryJson is Map) {
        decoded = Map<String, dynamic>.from(summaryJson);
      } else {
        return ThreadMessageRelatedDataSummary(votes: {}, total: 0);
      }

      // Create a copy to avoid modifying the original
      final Map<String, dynamic> voteData = Map.from(decoded);
      
      // Extract total field if it exists (it's reliable when present)
      int totalFromJson = 0;
      if (voteData.containsKey('total')) {
        final totalValue = voteData.remove('total'); // Remove and get the value
        if (totalValue is int) {
          totalFromJson = totalValue;
        } else if (totalValue is String) {
          totalFromJson = int.tryParse(totalValue) ?? 0;
        }
      }
      
      // Convert all remaining fields to vote counts
      final Map<String, int> votes = voteData.map((k, v) => 
        MapEntry(k, v is int ? v : int.tryParse(v.toString()) ?? 0));
      
      // Use total from JSON if available, otherwise calculate from votes
      final int finalTotal = voteData.containsKey('total') 
          ? totalFromJson 
          : votes.values.fold(0, (sum, count) => sum + count);
      
      return ThreadMessageRelatedDataSummary(votes: votes, total: finalTotal);
    } catch (e) {
      return ThreadMessageRelatedDataSummary(votes: {}, total: 0);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = Map.from(votes);
    result['total'] = total;
    return result;
  }

  int getVoteCountByItemUuid(String itemUuid) {
    return votes[itemUuid] ?? 0;
  }
}