import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

import '../account.dart';
import 'thread_message_related_data_items.dart';
import 'thread_message_related_data_summary.dart';
import 'thread_message_related_data_poll_details.dart';

part 'thread_message_related_data.g.dart';

@JsonSerializable()
class ThreadMessageRelatedData {
  @Json<PERSON>ey(name: '_')
  int? underscore;
  String? uuid;
  int? status;
  @Json<PERSON><PERSON>(name: 'user_id')
  String? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'thread_id')
  String? threadId;
  @J<PERSON><PERSON><PERSON>(name: 'message_id')
  String? messageId;
  String? name;
  @Json<PERSON>ey(name: 'from_date')
  String? fromDate;
  @Json<PERSON>ey(name: 'to_date')
  String? toDate;
  dynamic items;
  dynamic summary;
  @J<PERSON><PERSON>ey(name: 'poll_details')
  dynamic pollDetails;
  @<PERSON>son<PERSON>ey(name: 'extra_data')
  dynamic extraData;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  String? createdAt;
  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  String? updatedAt;
  @J<PERSON><PERSON><PERSON>(name: 'deleted_at')
  String? deletedAt;
  bool? multiple;
  @J<PERSON><PERSON><PERSON>(name: 'latitude')
  double? latitude;
  @JsonKey(name: 'longitude')
  double? longitude;
  @JsonKey(name: 'address')
  String? address;
  @JsonKey(name: 'photoUrl')
  String? photoUrl;
  @JsonKey(name: 'city')
  String? city;
  @JsonKey(name: 'time')
  String? time;
  @JsonKey(name: 'description')
  String? description;

  ThreadMessageRelatedData({
    this.underscore,
    this.uuid,
    this.status,
    this.createdAt,
    this.deletedAt,
    this.extraData,
    this.fromDate,
    this.items,
    this.messageId,
    this.name,
    this.threadId,
    this.summary,
    this.pollDetails,
    this.toDate,
    this.updatedAt,
    this.userId,
    this.multiple = false,
    this.latitude,
    this.longitude,
    this.address,
    this.photoUrl,
    this.city,
    this.time,
    this.description,
  });

  /// Type-aware fromJson: only map poll fields for poll, generic for others
  static ThreadMessageRelatedData fromJson(
    Map<String, dynamic> json, {
    String? messageType,
  }) {
    if (messageType == 'poll' || messageType == 'event') {
      // Map all fields for poll and event
      return _$ThreadMessageRelatedDataFromJson(json);
    } else {
      // Only map generic fields for other types, provide safe defaults
      return ThreadMessageRelatedData(
        underscore: (json['_'] as num?)?.toInt(),
        uuid: json['uuid'] as String?,
        status: (json['status'] as num?)?.toInt(),
        createdAt: json['created_at'] as String?,
        deletedAt: json['deleted_at'] as String?,
        extraData: json['extra_data'],
        fromDate: json['from_date'] as String?,
        items: json['items'],
        messageId: json['message_id'] as String? ?? '',
        name: json['name'] as String? ?? '',
        threadId: json['thread_id'] as String? ?? '',
        toDate: json['to_date'] as String?,
        updatedAt: json['updated_at'] as String?,
        userId: json['user_id'] as String?,
        multiple: json['multiple'] as bool? ?? false,
        summary: null,
        pollDetails: null,
        latitude: (json['latitude'] as num?)?.toDouble(),
        longitude: (json['longitude'] as num?)?.toDouble(),
        address: json['address'] as String?,
        photoUrl: json['photoUrl'] as String?,
        city: json['city'] as String?,
        time: json['time'] as String?,
        description: json['description'] as String?,
      );
    }
  }

  Map<String, dynamic> toJson() => _$ThreadMessageRelatedDataToJson(this);

  /// Helper method to decode the `items` field
  ThreadMessageRelatedDataItems getDecodedItems() {
    if (items == null) return ThreadMessageRelatedDataItems(items: []);
    try {
      return ThreadMessageRelatedDataItems.fromJson(items);
    } catch (_) {
      return ThreadMessageRelatedDataItems(items: []);
    }
  }

  /// Helper method to decode the `summary` field (poll only)
  ThreadMessageRelatedDataSummary getDecodedSummary() {
    if (summary == null)
      return ThreadMessageRelatedDataSummary(votes: {}, total: 0);
    try {
      return ThreadMessageRelatedDataSummary.fromJson(summary);
    } catch (_) {
      return ThreadMessageRelatedDataSummary(votes: {}, total: 0);
    }
  }

  /// Method to find the item with the best vote (poll only)
  MapEntry<String, int>? getItemWithBestTotalCountVote() {
    final decodedSummary = getDecodedSummary();
    if (decodedSummary.votes.isEmpty) {
      return const MapEntry<String, int>('', 0);
    }
    return decodedSummary.votes.entries
        .reduce((a, b) => a.value > b.value ? a : b);
  }

  /// Helper method to get the item info with the best total count vote (poll only)
  ThreadMessageRelatedDataItem? getItemWithBestTotalCountVoteInfo() {
    final bestVoteEntry = getItemWithBestTotalCountVote();
    if (bestVoteEntry == null ||
        bestVoteEntry.key.isEmpty ||
        bestVoteEntry.value == 0) {
      return null;
    }
    final decodedItems = getDecodedItems();
    for (final item in decodedItems.items) {
      if (item.uuid == bestVoteEntry.key) {
        return item;
      }
    }
    return null;
  }

  /// Helper method to decode the `pollDetails` field (poll only)
  ThreadMessageRelatedDataPollDetails getDecodedPollDetails() {
    if (pollDetails == null)
      return ThreadMessageRelatedDataPollDetails(voters: {});
    try {
      return ThreadMessageRelatedDataPollDetails.fromJson(pollDetails);
    } catch (_) {
      return ThreadMessageRelatedDataPollDetails(voters: {});
    }
  }

  /// Helper method to check if a user has voted in the poll (poll only)
  bool hasUserVoted(String? userUuid) {
    if (userUuid == null || userUuid.isEmpty) {
      return false;
    }
    try {
      final pollDetails = getDecodedPollDetails();
      return pollDetails.voters.containsKey(userUuid);
    } catch (_) {
      return false;
    }
  }

  /// Helper method to get the vote count for a specific item UUID (poll only)
  int getVoteCountByItemUuid(String itemUuid) {
    try {
      final summary = getDecodedSummary();
      return summary.getVoteCountByItemUuid(itemUuid);
    } catch (_) {
      return 0;
    }
  }

  /// Helper method to get voters' details (including photo URLs) for each item (poll only)
  Map<String, List<Account>> getVotersWithPhotoUrls(List<Account> accounts) {
    try {
      final voters = getDecodedPollDetails();
      return voters.getVotersWithPhotoUrls(accounts);
    } catch (_) {
      return {};
    }
  }

  /// Helper method to get options with their voters (poll only)
  List<ItemWithVoters> getItemsWithVoters(List<Account> accounts) {
    try {
      final decodedItems = getDecodedItems();
      final votersWithPhotoUrls = getVotersWithPhotoUrls(accounts);
      return decodedItems.items.map((item) {
        final itemUuid = item.uuid;
        final voters = votersWithPhotoUrls[itemUuid] ?? [];
        return ItemWithVoters(
          uuid: itemUuid,
          name: item.name,
          voters: voters,
        );
      }).toList();
    } catch (_) {
      return [];
    }
  }

  /// Helper method to parse `toDate` into a DateTime object (poll only)
  DateTime getEndDate() {
    if (toDate == null || toDate!.isEmpty) {
      return DateTime.now();
    }
    try {
      final formatter = DateFormat("yyyy-MM-dd HH:mm:ss");
      return formatter.parse(toDate!);
    } catch (_) {
      return DateTime.now();
    }
  }

  /// Method to check if the poll has ended (poll only)
  bool isEnded() {
    if (fromDate == null || toDate == null) return false;
    try {
      final endDate = getEndDate();
      final now = DateTime.now();
      return now.isAfter(endDate);
    } catch (_) {
      return false;
    }
  }
}

class ItemWithVoters {
  final String? uuid;
  final String name;
  final List<Account>? voters;

  ItemWithVoters({
    required this.name,
    this.uuid,
    this.voters,
  });

  @override
  String toString() {
    return 'ItemWithVoters(uuid: $uuid, name: $name, voters: ${voters?.map((v) => v.fullName).toList()})';
  }
}
