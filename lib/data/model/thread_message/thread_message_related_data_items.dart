import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'thread_message_related_data_items.g.dart';

@JsonSerializable()
class ThreadMessageRelatedDataItem {
  final String name;
  final String uuid;

  ThreadMessageRelatedDataItem({
    required this.name,
    required this.uuid,
  });

  factory ThreadMessageRelatedDataItem.fromJson(Map<String, dynamic> json) =>
      _$ThreadMessageRelatedDataItemFromJson(json);

  Map<String, dynamic> toJson() => _$ThreadMessageRelatedDataItemToJson(this);
}

@JsonSerializable()
class ThreadMessageRelatedDataItems {
  final List<ThreadMessageRelatedDataItem> items;

  ThreadMessageRelatedDataItems({required this.items});

  factory ThreadMessageRelatedDataItems.fromJson(dynamic itemsJson) {
    if (itemsJson == null) {
      return ThreadMessageRelatedDataItems(items: []);
    }

    try {
      List<dynamic> decoded;
      if (itemsJson is String) {
        if (itemsJson.isEmpty) return ThreadMessageRelatedDataItems(items: []);
        decoded = jsonDecode(itemsJson);
      } else if (itemsJson is List) {
        decoded = itemsJson;
      } else if (itemsJson is Map && itemsJson['items'] is List) {
        decoded = itemsJson['items'];
      } else {
        return ThreadMessageRelatedDataItems(items: []);
      }

      final List<ThreadMessageRelatedDataItem> items = decoded
          .map((item) => ThreadMessageRelatedDataItem.fromJson(item as Map<String, dynamic>))
          .toList();

      return ThreadMessageRelatedDataItems(items: items);
    } catch (e) {
      // Return an empty list if decoding fails
      return ThreadMessageRelatedDataItems(items: []);
    }
  }

  Map<String, dynamic> toJson() {
    final List<Map<String, dynamic>> result =
        items.map((item) => item.toJson()).toList();
    return {'items': result};
  }
}
