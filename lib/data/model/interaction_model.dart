import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

part 'interaction_model.g.dart';

@JsonSerializable()
class InteractionModel {
  @Json<PERSON>ey(name: '_')
  final int? id;
  final String? uuid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'attachment_id')
  final String? attachmentId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'parent_id')
  final String? parentId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'deleted_at')
  final String? deletedAt;
  @J<PERSON><PERSON><PERSON>(name: 'family_id')
  final String? familyId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final String? userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_name')
  final String? userName;
  @J<PERSON><PERSON>ey(name: 'file_id')
  final String? fileId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'activity_type')
  final String? activityType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'activity_content')
  final String? activityContent;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'extra_data')
  final String? extraData;

  const InteractionModel({
    this.id,
    this.uuid,
    this.attachmentId,
    this.parentId,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.familyId,
    this.userId,
    this.userName,
    this.fileId,
    this.activityType,
    this.activityContent,
    this.extraData,
  });

  @override
  String toString() {
    return 'InteractionModel(id: $id, uuid: $uuid, attachmentId: $attachmentId, parentId: $parentId, createdAt: $createdAt, updatedAt: $updatedAt, deletedAt: $deletedAt, familyId: $familyId, userId: $userId, userName: $userName, fileId: $fileId, activityType: $activityType, activityContent: $activityContent, extraData: $extraData)';
  }

  factory InteractionModel.fromJson(Map<String, dynamic> json) {
    return _$InteractionModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$InteractionModelToJson(this);

  InteractionModel copyWith({
    int? id,
    String? uuid,
    String? attachmentId,
    String? parentId,
    String? createdAt,
    String? updatedAt,
    String? deletedAt,
    String? familyId,
    String? userId,
    String? userName,
    String? fileId,
    String? activityType,
    String? activityContent,
    String? extraData,
  }) {
    return InteractionModel(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      attachmentId: attachmentId ?? this.attachmentId,
      parentId: parentId ?? this.parentId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      familyId: familyId ?? this.familyId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      fileId: fileId ?? this.fileId,
      activityType: activityType ?? this.activityType,
      activityContent: activityContent ?? this.activityContent,
      extraData: extraData ?? this.extraData,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! InteractionModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      uuid.hashCode ^
      attachmentId.hashCode ^
      parentId.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      deletedAt.hashCode ^
      familyId.hashCode ^
      userId.hashCode ^
      userName.hashCode ^
      fileId.hashCode ^
      activityType.hashCode ^
      activityContent.hashCode ^
      extraData.hashCode;
}
