import 'package:json_annotation/json_annotation.dart';

part 'category.g.dart';

@JsonSerializable()
class Category {
  String uuid;

  @Json<PERSON>ey(name: 'created_at')
  String? createdAt;

  @J<PERSON><PERSON>ey(name: 'updated_at')
  String? updatedAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'deleted_at')
  String? deletedAt;

  int? status;
  String? name;
  String? image;
  String? color;

  @J<PERSON><PERSON>ey(name: 'created_by')
  String? createdBy;

  Category({
    required this.uuid,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.status,
    this.name,
    this.image,
    this.color,
    this.createdBy,
  });

  String toString() {
    return 'Category{uuid: $uuid,  name: $name }';
  }

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);
  Map<String, dynamic> toJson() => _$CategoryToJson(this);
}
