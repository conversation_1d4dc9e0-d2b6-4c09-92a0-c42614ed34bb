import 'package:collection/collection.dart';
import 'package:family_app/data/model/interaction_model.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'memory_model.g.dart';

@JsonSerializable()
class MemoryModel {
  final String? uuid;
  final int? status;
  @J<PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;
  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'family_id')
  final String? familyId;
  @Json<PERSON>ey(name: 'user_id')
  final String? userId;
  @Json<PERSON><PERSON>(name: 'file_id')
  final String? fileId;
  @J<PERSON><PERSON><PERSON>(name: 'activity_id')
  final String? activityId;
  @J<PERSON><PERSON><PERSON>(name: 'attachment_type')
  final String? attachmentType;
  final String? caption;
  final String? lat;
  final String? lon;
  final String? tags;
  final String? members;
  @Json<PERSON>ey(name: 'extra_data')
  final String? extraData;
  final List<StorageModel>? files;
  final List<InteractionModel>? interact;
  final List<InteractionModel>? comments;
  // Non-api field
  @J<PERSON><PERSON>ey(includeFromJson: false, includeToJson: false)
  final bool isLiked;
  @J<PERSON><PERSON>ey(includeFromJson: false, includeToJson: false)
  final List<String> extraTags;

  const MemoryModel({
    this.uuid,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.familyId,
    this.userId,
    this.fileId,
    this.activityId,
    this.attachmentType,
    this.caption,
    this.lat,
    this.lon,
    this.tags,
    this.members,
    this.extraData,
    this.files,
    this.interact,
    this.comments,
    this.isLiked = false,
    this.extraTags = const [],
  });

  @override
  String toString() {
    return 'MemoryModel(uuid: $uuid, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, familyId: $familyId, userId: $userId, fileId: $fileId, activityId: $activityId, attachmentType: $attachmentType, caption: $caption, lat: $lat, lon: $lon, tags: $tags, members: $members, extraData: $extraData, files: $files)';
  }

  factory MemoryModel.fromJson(Map<String, dynamic> json) {
    return _$MemoryModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$MemoryModelToJson(this);

  MemoryModel copyWith({
    String? uuid,
    int? status,
    String? createdAt,
    String? updatedAt,
    String? familyId,
    String? userId,
    String? fileId,
    String? activityId,
    String? attachmentType,
    String? caption,
    String? lat,
    String? lon,
    String? tags,
    String? members,
    String? extraData,
    List<StorageModel>? files,
    List<InteractionModel>? interact,
    List<InteractionModel>? comments,
    bool? isLiked,
    List<String>? extraTags,
  }) {
    return MemoryModel(
      uuid: uuid ?? this.uuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      familyId: familyId ?? this.familyId,
      userId: userId ?? this.userId,
      fileId: fileId ?? this.fileId,
      activityId: activityId ?? this.activityId,
      attachmentType: attachmentType ?? this.attachmentType,
      caption: caption ?? this.caption,
      lat: lat ?? this.lat,
      lon: lon ?? this.lon,
      tags: tags ?? this.tags,
      members: members ?? this.members,
      extraData: extraData ?? this.extraData,
      files: files ?? this.files,
      interact: interact ?? this.interact,
      comments: comments ?? this.comments,
      isLiked: isLiked ?? this.isLiked,
      extraTags: extraTags ?? this.extraTags,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! MemoryModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      uuid.hashCode ^
      status.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      familyId.hashCode ^
      userId.hashCode ^
      fileId.hashCode ^
      activityId.hashCode ^
      attachmentType.hashCode ^
      caption.hashCode ^
      lat.hashCode ^
      lon.hashCode ^
      tags.hashCode ^
      members.hashCode ^
      extraData.hashCode ^
      files.hashCode ^
      interact.hashCode ^
      comments.hashCode ^
      isLiked.hashCode ^
      extraTags.hashCode;
}
