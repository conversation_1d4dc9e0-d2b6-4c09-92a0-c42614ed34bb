import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'ai_butler_api.g.dart';

@RestApi()
abstract class AIButlerAPI {
  factory AIButlerAPI(Dio dio, {String? baseUrl}) = _AIButlerAPI;

  // @POST('/ai/role/config/user_role')
  // Future<BaseResponse> setAiUserConfig(@Body() Map<String, dynamic> body);

  // @GET('/ai/role/config/user_role')
  // Future<BaseResponse> getAiUserConfig(@Queries() Map<String, dynamic> queries);
}
