import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'activity_api.g.dart';

@RestApi()
abstract class ActivityAPI {
  factory ActivityAPI(Dio dio, {String? baseUrl}) = _ActivityAPI;

  @POST('/activity/')
  Future<BaseResponse> createActivity(@Body() Map<String, dynamic> body);

  @GET('/activity/all')
  Future<BaseResponse> getUserActivity(@Queries() Map<String, dynamic> queries);

  @GET('/activity/{id}')
  Future<BaseResponse> getActivityById(@Path() String id, @Queries() Map<String, dynamic> queries);

  @GET('/activity/all?familyid={id}')
  Future<BaseResponse> getAllActivity(@Path() String id, @Queries() Map<String, dynamic> queries);

  @PUT('/activity/{id}')
  Future<BaseResponse> updateActivity(@Path() String id, @Body() Map<String, dynamic> body);

  @PUT('/activity/itinerary/{id}')
  Future<BaseResponse> updateActivityItinerary(@Path() String id, @Body() Map<String, dynamic> body);

  @DELETE('/activity/{id}')
  Future<BaseResponse> deleteActivity(@Path() String id);

  @POST('/activity/item/')
  Future<BaseResponse> createItemInActivity(@Body() FormData body);
  @GET('/activity/item/all')
  Future<BaseResponse> getAllItemInActivity(@Queries() Map<String, dynamic> queries);

  @DELETE('/activity/item/{id}')
  Future<BaseResponse> deleteItemInActivity(@Path() String id);

  @PUT('/activity/item/{id}')
  Future<BaseResponse> updateItemInActivity(@Path() String id, @Queries() Map<String, dynamic> queries);

  @GET('/activity/item/{id}')
  Future<BaseResponse> getActivityByFamily(@Path() String id);

  @GET('/activity/item/log/{activityId}/all')
  Future<BaseResponse> getAllItemLogInActivity(@Path() String activityId);
}
