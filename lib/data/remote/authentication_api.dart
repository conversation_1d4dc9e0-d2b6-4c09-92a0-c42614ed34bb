import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'authentication_api.g.dart';

@RestApi()
abstract class AuthenticationAPI {
  factory AuthenticationAPI(Dio dio, {String? baseUrl}) = _AuthenticationAPI;

  @POST('/login')
  Future<BaseResponse> login(@Body() Map<String, dynamic> body);

  @POST('/oauth/google')
  Future<BaseResponse> loginWithGoogle(@Body() Map<String, dynamic> body);

  @POST('/oauth/apple')
  Future<BaseResponse> loginWithApple(@Body() Map<String, dynamic> body);

  @POST('/logout')
  Future<BaseResponse> logout();

  @POST('/login/otp')
  Future<BaseResponse> sendOTP(@Body() Map<String, dynamic> body);

  @POST('/login/otp/verify')
  Future<BaseResponse> verifyOTP(@Body() Map<String, dynamic> body);

  @POST('/signup')
  Future<BaseResponse> signUp(@Body() Map<String, dynamic> body);

  @POST('/family/validate/check')
  Future<BaseResponse> verifyEmail(@Body() Map<String, dynamic> body);

  @POST('/forgot')
  Future<BaseResponse> forgotPass(@Body() Map<String, dynamic> body);
}
