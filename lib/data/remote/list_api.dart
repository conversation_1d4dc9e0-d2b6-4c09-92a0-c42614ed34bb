import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'list_api.g.dart';

@RestApi()
abstract class ListAPI {
  factory ListAPI(Dio dio, {String? baseUrl}) = _ListAPI;

  @POST('/list/')
  Future<BaseResponse> createList(@Body() Map<String, dynamic> body);

  @PUT('/list/{id}')
  Future<BaseResponse> updateList(@Path() String id, @Body() Map<String, dynamic> body);

  @GET('/list/all/{id}')
  Future<BaseResponse> getListByFamily(@Path() String id, @Queries() Map<String, dynamic> queries);

  @GET('/list/{id}')
  Future<BaseResponse> getListDetail(@Path() String id);

  @DELETE('/list/{id}')
  Future<BaseResponse> deleteList(@Path() String id);

  @POST('/list/item/')
  Future<BaseResponse> createItemInList(@Body() Map<String, dynamic> body);

  @GET('/list/item/{id}')
  Future<BaseResponse> getItemDetail(@Path() String id);

  @GET('/list/item/log/{listId}/all')
  Future<BaseResponse> getAllItemLogInList(@Path() String listId);

  @DELETE('/list/item/{id}')
  Future<BaseResponse> deleteItemInList(@Path() String id);

  @PUT('/list/item/{id}')
  Future<BaseResponse> updateItemInList(@Path() String id, @Queries() Map<String, dynamic> queries);

  @GET('/list/item/all')
  Future<BaseResponse> getAllItemInList(@Queries() Map<String, dynamic> queries);
}
