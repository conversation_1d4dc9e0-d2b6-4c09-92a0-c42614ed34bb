import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';

import '../model/base_response.dart';

part 'thread_api.g.dart';

@RestApi()
abstract class ThreadAPI {

  factory ThreadAPI(Dio dio, {String? baseUrl}) = _ThreadAPI;

  @GET('/thread/all/{id}')
  Future<BaseResponse> getAllThread(@Path() String id);

  @POST('/thread/')
  Future<BaseResponse> createThread(@Body() Map<String, dynamic> body);

  @GET('/thread/{id}')
  Future<BaseResponse> getThreadDetail(@Path() String id);

  @GET('/thread/message/{id}')
  Future<BaseResponse> getThreadMessage(@Path() String id);

  @POST('/thread/message/{id}')
  Future<BaseResponse> createThreadMessage(@Body() Map<String, dynamic> body, @Path() String id);

  @PUT('/thread/{id}')
  Future<BaseResponse> updateMemberThread(@Body() Map<String, dynamic> body, @Path() String id);

  @DELETE('/thread/message/{id}')
  Future<BaseResponse> deleteThreadMessage(@Path() String id);

  @PUT('/thread/message/read/{id}')
  Future<BaseResponse> markMessageAsRead(@Path() String id);
}