import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'family_api.g.dart';

// Steps to add new API
// 1. add the new API endpoint to the family_api.dart
// 2. run the command `flutter pub run build_runner build` to generate the new API
// 3. import the new API to the repository file , e.g. family_repository.dart
// 4. add a new interface in ifamily_repository.dart
// 5. Use the new API in the code

@RestApi()
abstract class FamilyApi {
  factory FamilyApi(Dio dio, {String? baseUrl}) = _FamilyApi;

  @GET('/family/{userId}')
  Future<BaseResponse> getUserInfo(@Path() String userId);

  @GET('/family/me')
  Future<BaseResponse> getMyProfile();

  @GET('/family/members/{id}')
  Future<BaseResponse> getUserInFamily(@Path() String id);

  @PUT('/family/update/me')
  Future<BaseResponse> updateFamilyInfo(@Body() Map<String, dynamic> body);

  @POST('/family/invite')
  Future<BaseResponse> invite(@Body() Map<String, dynamic> body);

  @POST('/family/invite')
  Future<BaseResponse> inviteNoParameter();

  @GET('/family/members')
  Future<BaseResponse> getFamilyMembers(@Queries() Map<String, dynamic> queries);

  @GET('/family/belong')
  Future<BaseResponse> getFamilyBelong();

  @PUT("/family/accept/{id}")
  Future<BaseResponse> acceptInvitation(@Path() String id);

  @PUT("/family/decline/{id}")
  Future<BaseResponse> declineInvitation(@Path() String id);

  @DELETE("/family/kick/{id}")
  Future<BaseResponse> kickMember(@Path() String id);

  @PUT('/family/member/{id}')
  Future<BaseResponse> updateMember(@Path() String id, @Body() Map<String, dynamic> body);

  @PUT('/family/active/{id}')
  Future<BaseResponse> activeFamily(@Path() String id);

  @PUT('/family/deactive/{id}')
  Future<BaseResponse> deactiveFamily(@Path() String id);

  @GET('/family/upcoming/{familyId}')
  Future<BaseResponse> getUpcoming(@Path() String familyId, @Queries() Map<String, dynamic> queries);

  @POST('/push/notification')
  Future<BaseResponse> pushNotification(@Body() Map<String, dynamic> body);

  @POST('/family/photo/{id}')
  Future<BaseResponse> updateAvatar(@Path() String id, @Body() FormData body);

  @GET('/family/photo/{userUuid}/{photoUuid}')
  Future<BaseResponse> getS3Avatar(@Path() String userUuid, @Path() String photoUuid);

  @GET('/privacy')
  Future<BaseResponse> privacyPolicy();

  // storage API

  @POST('/family/storage/presign/{familyId}')
  Future<BaseResponse> getStoragePresignedUrls(@Path() String familyId);

  @POST('/family/storage/{id}')
  Future<BaseResponse> updateFileInStorage(@Path() String id, @Body() FormData body);

  @POST('/family/storage/attach')
  Future<BaseResponse> attachFileToStorage(@Body() FormData body);

  @POST('/family/attach')
  Future<BaseResponse> attachStorageToMemory(@Body() Map<String, dynamic> body);

  @DELETE('/family/attach/{attachId}')
  Future<BaseResponse> removeStorageFromMemory(@Path() String attachId);

//Get the s3 link of any item in storage
  @GET('/family/storage/all/{familyId}')
  Future<BaseResponse> getStorageFilesByFamily(@Path() String familyId);

  @GET('/family/storage/{id}')
  Future<BaseResponse> getStorageFile(@Path() String id);

  @DELETE('/family/storage/{id}')
  Future<BaseResponse> removeStorageFile(@Path() String id);

  @GET('/family/attach/family/{familyId}')
  Future<BaseResponse> getMemoryListByFamily(@Path() String familyId, @Queries() Map<String, dynamic> queries);

  @GET('/family/attach/activity/{id}')
  Future<BaseResponse> getAttachActivity(@Path() String id);

  @GET('/family/attach/{id}')
  Future<BaseResponse> getAttachInfo(@Path() String id);

  @DELETE('/family/attach/{attachId}')
  Future<BaseResponse> deleteAttach(@Path() String attachId);

  @PUT('/family/attach/{attachId}')
  Future<BaseResponse> updateAttachInfo(@Path() String attachId, @Body() Map<String, dynamic> body);

  @POST('/family/attach/{memoryId}/activity')
  Future<BaseResponse> upsertMemoryActivity(@Path() String memoryId, @Body() Map<String, dynamic> body);

  @DELETE('/family/attach/activity/{interactionId}')
  Future<BaseResponse> deleteMemoryActivity(@Path() String interactionId);

  //https://vrelay-vn1.5gencare.com/v1/family/items/all?order_by=created_at&ordrering=asc&limit=2&family_id=7e7ec94d-4946-4dad-9029-ef1d5a4bd2bb&from=null&to=null'@
  @GET('/family/items/all')
  Future<BaseResponse> getItemsAll(@Queries() Map<String, dynamic> queries);

  @POST('/family/profile')
  Future<BaseResponse> createProfile(@Body() Map<String, dynamic> body);

  @PUT('/family/profile/{id}')
  Future<BaseResponse> updateProfileById(@Path() String id, @Body() Map<String, dynamic> body);

  @GET('/family/profile/{id}')
  Future<BaseResponse> getProfileById(@Path() String id);

  @POST('/family/changepassword')
  Future<BaseResponse> changePassword(@Body() Map<String, dynamic> body);

  @PUT('/family/member/{id}/{familyId}')
  Future<BaseResponse> updateMemberByFamilyId(@Path() String id, @Path() String familyId, @Body() Map<String, dynamic> body);
}
