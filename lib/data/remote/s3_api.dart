import 'dart:io';

import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 's3_api.g.dart';

@RestApi()
abstract class S3API {
  factory S3API(Dio dio) = _S3API;

  @PUT('')
  Future<BaseResponse> uploadFileToS3(
      Uri presignedUrl,
      @Header('Content-Type') String contentType,
      @Header('Content-Length') int contentLength,
      @Body() File file);
}
