import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/utils/log/app_logger.dart';

class AmadeusInterceptor extends InterceptorsWrapper {
  final LocalStorage _localStorage;

  AmadeusInterceptor({
    required Dio dio,
    required LocalStorage localStorage,
  }) : _localStorage = localStorage;

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // Log detailed request information
    final fullUrl = '${options.baseUrl}${options.path}';
    final queryParams = options.queryParameters.isNotEmpty
        ? '?${options.queryParameters.entries.map((e) => '${e.key}=${e.value}').join('&')}'
        : '';

    // Handle authentication
    final token = _localStorage.amadeusAccessToken;
    final tokenExpired = _localStorage.amadeusTokenExpired;
    bool isExpired = _isTokenExpired(tokenExpired);
    if (token != null && token.isNotEmpty && !isExpired) {
      options.headers
          .putIfAbsent("Authorization", () => "Bearer $token".trim());
    }

    AppLogger.d(
        'AMADEUS req HDR:    ${options.method} $fullUrl$queryParams \n  ${options.headers}');

    super.onRequest(options, handler);
  }

  bool _isTokenExpired(String? tokenExpired) {
    if (tokenExpired == null) return true;
    final expiredDate = DateTime.parse(tokenExpired);
    return expiredDate.isBefore(DateTime.now());
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Log response details
    final fullUrl =
        '${response.requestOptions.baseUrl}${response.requestOptions.path}';
    // Log response data (truncated if too long)
    final responseData = response.data.toString();
    final truncatedData = responseData.length > 1000
        ? '${responseData.substring(0, 1000)}...[TRUNCATED]'
        : responseData;
    AppLogger.d(
        'AMADEUS Res: ${response.statusCode} $fullUrl \n [hrd:] ${response.headers} \n [data:] $truncatedData');

    if (response.data is Map) {
      final map = response.data as Map;
      if ((map['code'] ?? 200) != 200) {
        AppLogger.d(' AMADEUS ERROR: Bad response code ${map['code']}');
        throw DioException.badResponse(
          statusCode: map['code'] ?? 200,
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Log error details
    final fullUrl = '${err.requestOptions.baseUrl}${err.requestOptions.path}';
    AppLogger.d('AMADEUS ERROR: ${err.type} - ${err.message} - $fullUrl');

    if (err.response != null) {
      AppLogger.d(
          'AMADEUS ERROR RESPONSE: ${err.response!.statusCode} - ${err.response!.data}');
    }

    handler.next(err);
  }
}
