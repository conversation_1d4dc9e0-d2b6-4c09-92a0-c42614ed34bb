import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:dio/dio.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/usecase/sign_in_usecase.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/loading.dart';

class DefaultInterceptor extends InterceptorsWrapper {
  final Dio _dio;
  final LocalStorage _localStorage;

  DefaultInterceptor({
    required Dio dio,
    required LocalStorage localStorage,
  })  : _dio = dio,
        _localStorage = localStorage;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    options.headers.putIfAbsent("api-key", () => AppConfig.API_KEY);
    final token = await _localStorage.accessToken();
    if (token != null) {
      options.headers.putIfAbsent("Authorization", () => "Bearer $token".trim());
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.data is Map) {
      final map = response.data as Map;
      if ((map['code'] ?? 200) != 200) {
        throw DioException.badResponse(
          statusCode: map['code'] ?? 200,
          requestOptions: response.requestOptions,
          response: response,
        );
      }
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    try {
      final statusCode = err.response?.data['code'] ?? err.response?.statusCode;

      if (statusCode == HttpStatus.forbidden || statusCode == HttpStatus.unauthorized) {
        if (_localStorage.loggedIn()) {
          var email = await _localStorage.getEmail();
          var password = await _localStorage.getPassword();
          if (email.isNotEmpty && password.isNotEmpty) {
            try {
              await locator
                  .get<SignInUsecase>()
                  .call(SignInParameter(email: email, password: password, isLoggedIn: true));
              _renewRequest(err, handler);
            } catch (e) {
              handler.next(err);
            }
          } else {
            handler.next(err);
          }
        } else {
          // _renewToken(err, handler);
          dismissLoading();
          locator.get<LocalStorage>().clear();
          navigatorKey.currentContext!.router.replaceAll([const AuthRoute()]);
        }
      } else {
        handler.next(err);
      }
    } catch (e) {
      handler.next(err);
    }
  }

  Future<void> _renewRequest(DioException error, ErrorInterceptorHandler handler) async {
    try {
      final requestOptions = error.requestOptions;
      var requestData = requestOptions.data;
      final options = Options(
        method: requestOptions.method,
        headers: requestOptions.headers,
      );
      if (requestData is FormData) {
        requestData = requestData.clone();
      }

      // return errorHandler.resolve(request);
      var request = await _dio.request(
        "${requestOptions.baseUrl}${requestOptions.path}",
        data: requestData,
        queryParameters: requestOptions.queryParameters,
        options: options,
      );
      handler.resolve(request);
    } catch (exception) {
      if (exception is DioException) {
        handler.next(exception);
      } else {
        handler.next(error);
      }
    }
  }
}
