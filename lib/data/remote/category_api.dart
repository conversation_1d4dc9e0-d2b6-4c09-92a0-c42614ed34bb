import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'category_api.g.dart';

@RestApi()
abstract class CategoryAPI {
  factory CategoryAPI(Dio dio, {String? baseUrl}) = _CategoryAPI;

  @GET('/list/category/all')
  Future<BaseResponse> getAllCategory(@Queries() Map<String, dynamic> queries);

  @POST('/list/category/')
  Future<BaseResponse> createCategory(@Body() Map<String, dynamic> body);

  @GET('/list/category/{id}')
  Future<BaseResponse> getCategoryById(@Path() String id, @Queries() Map<String, dynamic> queries);

  @PUT('/list/category/{id}')
  Future<BaseResponse> updateCategory(@Path() String id, @Body() Map<String, dynamic> body);

  @DELETE('/list/category/{id}')
  Future<BaseResponse> deleteCategory(@Path() String id);
}
