import 'package:dio/dio.dart';
import 'package:family_app/data/model/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'premium_api.g.dart';

@RestApi()
abstract class PremiumAPI {
  factory PremiumAPI(Dio dio, {String? baseUrl}) = _PremiumAPI;

  @POST('/ios/hook')
  Future<BaseResponse> hook(@Body() Map<String, dynamic> body);

  @GET('/family/ispremium')
  Future<BaseResponse> getIsPremium();

  @GET('/family/transactions/{id}')
  Future<BaseResponse> validatePurchase(@Path() String id, @Queries() Map<String, dynamic> queries);
}
