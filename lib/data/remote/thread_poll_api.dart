import 'package:dio/dio.dart';
import 'package:retrofit/http.dart';

import '../model/base_response.dart';

part 'thread_poll_api.g.dart';

@RestApi()
abstract class ThreadPollAPI {
  factory ThreadPollAPI(Dio dio, {String? baseUrl}) = _ThreadPollAPI;

  @POST('/thread/poll/')
  Future<BaseResponse> threadPollCreate(@Body() Map<String, dynamic> body);

  @POST('/thread/poll/vote/{id}')
  Future<BaseResponse> threadPollVote(@Path() String id, @Body() Map<String, dynamic> body);
}
