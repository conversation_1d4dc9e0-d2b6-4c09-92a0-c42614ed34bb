import 'package:auto_route/auto_route.dart';
import 'package:family_app/data/model/document_model.dart';
import 'package:family_app/data/model/family_profile.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/screen/account_edit/account_edit_screen.dart';
import 'package:family_app/screen/authentication/active_account/active_account_screen.dart';
import 'package:family_app/screen/authentication/auth/auth_screen.dart';
import 'package:family_app/screen/authentication/change_password/screen.dart';
import 'package:family_app/screen/authentication/forgot_password/forgot_password_screen.dart';
import 'package:family_app/screen/authentication/sign_up/sign_up_screen.dart';
import 'package:family_app/screen/authentication/sign_up_family/sign_up_family_screen.dart';
import 'package:family_app/screen/authentication/sign_up_name/sign_up_name_screen.dart';
import 'package:family_app/screen/family_create/family_create_screen.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_parameter.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_screen.dart';
import 'package:family_app/screen/family_create/family_success/family_success_screen.dart';
import 'package:family_app/screen/family_create/invite_member_success/invite_member_success_parameter.dart';
import 'package:family_app/screen/family_create/invite_member_success/invite_member_success_screen.dart';
import 'package:family_app/screen/family_list/family_list_screen.dart';
import 'package:family_app/screen/family_list/member_detail/member_detail_parameter.dart';
import 'package:family_app/screen/family_list/member_detail/member_detail_screen.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_screen.dart';
import 'package:family_app/screen/family_profile/family_profile_screen.dart';
import 'package:family_app/screen/main/ai_butler/ai_butler_screen.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_detail_parameter.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_detail_screen.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_upsert_screen.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/chat/chat_screen.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_screen.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_screen.dart';
import 'package:family_app/screen/main/daily_tasks/screen.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_screen.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_screen.dart';
import 'package:family_app/screen/main/home/<USER>/activity_home_screen.dart';
import 'package:family_app/screen/main/home/<USER>/activity_select_type_screen.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_screen.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/home/<USER>/list_activity_screen.dart';
import 'package:family_app/screen/main/home/<USER>/upsert_activity_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/upsert_activity_screen.dart';
import 'package:family_app/screen/main/memories/memories_screen.dart';
import 'package:family_app/screen/main/memories/memory_filter_screen.dart';
import 'package:family_app/screen/main/profile/privacy_policy/policy_parameter.dart';
import 'package:family_app/screen/main/profile/privacy_policy/policy_screen.dart';
import 'package:family_app/screen/main/search/search_screen.dart';
import 'package:family_app/screen/main/thread/thread_screen.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_screen.dart';
import 'package:family_app/screen/main/trip/flight/booking/flight_booking_parameter.dart';
import 'package:family_app/screen/main/trip/flight/booking/flight_booking_screen.dart';
import 'package:family_app/screen/main/trip/flight/flight_offer_parameter.dart';
import 'package:family_app/screen/main/trip/flight/flight_offer_screen.dart';
import 'package:family_app/screen/main/trip/document/document_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/booking/info/hotel_booking_info_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/booking/info/hotel_booking_info_screen.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_screen.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_screen.dart';
import 'package:family_app/screen/main/trip/place/place_map_selection_screen.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_screen.dart';
import 'package:family_app/screen/main/trip/document/document_upsert_screen.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_screen.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/trip_screen.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_screen.dart';
import 'package:family_app/screen/main/trip/trip_upsert_screen.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_screen.dart';
import 'package:family_app/screen/member_info/member_info_screen.dart';
import 'package:family_app/screen/premium/premium_screen.dart';
import 'package:family_app/screen/screen.dart';
import 'package:family_app/screen/verify_code/verify_code_screen.dart';
import 'package:flutter/material.dart';

import '../screen/authentication/create_account/create_account_screen.dart';
import '../screen/family_list/scale_avatar/scale_avatar_parameter.dart';
import '../screen/family_list/scale_avatar/scale_avatar_screen.dart';
import 'package:family_app/screen/main/home/<USER>/home_add_page.dart';

part 'app_route.gr.dart';

@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(page: SplashRoute.page, initial: true),
        AutoRoute(page: PreviewImageRoute.page),
        AutoRoute(page: AuthRoute.page),
        AutoRoute(page: SignInRoute.page),
        AutoRoute(page: ForgotPasswordRoute.page),
        AutoRoute(page: ChangePasswordRoute.page),
        AutoRoute(page: SignUpRoute.page),
        AutoRoute(page: VerifyCodeRoute.page),
        AutoRoute(page: SignUpNameRoute.page),
        AutoRoute(page: ActiveAccountRoute.page),
        AutoRoute(page: SignUpFamilyRoute.page),
        AutoRoute(page: FamilyCreateRoute.page),
        AutoRoute(page: FamilySuccessRoute.page),
        AutoRoute(page: FamilyInviteRoute.page),
        AutoRoute(page: AccountEditRoute.page),
        AutoRoute(page: FamilyListRoute.page),
        AutoRoute(page: MemberListRoute.page),
        AutoRoute(page: MemberDetailRoute.page),
        AutoRoute(page: FamilyProfileRoute.page),
        AutoRoute(page: MemberInfoRoute.page),
        AutoRoute(page: CheckListRoute.page),
        AutoRoute(page: MemoriesRoute.page),
        AutoRoute(page: MemoryFilterRoute.page),
        AutoRoute(page: ThreadRoute.page),
        AutoRoute(page: DailyTasksRoute.page),
        AutoRoute(page: ThreadDetailRoute.page),
        AutoRoute(page: PremiumRoute.page),
        AutoRoute(page: MainRoute.page, children: [
          AutoRoute(page: HomeRoute.page),
          AutoRoute(page: CalendarRoute.page),
          AutoRoute(page: CheckListRoute.page),
          AutoRoute(page: ProfileRoute.page),
          AutoRoute(page: HomeAddRoute.page),
        ]),
        AutoRoute(page: DetailActivityRoute.page),
        AutoRoute(page: UpsertActivityRoute.page),
        AutoRoute(page: ListDetailRoute.page),
        AutoRoute(page: UpsertListItemRoute.page),
        AutoRoute(page: ListActivityRoute.page),
        AutoRoute(page: DetailEventRoute.page),
        AutoRoute(page: UpsertEventRoute.page),
        AutoRoute(page: AiButlerRoute.page),
        AutoRoute(page: SearchRoute.page),
        AutoRoute(page: InviteMemberSuccessRoute.page),
        AutoRoute(page: PolicyRoute.page),
        AutoRoute(page: ActivityHomeRoute.page),
        AutoRoute(page: ChatRoute.page),
        AutoRoute(page: TripRoute.page),
        AutoRoute(page: TripDetailRoute.page),
        AutoRoute(page: HotelListRoute.page),
        AutoRoute(page: HotelDetailRoute.page),
        AutoRoute(page: HotelBookingInfoRoute.page),
        AutoRoute(page: FlightOfferRoute.page),
        AutoRoute(page: FlightBookingRoute.page),

        AutoRoute(page: TripTransferUpsertRoute.page),
        AutoRoute(page: ActivitySelectTypeRoute.page),
        AutoRoute(page: BirthdayRegistryUpsertRoute.page),
        AutoRoute(page: BirthdayRegistryDetailRoute.page),
        AutoRoute(page: VisualTripPlannerRoute.page),
        AutoRoute(page: TripUpsertRoute.page),
        AutoRoute(page: PlaceUpsertRoute.page),
        AutoRoute(page: DocumentUpsertRoute.page),
        AutoRoute(page: PlaceMapSelectionRoute.page),
        AutoRoute(page: CreateAccountRoute.page),
        AutoRoute(page: ScaleAvatarRoute.page)
      ];
}
