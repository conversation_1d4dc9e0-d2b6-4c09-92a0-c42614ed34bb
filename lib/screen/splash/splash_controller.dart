import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/base_controller.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/usecase/verify_account_usecase.dart';
import 'package:family_app/router/app_route.dart';

class SplashController extends BaseController {
  final AccountService accountService = locator.get();
  final IFamilyRepository familyRepository = locator.get();
  final LocalStorage localStorage = locator.get();
  final VerifyAccountUsecase verifyAccountUsecase = locator.get();

  SplashController(super.state) {
    initService();
  }

  initService() async {
    await Future.delayed(const Duration(seconds: 1)).then((value) => goToOnboardingPage());
  }

  Future<void> goToOnboardingPage() async {
    try {
      final token = (await localStorage.accessToken() ?? '');

      if (token.isEmpty) {
        localStorage.clear();
        context.replaceRoute(const AuthRoute());
      } else {
        await accountService.initMyProfile();
        final isValid = await verifyAccountUsecase.call();
        if (!isValid) {
          throw Exception('Family not found');
        }
        context.replaceRoute(const HomeRoute());
      }
    } catch (e) {
      // localStorage.clear();
      context.replaceRoute(const AuthRoute());
    }
  }
}
