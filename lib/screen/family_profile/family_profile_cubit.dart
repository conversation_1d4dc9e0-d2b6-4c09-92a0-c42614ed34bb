import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/screen/family_profile/family_profile_state.dart';
import 'package:family_app/widget/image/image_picker_handler.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class FamilyProfileCubit extends BaseCubit<FamilyProfileState> {
  FamilyProfileCubit() : super(FamilyProfileState());

  late TextFieldHandler familyName;

  @override
  void onInit() {
    super.onInit();
    familyName = TextFieldHandler(
      field: 'Family name',
      title: LocaleKeys.family_name_text.tr(),
      inputType: TextInputType.emailAddress,
    );
  }

  void onUpdtaeAvatar() async {
    final pickedFile = await ImagePickerHandler.onGetImage();

    if (pickedFile != null) {
      emit(FamilyProfileState(imageFile: pickedFile));
    }
  }
}
