import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/family_profile/family_profile_cubit.dart';
import 'package:family_app/screen/family_profile/family_profile_state.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/textfield/title_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class FamilyProfilePage extends BaseBlocProvider<FamilyProfileState, FamilyProfileCubit> {
  const FamilyProfilePage({super.key});

  @override
  Widget buildPage() => const FamilyProfileView();

  @override
  FamilyProfileCubit createCubit() => FamilyProfileCubit();
}

class FamilyProfileView extends StatefulWidget {
  const FamilyProfileView({super.key});

  @override
  State<FamilyProfileView> createState() => _FamilyProfileViewState();
}

class _FamilyProfileViewState
    extends BaseBlocNoAppBarPageState<FamilyProfileView, FamilyProfileState, FamilyProfileCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  Widget buildBody(BuildContext context, FamilyProfileCubit cubit, FamilyProfileState state) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Image.asset(Assets.images.bgFamilyCreate.path),
        SafeArea(
          child: Padding(
            padding: padding(vertical: 16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(onPressed: context.back, icon: SvgPicture.asset(Assets.icons.back.path)),
                    Text('Johnson', style: AppStyle.medium16()),
                    IconButton(onPressed: null, icon: SizedBox()),
                  ],
                ),
                Padding(
                  padding: padding(top: 20, horizontal: 32),
                  child: Column(
                    children: [
                      Text(LocaleKeys.click_the_image_to_update_family_avatar_text
                              .tr(), style: AppStyle.regular14()),
                      SizedBox(height: 17.h),
                      Stack(
                        children: [
                          CircleAvatarCustom(
                            borderWidth: 3.w,
                            size: 80,
                            imageFile: state.imageFile,
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: InkWell(
                              onTap: cubit.onUpdtaeAvatar,
                              child: Image.asset(Assets.images.takephoto.path, width: 32.w),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 36.h),
                      TitleTextField(
                        fieldNode: cubit.familyName,
                        filledColor: appTheme.bgInputColor,
                        textStyle: AppStyle.regular16(),
                        hintStyle: AppStyle.regular16(color: appTheme.hintColor),
                        inputBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: appTheme.borderInputColor, width: 1.w),
                          borderRadius: BorderRadius.circular(50),
                        ),
                      ),
                      SizedBox(height: 16.h),
                      PrimaryButton(
                        text: LocaleKeys.ok_text.tr(),
                        onTap: context.back,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget buildBottomView(BuildContext context, FamilyProfileCubit cubit, FamilyProfileState state) {
    return Padding(
      padding: padding(top: 12, bottom: 24, horizontal: 16),
      child: Container(
        padding: padding(vertical: 14, horizontal: 25),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(1000),
          border: Border.all(width: 1.w, color: appTheme.hintColor),
        ),
        child: Text(LocaleKeys.disband_family_text.tr(), style: AppStyle.medium16(color: appTheme.red3CColor)),
      ),
    );
  }
}
