import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_parameter.dart';
import 'package:family_app/screen/family_create/invite_member_success/invite_member_success_cubit.dart';
import 'package:family_app/screen/family_create/invite_member_success/invite_member_success_parameter.dart';
import 'package:family_app/screen/family_create/invite_member_success/invite_member_success_state.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/custom_border_button.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:flutter/material.dart';

@RoutePage()
class InviteMemberSuccessPage extends BaseBlocProvider<InviteMemberSuccessState, InviteMemberSuccessCubit> {
  const InviteMemberSuccessPage({super.key, required this.parameter});

  final InviteMemberSuccessParameter parameter;

  @override
  Widget buildPage() => InviteMemberSuccessView(parameter: parameter);

  @override
  InviteMemberSuccessCubit createCubit() => InviteMemberSuccessCubit();
}

class InviteMemberSuccessView extends StatefulWidget {
  const InviteMemberSuccessView({super.key, required this.parameter});

  final InviteMemberSuccessParameter parameter;

  @override
  State<InviteMemberSuccessView> createState() => _InviteMemberSuccessViewState();
}

class _InviteMemberSuccessViewState
    extends BaseBlocNoAppBarPageState<InviteMemberSuccessView, InviteMemberSuccessState, InviteMemberSuccessCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  Widget buildBody(BuildContext context, InviteMemberSuccessCubit cubit, InviteMemberSuccessState state) {
    final account = widget.parameter.invitedAccount;
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Assets.images.bgFamilyCreate.image(),
        SafeArea(
          child: Padding(
            padding: padding(vertical: 16),
            child: Column(
              children: [
                AppBarCustom(
                    showBack: widget.parameter.type == FamilyInviteType.profile,
                    title: LocaleKeys.you_want_to_invite_text.tr()),
                Padding(
                  padding: padding(top: 20, horizontal: 16),
                  child: Column(
                    children: [
                      Container(
                        padding: padding(left: 16, right: 8, top: 3, bottom: 7),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: appTheme.whiteText,
                          boxShadow: [
                            const BoxShadow(
                              color: Color(0x185F657C),
                              offset: Offset(0, 0),
                              blurRadius: 6,
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(account.fullName ?? '', style: AppStyle.medium22()),
                                  SizedBox(height: 11.h),
                                  Text(account.email ?? '', style: AppStyle.regular14()),
                                ],
                              ),
                            ),
                            Image.asset(Assets.images.familyLogo.path, width: 78.w),
                          ],
                        ),
                      ),
                      SizedBox(height: 28.h),
                      PrimaryButton(
                        text: LocaleKeys.share_text.tr(),
                        onTap: () {},
                      ),
                      SizedBox(height: 16.h),
                      Row(
                        children: [
                          Flexible(
                            child: CustomBorderButton(
                              buttonText: LocaleKeys.home_text.tr(),
                              radius: 50,
                              icon: Assets.icons.home.svg(),
                              onPressed: () {
                                if (widget.parameter.type == FamilyInviteType.auth) {
                                  context.router.replaceAll([const MainRoute()]);
                                } else {
                                  context.maybePop();
                                }
                              },
                            ),
                          ),
                          SizedBox(width: 15.w),
                          Flexible(
                            child: CustomBorderButton(
                              buttonText: LocaleKeys.invite_more_text.tr(),
                              radius: 50,
                              onPressed: () => context.replaceRoute(FamilyInviteRoute(
                                parameter: FamilyInviteParameter(type: widget.parameter.type),
                              )),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
