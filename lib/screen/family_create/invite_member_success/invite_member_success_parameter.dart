// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/data/model/account.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_parameter.dart';

class InviteMemberSuccessParameter {
  final Account invitedAccount;
  final FamilyInviteType type;

  InviteMemberSuccessParameter({required this.invitedAccount, this.type = FamilyInviteType.auth});
}
