import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';

class FamilyInviteState extends BaseState {
  final bool isRolesSelected;
  final Account? inviteAccount;

  FamilyInviteState({this.isRolesSelected = false, super.isLoading, this.inviteAccount});

  @override
  List<Object?> get props => [isRolesSelected, isLoading, inviteAccount];

  FamilyInviteState copyWith({bool? isRolesSelected, bool? isLoading, Account? inviteAccount}) {
    return FamilyInviteState(
        isRolesSelected: isRolesSelected ?? this.isRolesSelected,
        inviteAccount: inviteAccount ?? this.inviteAccount,
        isLoading: isLoading ?? this.isLoading);
  }
}
