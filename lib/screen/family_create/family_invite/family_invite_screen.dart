import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/base/widget/cubit/screen/family_home_base_page.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_cubit.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_parameter.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_state.dart';
import 'package:family_app/screen/family_create/family_invite/widget/item_row_input_widget.dart';
import 'package:family_app/screen/family_create/invite_member_success/invite_member_success_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_cubit.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/item_selection_view.dart';
import 'package:family_app/widget/line_widget.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/textfield/row_text_field_view.dart';
import 'package:flutter/material.dart';

@RoutePage()
class FamilyInvitePage extends BaseBlocProvider<FamilyInviteState, FamilyInviteCubit> {
  const FamilyInvitePage({super.key, required this.parameter});

  final FamilyInviteParameter parameter;

  @override
  Widget buildPage() => FamilyInviteView(parameter: parameter);

  @override
  FamilyInviteCubit createCubit() => FamilyInviteCubit(
        familyRepository: locator.get(),
        accountService: locator.get(),
        parameter: parameter,
      );
}

class FamilyInviteView extends StatefulWidget {
  const FamilyInviteView({super.key, required this.parameter});

  final FamilyInviteParameter parameter;

  @override
  State<FamilyInviteView> createState() => _FamilyInviteViewState();
}

class _FamilyInviteViewState extends FamilyHomeBasePage<FamilyInviteView, FamilyInviteState, FamilyInviteCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  bool listenWhen(FamilyInviteState previous, FamilyInviteState current) {
    if (previous.inviteAccount == null && current.inviteAccount != null) {
      if (locator.isRegistered<MemberListCubit>()) {
        locator.get<MemberListCubit>().onInviteSuccess(current.inviteAccount!);
      }
      context.replaceRoute(
        InviteMemberSuccessRoute(
          parameter: InviteMemberSuccessParameter(invitedAccount: current.inviteAccount!, type: widget.parameter.type),
        ),
      );
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, FamilyInviteCubit cubit, FamilyInviteState state) {
    return Column(
      children: [
        AppBarCustom(
          title: LocaleKeys.you_want_to_invite_text.tr(),
          onBack: () {
            if (widget.parameter.type == FamilyInviteType.profile) {
              context.maybePop();
            } else {
              context.router.replaceAll([const MainRoute()]);
            }
          },
        ),
        Padding(
          padding: padding(top: 20, horizontal: 16),
          child: Column(
            children: [
              Container(
                padding: padding(horizontal: 16, bottom: 16),
                decoration: BoxDecoration(
                  color: appTheme.whiteText,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    const BoxShadow(color: Color(0x185F657C), offset: Offset(0, 0), blurRadius: 9),
                  ],
                ),
                child: Column(
                  children: [
                    RowTextFieldView(
                      handler: cubit.name,
                      topPadding: 0,
                      bottomSizedBox: 0,
                      viewPadding: EdgeInsets.zero,
                      formHandler: cubit.handler,
                    ),
                    const LineWidget(),
                    RowTextFieldView(
                      handler: cubit.email,
                      topPadding: 0,
                      bottomSizedBox: 0,
                      viewPadding: EdgeInsets.zero,
                      formHandler: cubit.handler,
                    ),
                    const LineWidget(),
                    RowTextFieldView( 
                      handler: cubit.relation,
                      selections: FamilyMemberRelationship.relationship,
                      topPadding: 0,
                      viewPadding: EdgeInsets.zero,
                      formHandler: cubit.handler,
                    ),
                    SizedBox(height: 14.5.h),
                    const LineWidget(),
                    SizedBox(height: 16.h),
                    itemRowInputWidget(
                      name: LocaleKeys.roles.tr(),
                      widget: Row(
                        children: [
                          SizedBox(width: 12.w),
                          ItemSelectionView(
                            status: state.isRolesSelected,
                            onTap: () => cubit.toggleRole(true),
                            text: LocaleKeys.editor.tr(),
                          ),
                          SizedBox(width: 29.w),
                          ItemSelectionView(
                            status: !state.isRolesSelected,
                            onTap: () => cubit.toggleRole(false),
                            text: LocaleKeys.viewer.tr(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24.h),
              PrimaryButton(
                text: LocaleKeys.send_invitation_text.tr(),
                isLoading: state.isLoading,
                onTap: cubit.handler.onSubmit,
              )
            ],
          ),
        ),
      ],
    );
  }
}
