import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:family_app/widget/textfield/title_text_field.dart';
import 'package:flutter/material.dart';

Widget itemRowInputWidget({TextFieldHandler? fieldNode, required String name, Widget? widget}) {
  return Padding(
    padding: padding(vertical: 7),
    child: Row(
      children: [
        SizedBox(
          width: 56,
          child: Text(name, style: AppStyle.regular14(color: appTheme.labelColor)),
        ),
        SizedBox(width: 20),
        Expanded(
          child: widget ??
              TitleTextField(
                fieldNode: fieldNode,
                inputBorder: InputBorder.none,
                textStyle: AppStyle.regular16(),
                hintStyle: AppStyle.regular14(color: appTheme.hintColor),
              ),
        ),
      ],
    ),
  );
}
