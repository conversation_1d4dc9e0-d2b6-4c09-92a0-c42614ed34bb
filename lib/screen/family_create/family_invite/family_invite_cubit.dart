import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/repository/family/family_exception.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/family/model/invite_parameter.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_parameter.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class FamilyInviteCubit extends BaseCubit<FamilyInviteState> {
  final IFamilyRepository familyRepository;
  final AccountService accountService;
  final FamilyInviteParameter parameter;

  FamilyInviteCubit({
    required this.familyRepository,
    required this.accountService,
    required this.parameter,
  }) : super(FamilyInviteState());

  late TextFieldHandler name;
  late TextFieldHandler email;
  late TextFieldHandler relation;
  late FormTextFieldHandler handler;

  @override
  Future<void> close() {
    handler.dispose();
    return super.close();
  }

  @override
  void onInit() {
    super.onInit();
    name = TextFieldHandler(
      field: 'full_name',
      isRequired: true,
      title: LocaleKeys.name_text.tr(),
      hintText: LocaleKeys.name_text.tr(),
    );
    email = TextFieldHandler(
      field: 'email',
      isRequired: true,
      title: LocaleKeys.email_text.tr(),
      hintText: '<EMAIL>',
      inputType: TextInputType.emailAddress,
    );
    relation = TextFieldHandler(
      field: 'relationship',
      isRequired: true,
      title: LocaleKeys.relation_text.tr(),
      hintText: LocaleKeys.select_or_input_text.tr(),
    );
    handler = FormTextFieldHandler(handlers: [name, email, relation], validateForm: onSubmit);
  }

  void updateRelation(String newRelation) {
    relation = TextFieldHandler(field: newRelation, title: newRelation);
  }

  void toggleRole(bool isEditor) {
    emit(state.copyWith(isRolesSelected: isEditor));
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      emit(state.copyWith(isLoading: true));
      final role = state.isRolesSelected ? Role.editor : Role.viewer;
      final result = await familyRepository
          .invite(InviteParameter(name: name.text, email: email.text, relations: relation.text, role: role));
      if (result != null) {
        if (parameter.type == FamilyInviteType.profile && parameter.account == null) {
          accountService.memberInFamily.addValue(result);
        }
        emit(state.copyWith(isLoading: false, inviteAccount: result));
      } else {
        showSimpleToast(LocaleKeys.action_fail.tr());
      }
    } catch (e) {
      if (e is CantInviteMyselfError) {
        showSimpleToast('${LocaleKeys.cant_invite_myself_text.tr()}!');
        emit(state.copyWith(isLoading: false));
      } else {
        emit(state.copyWith(isLoading: false));
        showSimpleToast(LocaleKeys.action_fail.tr());
      }
    }
  }
}
