import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_create/family_create_cubit.dart';
import 'package:family_app/screen/family_create/family_create_state.dart';
import 'package:family_app/screen/family_list/family_list_cubit.dart';
import 'package:family_app/screen/family_list/member_list/member_list_cubit.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/log/app_logger.dart';

@RoutePage()
class FamilyCreatePage extends BaseBlocProvider<FamilyCreateState, FamilyCreateCubit> {
  const FamilyCreatePage({super.key, this.familyId});

  final String? familyId;

  @override
  Widget buildPage() => const FamilyCreateView();

  @override
  FamilyCreateCubit createCubit() => FamilyCreateCubit(
    familyRepository: locator.get(),
    familyId: familyId,
  );
}

class FamilyCreateView extends StatefulWidget {
  const FamilyCreateView({super.key});

  @override
  State<FamilyCreateView> createState() => _FamilyCreateViewState();
}

class _FamilyCreateViewState extends BaseBlocPageState<FamilyCreateView, FamilyCreateState, FamilyCreateCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get isSafeArea => true;

  @override
  Widget buildAppBar(BuildContext context, FamilyCreateCubit cubit, FamilyCreateState state) {
    return AppBarCustom(
      showBack: true,
      titleView: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text( cubit.familyId != null ? LocaleKeys.edit_family.tr() : LocaleKeys.create_a_new_family.tr(),
                    style: AppStyle.bold16(color: Colors.black),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      // title: "${state.threadDetail?.name ?? ''} \n ${state.threadDetail?.members?.length ?? 0} members",
        onBack: () {
          final hasFamilies = cubit.accountService.myFamilyBelong.value.isNotEmpty;
          if (hasFamilies) {
            context.maybePop();
          } else {
            context.router.replaceAll([const HomeRoute()]);
          }
        },

    );
  }

  @override
  bool listenWhen(FamilyCreateState previous, FamilyCreateState current) {
    if (current.status == FamilyCreateStatus.loading) {
      showLoading();
    } else if (previous.status == FamilyCreateStatus.loading && current.status != FamilyCreateStatus.loading) {
      dismissLoading();

      if (current.status == FamilyCreateStatus.success) {
        if (context.read<FamilyCreateCubit>().familyId == null) {
          accountService.initMyProfile();
        }
        _showSuccessDialog(context.read<FamilyCreateCubit>(), context.read<FamilyCreateCubit>().familyId);
      } else if (current.status == FamilyCreateStatus.error) {
        showSimpleToast(current.errorMessage ?? LocaleKeys.family_creation_failed.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  void _showSuccessDialog(FamilyCreateCubit cubit, String? familyId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ImageAssetCustom(
                imagePath: Assets.icons.icCheckAllItems.path,
                width: 60.w,
                height: 60.w,
              ),
              const SizedBox(height: 16),
              Text(
                familyId != null
                    ? LocaleKeys.family_edited_successfully.tr()
                    : LocaleKeys.family_created_successfully.tr(),
                style: AppStyle.regular14(color: appTheme.grayV2),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            Center(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: PrimaryButtonV2(
                  text: LocaleKeys.continue_text.tr(),
                  onTap: () {
                    // Refresh the family and member list get the latest data
                    try {
                      final memberListCubit = locator.get<MemberListCubit>();
                      final familyListCubit = locator.get<FamilyListCubit>();
                      final homeCubit = locator.get<HomeCubit>();

                      memberListCubit.refresh();
                      familyListCubit.refresh();

                      // If family edited is active family, refresh the profile
                      if (cubit.familyId != null && cubit.accountService.isActiveFamily(cubit.familyId!)) {
                        accountService.initMyProfile();
                        homeCubit.onRefresh();
                      }
                    } catch (e) {
                      AppLogger.e('Error resetting form: $e');
                    }
                    Navigator.of(context).pop();
                    context.maybePop();
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget buildBody(BuildContext context, FamilyCreateCubit cubit, FamilyCreateState state) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.only(bottom: context.bottom),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: _buildFormContainer(context, cubit, state),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFormContainer(BuildContext context, FamilyCreateCubit cubit, FamilyCreateState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildAvatarSection(context, cubit, state),
          SizedBox(height: 13.h),
          // Text(
          //   LocaleKeys.click_the_image_to_update_family_avatar_text.tr(),
          //   style: AppStyle.regular12(color: appTheme.grayV2),
          //   textAlign: TextAlign.center,
          // ),
          // Text(
          //   LocaleKeys.recommend_size_upload_text.tr(),
          //   style: AppStyle.regular12(color: appTheme.grayV2),
          //   textAlign: TextAlign.center,
          // ),
          SizedBox(height: 21.h),
          _buildForm(context, cubit),
        ],
      ),
    );
  }

  Widget _buildAvatarSection(
      BuildContext context, FamilyCreateCubit cubit, FamilyCreateState state) {
    return Stack(
      alignment: Alignment.center,
      children: [
        CircleAvatarCustom(
          borderWidth: 2.w,
          size: 88.w,
          imageFile: state.imageFile,
          imageUrl: cubit.profileHandler.photoUrl ?? '',
          borderColor: appTheme.borderColorV2,
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: GestureDetector(
            onTap: (){},
            child: Container(
              padding: EdgeInsets.all(2.w),
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: ButtonIcon(
                cubit.familyId != null
                  ? Assets.icons.icEdit.path
                  : Assets.icons.icPlus.path,
                () async {
                  cubit.onUpdateAvatar();
                },
                size: 24.w,
                sizeIcon: 16.w,
                colorIcon: Colors.white,
                bg: appTheme.primaryColorV2,
                border: Border.all(
                  width: 1.w,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildForm(BuildContext context, FamilyCreateCubit cubit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextBox(
          fieldNode: cubit.profileHandler.familyName,
          validatedForm: cubit.formHandler.formValidated,
          hintText: LocaleKeys.hint_family_name_text.tr(),
        ),
        SizedBox(height: 21.h),
        // _buildTextBox(
        //   fieldNode: cubit.profileHandler.description,
        //   validatedForm: cubit.formHandler.formValidated,
        //   hintText: LocaleKeys.hint_enter_description.tr(),
        //   maxLine: 3,
        // ),
        SizedBox(height: 21.h),
        PrimaryButtonV2(
          text: LocaleKeys.create_family.tr(),
          onTap: () => cubit.formHandler.onSubmit(),
        ),
      ],
    );
  }

  Widget _buildTextBox({
    required dynamic fieldNode,
    required ValueNotifier<bool> validatedForm,
    required String hintText,
    int maxLine = 1,
  }) {
    return ValueListenableBuilder(
      valueListenable: validatedForm,
      builder: (context, isValidate, _) => TitleTextFieldV2(
        fieldNode: fieldNode,
        validatedForm: isValidate,
        hintText: hintText,
        maxLine: maxLine,
        prefix: SvgPicture.asset(
          Assets.icons.icHome.path,
        ),
        showTitle: true,
      ),
    );
  }
}
