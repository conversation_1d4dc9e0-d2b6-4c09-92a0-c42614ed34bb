import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';

enum FamilyCreateStatus {
  initial,
  loading,
  success,
  error,
}

class FamilyCreateState extends BaseState {
  final File? imageFile;
  final String? photoUrl;
  final FamilyCreateStatus status;
  final String? errorMessage;
  bool isChange = false;


  FamilyCreateState({
    this.imageFile,
    this.photoUrl,
    this.status = FamilyCreateStatus.initial,
    this.errorMessage,
    this.isChange = false,
  });

  FamilyCreateState copyWith({
    File? imageFile,
    String? photoUrl,
    FamilyCreateStatus? status,
    String? errorMessage,
    bool? isChange,
  }) {
    return FamilyCreateState(
      imageFile: imageFile ?? this.imageFile,
      photoUrl: photoUrl ?? this.photoUrl,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      isChange: isChange ?? this.isChange,
    );
  }

  @override
  List<Object?> get props => [imageFile, photoUrl, status, errorMessage, isChange];
}
