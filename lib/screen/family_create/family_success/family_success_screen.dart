import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/base/widget/cubit/screen/family_home_base_page.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_create/family_invite/family_invite_parameter.dart';
import 'package:family_app/screen/family_create/family_success/family_success_cubit.dart';
import 'package:family_app/screen/family_create/family_success/family_success_state.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/custom_border_button.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class FamilySuccessPage extends BaseBlocProvider<FamilySuccessState, FamilySuccessCubit> {
  const FamilySuccessPage({super.key, this.isSignUp = false});

  final bool isSignUp;

  @override
  Widget buildPage() => FamilySuccessView(isSignUp: isSignUp);

  @override
  FamilySuccessCubit createCubit() => FamilySuccessCubit(accountService: locator.get());
}

class FamilySuccessView extends StatefulWidget {
  const FamilySuccessView({super.key, this.isSignUp = false});

  final bool isSignUp;

  @override
  State<FamilySuccessView> createState() => _FamilySuccessViewState();
}

class _FamilySuccessViewState extends FamilyHomeBasePage<FamilySuccessView, FamilySuccessState, FamilySuccessCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  Widget buildBody(BuildContext context, FamilySuccessCubit cubit, FamilySuccessState state) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Positioned(top: 44, left: 0, right: 0, child: Assets.images.bgSuccess.image()),
        SafeArea(
          child: Padding(
            padding: padding(vertical: 16),
            child: Column(
              children: [
                if (!widget.isSignUp) const AppBarCustom(),
                Padding(
                  padding: padding(top: 20, horizontal: 32),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Stack(
                            alignment: Alignment.center,
                            children: [
                              SvgPicture.asset(Assets.icons.border.path),
                              SvgPicture.asset(Assets.icons.tick.path),
                            ],
                          ),
                          SizedBox(width: 6.w),
                          Text('${LocaleKeys.you_have_created_a_new_family.tr()}!', style: AppStyle.medium16()),
                        ],
                      ),
                      SizedBox(height: 23.h),
                      Container(),
                      CircleAvatarCustom(borderWidth: 3.w, size: 80),
                      SizedBox(height: 10.h),
                      Text('${cubit.accountService.account?.familyName ?? ''}!', style: AppStyle.medium22()),
                      SizedBox(height: 58.h),
                      PrimaryButton(
                        text: LocaleKeys.invite_text.tr(),
                        onTap: () => context.replaceRoute(
                            FamilyInviteRoute(parameter: FamilyInviteParameter(type: FamilyInviteType.auth))),
                      ),
                      SizedBox(height: 14.h),
                      CustomBorderButton(
                        buttonText: LocaleKeys.enjoy_text.tr(),
                        radius: 25,
                        onPressed: () => context.router.replaceAll([const MainRoute()]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
