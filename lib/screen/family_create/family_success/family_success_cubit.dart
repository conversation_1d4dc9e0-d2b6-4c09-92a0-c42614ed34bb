import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/screen/family_create/family_success/family_success_state.dart';

class FamilySuccessCubit extends BaseCubit<FamilySuccessState> {
  final AccountService accountService;

  FamilySuccessCubit({required this.accountService}) : super(FamilySuccessState());
}
