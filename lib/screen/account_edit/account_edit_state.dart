import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';

enum AccountEditStatus { none, loading, success, error }

class AccountEditState extends BaseState {
  final File? imageFile;
  final AccountEditStatus status;
  final bool isShowYear;
  final DateTime? birthday;
  final bool isChanging;

  AccountEditState({
    this.imageFile,
    this.status = AccountEditStatus.none,
    this.isShowYear = true,
    this.isChanging = false,
    this.birthday,
  });

  @override
  List<Object?> get props => [imageFile, status, isShowYear, birthday, isChanging];

  AccountEditState copyWith({
    File? imageFile,
    AccountEditStatus? status,
    bool? isShowYear,
    DateTime? birthday,
    bool? isChanging,
  }) {
    return AccountEditState(
      status: status ?? this.status,
      imageFile: imageFile ?? this.imageFile,
      isShowYear: isShowYear ?? this.isShowYear,
      birthday: birthday ?? this.birthday,
      isChanging: isChanging ?? this.isChanging,
    );
  }
}
