import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/account_edit/account_edit_cubit.dart';
import 'package:family_app/screen/account_edit/account_edit_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/bottom_sheet/date_picker_bottom_sheet.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/line_widget.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/switch.dart';
import 'package:family_app/widget/textfield/row_text_field_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class AccountEditPage extends BaseBlocProvider<AccountEditState, AccountEditCubit> {
  const AccountEditPage({super.key});

  @override
  Widget buildPage() => const AccountEditView();

  @override
  AccountEditCubit createCubit() => AccountEditCubit(
        accountService: locator.get(),
        familyRepository: locator.get(),
        mainCubit: locator.get(),
      );
}

class AccountEditView extends StatefulWidget {
  const AccountEditView({super.key});

  @override
  State<AccountEditView> createState() => _AccountEditViewState();
}

class _AccountEditViewState extends BaseBlocNoAppBarPageState<AccountEditView, AccountEditState, AccountEditCubit> {
  // @override
  // Color get backgroundColor => appTheme.whiteText;

  @override
  String get title => LocaleKeys.edit_profile.tr();

  @override
  bool get isSafeArea => true;

  @override
  Widget buildAppBar(BuildContext context, AccountEditCubit cubit, AccountEditState state) {
    return CustomAppBar2(
      title: title,
      showBack: true,
    );
  }

  @override
  bool listenWhen(AccountEditState previous, AccountEditState current) {
    if (previous.status != current.status) {
      if (current.status == AccountEditStatus.success) {
        showSimpleToast(LocaleKeys.update_account_info_success.tr());
        context.maybePop();
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, AccountEditCubit cubit, AccountEditState state) {
    return Stack(
      children: [
        SingleChildScrollView(
          child: Container(
            margin: const EdgeInsets.only(top: 16, left: 8, right: 8),
            padding: padding(all: 16),
            decoration: BoxDecoration(
              color: appTheme.backgroundWhite,
              borderRadius: const BorderRadius.all(Radius.circular(20)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: cubit.onUpdateAvatar,
                    behavior: HitTestBehavior.opaque,
                    child: Stack(
                      children: [
                        CircleAvatarCustom(
                          imageUrl: cubit.accountService.account?.photoUrl ?? '',
                          color: appTheme.gray2Color,
                          borderWidth: 3.w,
                          size: 80,
                          imageFile: state.imageFile,
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: InkWell(
                            onTap: cubit.onUpdateAvatar,
                            child: Assets.icons.icTakePhoto.svg(
                              width: 32.w,
                              height: 32.h,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20.h),
                Text(LocaleKeys.email_text.tr(), style: AppStyle.regular14V2(color: appTheme.grayV2)),
                Container(
                  width: double.infinity,
                  padding: padding(horizontal: 16, vertical: 16),
                  decoration: BoxDecoration(
                    color: appTheme.borderColorV2,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: appTheme.borderColor),
                  ),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    accountService.account?.email ?? '',
                    style: AppStyle.regular16V2(color: appTheme.blackText),
                  ),
                ),
                // RowTextFieldView(
                //   handler: cubit.email,
                //   topPadding: 0,
                //   bottomSizedBox: 0,
                //   formHandler: cubit.form,
                //   border: Border.all(color: appTheme.borderColor),
                // ),
                SizedBox(height: 24.h),
                Text(LocaleKeys.username.tr(), style: AppStyle.regular14V2(color: appTheme.grayV2)),
                // TextField(
                //   controller: cubit.textEditingController,
                //   decoration: InputDecoration(
                //     hintText: LocaleKeys.username.tr(),
                //     hintStyle: AppStyle.regular14V2(color: appTheme.hintColor),
                //     filled: true,
                //     fillColor: appTheme.whiteText,
                //     border: OutlineInputBorder(
                //       borderRadius: BorderRadius.circular(8),
                //       borderSide: BorderSide(color: appTheme.hintColor, width: 1),
                //     ),
                //     focusedBorder: OutlineInputBorder(
                //       borderRadius: BorderRadius.circular(8),
                //       borderSide: BorderSide(color: appTheme.primaryColorV2, width: 1),
                //     ),
                //     enabledBorder: OutlineInputBorder(
                //       borderRadius: BorderRadius.circular(8),
                //       borderSide: BorderSide(color: appTheme.hintColor, width: 1),
                //     ),
                //   ),
                //   onChanged: (e) {
                //     cubit.onChangedName(e);
                //   },
                // ),
                RowTextFieldView(
                  handler: cubit.name,
                  topPadding: 0,
                  bottomSizedBox: 0,
                  formHandler: cubit.form,
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  border: Border.all(color: appTheme.borderColor),

                ),
                SizedBox(height: 24.h),
                Text(LocaleKeys.birthday.tr(), style: AppStyle.regular14V2(color: appTheme.grayV2)),
                Container(
                  padding: padding(horizontal: 16, vertical: 16),
                  decoration: BoxDecoration(
                    color: appTheme.whiteText,
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                    border: Border.all(color: appTheme.borderColorV2, width: 1),
                  ),
                  child: InkWell(
                    // onTap: () => DatePickerBottomSheet.show(
                    //   context,
                    //   title: title,
                    //   onSelected: cubit.onChangeBirthday,
                    //   initialDate: state.birthday,
                    // ),
                    onTap: () => cubit.onTapChangeBirthday(context),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            state.birthday?.ddMMyy ?? 'dd / MM / yyyy',
                            style: AppStyle.regular14V2(color: state.birthday != null ? appTheme.blackText : appTheme.hintColor),
                          ),
                        ),
                        SvgPicture.asset(
                          Assets.icons.icCalendar32.path,
                          width: 24.w,
                          height: 24.h,
                          colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Text(LocaleKeys.show_year.tr(), style: AppStyle.regular14V2(color: appTheme.grayV2)),
                    const Spacer(),
                    SwitchCustom(state.isShowYear, cubit.handelChangeShowYear),
                  ],
                ),
                SizedBox(height: 32.h),
                PrimaryButton(
                  text: LocaleKeys.save_changes.tr(),
                  backgroundColor: appTheme.primaryColorV2,
                  borderRadius: 8,
                  onTap: () {
                    // if (state.imageFile != null) {
                    //   CachedNetworkImage.evictFromCache(accountService.account?.photoUrl ?? '');
                    // }
                    // cubit.form.onSubmit();
                    cubit.onSaveChange();
                  },
                  isActive: state.isChanging,
                  isLoading: state.status == AccountEditStatus.loading,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
