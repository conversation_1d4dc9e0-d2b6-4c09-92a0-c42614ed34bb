import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/account_edit/account_edit_state.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/upload.dart';
import 'package:family_app/widget/image/image_picker_handler.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class AccountEditCubit extends BaseCubit<AccountEditState> {
  final AccountService accountService;
  final IFamilyRepository familyRepository;
  final HomeCubit mainCubit;

  AccountEditCubit({
    required this.accountService,
    required this.familyRepository,
    required this.mainCubit,
  }) : super(AccountEditState());

  // TextEditingController textEditingController = TextEditingController();

  late TextFieldHandler name;
  // late TextFieldHandler email;
  late FormTextFieldHandler form;

  @override
  Future<void> onInit() async {
    super.onInit();
    // textEditingController.text = accountService.account?.fullName ?? '';
    name = TextFieldHandler(
      field: 'Name',
      // title: LocaleKeys.name_text.tr(),
      inputType: TextInputType.name,
      initializeText: accountService.account?.fullName ?? '',
      onListenText: () => onChangedName(name.text),
    );
    // email = TextFieldHandler(
    //   field: 'Email',
    //   // title: LocaleKeys.email_text.tr(),
    //   inputType: TextInputType.emailAddress,
    //   initializeText: accountService.account?.email ?? '',
    // );
    form = FormTextFieldHandler(handlers: [], validateForm: onValidateForm);

    await accountService.initMyProfile();

    DateTime? birthday = accountService.account?.birthday?.convertDateTime;
    bool isShowYear = accountService.account?.showYear == 1;
    emit(state.copyWith(
      birthday: birthday,
      isShowYear: isShowYear,
    ));
  }

  @override
  Future<void> close() {
    // form.dispose();
    return super.close();
  }

  void onUpdateAvatar() async {
    try {
      showLoading();

      final pickedFile = await ImagePickerHandler.onGetImage();

      if (pickedFile != null) {
        emit(state.copyWith(imageFile: pickedFile, isChanging: true));
      }
    } catch (e) {
      showSimpleToast(LocaleKeys.update_fail_text.tr());
    } finally {
      dismissLoading();
    }
  }

  Future<void> onValidateForm_old(Map<String, dynamic> json) async {
    emit(state.copyWith(status: AccountEditStatus.loading));
    try {
      if (state.imageFile != null) {
        final result = await familyRepository.updateAvatar(accountService.familyId, file: state.imageFile!);
        if (!result) {
          emit(state.copyWith(status: AccountEditStatus.error));
          return;
        }
      }

      final result = await familyRepository.updateFamilyInfo(fullName: name.text);
      accountService.setAccount(result);
      emit(state.copyWith(status: AccountEditStatus.success));
    } catch (e) {
      emit(state.copyWith(status: AccountEditStatus.error));
    }
  }

  Future<void> onValidateForm(Map<String, dynamic> json) async {
    onSaveChange();
  }

  void handelChangeShowYear(bool value) {
    emit(state.copyWith(isShowYear: value, isChanging: true));
  }

  onTapChangeBirthday(BuildContext context) async {
    final date = state.birthday;
    final now = DateTime.now();
    final DateTime? datePicked = await showDatePicker(
      context: context,
      initialDate: date ?? now,
      firstDate: DateTime(now.year - 150),
      lastDate: DateTime(now.year + 1),
    );
    if (datePicked != null) {
      emit(state.copyWith(birthday: datePicked, isChanging: true));
    }
  }

  onSaveChange() async {
    emit(state.copyWith(status: AccountEditStatus.loading));
    final accountUuid = accountService.account?.uuid ?? '';
    try {
      if (state.imageFile != null) {
        final upload = Upload(familyId: accountUuid);
        final storageModel = await upload.uploadImage(state.imageFile!, "profile");

        if (storageModel.uuid != null) {
          logd('Image uploaded successfully.');
          // return storageModel;
        } else {
          logd('Image upload failed.');
        }

        //Update avatar name
        final result = await familyRepository.updateAvatarS3(accountUuid, fileUuid: storageModel.uuid!);
        if (!result) {
          emit(state.copyWith(status: AccountEditStatus.error));
          return;
        }
        logd("updateFamilyInfo storageModel.uuid: ${storageModel.uuid}");
      }

      // Map<String, dynamic> data = {};
      // if (state.birthday != null) {
      //   data['birthday'] = state.birthday!.yyyy_MM_dd;
      // }
      // if (state.isShowYear) {
      //   data['show_year'] = state.isShowYear ? "true" : "false";
      // }
      // if (name.text.isNotEmpty) {
      //   data['full_name'] = name.text;
      // }
      //
      // if (data.isNotEmpty) {
      //
      // }

      final result = await familyRepository.updateProfileById(
        accountUuid,
        familyName: accountService.account?.familyName ?? '',
        full_name: name.text,
        birthday: state.birthday?.yyyy_MM_dd,
        showYear: state.isShowYear ? 1 : 0,
      );
      logd("updateProfileInfo result: $result");
      // accountService.setAccount(result);
      accountService.initMyProfile();

      mainCubit.refreshList();
      emit(state.copyWith(status: AccountEditStatus.success, isChanging: false));
    } catch (e) {
      emit(state.copyWith(status: AccountEditStatus.error, isChanging: false));
    }
  }

  void onChangedName(String name) {
    if (name.isNotEmpty) {
      emit(state.copyWith(isChanging: true));
    } else {
      emit(state.copyWith(isChanging: false));
    }
  }
}
