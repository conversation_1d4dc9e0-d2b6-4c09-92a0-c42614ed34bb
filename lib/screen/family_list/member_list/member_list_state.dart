import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';

class MemberListState extends BaseState {
  final List<Account> members;
  String url_invite = '';
  bool isEdit = false;
  File? imageFile;
  String photoUrl = '';
  bool isChange = false;


  MemberListState({
    this.members = const [],
    this.url_invite = '',
    this.isEdit = false,
    this.imageFile,
    this.photoUrl = '',
    this.isChange = false
  });

  MemberListState copyWith({
    List<Account>? members,
    String? url_invite,
    bool? isEdit,
    File? imageFile,
    String? photoUrl,
    bool? isChange,
  }) {
    return MemberListState(
      members: members ?? this.members,
      url_invite: url_invite ?? this.url_invite,
      isEdit: isEdit ?? this.isEdit,
      imageFile: imageFile ?? this.imageFile,
      photoUrl: photoUrl ?? this.photoUrl,
      isChange: isChange ?? this.isChange,
    );
  }

  @override
  List<Object?> get props => [members, url_invite, isEdit, imageFile, photoUrl, isChange];
}
