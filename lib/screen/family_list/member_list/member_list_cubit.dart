import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/family_profile.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_list/family_list_cubit.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_state.dart';
import 'package:family_app/screen/family_list/scale_avatar/scale_avatar_parameter.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../data/model/storage_model.dart';
import '../../../data/repository/family/model/invite_parameter.dart';
import '../../../utils/log/app_logger.dart';
import '../../../utils/upload.dart';
import '../../../widget/image/image_picker_handler.dart';
import '../../../widget/textfield/text_field_node.dart';
import '../../main/home/<USER>';

class MemberListCubit extends BaseCubit<MemberListState> {
  final AccountService accountService;
  final IFamilyRepository familyRepository;
  final MemberListParameter parameter;
  final HomeCubit mainCubit = locator.get<HomeCubit>();
  FamilyProfile? family;

  MemberListCubit({
    required this.accountService,
    required this.familyRepository,
    required this.parameter,
    required this.family,
  }) : super(MemberListState());

  late final StreamSubscription<List<Account>>? _memberSub;
  late TextFieldHandler nameFamily;
  late FormTextFieldHandler formHandler;

  @override
  Future<void> close() {
    _memberSub?.cancel();
    locator.unregister<MemberListCubit>();
    return super.close();
  }

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();
    _memberSub = accountService.userRole == Role.owner
        ? accountService.memberInFamily.stream.listen((member) {
            if (isClose) return;
            emit(state.copyWith(members: member));
          })
        : null;
    refresh();
    nameFamily = TextFieldHandler(
      field: 'nameFamily',
      inputType: TextInputType.name,
      isFieldValid: (value) => value.isNotEmpty,
      isRequired: true,
    );
    formHandler = FormTextFieldHandler(handlers: [nameFamily], validateForm: onSubmit);
  }



  refresh() async {
    showLoading();
    final familyId = parameter.familyId;
    try {
      family = await familyRepository.getProfileById(familyId);
      final members = await familyRepository.getUserInFamily(familyId);
      emit(state.copyWith(members: members));
    } catch (e) {
      log(e.toString());
    }
    dismissLoading();
  }

  onTapEditNameFamily() {
    nameFamily.text = family?.familyName ?? '';
    nameFamily.requestFocus();
    emit(state.copyWith(isEdit: true));
  }

  onUpdateNameFamily() {
    if (nameFamily.text.isEmpty) {
      showSimpleToast(LocaleKeys.family_name_invalid_text.tr());
      return;
    }
    nameFamily.unFocus();
    emit(state.copyWith(isEdit: false));
    // showLoading();
    // familyRepository.updateFamilyName(
    //   parameter.familyId,
    //   name
  }

  Future<void> onUpdateRoleOfMember(
      int index, Account member, String role) async {
    showLoading();
    try {
      final updatedMember =
          await familyRepository.updateMember(member.uuid ?? '', role: role);
      final updatedMembers = List<Account>.from(state.members)
        ..[index] = updatedMember;
      emit(state.copyWith(members: updatedMembers));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    } finally {
      dismissLoading();
    }
  }

  void onInviteSuccess(Account member) {
    final members = [...state.members];
    members.add(member);
    emit(state.copyWith(members: members));
  }

  Future<void> getUrlInvite() async {
    try {
      final result = await familyRepository
          .invite(InviteParameter(familyId: parameter.familyId));
      if (result != null) {
        Share.share('${result.invite_url}');
        print("getUrlInvite ${result.invite_url}");
        // showSimpleToast(LocaleKeys.action_fail.tr());
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> onDeleteMember(BuildContext context, Account member) async {
    await DialogUtils.showDeleteDialogV2(
      context,
      content: LocaleKeys.confirm_delete_member
          .tr(namedArgs: {'field': '${member.fullName}'}),
      confirmText: LocaleKeys.remove.tr(),
      onConfirm: () async {
        final result = await familyRepository.kickMember(member.uuid ?? '');
        if (result) {
          refresh();
          FamilyListCubit familyListCubit = locator.get();
          familyListCubit.refresh();
          context.maybePop();
          showSimpleToast(LocaleKeys.remove_member_success.tr());
        } else {
          showSimpleToast(LocaleKeys.action_fail.tr());
        }
      },
    );
  }

  bool isFamilyOwner() {
    // Because only the owner can invite themselves
    return family?.userUuid == accountService.account?.uuid;
  }

  Future<void> onUpdateAvatar(BuildContext context) async {
    final pickedFile = await ImagePickerHandler.onGetImage();
    if (pickedFile != null) {
      emit(state.copyWith(imageFile: pickedFile));
    }
  }

  Future<void> onRemoveAvatar() async {
    try {
      showLoading();

      String? newFamilyId = parameter.familyId;
      await familyRepository.updateAvatarS3(
          newFamilyId,
          fileUuid: "",
        );
      emit(state.copyWith(isChange: true));
    } catch (e) {
      // emit(state.copyWith(
      //   status: FamilyCreateStatus.error,
      //   errorMessage: LocaleKeys.family_creation_failed.tr(),
      // ));
    } finally {
      dismissLoading();
      refresh();
    }
  }



  String? getPhotoURLOwner() {
    final members = [...state.members];
    if (members.isEmpty) return '';

    final owners = members.where((m) => m.relationship == 'owner');
    if (owners.isEmpty) return '';

    return owners.first.photoUrl?.toString() ?? '';
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      await familyRepository.updateProfileById(
        family?.uuid ?? "",
        familyName: nameFamily.text,
      );
      emit(state.copyWith(isChange: true));
    } catch (e) {
      // emit(state.copyWith(
      //   status: FamilyCreateStatus.error,
      //   errorMessage: LocaleKeys.family_creation_failed.tr(),
      // ));
    } finally {
      dismissLoading();
      await accountService.initMyProfile();
      await mainCubit.onRefresh();
      nameFamily.unFocus();
      emit(state.copyWith(isEdit: false));
      refresh();
    }
  }

  Future<StorageModel> uploadImage(File imageFile, String familyId) async {
    try {
      final upload = Upload(
        familyId: familyId,
      );
      final storageModel = await upload.uploadImage(imageFile, null);
      if (storageModel.uuid != null) {
        return storageModel;
      }
    } catch (e) {
      AppLogger.d('Image upload error: $e');
    }
    return const StorageModel();
  }

  Future<void> updateAvatar(Uint8List bytes) async {
    try {
      showLoading();

      String? newFamilyId = parameter.familyId;

        final file = await _bytesToTempFile(bytes);
        final storageModel = await uploadImage(file, newFamilyId);
        if (storageModel.uuid?.isNotEmpty == true) {
          await familyRepository.updateAvatarS3(
            newFamilyId,
            fileUuid: storageModel.uuid!,
          );
        }
    } catch (e) {
      // emit(state.copyWith(
      //   status: FamilyCreateStatus.error,
      //   errorMessage: LocaleKeys.family_creation_failed.tr(),
      // ));
    } finally {
      dismissLoading();
      await accountService.initMyProfile();
      await mainCubit.onRefresh();
      refresh();
    }
  }

  String _two(int n) => n.toString().padLeft(2, '0');

  Future<File> _bytesToTempFile(Uint8List bytes) async {
    final dir = await getTemporaryDirectory();

    final now = DateTime.now();
    final formattedTime = '${now.year}-${_two(now.month)}-${_two(now.day)}_${_two(now.hour)}-${_two(now.minute)}-${_two(now.second)}';
    final filename = 'cropped_$formattedTime.jpg';

    final file = File('${dir.path}/$filename');
    return await file.writeAsBytes(bytes);
  }
}
