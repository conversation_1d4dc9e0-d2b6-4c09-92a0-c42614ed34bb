import 'dart:typed_data';

import 'package:adaptive_action_sheet/adaptive_action_sheet.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/family_profile.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_list/member_detail/member_detail_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_cubit.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/screen/family_list/member_list/member_list_state.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/line_widget.dart';
import 'package:family_app/widget/member_info_row_widget.dart';
import 'package:family_app/widget/popup/popup.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

import '../../../utils/assets/shadow_util.dart';
import '../../../widget/appbar_custom.dart';
import '../../../widget/button_icon.dart';
import '../../../widget/image/circle_avatar_custom.dart';
import '../../../widget/image_asset_custom.dart';
import '../../../widget/textfield/title_text_field_v2.dart';
import '../../main/home/<USER>';
import '../scale_avatar/scale_avatar_parameter.dart';

@RoutePage()
class MemberListPage
    extends BaseBlocProvider<MemberListState, MemberListCubit> {
  const MemberListPage({required this.parameter, super.key, this.family});

  final FamilyProfile? family;
  final MemberListParameter parameter;

  @override
  Widget buildPage() => MemberListView(family: family, parameter: parameter);

  @override
  MemberListCubit createCubit() => MemberListCubit(
        accountService: locator.get(),
        familyRepository: locator.get(),
        parameter: parameter,
        family: family,
      );
}

class MemberListView extends StatefulWidget {
  const MemberListView({super.key, this.family, required this.parameter});

  final FamilyProfile? family;
  final MemberListParameter parameter;

  @override
  State<MemberListView> createState() => _MemberListViewState();
}

class _MemberListViewState extends BaseBlocPageState<MemberListView,
    MemberListState, MemberListCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  void onTapScreen(BuildContext context) {
    super.onTapScreen(context);
  }

  Widget buildAppBar(
      BuildContext context, MemberListCubit cubit, MemberListState state) {
    return AppBarCustom(
      viewPadding: padding(horizontal: 16, vertical: 8),
      titleView: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 20, right: 40),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (state.isEdit) ...[
                    ValueListenableBuilder(
                      valueListenable: cubit.formHandler.formValidated,
                      builder: (context, isValidate, _) => TitleTextFieldV2(
                          fieldNode: cubit.nameFamily,
                          validatedForm: isValidate),
                    ),
                  ] else ...[
                    Text(
                      cubit.family?.familyName ?? "",
                      style: AppStyle.medium16(color: appTheme.blackText),
                      textAlign: TextAlign.center,
                    ),
                  ]
                ],
              ),
            ),
          ),
        ],
      ),
      // title: "${state.threadDetail?.name ?? ''} \n ${state.threadDetail?.members?.length ?? 0} members",
      onBack: () {
        Navigator.pop(context, state.isChange);
      },
      actions: [
        if (cubit.isFamilyOwner())
          GestureDetector(
            onTap: () async {
              if (state.isEdit) {
                // Save changes
                cubit.formHandler.onSubmit();
              } else {
                // Edit family name
                cubit.onTapEditNameFamily();
              }
              // MemoryUpsertBts.show(context).then(
              //       (result) {
              //     if (result == true) {
              //       c.fetchMemories();
              //     }
              //   },
              // );
            },
            behavior: HitTestBehavior.opaque,
            child: CircleItem(
              backgroundColor: appTheme.backgroundV2,
              padding: padding(all: 7),
              child: SvgPicture.asset(state.isEdit
                  ? Assets.icons.iconAgree.path
                  : Assets.icons.iconEdit.path),
            ),
          )
      ],
    );
  }

  @override
  Widget buildBody(
      BuildContext context, MemberListCubit cubit, MemberListState state) {
    return BlocListener<MemberListCubit, MemberListState>(
        listenWhen: (prev, curr) => prev.imageFile != curr.imageFile ||
            prev.isChange != curr.isChange,
        listener: (context, state) async {
          final cubit = context.read<MemberListCubit>();
          if (state.imageFile != null) {
            final result = await context.pushRoute(
              ScaleAvatarRoute(
                parameter: ScaleAvatarParameter(
                  imageFile: state.imageFile!,
                  familyUuid: cubit.family?.familyUuid ?? '',
                ),
              ),
            );
            if (result != null && result is Uint8List) {
              cubit.updateAvatar(result);
            }
          }
        },
        child: Padding(
          padding: padding(top: 0),
          child: SingleChildScrollView(
            padding: EdgeInsets.only(bottom: context.bottom),
            child: state.members.isEmpty
                ? Container()
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: _buildAvatarSection(context, cubit, state),
                      ),
                      Padding(
                        padding:
                            const EdgeInsets.only(top: 18, left: 12, right: 8),
                        child: Text(
                          LocaleKeys.members_text.tr(namedArgs: {
                            'field': cubit.family?.members.toString() ??
                                state.members.length.toString()
                          }),
                          style: AppStyle.bold16V2(color: appTheme.blackTextV2),
                          textAlign: TextAlign.left,
                        ),
                      ),
                      Padding(
                        padding:
                            const EdgeInsets.only(top: 8, left: 8, right: 8),
                        child: _boxBuilder(([...state.members]..sort((a, b) {
                                final aIsPriority = a.familyMemberUuid == cubit.parameter.familyId;
                                final bIsPriority = b.familyMemberUuid == cubit.parameter.familyId;

                                if (aIsPriority && !bIsPriority) return -1;
                                if (!aIsPriority && bIsPriority) return 1;
                                return 0;
                              }))
                            .asMap()
                            .entries
                            .map((entry) {
                          final index = entry.key;
                          final member = entry.value;

                          return InkWell(
                            onTap: () {
                              context.pushRoute(MemberDetailRoute(
                                  parameter: MemberDetailParameter(
                                isFamilyOwner: cubit.isFamilyOwner(),
                                member: member,
                                familyUuid: cubit.family?.familyUuid ?? '',
                              )));
                            },
                            child: Column(
                              children: [
                                _buildMemberCard(cubit, state, member),
                                // if (index < state.members.length - 1) _buildDivider(),
                              ],
                            ),
                          );
                        }).toList()),
                      ),
                      if (cubit.isFamilyOwner())
                        Padding(
                          padding:
                              const EdgeInsets.only(top: 8, left: 8, right: 8),
                          child: _buildAddMember(cubit),
                        ),
                    ],
                  ),
          ),
        ));
  }

  Widget _buildDivider() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Divider(
        height: 1,
        thickness: 1,
        color: appTheme.borderColor,
      ),
    );
  }

  Widget _buildMemberCard(
      MemberListCubit cubit, MemberListState state, Account member) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: UserProfileRow(
        name: member.fullName ?? '',
        text: member.relationship ?? "",

        imageUrl: member.photoUrl ?? '',
        widget: cubit.isFamilyOwner()
            ? member.familyMemberUuid != cubit.accountService.account?.uuid
                ? null//_buildDeleteMember(cubit, state, member)
                : Text(member.familyMemberUuid == cubit.parameter.familyId
                        ? LocaleKeys.admin.tr()
                        : member.role.toString().tr(),
                    style: AppStyle.bold12V2(color: appTheme.primaryColorV2))
            : Text(
            member.familyMemberUuid == cubit.parameter.familyId
                ? LocaleKeys.admin.tr()
                : '',
            style: AppStyle.bold12V2(
                color: appTheme
                    .primaryColorV2)) ,
      ),
    );
  }

  _buildDeleteMember(
      MemberListCubit cubit, MemberListState state, Account member) {
    return InkWell(
      onTap: () {
        cubit.onDeleteMember(context, member);
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 12, bottom: 12),
        child: Container(
          padding: padding(horizontal: 8, vertical: 4),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            border: Border.all(width: 1.w, color: appTheme.typeRedColor),
            color: appTheme.whiteText,
          ),
          child: Row(
            children: [
              Assets.icons.iconDelete.svg(width: 12.w, height: 12.w),
              SizedBox(width: 4.w),
              Text(
                LocaleKeys.remove.tr(),
                style: AppStyle.bold12V2(color: appTheme.typeRedColor),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRolePopup(MemberListCubit cubit, MemberListState state,
      Account member, String role) {
    return CustomPopup(
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: Role.roles
            .asMap()
            .entries
            .map((e) => _buildRoleView(
                  cubit: cubit,
                  member: member,
                  role: e.value,
                  isFinal: e.key == Role.roles.length - 1,
                ))
            .toList(),
      ),
      child: _buildUpdateRoleView(role.tr()),
    );
  }

  Widget _buildRoleText(String role) {
    return Text(
      role.tr(),
      style: AppStyle.regular14(color: appTheme.typeRedColor),
    );
  }

  Widget _buildRoleView({
    required MemberListCubit cubit,
    required Account member,
    required String role,
    required bool isFinal,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildRoleOption(cubit: cubit, member: member, role: role),
        if (!isFinal) _buildRoleDivider(),
      ],
    );
  }

  Widget _buildRoleDivider() {
    return Padding(
      padding: padding(vertical: 8),
      child: LineWidget(
        widget: 68.w,
        height: 1.h,
        color: appTheme.borderColor,
      ),
    );
  }

  Widget _buildRoleOption({
    required MemberListCubit cubit,
    required Account member,
    required String role,
  }) {
    return GestureDetector(
      onTap: () {
        context.maybePop();
        cubit.onUpdateRoleOfMember(
          cubit.state.members.indexOf(member),
          member,
          role,
        );
      },
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(left: 4, top: 6),
        child: Text(
          role.tr(),
          style: AppStyle.regular14(
            color: role == member.role ? appTheme.typeRedColor : null,
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateRoleView(String role) {
    return Container(
      padding: padding(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1000),
        border: Border.all(width: 1.w, color: appTheme.hintColor),
        color: appTheme.whiteText,
      ),
      child: Row(
        children: [
          Text(role, style: AppStyle.medium12(color: appTheme.typeRedColor)),
          SizedBox(width: 4.w),
          Image.asset(Assets.images.downArrow.path, width: 8.w),
        ],
      ),
    );
  }

  Widget _boxBuilder(List<Widget> children) {
    return Container(
      padding: padding(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.w2),
        boxShadow: ShadowUtil.backgroundShadow,
      ),
      child: Column(children: children),
    );
  }

  Widget _buildInviteRow(BuildContext context, MemberListCubit cubit) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: cubit.getUrlInvite,
            child: Container(
              padding: padding(all: 8.w2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(999),
                color: appTheme.whiteText,
                boxShadow: ShadowUtil.backgroundShadow,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ImageAssetCustom(
                    imagePath: Assets.icons.iconInvite.path,
                    width: 40,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    LocaleKeys.add_new_member.tr(),
                    style: AppStyle.regular14(color: appTheme.lightBotColor),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAvatarSection(
      BuildContext context, MemberListCubit cubit, MemberListState state) {
    return state.members.isEmpty
        ? Container()
        : Stack(
            alignment: Alignment.center,
            children: [
              CircleAvatarCustom(
                borderWidth: 2.w,
                size: 88.w,
                imageUrl: cubit.getPhotoURLOwner() ?? '',
                borderColor: appTheme.borderColorV2,
              ),
              if (cubit.isFamilyOwner())
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: const BoxDecoration(
                      color: Colors.amber,
                      shape: BoxShape.circle,
                    ),
                    child: ButtonIcon(
                      Assets.icons.icPlus.path,
                      () async {
                        showAdaptiveActionSheet(
                          context: context,
                          androidBorderRadius: 30,
                          actions: <BottomSheetAction>[
                            BottomSheetAction(
                                title: Text(
                                  LocaleKeys.upload_new_avatar.tr(),
                                  style: AppStyle.regular17(
                                      color: appTheme.blueColorV2),
                                ),
                                onPressed: (context) {
                                  cubit.onUpdateAvatar(context);
                                  context.maybePop();
                                }),
                            BottomSheetAction(
                                title: Text(
                                  LocaleKeys.remove_avatar.tr(),
                                  style: AppStyle.regular17(
                                      color: appTheme.redColor),
                                ),
                                onPressed: (context) {
                                  cubit.onRemoveAvatar();
                                  context.maybePop();
                                }),
                          ],
                          cancelAction: CancelAction(
                            title: Text(
                              LocaleKeys.cancel.tr(),
                              style: AppStyle.regular17(
                                  color: appTheme.blueColorV2),
                            ),
                          ),
                        );
                        // cubit.onUpdateAvatar();
                      },
                      size: 24.w,
                      sizeIcon: 16.w,
                      colorIcon: Colors.white,
                      bg: appTheme.primaryColorV2,
                      border: Border.all(
                        width: 1.w,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
            ],
          );
  }

  Widget _buildAddMember(MemberListCubit cubit) {
    return InkWell(
      onTap: () {
        cubit.getUrlInvite();
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 12),
        child: Container(
          decoration: BoxDecoration(
            color: appTheme.backgroundWhite,
            borderRadius: BorderRadius.circular(20.w),
            border: null,
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            minTileHeight: 60,
            leading: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
              ),
              clipBehavior: Clip.hardEdge,
              // để bo góc cho SVG
              child: SvgPicture.asset(
                Assets.icons.icAddUser.path,
                width: 48,
                height: 48,
                fit: BoxFit.cover, // để ảnh phủ kín như BoxFit.cover
              ),
            ),
            title: Text(
              LocaleKeys.add_new_member.tr(),
              style: AppStyle.medium16(color: appTheme.blackText),
            ),
          ),
        ),
      ),
    );
  }
}
