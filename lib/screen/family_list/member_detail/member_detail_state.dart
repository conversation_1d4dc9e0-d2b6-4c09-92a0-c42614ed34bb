import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';

enum MemberDetailStatus { none, loading, success, error }

class MemberDetailState extends BaseState {
  final Account member;
  final bool isChanging;
  final bool isEditor;
  final bool isFamilyOwner;
  final MemberDetailStatus status;
  final bool isDelete;

  MemberDetailState({
    required this.member,
    this.isChanging = false,
    this.isEditor = false,
    this.isFamilyOwner = false,
    this.status = MemberDetailStatus.none,
    this.isDelete = false,
  });

  MemberDetailState copyWith({
    Account? member,
    bool? isChanging,
    bool? isEditor,
    bool? isFamilyOwner,
    MemberDetailStatus? status,
    bool? isDelete,
  }) {
    return MemberDetailState(
      member: member ?? this.member,
      isChanging: isChanging ?? this.isChanging,
      isEditor: isEditor ?? this.isEditor,
      status: status ?? this.status,
      isFamilyOwner: isFamilyOwner ?? this.isFamilyOwner,
      isDelete: isDelete ?? this.isDelete,
    );
  }

  @override
  List<Object?> get props => [member, isChanging, status, isEditor, isFamilyOwner, isDelete];
}
