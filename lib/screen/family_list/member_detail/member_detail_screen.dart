import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/switch.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../gen/assets.gen.dart';
import '../../../widget/circle_item.dart';
import '../../../widget/textfield/title_text_field_v2.dart';
import 'member_detail_cubit.dart';
import 'member_detail_parameter.dart';
import 'member_detail_state.dart';

@RoutePage()
class MemberDetailPage extends BaseBlocProvider<MemberDetailState, MemberDetailCubit> {
  const MemberDetailPage({super.key, required this.parameter});

  final MemberDetailParameter parameter;

  @override
  Widget buildPage() => MemberDetailView(parameter: parameter);

  @override
  MemberDetailCubit createCubit() => MemberDetailCubit(
        accountService: locator.get(),
        familyRepository: locator.get(),
        memberListCubit: locator.get(),
        parameter: parameter,
      );
}

class MemberDetailView extends StatefulWidget {
  const MemberDetailView({super.key, required this.parameter});

  final MemberDetailParameter parameter;

  @override
  State<MemberDetailView> createState() => _MemberDetailViewState();
}

class _MemberDetailViewState extends BaseBlocPageState<MemberDetailView, MemberDetailState, MemberDetailCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  void onTapScreen(BuildContext context) {
    super.onTapScreen(context);
  }

  @override
  bool listenWhen(MemberDetailState previous, MemberDetailState current) {
    if (previous.status != current.status) {
      if (current.status == MemberDetailStatus.success) {
        showSimpleToast(LocaleKeys.update_member_info_success.tr());
        context.maybePop();
      } else if (current.status == MemberDetailStatus.error) {
        showErrorToast(LocaleKeys.update_member_info_failed.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, MemberDetailCubit cubit, MemberDetailState state) {

    return AppBarCustom(
      viewPadding: padding(horizontal: 16, vertical: 8),
      titleView: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 20, right: 40),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                    Text(
                      LocaleKeys.view_member.tr(),
                      style: AppStyle.medium16(color: appTheme.blackText),
                      textAlign: TextAlign.center,
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
      onBack: () {
        Navigator.pop(context);
      },
      actions: [
        if (state.isFamilyOwner)
          GestureDetector(
            onTap: () async {
              cubit.onDeleteMember(context, widget.parameter.member);
            },
            behavior: HitTestBehavior.opaque,
            child: CircleItem(
              backgroundColor: appTheme.backgroundV2,
              padding: padding(all: 7),
              child: SvgPicture.asset(Assets.icons.icDeleteMember.path),
            ),
          )
      ],
    );
  }

  Widget _infoField({required String label, required String value, String? icon}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: padding(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: appTheme.borderGreyColor),
          ),
          alignment: Alignment.centerLeft,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (icon != null)
                SvgPicture.asset(
                  icon,
                  colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcIn),
                ),
              Expanded(child: Text(
                value,
                style: AppStyle.regular16V2(color: appTheme.blackText),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),)
            ],
          )
        ),
      ],
    );
  }

  final relationshipOptions = const ['dad', 'mom', 'son', 'grandson', 'husband', 'wife'];

  Widget _relationshipAutocomplete(MemberDetailCubit cubit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(LocaleKeys.relationship.tr(),
            style: AppStyle.regular14V2(color: appTheme.grayV2)),
        SizedBox(height: 8),
        Autocomplete<String>(
          optionsBuilder: (TextEditingValue textEditingValue) {
            if (textEditingValue.text.isEmpty) {
              return relationshipOptions;
            }
            return relationshipOptions.where((option) =>
                option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
          },
          initialValue: TextEditingValue(text: cubit.relationship.text),
          onSelected: (selection) {
            cubit.relationship.text = selection;
            setState(() {});
          },
          fieldViewBuilder: (context, controller, focusNode, onFieldSubmitted) {
            controller.text = cubit.relationship.text;
            controller.selection = TextSelection.fromPosition(
                TextPosition(offset: controller.text.length));
            return TextFormField(
              controller: controller,
              focusNode: focusNode,
              onChanged: (val) {
                cubit.relationship.text = val;
                setState(() {});
              },
              decoration: InputDecoration(
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: appTheme.borderColorV2),

                ),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                filled: true,
                fillColor: appTheme.transparentWhiteColor,
                hintText: LocaleKeys.relationship.tr(),
              ),
              style: AppStyle.regular16V2(color: appTheme.blackText),
            );
          },
          optionsViewBuilder: (context, onSelected, options) {
            return Align(
              alignment: Alignment.topLeft,
              child: Material(
                elevation: 4.0,
                borderRadius: BorderRadius.circular(8),
                child: SizedBox(
                  width: 300,
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: options.length,
                    itemBuilder: (context, index) {
                      final option = options.elementAt(index);
                      return ListTile(
                        title: Text(option[0].toUpperCase() + option.substring(1)),
                        onTap: () => onSelected(option),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, MemberDetailCubit cubit, MemberDetailState state) {
    final member = widget.parameter.member;
    return SingleChildScrollView(
      padding: EdgeInsets.only(bottom: context.bottom),
      child: Container(
        margin: const EdgeInsets.only(top: 8, left: 8, right: 8),
        padding: padding(all: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Column(
                children: [
                  CircleAvatarCustom(
                    imageUrl: state.member.photoUrl ?? '',
                    borderColor: appTheme.borderColorV2,
                    borderWidth: 2.w,
                    size: 110.w,
                  ),
                  Text(
                    widget.parameter.member.fullName ?? '',
                    style: AppStyle.bold28V2(color: appTheme.blackText),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    widget.parameter.member.relationship ?? '',
                    style: AppStyle.regular16V2(color: appTheme.blackText),
                    textAlign: TextAlign.center,
                  ),
                ],
              )
            ),
            SizedBox(height: 21.h),
            _infoField(label: LocaleKeys.username.tr(), value: cubit.birthday.isEmpty? '--/--/--' : cubit.birthday, icon: Assets.icons.icBirthdayV2.path),
            SizedBox(height: 21.h),
            _infoField(label: LocaleKeys.email_text.tr(), value: member.email ?? '-', icon: Assets.icons.icEmail.path),
            SizedBox(height: 21.h),

            if (state.isFamilyOwner) ...[
              _relationshipAutocomplete(cubit),
              SizedBox(height: 21.h,),

              TitleTextFieldV2(
                fieldNode: cubit.nickName,
                hintText: LocaleKeys.nickname.tr(),
                maxLine: 1,
                showTitle: true,
              )

            ] else ...[
              // _infoField(label: LocaleKeys.relationship.tr(), value: member.relationship ?? '-'),

            ],
            // if (member.role != 'owner') ...[
            //   SizedBox(height: 21.h),
            //   Row(
            //     children: [
            //       Text(LocaleKeys.editor.tr(),
            //           style: AppStyle.regular14V2(color: appTheme.grayV2)),
            //       const Spacer(),
            //       SwitchCustom(
            //         state.isEditor,
            //         cubit.handelChangeEditor,
            //         disabled: !state.isFamilyOwner, // disable if not owner
            //       ),
            //     ],
            //   ),
            // ],
            if (state.isFamilyOwner) ...[
              SizedBox(height: 21.h),
              PrimaryButtonV2(
                text: LocaleKeys.save_changes.tr(),
                onTap: () => cubit.form.onSubmit(),
                isActive: state.isChanging,
                isLoading: state.status == MemberDetailStatus.loading,
              ),
            ],
          ]
        ),
      ),
    );
  }
}
