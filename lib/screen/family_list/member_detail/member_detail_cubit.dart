import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/family_list/member_list/member_list_cubit.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/widgets.dart';

import '../../../data/model/account.dart';
import '../../../gen/assets.gen.dart';
import '../../../utils/dialog.dart';
import '../../../utils/flash/toast.dart';
import '../../../utils/loading.dart';
import '../family_list_cubit.dart';
import 'member_detail_parameter.dart';
import 'member_detail_state.dart';

class MemberDetailCubit extends BaseCubit<MemberDetailState> {
  final tag = "MemberDetailCubit";
  final AccountService accountService;
  final IFamilyRepository familyRepository;
  final MemberListCubit memberListCubit;
  final MemberDetailParameter parameter;

  MemberDetailCubit({
    required this.accountService,
    required this.familyRepository,
    required this.memberListCubit,
    required this.parameter,
  }) : super(MemberDetailState(member: parameter.member));

  late TextFieldHandler relationship, nickName;
  late FormTextFieldHandler form;
  late String birthday = "";

  @override
  Future<void> close() {
    locator.unregister<MemberDetailCubit>();
    return super.close();
  }

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();
    logd("$tag - onInit familyUuid  ${parameter.member.familyMemberUuid} == ${accountService.account?.familyUuid}");
    emit(state.copyWith(
      isFamilyOwner: parameter.isFamilyOwner,
      isEditor: parameter.member.role == Role.editor,
    ));
    relationship = TextFieldHandler(
      field: 'relationship',
      hintText: LocaleKeys.relationship.tr(),
      initializeText: parameter.member.relationship ?? '',
      errorText: (value) => LocaleKeys.relationship_invalid_text.tr(),
      isFieldValid: (value) => value.isNotEmpty,
      isRequired: true,
      onListenText: () => onChangedRelationship(relationship.text),
    );

    nickName = TextFieldHandler(
      field: 'nickName',
      hintText: LocaleKeys.nickname.tr(),
      initializeText: parameter.member.nickname ?? '',
      title: LocaleKeys.nickname.tr(),
      isRequired: true,
      onListenText: () => onChangedRelationship(relationship.text),
    );
    form = FormTextFieldHandler(handlers: [relationship], validateForm: onValidateForm);
    getMemberInfo();
    // showLoading();
  }

  getMemberInfo() async {
    // showLoading();
    final memberInfo = await familyRepository.getUserInfo(parameter.member.familyMemberUuid.toString());
    birthday = memberInfo.birthday.toString();
    print("$tag - getMemberInfo: ${memberInfo.toJson()}");
  }

  void onChangedRelationship(String text) {
    emit(state.copyWith(isChanging: true));
  }

  Future<void> onValidateForm(Map<String, dynamic> map) async {
    emit(state.copyWith(status: MemberDetailStatus.loading));
    try {
      final currentRole = parameter.member.role;
      // final newRole = state.isEditor ? Role.editor : Role.viewer;
      // Omit role if current role is owner
      final shouldUpdateRole = currentRole != Role.owner;
      final updatedMember = await familyRepository.updateMemberByFamilyId(
        parameter.member.familyMemberUuid ?? '',
        parameter.member.familyUuid ?? '',
        relationship: map['relationship'],
        role: null,
        nickname: nickName.text.toString()
      );
      emit(state.copyWith(status: MemberDetailStatus.success, member: updatedMember));
      memberListCubit.refresh();
    } catch (e) {
      logd("Error updating member relationship: $e");
      emit(state.copyWith(status: MemberDetailStatus.error));
    }
  }

  void handelChangeEditor(bool value) {
    emit(state.copyWith(isEditor: value, isChanging: true));
  }

  Future<void> onDeleteMember(BuildContext context, Account member) async {
    await DialogUtils.showDeleteDialogV2(
      context,
      pathIcon: Assets.images.iconWarining.path,
      content: LocaleKeys.confirm_delete_member
          .tr(namedArgs: {'field': '${member.fullName}'}),
      confirmText: LocaleKeys.remove.tr(),
      onConfirm: () async {
        final result = await familyRepository.kickMember(member.uuid ?? '');
        if (result) {
          // refresh();
          FamilyListCubit familyListCubit = locator.get();
          MemberListCubit memberListCubit = locator.get();
          await familyListCubit.refresh();
          await memberListCubit.refresh();
          Navigator.of(context).pop();
          Navigator.of(context).pop();
          showSimpleToast(LocaleKeys.remove_member_success.tr());
          emit(state.copyWith(isDelete: true, isChanging: true));
        } else {
          showSimpleToast(LocaleKeys.action_fail.tr());
        }
      },
    );
  }


}
