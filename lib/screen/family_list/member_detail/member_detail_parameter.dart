import 'package:family_app/data/model/account.dart';

class MemberDetailParameter {
  final bool isFamilyOwner;
  final Account member;
  final String familyUuid;

  MemberDetailParameter({
    required this.isFamilyOwner,
    required this.member,
    required this.familyUuid,
  });

  MemberDetailParameter copyWith({
    bool? isOwner,
    Account? member,
  }) {
    return MemberDetailParameter(
      isFamilyOwner: isOwner ?? this.isFamilyOwner,
      member: member ?? this.member,
      familyUuid: familyUuid,
    );
  }

  @override
  String toString() {
    return 'MemberDetailParameter{isFamilyOwner: $isFamilyOwner, memberId: ${member.uuid}}, familyUuid: $familyUuid}';
  }
}
