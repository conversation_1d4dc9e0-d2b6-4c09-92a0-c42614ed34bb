import 'package:auto_route/annotations.dart';
import 'package:crop_your_image/crop_your_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/screen/family_list/scale_avatar/scale_avatar_cubit.dart';
import 'package:family_app/screen/family_list/scale_avatar/scale_avatar_parameter.dart';
import 'package:family_app/screen/family_list/scale_avatar/scale_avatar_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../base/widget/cubit/base_bloc_page.dart';
import '../../../base/widget/cubit/base_bloc_provider.dart';
import '../../../config/lang/locale_keys.g.dart';
import '../../../config/service/app_service.dart';
import '../../../config/theme/style/style_theme.dart';
import '../../../extension.dart';
import '../../../gen/assets.gen.dart';
import '../../../main.dart';
import '../../../widget/appbar_custom.dart';


@RoutePage()
class ScaleAvatarPage extends BaseBlocProvider<ScaleAvatarState, ScaleAvatarCubit> {
  const ScaleAvatarPage({super.key, required this.parameter});

  final ScaleAvatarParameter parameter;

  @override
  Widget buildPage() => ScaleAvatarView(parameter: parameter);

  @override
  ScaleAvatarCubit createCubit() => ScaleAvatarCubit(
    familyRepository: locator.get(),
    parameter: parameter,
  );
}

class ScaleAvatarView extends StatefulWidget {
  const ScaleAvatarView({super.key, required this.parameter});

  final ScaleAvatarParameter parameter;

  @override
  State<ScaleAvatarView> createState() => _ScaleAvatarViewState();
}

class _ScaleAvatarViewState extends BaseBlocPageState<ScaleAvatarView, ScaleAvatarState, ScaleAvatarCubit> {

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  void onTapScreen(BuildContext context) {
    super.onTapScreen(context);
  }

  @override
  Widget buildAppBar(BuildContext context, ScaleAvatarCubit cubit, ScaleAvatarState state) {
    return Container();
  }

  @override
  Widget buildBody(BuildContext context, ScaleAvatarCubit cubit, ScaleAvatarState state) {
    final file = state.imageFile;
    if (file == null) return Center(child: Text("No image"));

    final bytes = file.readAsBytesSync();

    if (state.croppedImageBytes != null) {
      return Image.memory(state.croppedImageBytes!);
    }
    return Stack(
      children: [
        // Nền + cropper
        Container(
          color: Colors.black,
          child: Crop(
            controller: cubit.cropController,
            image: bytes,
            onCropped: (result) {
              if (result is CropSuccess) {
                // cubit.onCropped(result.croppedImage);
                Navigator.pop(context, result.croppedImage);
              } else if (result is CropFailure) {
                cubit.onError(result.cause.toString(), StackTrace.current);
              }
            },
            aspectRatio: 1,
            withCircleUi: false,
            baseColor: Colors.black.withOpacity(0.6),
            maskColor: Colors.black.withOpacity(0.6),
            cornerDotBuilder: (size, edgeAlignment) => const DotControl(color: Colors.white),
          ),
        ),

        Positioned(
          top: 16,
          left: 0,
          right: 0,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: SvgPicture.asset(
                    Assets.icons.iconBack.path,
                    width: 44.w,
                  ),
                ),
                Text(
                  LocaleKeys.move_or_scale.tr(),
                  style: AppStyle.bold18V2(color: appTheme.whiteText),
                  textAlign: TextAlign.center,
                ),

                // Check icon
                GestureDetector(
                  onTap: () => cubit.cropController.crop(),
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    child: SvgPicture.asset(
                      Assets.icons.iconComplete.path,
                      width: 32.w,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
    return Container(
      color: appTheme.backgroundV2,
      child: Center(
        child: Text(
          LocaleKeys.view_member.tr(),
          style: AppStyle.medium16(color: appTheme.blackText),
        ),
      ),
    );
  }

}