import 'dart:typed_data';
import 'package:crop_your_image/crop_your_image.dart';
import 'package:family_app/screen/family_list/scale_avatar/scale_avatar_parameter.dart';
import 'package:family_app/screen/family_list/scale_avatar/scale_avatar_state.dart';

import '../../../base/widget/cubit/base_cubit.dart';
import '../../../config/service/account_service.dart';
import '../../../config/service/app_service.dart';
import '../../../data/repository/family/ifamily_repository.dart';

class ScaleAvatarCubit extends BaseCubit<ScaleAvatarState> {

  final IFamilyRepository familyRepository;
  final AccountService accountService = locator.get();
  final ScaleAvatarParameter parameter;

  final String? familyId;
  final CropController cropController = CropController();


  ScaleAvatarCubit({
    required this.familyRepository,
    this.familyId,
    required this.parameter,
  }) : super(ScaleAvatarState(imageFile: parameter.imageFile));


  @override
  Future<void> close() {
    locator.unregister<ScaleAvatarCubit>();
    return super.close();
  }

  @override
  void onInit() {
    locator.registerSingleton(this);
    super.onInit();
  }



  void onCropped(Uint8List bytes) {
    emit(state.copyWith(
      croppedImageBytes: bytes,
      status: ScaleAvatarStatus.success,
    ));
  }

  void error(String message) {
    emit(state.copyWith(
      status: ScaleAvatarStatus.error,
      errorMessage: message,
    ));
  }


}

