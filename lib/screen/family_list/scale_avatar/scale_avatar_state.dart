import 'dart:io';
import 'dart:typed_data';
import '../../../base/widget/cubit/base_state.dart';

enum ScaleAvatarStatus {
  loading,
  success,
  error,
  none
}

class ScaleAvatarState extends BaseState {
  final File? imageFile;
  final ScaleAvatarStatus status;
  final String? errorMessage;
  final Uint8List? croppedImageBytes;


  ScaleAvatarState({
    this.imageFile,
    this.status = ScaleAvatarStatus.none,
    this.errorMessage,
    this.croppedImageBytes,
  });

  ScaleAvatarState copyWith({
    File? imageFile,
    ScaleAvatarStatus? status,
    String? errorMessage,
    Uint8List? croppedImageBytes,

  }) {
    return ScaleAvatarState(
      imageFile: imageFile ?? this.imageFile,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      croppedImageBytes: croppedImageBytes ?? this.croppedImageBytes,
    );
  }

  @override
  List<Object?> get props => [imageFile, status, errorMessage, croppedImageBytes];
}
