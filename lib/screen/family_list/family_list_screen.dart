import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_list/family_list_cubit.dart';
import 'package:family_app/screen/family_list/family_list_state.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../data/model/family.dart';
import '../../gen/assets.gen.dart';
import '../../utils/assets/shadow_util.dart';
import '../../widget/appbar_custom.dart';

@RoutePage()
class FamilyListPage
    extends BaseBlocProvider<FamilyListState, FamilyListCubit> {
  const FamilyListPage({super.key});

  @override
  Widget buildPage() => const FamilyListView();

  @override
  FamilyListCubit createCubit() => FamilyListCubit(
        accountService: locator.get(),
        familyRepository: locator.get(),
        mainCubit: locator.get(),
      );
}

class FamilyListView extends StatefulWidget {
  const FamilyListView({super.key});

  @override
  State<FamilyListView> createState() => _FamilyListViewState();
}

class _FamilyListViewState extends BaseBlocPageState<FamilyListView,
    FamilyListState, FamilyListCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  bool listenWhen(FamilyListState previous, FamilyListState current) {
    if (current.shouldForceCreateFamily) {
      _navigateToCreateFamily();
    }
    return super.listenWhen(previous, current);
  }

  void _navigateToCreateFamily() {
    context.pushRoute(FamilyCreateRoute());
  }

  @override
  void onTapScreen(BuildContext context) {
    super.onTapScreen(context);
  }

  Widget buildAppBar(
      BuildContext context, FamilyListCubit cubit, FamilyListState state) {
    return AppBarCustom(
      titleView: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    LocaleKeys.my_families_text.tr(),
                    style: AppStyle.bold16(color: Colors.black),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      // title: "${state.threadDetail?.name ?? ''} \n ${state.threadDetail?.members?.length ?? 0} members",
      onBack: () {
        Navigator.pop(context);
      },
    );
    //   CustomAppBar2(
    //   title: LocaleKeys.my_families_text.tr(),
    //   showBack: true,
    //   actions: [
    //     GestureDetector(
    //       onTap: _navigateToCreateFamily,
    //       behavior: HitTestBehavior.opaque,
    //       child: CircleItem(
    //         backgroundColor: appTheme.backgroundV2,
    //         padding: padding(all: 8),
    //         child: SvgPicture.asset(Assets.icons.icPlus.path),
    //       ),
    //     )
    //   ],
    // );
  }

  @override
  Widget buildBody(
      BuildContext context, FamilyListCubit cubit, FamilyListState state) {
    final account = cubit.accountService.account;
    return BlocListener<FamilyListCubit, FamilyListState>(
        listenWhen: (prev, curr) => prev.props != curr.props,
        listener: (context, state) async {
          final cubit = context.read<FamilyListCubit>();
          switch (state.action) {
            case FamilyListAction.createFamily:
              final result = await context.pushRoute(FamilyCreateRoute());
              if (result == true) {
                cubit.refresh();
              }
              break;
            case FamilyListAction.openFamily:
              final result = await context.pushRoute(MemberListRoute(
                parameter: MemberListParameter(
                  familyId: cubit.family!.familyUuid ?? '',
                ),
              ));
              if (result == true) {
                cubit.refresh();
              }
              break;
            default:
              break;
          }
          cubit.emit(state.copyWith(action: FamilyListAction.none));
          // final cubit = context.read<FamilyListCubit>();
          //   final result = context.pushRoute(FamilyCreateRoute());
          //   if (result == true) {
          //       cubit.refresh();
          //   }
        },
        child: TwoBaseNormalStreamBuilder(
          firstController: cubit.accountService.myActiveFamily,
          secondController: cubit.accountService.myFamilyBelong,
          builder: (activeFamily, familys) {
            if (familys.isEmpty) {
              // If no families, navigate to create family screen
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _navigateToCreateFamily();
              });
              return const SizedBox
                  .shrink(); // Return empty widget while navigating
            }

            // final sortedFamily = familys.toList()
            //   ..sort((a, b) => b.isActive!.compareTo(a.isActive ?? 0));

            // Separate active and inactive families
            final activeFamilies =
                familys.where((f) => f.isActive == 1).toList();
            final inactiveFamilies =
                familys.where((f) => f.isActive != 1).toList();

            // Create a combined list with divider
            final List<Widget> combinedFamilies = [];

            // Add active families
            if (activeFamilies.isNotEmpty) {
              combinedFamilies.addAll(activeFamilies
                  .map((family) => _buildFamilyCard(cubit, family)));
            }

            // Add divider if both types exist
            if (activeFamilies.isNotEmpty && inactiveFamilies.isNotEmpty) {
              combinedFamilies.add(
                Padding(
                  padding: const EdgeInsets.only(
                      left: 16, right: 16, bottom: 0, top: 0),
                  child: Container(),
                ),
              );
            }

            // Add inactive families
            if (inactiveFamilies.isNotEmpty) {
              combinedFamilies.addAll(inactiveFamilies
                  .map((family) => _buildFamilyCard(cubit, family)));
            }
            final allFamilies = [...activeFamilies, ...inactiveFamilies];
            final index = familys.indexWhere((f) => f.isActive == 1);
            final _pageController = PageController(
              viewportFraction: 0.9,
              initialPage: index >= 0 ? index : 0,
            );
            Timer? _pageChangeTimer;
            int _currentPageIndex = 0;

            void _onPageChanged(int index) {
              _currentPageIndex = index;
              _pageChangeTimer?.cancel();
              _pageChangeTimer = Timer(Duration(milliseconds: 1500), () {
                final family = familys[index];
                if (family.isActive != 1)
                  cubit.activeFamily(family.familyUuid ?? '');
              });
            }

            return Padding(
              padding: padding(top: 0),
              child: ListView(
                padding: EdgeInsets.only(bottom: context.bottom),
                children: [
                  SizedBox(height: 8.h2),
                  SizedBox(
                    height: 300, // hoặc 300 tuỳ chiều cao card của bạn
                    child: PageView.builder(
                      itemCount: familys.length,
                      controller: _pageController,
                      onPageChanged: _onPageChanged,
                      itemBuilder: (context, index) {
                        final family = familys[index];
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: _buildFamilyCard(cubit, family),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 12),
                  Center(
                    child: SmoothPageIndicator(
                      controller: _pageController,
                      count: familys.length,
                      effect: WormEffect(
                        dotHeight: 10,
                        dotWidth: 10,
                        spacing: 8,
                        activeDotColor: Colors.blue,
                        dotColor: Colors.grey.shade300,
                      ),
                    ),
                  ),
                  SizedBox(height: 30.h2),

                  _buildCard(
                    LocaleKeys.create_new_family.tr(),
                    Assets.icons.iconCreateFamily.path,
                    _navigateToCreateFamily,
                  ),

                  _buildCard(
                    LocaleKeys.link_to_an_existing_family.tr(),
                    Assets.icons.iconSubtract.path,
                    () {
                      showDialog<void>(
                        context: context,
                        builder: (BuildContext context) => AlertDialog(
                          content: Text(
                            LocaleKeys.coming_soon.tr(),
                            style: AppStyle.bold16(color: Colors.black),
                            textAlign: TextAlign.center,
                          ),
                          actions: <Widget>[
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: Text(
                                LocaleKeys.close.tr(),
                                style:
                                    AppStyle.regular16V2(color: Colors.black),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          },
        ));
  }

  Widget _boxBuilder(List<Widget> children) {
    return Container(
      padding: padding(top: 16, bottom: 4),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.w2),
          boxShadow: ShadowUtil.backgroundShadow),
      child: Column(children: children),
    );
  }

  Widget _buildFamilyCard1(FamilyListCubit cubit, Family family) {
    return InkWell(
      onTap: () => cubit.onDetailFamily(family),
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 0),
        child: Container(
          decoration: BoxDecoration(
            color: appTheme.backgroundWhite,
            borderRadius: BorderRadius.circular(20.w),
            // border: family.isActive == 1
            //     ? Border.all(color: appTheme.greenV2, width: 1)
            //     : null,
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            minTileHeight: 80,
            leading: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                image: family.photoUrl != null && family.photoUrl!.isNotEmpty
                    ? DecorationImage(
                        image: CachedNetworkImageProvider(family.photoUrl!),
                        fit: BoxFit.cover,
                      )
                    : DecorationImage(
                        image: AssetImage(Assets.images.avatarFamily.path),
                        fit: BoxFit.cover,
                      ),
              ),
            ),
            title: Text(
              family.familyName ?? '',
              style: AppStyle.medium16(color: appTheme.blackText),
            ),
            subtitle: Text(
                LocaleKeys.members_text
                    .tr(namedArgs: {'field': '${family.members ?? 0}'}),
                style: AppStyle.regular12(color: appTheme.grayV2)),
            trailing: Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: InkWell(
                onTap: () {
                  cubit.activeFamily(family.familyUuid ?? '');
                },
                child: SvgPicture.asset(
                  family.isActive == 1
                      ? Assets.icons.iconCheck.path
                      : Assets.icons.iconUnCheck.path,
                  width: 44,
                  height: 44,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFamilyCard(FamilyListCubit cubit, Family family) {
    final isActive = family.isActive == 1;

    return InkWell(
      onTap: () => cubit.onDetailFamily(family),
      child: Card(
        elevation: 6,
        margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        color: isActive ? Colors.white : Colors.grey.shade100,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: family.photoUrl != null && family.photoUrl!.isNotEmpty
                      ? CachedNetworkImage(
                    imageUrl: family.photoUrl!,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) => Image.asset(
                      Assets.images.avatarFamily.path,
                      fit: BoxFit.cover,
                    ),
                    placeholder: (context, url) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
                      : Image.asset(
                    Assets.images.avatarFamily.path,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                family.familyName ?? '',
                style: AppStyle.medium19(color: appTheme.blackText),
              ),
              Text(
                  LocaleKeys.members_text
                      .tr(namedArgs: {'field': '${family.members ?? 0}'}),
                  style: AppStyle.regular12(color: appTheme.grayV2)),
              const SizedBox(height: 12),

              Row(
                children: [
                  Icon(
                    isActive ? Icons.check_circle : Icons.remove_circle_outline,
                    size: 20,
                    color: isActive ? Colors.green : Colors.grey,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isActive ? LocaleKeys.active_family.tr() : "Not active",
                    style: AppStyle.regular16V2(
                      color: isActive ? Colors.green : Colors.grey,
                    ),
                  ),
                ],
              ),

              const Spacer(),

              // if (family.isActive != 1) ...[
              //   Align(
              //     alignment: Alignment.centerRight,
              //     child: ElevatedButton.icon(
              //       onPressed: () {
              //         cubit.activeFamily(family.familyUuid ?? '');
              //       },
              //       label: Text(
              //         LocaleKeys.set_active_text.tr(),
              //         textAlign: TextAlign.center,
              //         style: AppStyle.regular16V2(
              //           color: Colors.white,
              //         ),
              //       ),
              //       style: ElevatedButton.styleFrom(
              //         backgroundColor: appTheme.primaryColorV2,
              //       ),
              //     ),
              //   ),
              // ]
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCard(String name, String image, VoidCallback? onTap) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 12),
        child: Container(
          decoration: BoxDecoration(
            color: appTheme.backgroundWhite,
            borderRadius: BorderRadius.circular(20.w),
            border: null,
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            minTileHeight: 60,
            leading: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
              ),
              clipBehavior: Clip.hardEdge,
              // để bo góc cho SVG
              child: SvgPicture.asset(
                image,
                width: 48,
                height: 48,
                fit: BoxFit.cover, // để ảnh phủ kín như BoxFit.cover
              ),
            ),
            title: Text(
              name,
              style: AppStyle.medium16(color: appTheme.blackText),
            ),
          ),
        ),
      ),
    );
  }
}
