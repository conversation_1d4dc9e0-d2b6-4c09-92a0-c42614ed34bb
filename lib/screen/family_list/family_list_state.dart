import 'package:family_app/base/widget/cubit/base_state.dart';
enum FamilyListAction {
  none,
  createFamily,
  openFamily
}

class FamilyListState extends BaseState {
   bool shouldForceCreateFamily;
   FamilyListAction action;

  FamilyListState({
    this.shouldForceCreateFamily = false,
    this.action = FamilyListAction.none,
  });

  FamilyListState copyWith({
    bool? shouldForceCreateFamily,
    FamilyListAction? action,
  }) {
    return FamilyListState(
      shouldForceCreateFamily: shouldForceCreateFamily ?? this.shouldForceCreateFamily,
      action: action ?? this.action,
    );
  }

  @override
  List<Object?> get props => [shouldForceCreateFamily, action];
}
