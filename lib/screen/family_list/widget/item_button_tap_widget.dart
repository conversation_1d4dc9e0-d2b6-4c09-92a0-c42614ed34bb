import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/widgets.dart';

class ItemButtonTapWidget extends StatelessWidget {
  const ItemButtonTapWidget({
    super.key,
    required this.title,
    this.onTap,
  });

  final String title;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(1000),
          color: appTheme.primaryColor.withOpacity(0.2),
        ),
        child: Text(title, style: AppStyle.medium12(color: appTheme.primaryColor)),
      ),
    );
  }
}
