import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/in_app_purchase_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/widget/button.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/svg.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'premium_cubit.dart';
import 'premium_state.dart';

@RoutePage()
class PremiumPage extends BaseBlocProvider<PremiumState, PremiumCubit> {
  const PremiumPage({super.key});

  @override
  Widget buildPage() => const PremiumView();

  @override
  PremiumCubit createCubit() => PremiumCubit(locator.get());
}

class PremiumView extends StatefulWidget {
  const PremiumView({super.key});

  @override
  State<PremiumView> createState() => _PremiumViewState();
}

class _PremiumViewState extends BaseBlocPageStateV2<PremiumView, PremiumState, PremiumCubit> {
  late final LoadingListener _subLoading;
  late final _isPremium;
  final _index = ValueNotifier(1);

  final _durationAnim = const Duration(milliseconds: 300);
  final _durationAnim2 = const Duration(milliseconds: 200);

  @override
  Color get backgroundColor => Colors.white;

  @override
  void initState() {
    final iapService = locator.get<InAppPurchaseService>();
    _subLoading = iapService.addListenerLoading(_listenerLoading);
    _isPremium = iapService.isPremium;

    super.initState();
  }

  @override
  void dispose() {
    _subLoading.dispose();

    super.dispose();
  }

  @override
  Widget buildBody(BuildContext context, PremiumCubit cubit, PremiumState state) {
    return Stack(children: [
      state.isLoading ? Center(child: CupertinoActivityIndicator(radius: 16.w2)) : _mainBuilder(cubit, state),
      Positioned(
        top: 16 + context.top,
        right: 12.w2 + context.right,
        child:
            ButtonIcon(Assets.icons.icClose.path, context.maybePop, size: 48, bg: appTheme.backgroundV2, sizeIcon: 16),
      ),
    ]);
  }

  Widget _textLinear(String text, TextStyle style) {
    return ShaderMask(
      shaderCallback: (bounds) => LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [appTheme.blueColorV2, appTheme.errorV2],
        stops: [0.2076, 0.8177],
      ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
      blendMode: BlendMode.srcIn,
      child: Text(text, style: style),
    );
  }

  Widget _btnBuilder(int i, String title, VoidCallback callback) {
    return GestureDetector(
      onTap: callback,
      child: ValueListenableBuilder(
        valueListenable: _index,
        builder: (context, index, child) => AnimatedContainer(
            padding: paddingV2(all: 8, vertical: 4),
            decoration: BoxDecoration(
                color: i == index ? Colors.white : appTheme.backgroundV2, borderRadius: BorderRadius.circular(8.w2)),
            duration: _durationAnim,
            child: Text(title, style: AppStyle.bold14V2())),
      ),
    );
  }

  Widget _wrapPriceBuilder(bool selected, double top, Widget price, String period) {
    return AnimatedPositioned(
      duration: _durationAnim,
      top: selected ? 0 : top,
      child: AnimatedOpacity(opacity: selected ? 1 : 0, duration: _durationAnim, child: _priceBuilder(price, period)),
    );
  }

  Widget _priceBuilder(Widget price, String period) {
    return Row(mainAxisAlignment: MainAxisAlignment.center, mainAxisSize: MainAxisSize.min, children: [
      price,
      SizedBox(width: 4.w2),
      Text('/', style: AppStyle.regular24V2(height: 2, color: appTheme.grayV2)),
      SizedBox(width: 4.w2),
      Text(period, style: AppStyle.regular24V2(height: 2, color: appTheme.grayV2)),
    ]);
  }

  double _getSizeWithScaleText(double size) => MediaQuery.of(context).textScaler.scale(size);

  _mainBuilder(PremiumCubit cubit, PremiumState state) {
    if (cubit.month == null || cubit.year == null)
      return Center(child: Button(onTap: cubit.getData, child: Text('reload'.tr())));

    return Column(children: [
      Expanded(
        child: SingleChildScrollView(
          padding: paddingV2(vertical: 16, horizontal: 8).add(EdgeInsets.only(top: context.top)),
          child: Column(children: [
            Text(LocaleKeys.subscription_title_1.tr(), style: AppStyle.bold32V2()),
            Stack(clipBehavior: Clip.none, children: [
              _textLinear(LocaleKeys.subscription_title_2.tr(), AppStyle.bold32V2()),
              Positioned(left: -18.w2, child: Svg(Assets.icons.icStars.path)),
              Positioned(right: -18.w2, bottom: 0, child: Svg(Assets.icons.icStars.path, color: appTheme.errorV2)),
            ]),
            Text(LocaleKeys.subscription_title_3.tr(), style: AppStyle.bold32V2()),
            SizedBox(height: 24.h2),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Container(
                padding: paddingV2(all: 4),
                decoration: BoxDecoration(color: appTheme.backgroundV2, borderRadius: BorderRadius.circular(12.w2)),
                child: Row(children: [
                  _btnBuilder(0, cubit.month!.title, () => _index.value = 0),
                  _btnBuilder(1, cubit.year!.title, () => _index.value = 1),
                ]),
              ),
            ]),
            SizedBox(height: 8.h2),
            SizedBox(
              height: _getSizeWithScaleText(48),
              child: ValueListenableBuilder(
                valueListenable: _index,
                builder: (context, index, child) =>
                    Stack(fit: StackFit.expand, clipBehavior: Clip.none, alignment: Alignment.center, children: [
                  _wrapPriceBuilder(
                    index == 1,
                    -_getSizeWithScaleText(24),
                    _textLinear(cubit.year!.price, AppStyle.bold40V2()),
                    cubit.year!.periodUnit,
                  ),
                  _wrapPriceBuilder(
                    index == 0,
                    _getSizeWithScaleText(24),
                    Text(cubit.month!.price, style: AppStyle.bold40V2(color: appTheme.primaryColorV2)),
                    cubit.month!.periodUnit,
                  ),
                ]),
              ),
            ),
            // Stack(children: [
            //   SizedBox(height: _getSizeWithScaleText(48)),
            //   _wrapPriceBuilder(1, _textLinear('\$99.9', AppStyle.bold40V2()), LocaleKeys.year.tr()),
            //   _wrapPriceBuilder(
            //       0, Text('\$59.9', style: AppStyle.bold40V2(color: appTheme.primaryColorV2)), LocaleKeys.month.tr()),
            // ]),
            ValueListenableBuilder(
              valueListenable: _index,
              child: Padding(
                padding: paddingV2(top: 4.h2),
                child: RichText(
                  text: TextSpan(style: AppStyle.regular14V2(), children: [
                    TextSpan(
                      text: LocaleKeys.subscription_compare_to_month_1.tr(namedArgs: {
                        'percents':
                            _getYearlyDiscountPercent(cubit.month!.rawPrice, cubit.year!.rawPrice).toStringAsFixed(0)
                      }),
                      style: TextStyle(color: appTheme.primaryColorV2, fontWeight: FontWeight.w700),
                    ),
                    TextSpan(text: LocaleKeys.subscription_compare_to_month_2.tr()),
                  ]),
                ),
              ),
              builder: (context, index, child) => AnimatedContainer(
                duration: _durationAnim,
                height: index == 1 ? (4.h2 + _getSizeWithScaleText(24)) : 0,
                child: AnimatedOpacity(duration: _durationAnim2, opacity: index == 1 ? 1 : 0, child: child!),
              ),
            ),
            SizedBox(height: 24.h2),
            ...Iterable.generate(
              5,
              (i) => Padding(
                padding: paddingV2(all: 8),
                child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Svg(Assets.icons.icCheckV2.path, color: appTheme.greenV2),
                  SizedBox(width: 4.w2),
                  Expanded(
                    child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                      Text('subscription_label_${i + 1}'.tr(), style: AppStyle.bold16V2(color: appTheme.greenV2)),
                      Text('subscription_desc_${i + 1}'.tr(), style: AppStyle.regular16V2(color: appTheme.grayV2)),
                    ]),
                  ),
                ]),
              ),
            ),
          ]),
        ),
      ),
      Container(height: 1.h2, color: appTheme.borderColorV2),
      Padding(
        padding: paddingV2(bottom: 8, top: 15, horizontal: 24).add(EdgeInsets.only(bottom: context.bottom)),
        child: Column(children: [
          PrimaryButtonV2(text: LocaleKeys.get_premium_now.tr(), onTap: () => cubit.buy(_index.value)),
          SizedBox(height: 4.h2),
          ValueListenableBuilder(
            valueListenable: _index,
            builder: (context, index, child) => Text(LocaleKeys.subscription_endline
                .tr(namedArgs: {'price': index == 0 ? cubit.month!.price : cubit.year!.price})),
          ),
        ]),
      ),
    ]);
  }

  double _getYearlyDiscountPercent(double monthlyPrice, double yearlyPrice) {
    final yearlyIfPaidMonthly = monthlyPrice * 12;
    final discount = yearlyIfPaidMonthly - yearlyPrice;

    return (discount / yearlyIfPaidMonthly) * 100;
  }

  void _listenerLoading(bool isLoading) {
    if (isLoading)
      showDialog(
        barrierDismissible: false,
        context: context,
        builder: (context) =>
            PopScope(canPop: false, child: CupertinoActivityIndicator(radius: 16.w2, color: Colors.white)),
      );
    else
      Navigator.of(context).pop();

    if (_isPremium != locator.get<InAppPurchaseService>().isPremium) context.maybePop();
  }
}
