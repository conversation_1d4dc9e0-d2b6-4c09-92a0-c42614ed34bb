// ignore_for_file: depend_on_referenced_packages

import 'dart:io' show Platform;

import 'package:dartx/dartx.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/in_app_purchase_service.dart';
import 'package:family_app/utils/flash/toast.dart' show showSimpleToast;
import 'package:flutter/material.dart' show debugPrint;
import 'package:in_app_purchase/in_app_purchase.dart' show ProductDetails;
import 'package:in_app_purchase_android/in_app_purchase_android.dart' show GooglePlayProductDetails;
// import 'package:in_app_purchase_android/billing_client_wrappers.dart' show SubscriptionOfferDetailsWrapper;

import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart' show AppStoreProductDetails;

import 'premium_state.dart';

class Subscription {
  final double rawPrice;
  final String title, periodUnit, price;

  Subscription._({
    required this.rawPrice,
    required this.price,
    required this.title,
    required this.periodUnit,
  });
}

class PremiumCubit extends BaseCubit<PremiumState> {
  PremiumCubit(this._iapService) : super(PremiumState());

  final InAppPurchaseService _iapService;

  Subscription? get month {
    if (_iapService.listPremium?.isEmpty ?? true) return null;

    if (Platform.isAndroid)
      return _defineSubscriptionAndroid(_iapService.listPremium!.first as GooglePlayProductDetails, 0);

    if (Platform.isIOS) return _defineSubscriptionIos(_iapService.listPremium!.first);

    return null;
  }

  Subscription? get year {
    if ((_iapService.listPremium?.length ?? 0) < 2) return null;

    if (Platform.isAndroid)
      return _defineSubscriptionAndroid(_iapService.listPremium!.second as GooglePlayProductDetails, 1);

    if (Platform.isIOS) return _defineSubscriptionIos(_iapService.listPremium!.second);

    return null;
  }

  Subscription? _defineSubscriptionAndroid(GooglePlayProductDetails v, int index) {
    if ((v.productDetails.subscriptionOfferDetails?.length ?? 0) < index + 1) return null;

    final r = v.productDetails.subscriptionOfferDetails!.elementAt(index);
    final String? periodUnit;

    switch (r.pricingPhases.firstOrNull?.billingPeriod) {
      case 'P1Y':
        periodUnit = 'year'.tr();

      case 'P1M':
        periodUnit = 'month'.tr();

      default:
        periodUnit = null;
    }

    return Subscription._(
      periodUnit: periodUnit ?? '',
      price: v.price,
      rawPrice: v.rawPrice,
      title: premiumBasePlans.containsKey(r.basePlanId) ? premiumBasePlans[r.basePlanId]!.tr() : '',
    );
  }

  Subscription? _defineSubscriptionIos(ProductDetails v) {
    return Subscription._(
      periodUnit: (v as AppStoreProductDetails).skProduct.subscriptionPeriod?.unit.name.toString().tr() ?? '',
      price: v.price,
      rawPrice: v.rawPrice,
      title: v.title,
    );
  }

  @override
  void onInit() {
    getData();

    super.onInit();
  }

  Future<void> getData() async {
    emit(state.copyWith(isLoading: true));

    try {
      await _iapService.getListPremium();
    } catch (e) {
      debugPrint(e.toString());
      showSimpleToast(e.toString());
    }

    emit(state.copyWith(isLoading: false));
  }

  void buy(int v) {
    _iapService.buyPremium(v == 0 ? _iapService.listPremium!.first : _iapService.listPremium!.second);
  }
}
