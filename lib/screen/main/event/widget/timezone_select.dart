import 'package:flutter/material.dart';
import 'package:family_app/data/model/timezone.dart';
import 'custom_dropdown_builder.dart';
import 'package:family_app/utils/timezone.dart';

class TimezoneSelect extends StatelessWidget {
  final Timezone? timezone;
  final ValueChanged<Timezone> onChanged;
  final bool defaultToUserTimezone;

  const TimezoneSelect({
    Key? key,
    required this.timezone,
    required this.onChanged,
    this.defaultToUserTimezone = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final tzList = TimeZoneUtils.getUniqueTimezoneList();
    final defaultTimezone = TimeZoneUtils.getDefaultTimezone(defaultToUserTimezone: defaultToUserTimezone);
    return CustomDropdownBuilder<Timezone>(
      data: tzList,
      value: timezone ?? defaultTimezone,
      onChanged: onChanged,
      textBuilder: TimeZoneUtils.formatTimezoneGmtString,
    );
  }
}
