import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/popup/popup.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDropdownBuilder<T> extends StatelessWidget {
  final Iterable<T> data;
  final T value;
  final ValueChanged<T> onChanged;
  final String Function(T) textBuilder;

  const CustomDropdownBuilder({
    Key? key,
    required this.data,
    required this.value,
    required this.onChanged,
    required this.textBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPopup(
      contentPadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      content: StatefulBuilder(
        builder: (context, setState) {
          final items = data.toList();
          final selectedIndex = items.indexOf(value);
          final scrollController = ScrollController();
          const itemHeight = 36.1;

          bool hasScrolled = false;

          return NotificationListener<ScrollMetricsNotification>(
            onNotification: (_) => false,
            child: SingleChildScrollView(
              controller: scrollController,
              child: Builder(
                builder: (columnContext) {
                  // Only scroll after the first frame and only once
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (!hasScrolled && selectedIndex >= 0 && scrollController.hasClients) {
                      final maxScroll = scrollController.position.maxScrollExtent;
                      double targetOffset = (selectedIndex * itemHeight) - (itemHeight * 2);
                      targetOffset = targetOffset.clamp(0.0, maxScroll);
                      scrollController.jumpTo(targetOffset);
                      hasScrolled = true;
                    }
                  });

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: items.map(
                      (e) {
                        final isSelected = e == value;
                        return Container(
                          decoration: isSelected
                              ? BoxDecoration(
                                  color: appTheme.primaryColorV2.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                )
                              : null,
                          padding: paddingV2(all: 8),
                          child: GestureDetector(
                            onTap: () {
                              Navigator.maybePop(context);
                              onChanged(e);
                            },
                            child: Text(
                              textBuilder(e),
                              style: isSelected
                                  ? TextStyle(
                                      color: appTheme.primaryColorV2,
                                      fontWeight: FontWeight.bold,
                                    )
                                  : null,
                            ),
                          ),
                        );
                      },
                    ).toList(),
                  );
                },
              ),
            ),
          );
        },
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            textBuilder(value),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(width: 8),
          SvgPicture.asset(
            Assets.icons.icVerticalDoubleChevron.path,
            width: 24.w,
            height: 24.h,
            colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
          ),
        ],
      ),
    );
  }
}
