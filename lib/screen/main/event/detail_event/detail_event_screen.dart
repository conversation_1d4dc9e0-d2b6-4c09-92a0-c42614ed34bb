import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/member_horizontal_view.dart';
import 'package:family_app/widget/round_item_view.dart';
import 'package:flutter/material.dart';

import 'detail_event_cubit.dart';
import 'detail_event_state.dart';

@RoutePage()
class DetailEventPage extends BaseBlocProvider<DetailEventState, DetailEventCubit> {
  const DetailEventPage({required this.parameter, super.key});

  final DetailEventParameter parameter;

  @override
  Widget buildPage() => const DetailEventView();

  @override
  DetailEventCubit createCubit() => DetailEventCubit(parameter: parameter, eventRepository: locator.get());
}

class DetailEventView extends StatefulWidget {
  const DetailEventView({super.key});

  @override
  State<DetailEventView> createState() => _DetailEventViewState();
}

class _DetailEventViewState extends BaseBlocPageState<DetailEventView, DetailEventState, DetailEventCubit> {
  @override
  Color get backgroundColor => appTheme.background;

  @override
  String get title => LocaleKeys.event_text.tr();

  @override
  bool listenWhen(DetailEventState previous, DetailEventState current) {
    if (current.isNotFound) {
      showSimpleToast(LocaleKeys.event_not_found.tr());
      context.maybePop();
    }
    return super.listenWhen(previous, current);
  }

  @override
  List<Widget>? appBarActions(DetailEventCubit cubit, DetailEventState state) {
    if (isViewer) return [];
    return [
      GestureDetector(
        onTap: () async {
          DialogUtils.showDeleteDialog(
            context,
            title: LocaleKeys.delete_event_text.tr(),
            content: LocaleKeys.confirm_content_delete_event.tr(),
            confirmText: LocaleKeys.confirm.tr(),
            onConfirm: () async {
              final result = await cubit.onDelete();
              if (result) {
                showSimpleToast(LocaleKeys.delete_event_success.tr());
                context.back();
              } else {
                showSimpleToast(LocaleKeys.action_fail.tr());
              }
            },
          );
        },
        child: RoundItemView(
          child: ImageAssetCustom(imagePath: Assets.images.trash.path, size: 16, color: appTheme.redColor),
        ),
      ),
      const SizedBox(width: 12),
      GestureDetector(
        onTap: () async {
          if (isViewer) {
            showSimpleToast(LocaleKeys.viewer_not_permission_event.tr());
            return;
          }
          final result = await context
              .pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter(model: state.eventModels)));
          if (result is EventModels) {
            cubit.updateEventModel(result);
          }
        },
        child: RoundItemView(child: ImageAssetCustom(imagePath: Assets.images.iconEdit.path, size: 16)),
      ),
    ];
  }

  @override
  Widget buildBody(BuildContext context, DetailEventCubit cubit, DetailEventState state) {
    return Column(
      children: [
        ColoredBox(
          color: state.eventModels?.color?.toColor ?? appTheme.redColor,
          child: Stack(
            children: [
              Padding(
                padding: padding(vertical: 14, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(state.eventModels?.name ?? '', style: AppStyle.medium20(color: appTheme.whiteText)),
                    SizedBox(height: 6.h),
                    Text(state.eventModels?.description ?? '', style: AppStyle.regular14(color: appTheme.whiteText)),
                    SizedBox(height: 22.h),
                    Row(
                      children: [
                        ImageAssetCustom(imagePath: Assets.images.folder.path, color: appTheme.whiteText, width: 16.w),
                        SizedBox(width: 8.w),
                        Text(
                          state.eventModels?.activity?.name ?? '',
                          style: AppStyle.regular14(color: appTheme.whiteText),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                right: -45,
                bottom: -108,
                child: Assets.images.circleBackground.image(width: 222, height: 183, fit: BoxFit.cover),
              ),
            ],
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: padding(all: 13),
          color: appTheme.whiteText,
          child: Row(
            children: [
              ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w),
              SizedBox(width: 10.w),
              Expanded(
                  child: MemberHorizontalView(
                createBy: state.eventModels?.createdBy ?? '',
                accounts: state.eventModels?.members ?? <Account>[],
              ))
            ],
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: padding(all: 13),
          color: appTheme.whiteText,
          child: Row(
            children: [
              ImageAssetCustom(imagePath: Assets.images.calendar.path, width: 16.w),
              SizedBox(width: 12.w),
              Expanded(child: Text(state.eventModels?.fromDate?.MMM_DD_YYYY ?? '', style: AppStyle.regular14())),
              // const Spacer(),
              Text(LocaleKeys.to.tr(), style: AppStyle.regular14()),
              SizedBox(width: 15.w),
              Expanded(child: Text(state.eventModels?.toDate?.MMM_DD_YYYY ?? '', style: AppStyle.regular14())),
            ],
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: padding(all: 13),
          color: appTheme.whiteText,
          child: Row(
            children: [
              ImageAssetCustom(imagePath: Assets.images.notification.path, width: 20.w, color: appTheme.fadeTextColor),
              SizedBox(width: 6.w),
              Text('15 mins before ', style: AppStyle.regular14()),
            ],
          ),
        ),
      ],
    );
  }
}
