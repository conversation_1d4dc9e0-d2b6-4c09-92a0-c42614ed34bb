import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/event.dart';

class DetailEventState extends BaseState {
  final EventModels? eventModels;
  final bool isNotFound;

  DetailEventState({this.eventModels, this.isNotFound = false});

  @override
  List<Object?> get props => [eventModels, isNotFound];

  DetailEventState copyWith({
    EventModels? eventModels,
    bool? isNotFound,
  }) {
    return DetailEventState(
      eventModels: eventModels ?? this.eventModels,
      isNotFound: isNotFound ?? this.isNotFound,
    );
  }
}
