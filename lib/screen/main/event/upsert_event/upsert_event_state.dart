import 'dart:ui';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:flutter/material.dart';

enum UpsertEventStatus { none, loading, success, error }
class UpsertEventState extends BaseState {
  final UpsertEventStatus status;
  final bool isShowMenu;
  final DateTime? startDate;
  final DateTime? endDate;
  final Color? selectedColor;
  final EventModels? eventModels;
  final List<Account> memberList;
  final bool isOn;
  final bool isAllDay;
  final String? content;
  final EventRepeatType repeatType;
  final RepeatConfig? repeatConfig;
  final int? reminder;
  final ActivityModel? activity;
  final List<WeekDay> selectedDayOfWeek;
  final bool weekStartsOnMonday;
  final bool isDeviceEvent;
  final Timezone? timezone;

  UpsertEventState({
    this.status = UpsertEventStatus.none,
    this.isShowMenu = false,
    this.startDate,
    this.endDate,
    this.selectedColor,
    this.eventModels,
    this.memberList = const [],
    this.isOn = false,
    this.isAllDay = false,
    this.repeatType = EventRepeatType.none,
    this.repeatConfig,
    this.selectedDayOfWeek = const [],
    this.weekStartsOnMonday = true,
    this.reminder = 0,
    this.content,
    this.activity,
    this.isDeviceEvent = false,
    this.timezone,
  });

  @override
  List<Object?> get props => [
        status,
        isShowMenu,
        startDate,
        endDate,
        selectedColor,
        eventModels,
        memberList,
        reminder,
        isOn,
        isAllDay,
        repeatType,
        repeatConfig,
        selectedDayOfWeek,
        weekStartsOnMonday,
        content,
        activity,
        isDeviceEvent,
        timezone,
      ];

  UpsertEventState copyWith({
    UpsertEventStatus? status,
    bool? isShowMenu,
    DateTime? startDate,
    DateTime? endDate,
    Color? selectedColor,
    EventModels? eventModels,
    List<Account>? memberList,
    int? reminder,
    bool? isOn,
    bool? isAllDay,
    String? content,
    EventRepeatType? repeatType,
    RepeatConfig? repeatConfig,
    List<WeekDay>? selectedDayOfWeek,
    bool? weekStartsOnMonday,
    bool? isDeviceEvent,
    ActivityModel? activity,
    Timezone? timezone,
  }) {
    return UpsertEventState(
      status: status ?? this.status,
      isShowMenu: isShowMenu ?? this.isShowMenu,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      selectedColor: selectedColor ?? this.selectedColor,
      eventModels: eventModels ?? this.eventModels,
      memberList: memberList ?? this.memberList,
      reminder: reminder ?? this.reminder,
      isOn: isOn ?? this.isOn,
      isAllDay: isAllDay ?? this.isAllDay,
      content: content ?? this.content,
      repeatType: repeatType ?? this.repeatType,
      selectedDayOfWeek: selectedDayOfWeek ?? this.selectedDayOfWeek,
      weekStartsOnMonday: weekStartsOnMonday ?? this.weekStartsOnMonday,
      activity: activity ?? this.activity,
      repeatConfig: repeatConfig ?? this.repeatConfig,
      isDeviceEvent: isDeviceEvent ?? this.isDeviceEvent,
      timezone: timezone ?? this.timezone,
    );
  }
}
