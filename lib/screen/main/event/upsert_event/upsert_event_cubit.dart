import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/device_event.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/usecase/event_usecase.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/event.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';
import 'package:family_app/utils/timezone.dart';

import 'upsert_event_state.dart';

class UpsertEventCubit extends BaseCubit<UpsertEventState> {
  // --- Dependencies ---
  final EventUsecase eventUsecase;
  final IActivityRepository activityRepository;
  final UpsertEventParameter parameter;
  final AccountService accountService;
  final IEventRepository eventRepository;
  final EventService eventService;

  // --- Form Handlers ---
  late final TextFieldHandler title;
  late final TextFieldHandler description;
  late final FormTextFieldHandler formHandler;

  // --- Constructor ---
  UpsertEventCubit({
    required this.eventUsecase,
    required this.parameter,
    required this.eventRepository,
    required this.accountService,
    required this.activityRepository,
    required this.eventService,
  }) : super(UpsertEventState());

  // --- Lifecycle ---
  @override
  void onInit() async {
    super.onInit();
    _initTextFields();
    _initDateAndWeekStart();
    await _initEventData();
  }

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }

  // --- Init & State Setup ---
  void _initTextFields() {
    title = TextFieldHandler(
      field: 'title',
      hintText: LocaleKeys.untitled_event.tr(),
      isRequired: true,
      initializeText: parameter.model?.name ?? '',
    );
    final messageData = parameter.messageData;
    description = TextFieldHandler(
      field: 'description',
      hintText: LocaleKeys.description.tr(),
      isRequired: false,
      initializeText: messageData?.description ?? parameter.model?.description ?? '',
    );
    formHandler = FormTextFieldHandler(
      handlers: [title, description],
      validateForm: (map) async {},
    );
  }

  void _initDateAndWeekStart() {
    final messageData = parameter.messageData;
    final localStorage = locator.get<LocalStorage>();
    final weekStartsOnMonday = localStorage.isStartOfWeekMonday;
    final timezone = TimeZoneUtils.getDefaultTimezone();

    final dates = _resolveStartAndEndDate(messageData, timezone);

    emit(state.copyWith(
      weekStartsOnMonday: weekStartsOnMonday,
      startDate: dates.$1,
      endDate: dates.$2,
      isDeviceEvent: parameter.model is DeviceEventModel,
      timezone: timezone,
    ));
  }

  /// Resolves the initial start and end DateTime for event creation.
  ///
  /// Priority:
  /// 1. If [messageData] contains both fromDate and toDate, use them.
  /// 2. If [parameter.model] contains both fromDate and toDate, use them.
  /// 3. If [parameter.dateTime] is provided (DateTime), use its value as the base time,
  ///    then set startDate to the next hour (minute/second zeroed) and endDate one hour after.
  /// 4. Otherwise, use the current time as the base, then set startDate to the next hour and endDate one hour after.
  ///
  /// All returned DateTimes are converted to the given [timezone].
  (DateTime, DateTime) _resolveStartAndEndDate(dynamic messageData, Timezone timezone) {
    // 1. Use messageData if both dates exist
    if (messageData?.fromDate?.toDateTime() != null && messageData?.toDate?.toDateTime() != null) {
      return (messageData!.fromDate!.toDateTime(), messageData.toDate!.toDateTime());
    }
    // 2. Use parameter.model if both dates exist
    if (parameter.model?.fromDate?.toDateTime() != null && parameter.model?.toDate?.toDateTime() != null) {
      return (parameter.model!.fromDate!.toDateTime(), parameter.model!.toDate!.toDateTime());
    }
    // 3. Use parameter.dateTime as base, then next hour logic
    if (parameter.dateTime != null) {
      final base = parameter.dateTime!;
      final nextHour = DateTime(base.year, base.month, base.day, base.hour + 1);
      final start = TimeZoneUtils.convertDateTimeToTimezone(nextHour.toUtc(), timezone);
      final end = start.add(const Duration(hours: 1));
      return (start, end);
    }
    // 4. Fallback: use current time as base, then next hour logic
    final now = DateTime.now();
    final nextHour = DateTime(now.year, now.month, now.day, now.hour + 1);
    final start = TimeZoneUtils.convertDateTimeToTimezone(nextHour.toUtc(), timezone);
    final end = start.add(const Duration(hours: 1));
    return (start, end);
  }

  // --- Event Data Initialization ---
  Future<void> _initEventData() async {
    if (parameter.model != null) {
      await _loadEventState(parameter.model!.uuid, parameter.model);
    } else {
      _initNewEventDefaults();
    }
  }

  /// Called when creating a new event (no model or invalid model UUID).
  /// Sets default reminder, color, and timezone, and ensures start/end dates are set.
  void _initNewEventDefaults() {
    var defaultReminder = locator.get<LocalStorage>().defaultReminder;
    emit(state.copyWith(
      reminder: defaultReminder,
      selectedColor: themeUtil.selectionColor().first,
    ));
  }

  Future<void> _loadEventState(String? uuid, EventModels? model) async {
    // Handle device event early exit
    if (EventUtils.isDeviceEvent(model)) {
      emit(state.copyWith(
        isAllDay: (model as DeviceEventModel).event.allDay ?? false,
        isDeviceEvent: true,
      ));
      return;
    }

    // Handle invalid model UUID early exit (new event)
    if (!EventUtils.hasValidModelUuid(parameter.model)) {
      _initNewEventDefaults();
      return;
    }

    await _fetchAndEmitEventState();
  }

  Future<void> _fetchAndEmitEventState() async {
    try {
      final res = await eventRepository.getEventById(parameter.model!.uuid!);
      if (res == null) return;
      await _emitEventStateFromModel(res);
    } catch (e) {
      // Optionally log error or handle as needed
    }
  }

  Future<void> _emitEventStateFromModel(EventModels res) async {
    emit(state.copyWith(
      reminder: EventUtils.getReminder(res),
      repeatType: EventUtils.getRepeatTypeAndDays(res.repeat).type,
      selectedDayOfWeek: EventUtils.getRepeatTypeAndDays(res.repeat).days,
      selectedColor: res.color.toColor,
      activity: await _getActivity(res.activity, res.activityId ?? parameter.activityId),
      memberList: res.members ?? accountService.memberInFamily.value,
      isAllDay: res.allDay == 1,
      timezone: TimeZoneUtils.getTimezoneByOffset(res.timeZone),
    ));
  }

  Future<ActivityModel?> _getActivity(ActivityModel? current, String activityId) async {
    if (activityId.isNotEmpty) {
      return await activityRepository.getActivityById(activityId);
    }
    return current;
  }

  // --- Event CRUD ---
  void createEvent() async {
    try {
      final error = validateEventForm();
      if (formHandler.isFormError.value) return;
      if (error != null) {
        return showSimpleToast(error);
      }

      emit(state.copyWith(status: UpsertEventStatus.loading));
      showLoading();
      final eventParam = EventUtils.toEventParameter(
        state: state,
        parameter: parameter,
        title: title,
        description: description,
        accountService: accountService,
      );
      final result = await eventUsecase.call(eventParam);
      eventService.updateEventAndRefresh(result, accountService.familyId);
      emit(state.copyWith(eventModels: result, status: UpsertEventStatus.success));
    } catch (e) {
      print(e);
      emit(state.copyWith(status: UpsertEventStatus.error));
    } finally {
      dismissLoading();
    }
  }

  Future<void> _onDeleteEvent(BuildContext context) async {
    final event = parameter.model;
    if (event == null || event.uuid == null || event.uuid!.isEmpty) {
      showSimpleToast(LocaleKeys.event_not_found.tr());
      return;
    }
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(LocaleKeys.delete_event_text.tr()),
        content: Text(LocaleKeys.confirm_content_delete_event.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            child: Text(LocaleKeys.cancel.tr()),
          ),
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            child: Text(LocaleKeys.delete.tr()),
          ),
        ],
      ),
    );
    if (confirm != true) return;
    try {
      emit(state.copyWith(status: UpsertEventStatus.loading));
      showLoading();
      final success = await eventRepository.deleteEvent(event.uuid!);
      if (success) {
        eventService.deleteEventAndRefresh(event.uuid!, accountService.familyId);
        showToast(LocaleKeys.delete_event_success.tr(), type: ToastType.success);
        emit(state.copyWith(eventModels: null, status: UpsertEventStatus.success));
      } else {
        emit(state.copyWith(status: UpsertEventStatus.none));
        showSimpleToast(LocaleKeys.delete_event_failed.tr());
      }
    } catch (e) {
      emit(state.copyWith(status: UpsertEventStatus.error));
      showSimpleToast(LocaleKeys.delete_event_failed.tr());
    } finally {
      dismissLoading();
    }
  }

  onMore(BuildContext context, String value) async {
    switch (value) {
      case 'edit':
        createEvent();
        break;
      case 'delete':
        await _onDeleteEvent(context);
        break;
    }
  }

  // --- UI Update Helpers ---
  void toggleSwitch(bool value) {
    String content = value ? "$REMIND_TIME mins before" : "Event Reminder";
    emit(state.copyWith(isOn: value, content: content));
  }

  void updateSelectedColor(Color color) {
    emit(state.copyWith(selectedColor: color));
  }

  void updateSelectedMember(List<Account> selectedMember) async {
    emit(state.copyWith(memberList: selectedMember));
  }

  void updateStartDate(DateTime newDateTime) {
    final allDayDates = EventUtils.getAllDaySyncedDates(
      isAllDay: state.isAllDay,
      newDateTime: newDateTime,
    );
    if (allDayDates != null) {
      emit(state.copyWith(startDate: allDayDates.$1, endDate: allDayDates.$2));
    } else if (state.endDate != null && !state.endDate!.isAfter(newDateTime)) {
      emit(state.copyWith(
        startDate: newDateTime,
        endDate: newDateTime.add(const Duration(hours: 1)),
      ));
    } else {
      emit(state.copyWith(startDate: newDateTime));
    }
  }

  void updateEndDate(DateTime newDateTime) {
    final allDayDates = EventUtils.getAllDaySyncedDates(
      isAllDay: state.isAllDay,
      newDateTime: newDateTime,
    );
    if (allDayDates != null) {
      emit(state.copyWith(startDate: allDayDates.$1, endDate: allDayDates.$2));
      return;
    }
    if (state.startDate != null && newDateTime.isBefore(state.startDate!)) {
      return showSimpleToast(LocaleKeys.end_time_cannot_be_earlier_than_start_time_text.tr());
    }
    emit(state.copyWith(endDate: newDateTime));
  }

  void onChangeTimezone(Timezone value) {
    DateTime? newStartDate = state.startDate;
    DateTime? newEndDate = state.endDate;
    if (state.startDate != null) {
      newStartDate = TimeZoneUtils.convertDateTimeToTimezone(state.startDate!, value);
    }
    if (state.endDate != null) {
      newEndDate = TimeZoneUtils.convertDateTimeToTimezone(state.endDate!, value);
    }
    emit(state.copyWith(
      timezone: value,
      startDate: newStartDate,
      endDate: newEndDate,
    ));
  }

  void updateSelectedActivity(ActivityModel activity) async {
    emit(state.copyWith(activity: activity));
  }

  void handelEventAllDay(bool value) {
    emit(state.copyWith(isAllDay: value));
  }

  onChangeReminder(int? value) {
    emit(state.copyWith(reminder: value));
  }

  onChangeEventRepeatType(EventRepeatType value) {
    emit(state.copyWith(repeatType: value));
  }

  onChangeDayOfWeek(WeekDay e) {
    var dayOfWeeks = state.selectedDayOfWeek.toList();
    if (dayOfWeeks.contains(e)) {
      dayOfWeeks.remove(e);
    } else {
      dayOfWeeks.add(e);
    }
    emit(state.copyWith(selectedDayOfWeek: dayOfWeeks));
  }

  // --- Validation ---
  /// Validates the event form and returns an error message if invalid, otherwise null.
  ///
  /// Validation rules are grouped as follows:
  /// 1. Form field validation:
  ///    - Title and description fields must be valid (handled by formHandler).
  ///    - Title and description must not be empty.
  /// 2. Required selections:
  ///    - A color must be selected.
  ///    - Start and end dates must be selected.
  /// 3. Logical constraints:
  ///    - The event duration must be at least 5 minutes (unless all-day).
  ///    - For custom repeat events, at least one day of the week must be selected.
  ///
  /// Returns the first error message encountered, or null if all rules pass.
  String? validateEventForm() {
    formHandler.onSubmit();
    if (formHandler.isFormError.value) {
      return null; // Form error already handled by formHandler
    }
    if (title.text.isEmpty) {
      return LocaleKeys.please_input_title.tr();
    }
    if (state.selectedColor == null) {
      return LocaleKeys.please_select_a_color_text.tr();
    }
    if (state.startDate == null) {
      return LocaleKeys.please_select_a_start_time_text.tr();
    }
    if (state.endDate == null) {
      return LocaleKeys.please_select_an_end_time_text.tr();
    }
    var difference = state.endDate!.difference(state.startDate!);
    if (!state.isAllDay && difference.inMinutes < 5) {
      return LocaleKeys.limit_event_duration.tr();
    }
    if (state.repeatType == EventRepeatType.custom && state.selectedDayOfWeek.isEmpty) {
      return LocaleKeys.please_select_day_of_week.tr();
    }
    return null;
  }

  // --- Activity Fetch ---
  Future<List<ActivityModel>> getAllActivities({int page = 1, int limit = LIMIT, String? name}) async {
    try {
      final result = await activityRepository.getAllActivities(accountService.familyId, name: name);
      return result;
    } catch (e) {
      return [];
    }
  }
}
