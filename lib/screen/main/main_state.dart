// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/upcoming.dart';

class MainState extends BaseState {
  final List<ListItem> listItems;
  final int count;
  final List<ActivityModel> activityList;
  final Upcoming? upcoming;
  final DateTime currentMonth;

  MainState({
    this.listItems = const [],
    this.count = 0,
    this.activityList = const [],
    this.upcoming,
    required this.currentMonth,
  });

  MainState copyWith({
    List<ListItem>? listItems,
    // List<ActivityModels>? activityItems,
    int? count,
    List<ActivityModel>? activityList,
    Upcoming? upcoming,
    DateTime? currentMonth,
  }) {
    return MainState(
      listItems: listItems ?? this.listItems,
      // activityItems: activityItems ?? this.activityItems,
      count: count ?? this.count,
      activityList: activityList ?? this.activityList,
      upcoming: upcoming ?? this.upcoming,
      currentMonth: currentMonth ?? this.currentMonth,
    );
  }

  @override
  List<Object?> get props => [
        listItems,
        // activityItems,
        count,
        activityList,
        upcoming,
        currentMonth,
      ];
}
