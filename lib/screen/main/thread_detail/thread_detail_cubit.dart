import 'dart:async';
import 'dart:io';

import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/flash/toast.dart';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import '../../../data/model/account.dart';
import 'package:family_app/data/model/thread_family.dart';
import '../../../data/repository/thread/ithread_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'model/pending_message.dart';
import 'services/thread_message_service.dart';
import 'services/thread_member_service.dart';
import 'services/thread_upload_service.dart';
import 'package:family_app/config/constant/app_constant.dart';

import 'thread_detail_state.dart';

const int messagePollingInterval = 5; // seconds

class ThreadDetailCubit extends BaseCubit<ThreadDetailState> {
  final IThreadRepository _familyRepository;
  final AccountService _accountService;
  final ThreadFamily threadFamily;

  // Services
  late final ThreadMessageService _messageService;
  late final ThreadMemberService _memberService;
  late final ThreadUploadService _uploadService;

  // Timer and navigation
  final StreamController<ThreadDetailRoute> _navigationController =
      StreamController();

  // Message read tracking
  // ignore: prefer_final_fields
  Set<String> _readMessageIds = {};
  // ignore: unused_field
  String? _lastReadMessageId;

  Stream<ThreadDetailRoute> get navigationStream =>
      _navigationController.stream;

  /// Static callback for notification-triggered refresh
  static void Function(String threadId)? onNewThreadMessageNotification;

  ThreadDetailCubit({
    required IThreadRepository familyRepository,
    required AccountService accountService,
    required this.threadFamily,
  })  : _familyRepository = familyRepository,
        _accountService = accountService,
        super(ThreadDetailState()) {
    // Initialize services
    _messageService = ThreadMessageService(
      threadRepository: _familyRepository,
      accountService: _accountService,
    );
    _memberService = ThreadMemberService(
      threadRepository: _familyRepository,
      accountService: _accountService,
    );
    _uploadService = ThreadUploadService(
      accountService: _accountService,
    );
  }

  @override
  void onInit() {
    super.onInit();
    onFetchThreadDetail(threadFamily.uuid.toString());
    _updateMemberLists();
  }

  Future<void> onRefresh() async {
    try {
      // Use the full fetch method for manual refresh to show loading state
      await onFetchThreadDetail(threadFamily.uuid.toString());
      // Mark all messages as read after refresh
      await markAllMessagesAsRead();
    } catch (e) {
      AppLogger.e("Error during refresh: $e");
    }
  }

  /// Handle app resume - mark messages as read
  Future<void> onAppResume() async {
    try {
      await markAllMessagesAsRead();
    } catch (e) {
      AppLogger.e("Error marking messages as read on app resume: $e");
    }
  }

  void _updateMemberLists() {
    final data = state.threadDetail ?? threadFamily;
    final memberList = _memberService.getMembersInThread(data);
    final memberListChat = _memberService.getMembersAvailableForChat(data);

    emit(state.copyWith(
      memberList: memberList,
      memberListChat: memberListChat,
    ));
  }

  onFetchThreadDetail(String threadId) async {
    try {
      emit(state.copyWith(status: ThreadDetailStatus.loading));
      final result = await _familyRepository.getThreadDetail(threadId);

      // First, update the state with thread detail
      emit(state.copyWith(
        status: ThreadDetailStatus.success,
        threadDetail: result,
      ));

      if (result.uuid != null) {
        onFetchThreadMessage(result.uuid.toString());
      }
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Fetch messages with loading state (for manual actions)
  onFetchThreadMessage(String threadId) async {
    try {
      emit(state.copyWith(status: ThreadDetailStatus.loading));
      final result = await _messageService.fetchThreadMessages(threadId);

      // Handle message read logic
      await _handleMessageReadLogic(result);

      emit(
          state.copyWith(status: ThreadDetailStatus.success, messages: result));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Fetch messages for auto-refresh without triggering UI loading state
  Future<void> fetchMessagesForAutoRefresh(String threadId) async {
    try {
      // First, update thread detail to get latest unread count
      final threadDetail = await _familyRepository.getThreadDetail(threadId);

      // Update thread detail in state (includes unread count)
      emit(state.copyWith(threadDetail: threadDetail));

      // Always fetch messages to check for new ones, regardless of unread count
      // This ensures we don't miss messages due to timing issues
      final result = await _messageService.fetchThreadMessages(threadId);

      // Only emit if messages have changed to avoid unnecessary rebuilds
      if (result.length != state.messages.length ||
          !_areMessagesEqual(result, state.messages)) {
        emit(state.copyWith(messages: result));

        // Handle message read logic only after we've updated the messages
        // and only if there are unread messages
        if ((threadDetail.unread ?? 0) > 0) {
          await _handleMessageReadLogic(result);
        }
      } else {
        // Even if messages haven't changed, we should still handle read logic
        // if there are unread messages (in case the unread count is stale)
        if ((threadDetail.unread ?? 0) > 0) {
          await _handleMessageReadLogic(result);
        }
      }
    } catch (e) {
      // Don't emit error state for auto-refresh to avoid UI disruption
    }
  }

  /// Compare if two message lists are equal (optimized for auto-refresh)
  bool _areMessagesEqual(List<ThreadMessage> list1, List<ThreadMessage> list2) {
    if (list1.length != list2.length) return false;

    // Compare all messages to ensure accuracy
    for (int i = 0; i < list1.length; i++) {
      final msg1 = list1[i];
      final msg2 = list2[i];

      // Compare UUID, message, and messageStatus to detect changes
      if (msg1.uuid != msg2.uuid ||
          msg1.message != msg2.message ||
          msg1.messageStatus != msg2.messageStatus) {
        return false;
      }
    }

    return true;
  }

  /// Handle message read logic when messages are fetched
  Future<void> _handleMessageReadLogic(List<ThreadMessage> messages) async {
    if (messages.isEmpty) return;

    final currentUserId = _accountService.account?.uuid;
    if (currentUserId == null) return;

    // Get messages not from current user
    final otherUserMessages = messages
        .where((msg) => msg.userId != null && msg.userId != currentUserId)
        .toList();

    if (otherUserMessages.isEmpty) return;

    // Only mark messages as read if there are unread messages in the thread
    final threadDetail = state.threadDetail ?? threadFamily;

    if ((threadDetail.unread ?? 0) > 0) {
      // Case 1: First time loading - mark latest message as read
      if (_readMessageIds.isEmpty) {
        final latestMessage = otherUserMessages.last;
        if (latestMessage.uuid != null) {
          await _markMessageAsRead(latestMessage.uuid!);
          _lastReadMessageId = latestMessage.uuid;
        }
        return;
      }

      // Case 2: Auto-refresh - only mark new messages as read if they exist
      final newMessages = otherUserMessages
          .where(
              (msg) => msg.uuid != null && !_readMessageIds.contains(msg.uuid))
          .toList();

      if (newMessages.isNotEmpty) {
        // Mark the latest new message as read
        final latestNewMessage = newMessages.last;
        if (latestNewMessage.uuid != null) {
          await _markMessageAsRead(latestNewMessage.uuid!);
          _lastReadMessageId = latestNewMessage.uuid;
        }
      }
    }
  }

  /// Mark a single message as read
  Future<void> _markMessageAsRead(String messageId) async {
    try {
      await _familyRepository.markMessageAsRead(messageId);
      _readMessageIds.add(messageId);
    } catch (e) {
      // Don't throw error to avoid breaking the main flow
    }
  }

  /// Mark all messages in thread as read
  Future<void> markAllMessagesAsRead() async {
    final currentUserId = _accountService.account?.uuid;
    if (currentUserId == null) return;

    // Only mark as read if there are unread messages
    final threadDetail = state.threadDetail ?? threadFamily;
    if ((threadDetail.unread ?? 0) > 0) {
      final otherUserMessages = state.messages
          .where((msg) => msg.userId != null && msg.userId != currentUserId)
          .toList();

      if (otherUserMessages.isNotEmpty) {
        final latestMessage = otherUserMessages.last;
        if (latestMessage.uuid != null) {
          await _markMessageAsRead(latestMessage.uuid!);
          _lastReadMessageId = latestMessage.uuid;
        }
      }
    }
  }

  /// Mark messages as read when user scrolls to bottom
  Future<void> markMessagesAsReadOnScroll() async {
    // Only mark as read if there are unread messages
    final threadDetail = state.threadDetail ?? threadFamily;
    if ((threadDetail.unread ?? 0) > 0) {
      await markAllMessagesAsRead();
    }
  }

  updateSelectedMember(List<String> selectedMemberUuids) async {
    try {
      final result = await _memberService.updateSelectedMembers(
          selectedMemberUuids, threadFamily.uuid.toString());
      emit(state.copyWith(
          status: ThreadDetailStatus.success, threadDetail: result));
      _updateMemberLists();
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  onChatWithMember(Account member) async {
    try {
      final result = await _memberService.createThreadWithMember(member);
      if (result != null) {
        _navigationController.add(_memberService.getThreadDetailRoute(result));
      }
    } catch (e) {
      AppLogger.e("Error creating thread with member: $e");
    }
  }

  /// Pick and show images immediately, then upload in background
  Future<void> pickImage({bool allowMultiple = false}) async {
    try {
      // Pick images and get file information immediately
      final pickedFiles =
          await _uploadService.pickImages(allowMultiple: allowMultiple);

      if (pickedFiles.isNotEmpty) {
        // Add to pending messages immediately
        final pendingMessages = pickedFiles
            .map((pickedFile) => PendingMessage(
                  id: pickedFile.id,
                  type: PendingMessageType.image,
                  file: pickedFile.file,
                  fileName: pickedFile.fileName,
                  filePath: pickedFile.filePath,
                  isImage: pickedFile.isImage,
                  isVideo: pickedFile.isVideo,
                  createdAt: DateTime.now(),
                  isSending: true,
                ))
            .toList();

        emit(state.copyWith(
          pendingMessages: [...state.pendingMessages, ...pendingMessages],
        ));

        // Upload in background
        uploadImagesInBackground(pickedFiles);
      }
    } catch (e) {
      showErrorToast('Failed to pick images: ${e.toString()}');
    }
  }

  /// Show gallery picker and handle file selection
  Future<void> showGalleryPicker() async {
    try {
      // This will be called from the UI when the gallery picker is shown
      // The actual file picking logic is now in the MessengerGalleryPicker
    } catch (e) {
      showErrorToast('Failed to show gallery picker: ${e.toString()}');
    }
  }

  /// Pick and show image from camera immediately, then upload in background
  Future<void> pickImageFromCamera() async {
    try {
      // Pick image from camera and get file information immediately
      final pickedFiles = await _uploadService.pickImages(useCamera: true);

      if (pickedFiles.isNotEmpty) {
        // Add to pending messages immediately
        final pendingMessages = pickedFiles
            .map((pickedFile) => PendingMessage(
                  id: pickedFile.id,
                  type: PendingMessageType.image,
                  file: pickedFile.file,
                  fileName: pickedFile.fileName,
                  filePath: pickedFile.filePath,
                  isImage: pickedFile.isImage,
                  isVideo: pickedFile.isVideo,
                  createdAt: DateTime.now(),
                  isSending: true,
                ))
            .toList();

        emit(state.copyWith(
          pendingMessages: [...state.pendingMessages, ...pendingMessages],
        ));

        // Upload in background
        uploadImagesInBackground(pickedFiles);
      } else {
        showErrorToast('No image captured or permission denied');
      }
    } catch (e) {
      showErrorToast('Failed to capture image: ${e.toString()}');
    }
  }

  /// Pick and show files immediately, then upload in background
  Future<void> pickFiles({bool allowMultiple = false}) async {
    try {
      // Only allow document file types
      const allowedExtensions = [
        'pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx', 'csv', 'odt', 'ods', 'odp'
      ];
      // Pick files and get file information immediately
      final pickedFiles =
          await _uploadService.pickFiles(allowMultiple: allowMultiple, allowedExtensions: allowedExtensions);

      if (pickedFiles.isNotEmpty) {
        // Add to pending messages immediately
        final pendingMessages = pickedFiles
            .map((pickedFile) => PendingMessage(
                  id: pickedFile.id,
                  type: PendingMessageType.file,
                  file: pickedFile.file,
                  fileName: pickedFile.fileName,
                  filePath: pickedFile.filePath,
                  isImage: pickedFile.isImage,
                  isVideo: pickedFile.isVideo,
                  createdAt: DateTime.now(),
                  isSending: true,
                ))
            .toList();

        emit(state.copyWith(
          pendingMessages: [...state.pendingMessages, ...pendingMessages],
        ));

        // Upload in background
        uploadFilesInBackground(pickedFiles);
      } else {
        AppLogger.e(
            'ThreadDetailCubit: File operation failed - no files selected');
      }
    } catch (e) {
      showErrorToast('Failed to pick files: ${e.toString()}');
    }
  }

  /// Upload images in background and update state
  Future<void> uploadImagesInBackground(List<PickedFile> pickedFiles) async {
    try {
      // Upload all images in parallel
      final storageModels = await _uploadService.uploadPickedFiles(pickedFiles);

      if (storageModels.isNotEmpty) {
        // Remove from pending messages
        final pendingIds = pickedFiles.map((f) => f.id).toSet();
        final updatedPendingMessages = state.pendingMessages
            .where((msg) => !pendingIds.contains(msg.id))
            .toList();

        // Send message and update state
        await sendImageMessage(storageModels);
        emit(state.copyWith(
          pendingMessages: updatedPendingMessages,
          status: ThreadDetailStatus.success,
        ));
      } else {
        // Mark as failed
        _markPendingMessagesAsFailed(
            pickedFiles.map((f) => f.id).toList(), 'Upload failed');
      }
    } catch (e) {
      _markPendingMessagesAsFailed(pickedFiles.map((f) => f.id).toList(), e.toString());
    }
  }

  /// Upload files in background and update state
  Future<void> uploadFilesInBackground(List<PickedFile> pickedFiles) async {
    try {
      // Upload all files in parallel
      final storageModels = await _uploadService.uploadPickedFiles(pickedFiles);

      if (storageModels.isNotEmpty) {
        // Remove from pending messages
        final pendingIds = pickedFiles.map((f) => f.id).toSet();
        final updatedPendingMessages = state.pendingMessages
            .where((msg) => !pendingIds.contains(msg.id))
            .toList();

        // Send message and update state
        await sendMultipleFilesMessage(storageModels);
        emit(state.copyWith(
          pendingMessages: updatedPendingMessages,
          status: ThreadDetailStatus.success,
        ));
      } else {
        // Mark as failed
        _markPendingMessagesAsFailed(
            pickedFiles.map((f) => f.id).toList(), 'Upload failed');
      }
    } catch (e) {
      _markPendingMessagesAsFailed(pickedFiles.map((f) => f.id).toList(), e.toString());
    }
  }

  /// Upload videos in background and update state
  Future<void> uploadVideosInBackground(List<PickedFile> pickedFiles) async {
    try {
      // Upload all videos in parallel
      final storageModels = await _uploadService.uploadPickedFiles(pickedFiles);

      if (storageModels.isNotEmpty) {
        // Remove from pending messages
        final pendingIds = pickedFiles.map((f) => f.id).toSet();
        final updatedPendingMessages = state.pendingMessages
            .where((msg) => !pendingIds.contains(msg.id))
            .toList();

        // Send message and update state
        await sendMultipleVideosMessage(storageModels);
        emit(state.copyWith(
          pendingMessages: updatedPendingMessages,
          status: ThreadDetailStatus.success,
        ));
      } else {
        // Mark as failed
        _markPendingMessagesAsFailed(
            pickedFiles.map((f) => f.id).toList(), 'Upload failed');
      }
    } catch (e) {
      _markPendingMessagesAsFailed(pickedFiles.map((f) => f.id).toList(), e.toString());
    }
  }

  /// Mark pending messages as failed
  void _markPendingMessagesAsFailed(List<String> pendingIds, String error) {
    final updatedPendingMessages = state.pendingMessages.map((msg) {
      if (pendingIds.contains(msg.id)) {
        return msg.copyWith(isSending: false, error: error);
      }
      return msg;
    }).toList();

    emit(state.copyWith(pendingMessages: updatedPendingMessages));
    showErrorToast('Upload failed: $error');
  }

  /// Remove a pending message (e.g., when user cancels)
  void removePendingMessage(String pendingId) {
    final updatedPendingMessages =
        state.pendingMessages.where((msg) => msg.id != pendingId).toList();

    emit(state.copyWith(pendingMessages: updatedPendingMessages));
  }

  /// Retry a failed pending message
  Future<void> retryPendingMessage(String pendingId) async {
    final msg = state.pendingMessages.firstWhere((m) => m.id == pendingId);

    // Mark as sending again
    final updatedPendingMessages = state.pendingMessages.map((m) {
      if (m.id == pendingId) {
        return m.copyWith(isSending: true, error: null);
      }
      return m;
    }).toList();

    emit(state.copyWith(pendingMessages: updatedPendingMessages));

    // Recreate PickedFile and retry upload if needed
    try {
      if (msg.type == PendingMessageType.image || msg.type == PendingMessageType.file) {
        final file = msg.file ?? File(msg.filePath ?? '');
        final pickedFile = PickedFile(
          id: msg.id,
          fileName: msg.fileName ?? '',
          filePath: msg.filePath ?? '',
          isImage: msg.isImage,
          isVideo: msg.isVideo,
          file: file,
        );
        if (msg.type == PendingMessageType.image) {
          await uploadImagesInBackground([pickedFile]);
        } else {
          await uploadFilesInBackground([pickedFile]);
        }
      }
      // For text, poll, etc. you can add retry logic as needed
    } catch (e) {
      _markPendingMessagesAsFailed([pendingId], e.toString());
    }
  }

  /// Convenience method for picking multiple files
  Future<void> pickMultipleFiles() async {
    await pickFiles(allowMultiple: true);
  }

  /// Convenience method for picking multiple images
  Future<void> pickMultipleImages() async {
    await pickImage(allowMultiple: true);
  }

  /// Send a text message (optimistic UI)
  Future<void> sendTextMessage(String message) async {
    final pendingId = DateTime.now().millisecondsSinceEpoch.toString();
    final pendingMessage = PendingMessage(
      id: pendingId,
      type: PendingMessageType.text,
      text: message,
      createdAt: DateTime.now(),
      isSending: true,
    );
    emit(state.copyWith(
      pendingMessages: [...state.pendingMessages, pendingMessage],
    ));
    try {
      // Mark all previous messages as read before sending
      await markAllMessagesAsRead();
      await _messageService.sendTextMessage(
          threadFamily.uuid.toString(), message);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      // Remove the pending message after success
      emit(state.copyWith(
        pendingMessages: state.pendingMessages.where((m) => m.id != pendingId).toList(),
        status: ThreadDetailStatus.success,
      ));
    } catch (e) {
      // Mark as failed
      final updatedPendingMessages = state.pendingMessages.map((m) {
        if (m.id == pendingId) {
          return m.copyWith(isSending: false, error: e.toString());
        }
        return m;
      }).toList();
      emit(state.copyWith(
        pendingMessages: updatedPendingMessages,
        status: ThreadDetailStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Send an image message
  Future<void> sendImageMessage(List<StorageModel> storageModels) async {
    try {
      await _messageService.sendImageMessage(
          threadFamily.uuid.toString(), storageModels);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send a file message
  Future<void> sendFileMessage(StorageModel storageModel) async {
    try {
      await _messageService.sendFileMessage(
          threadFamily.uuid.toString(), storageModel);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send a video message
  Future<void> sendVideoMessage(StorageModel storageModel) async {
    try {
      await _messageService.sendVideoMessage(
          threadFamily.uuid.toString(), storageModel);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send an audio message
  Future<void> sendAudioMessage(StorageModel storageModel) async {
    try {
      await _messageService.sendAudioMessage(
          threadFamily.uuid.toString(), storageModel);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send multiple files message
  Future<void> sendMultipleFilesMessage(
      List<StorageModel> storageModels) async {
    try {
      await _messageService.sendMultipleFilesMessage(
          threadFamily.uuid.toString(), storageModels);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send multiple videos message
  Future<void> sendMultipleVideosMessage(
      List<StorageModel> storageModels) async {
    try {
      await _messageService.sendMultipleVideosMessage(
          threadFamily.uuid.toString(), storageModels);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send multiple audios message
  Future<void> sendMultipleAudiosMessage(
      List<StorageModel> storageModels) async {
    try {
      await _messageService.sendMultipleAudiosMessage(
          threadFamily.uuid.toString(), storageModels);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Delete a message
  Future<void> deleteMessage(String messageId) async {
    try {
      emit(state.copyWith(status: ThreadDetailStatus.loading));

      await _messageService.deleteMessage(messageId);

      // Refresh messages after deletion
      await onFetchThreadMessage(threadFamily.uuid.toString());

      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      showErrorToast('Failed to delete message: ${e.toString()}');
    }
  }

  /// Send a location message
  Future<void> sendLocationMessage(Map<String, dynamic> extraData) async {
    try {
      await _messageService.sendLocationMessage(
          threadFamily.uuid.toString(), extraData);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send an event message
  Future<void> sendEventMessage(String eventData, {String? eventId}) async {
    try {
      await _messageService.sendEventMessage(
          threadFamily.uuid.toString(), eventData,
          eventId: eventId);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send a task message
  Future<void> sendTaskMessage(String taskData,
      {String? taskId, List<String>? taskIds}) async {
    try {
      await _messageService.sendTaskMessage(
          threadFamily.uuid.toString(), taskData,
          taskId: taskId, taskIds: taskIds);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Send a poll message
  Future<void> sendPollMessage(String pollData, {String? pollId}) async {
    try {
      await _messageService.sendPollMessage(
          threadFamily.uuid.toString(), pollData,
          pollId: pollId);
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  Future<void> sendListMessage(String message, {String? listId}) async {
    try {
      await _messageService.sendMessage(
        threadId: threadFamily.uuid.toString(),
        messageType: ThreadMessageType.LIST,
        message: message,
        messageTypeId: listId,
      );
      await onFetchThreadMessage(threadFamily.uuid.toString());
      emit(state.copyWith(status: ThreadDetailStatus.success));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadDetailStatus.error, errorMessage: e.toString()));
    }
  }

  /// Handle files selected from MessengerGalleryPicker (moved from UI for clarity)
  Future<void> handleFilesSelectedFromGallery(List<File> files) async {
    try {
      final pickedFiles = files.map((file) {
        final isImage = _uploadService.isImageFile(file.path.split('.').last);
        final isVideo = _uploadService.isVideoFile(file.path.split('.').last);
        return PickedFile(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          fileName: file.path.split('/').last,
          filePath: file.path,
          isImage: isImage,
          isVideo: isVideo,
          file: file,
        );
      }).toList();

      // Add to pending messages
      final pendingMessages = pickedFiles.map((pickedFile) => PendingMessage(
        id: pickedFile.id,
        type: pickedFile.isImage ? PendingMessageType.image : PendingMessageType.file,
        file: pickedFile.file,
        fileName: pickedFile.fileName,
        filePath: pickedFile.filePath,
        isImage: pickedFile.isImage,
        isVideo: pickedFile.isVideo,
        createdAt: DateTime.now(),
        isSending: true,
      )).toList();
      emit(state.copyWith(
        pendingMessages: [...state.pendingMessages, ...pendingMessages],
      ));

      // Separate images and videos
      final pickedImages = pickedFiles.where((f) => f.isImage).toList();
      final pickedVideos = pickedFiles.where((f) => f.isVideo).toList();
      if (pickedImages.isNotEmpty) {
        uploadImagesInBackground(pickedImages);
      }
      if (pickedVideos.isNotEmpty) {
        uploadVideosInBackground(pickedVideos);
      }
    } catch (e) {
      showErrorToast('Failed to process selected files: $e');
    }
  }

  @override
  Future<void> close() {
    _navigationController.close();
    return super.close();
  }
}
