import 'package:auto_route/auto_route.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_cubit.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';
import 'dart:io';

import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'poll/detail_upsert/screen.dart';
import 'poll/upsert/screen.dart';
import 'widget/thread_detail_app_bar.dart';
import 'widget/popup/thread_detail_popup.dart';
import 'widget/message/thread_message_list.dart';
import 'widget/input/messenger_gallery_picker.dart';
import 'widget/popup/select_event_thread_bts.dart';
import 'widget/popup/select_list_thread_bts.dart';
import 'widget/popup/select_task_thread_bts.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'widget/input/input_row.dart';
import 'widget/input/input_area_menu_grid.dart';

@RoutePage()
class ThreadDetailPage
    extends BaseBlocProvider<ThreadDetailState, ThreadDetailCubit> {
  const ThreadDetailPage({required this.parameter, super.key});

  final ThreadFamily parameter;

  @override
  Widget buildPage() => const ThreadDetailView();

  @override
  ThreadDetailCubit createCubit() => ThreadDetailCubit(
        familyRepository: locator.get(),
        accountService: locator.get(),
        threadFamily: parameter,
      );
}

class ThreadDetailView extends StatefulWidget {
  const ThreadDetailView({super.key});

  @override
  State<ThreadDetailView> createState() => _ThreadDetailViewState();
}

class _ThreadDetailViewState extends BaseBlocPageState<ThreadDetailView,
    ThreadDetailState, ThreadDetailCubit> with WidgetsBindingObserver {
  // --- Controllers & State ---
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();
  final ValueNotifier<bool> _showMenuNotifier = ValueNotifier<bool>(false);
  late ThreadDetailCubit cubit;
  final ValueNotifier<bool> _showInputArea = ValueNotifier<bool>(true);

  // --- Lifecycle ---
  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChange);
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addObserver(this);
    // Register notification callback
    ThreadDetailCubit.onNewThreadMessageNotification = (threadId) {
      if (mounted && cubit.threadFamily.uuid == threadId) {
        cubit.fetchMessagesForAutoRefresh(threadId);
      }
    };
    // Start polling for new messages after first frame and thread detail is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Don't start auto-refresh immediately, wait for thread detail to be loaded
    });
  }

  @override
  void dispose() {
    // Unregister notification callback
    ThreadDetailCubit.onNewThreadMessageNotification = null;
    WidgetsBinding.instance.removeObserver(this);
    _focusNode.removeListener(_onFocusChange);
    _scrollController.removeListener(_onScroll);
    _focusNode.dispose();
    _scrollController.dispose();
    _controller.dispose();
    _showMenuNotifier.dispose();
    _showInputArea.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    cubit = context.read<ThreadDetailCubit>();
    cubit.navigationStream.listen((route) {
      context.pushRoute(route);
    });
  }

  // Pause/resume polling on app lifecycle changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive) {
    } else if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
    } else if (state == AppLifecycleState.resumed) {
      // Mark messages as read when app resumes
      cubit.onAppResume();
    }
  }

  // --- Utility ---
  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      // Hide menu when text field gains focus (keyboard shows)
      _showMenuNotifier.value = false;
    }
  }

  void _onScroll() {
    // Check if user has scrolled to bottom (or near bottom)
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100) {
      // Mark messages as read when user scrolls to bottom
      cubit.markMessagesAsReadOnScroll();
    }
  }

  // --- Build methods ---
  @override
  Widget buildAppBar(
      BuildContext context, ThreadDetailCubit cubit, ThreadDetailState state) {
    return ThreadDetailAppBar(
      members: (state.threadDetail ?? cubit.threadFamily).members ?? [],
      threadName: cubit.threadFamily.name,
      currentUserId: accountService.account?.uuid,
      onBack: () {
        Navigator.pop(context);
      },
      actions: [
        ThreadDetailPopup(
          memberList: state.memberList,
          memberListChat: state.memberListChat,
          onUpdateSelectedMember: (selectedMemberUuids) {
            cubit.updateSelectedMember(selectedMemberUuids);
          },
          onChatWithMember: (member) {
            Navigator.pop(context);
            cubit.onChatWithMember(member);
          },
        ),
      ],
    );
  }

  @override
  Widget buildBody(
      BuildContext context, ThreadDetailCubit cubit, ThreadDetailState state) {
    return GestureDetector(
      // Detect taps anywhere in the body to hide menu
      onTap: () {
        if (_showMenuNotifier.value) {
          _showMenuNotifier.value = false;
        }
        FocusScope.of(context).unfocus();
      },
      child: Container(
        color: appTheme.backgroundV2,
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  RefreshIndicator(
                    onRefresh: cubit.onRefresh,
                    child: state.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : ThreadMessageList(
                            messages: state.messages,
                            memberList: state.memberList,
                            scrollController: _scrollController,
                            currentUserId: accountService.account?.uuid,
                            onShowVoting: (ThreadMessage message) =>
                                _handleShowVoting(message, state.memberList),
                            pendingMessages: state.pendingMessages,
                            onRetryPendingMessage: (pendingId) =>
                                cubit.retryPendingMessage(pendingId),
                            onCancelPendingMessage: (pendingId) =>
                                cubit.removePendingMessage(pendingId),
                          ),
                  ),
                ],
              ),
            ),
            // Input area - now controlled by _showInputArea
            ValueListenableBuilder<bool>(
              valueListenable: _showInputArea,
              builder: (context, show, child) {
                return show
                    ? Container(
                        color: appTheme.backgroundV2,
                        child: SafeArea(
                          top: false,
                          child: GestureDetector(
                            onTap: () {},
                            child: _buildInputArea(
                              controller: _controller,
                              focusNode: _focusNode,
                              cubit: cubit,
                              state: state,
                            ),
                          ),
                        ),
                      )
                    : const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  // --- Widget builders ---
  Widget _buildInputArea({
    required TextEditingController controller,
    required FocusNode focusNode,
    required ThreadDetailCubit cubit,
    required ThreadDetailState state,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: appTheme.borderColorV2)),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: ValueListenableBuilder<bool>(
        valueListenable: _showMenuNotifier,
        builder: (context, showMenu, child) {
          return InputAreaWithMenu(
            controller: controller,
            focusNode: focusNode,
            showMenu: showMenu,
            onToggleMenu: _onToggleMenu,
            onSend: () {
              if (controller.text.trim().isNotEmpty) {
                final message = controller.text.trim();
                cubit.sendTextMessage(message);
                controller.clear();
              }
            },
          );
        },
      ),
    );
  }

  void _onToggleMenu() {
    _showMenuNotifier.value = !_showMenuNotifier.value;
    if (_showMenuNotifier.value) {
      FocusScope.of(context).unfocus();
    }
  }

  void _handleShowVoting(
      ThreadMessage message, List<Account> memberList) async {
    final relatedData = message.relatedData;
    if (relatedData != null && relatedData.uuid?.isNotEmpty == true) {
      final result =
          await VotingDetailUpsertBts.show(context, relatedData, memberList);
      if (result == true) {
        await cubit.onFetchThreadDetail(relatedData.threadId ?? '');
      }
    }
  }
}

class InputAreaWithMenu extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final bool showMenu;
  final VoidCallback onToggleMenu;
  final VoidCallback onSend;

  const InputAreaWithMenu({
    Key? key,
    required this.controller,
    required this.focusNode,
    required this.showMenu,
    required this.onToggleMenu,
    required this.onSend,
  }) : super(key: key);

  @override
  State<InputAreaWithMenu> createState() => _InputAreaWithMenuState();
}

class _InputAreaWithMenuState extends State<InputAreaWithMenu> {
  bool _showGalleryPicker = false;

  void _handleFilesSelected(List<File> files) {
    context.read<ThreadDetailCubit>().handleFilesSelectedFromGallery(files);
    _handleCloseGalleryPicker();
  }

  void _handleShowGalleryPicker() {
    setState(() {
      _showGalleryPicker = true;
    });
  }

  void _handleCloseGalleryPicker() {
    setState(() {
      _showGalleryPicker = false;
    });
  }

  Future<void> _handleLocation(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    final result = await context.router.push(
      PlaceMapSelectionRoute(
        parameter: PlaceUpsertParameter(
          tripId: cubit.threadFamily.uuid ?? '',
          dayIndex: 0,
          activityIndex: null,
          place: null,
          viewMode: false,
          allActivities: const [],
          initialLocation: null,
        ),
      ),
    );
    if (result is PlaceUpsertParameter &&
        result.place != null &&
        (result.place!.activities?.isNotEmpty ?? false)) {
      final activity = result.place!.activities!.first;
      if (activity.latitude != null && activity.longitude != null) {
        final extraData = <String, dynamic>{
          'latitude': activity.latitude,
          'longitude': activity.longitude,
          if (activity.venue != null) 'address': activity.venue,
          if (activity.activityImage != null)
            'photoUrl': activity.activityImage,
          if (activity.city != null) 'city': activity.city,
          'time': activity.time,
          if (activity.description != null && activity.description!.isNotEmpty)
            'description': activity.description,
        };
        await cubit.sendLocationMessage(extraData);
      }
    }
  }

  Future<void> _handleEvent(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    await SelectEventThreadBts.show(
      context,
      selectedEvents: [],
      onEventsSelected: (events) {
        final event = events.first;
        cubit.sendEventMessage(event.name ?? '', eventId: event.uuid);
      },
      title: 'Select Event',
    );
  }

  Future<void> _handleList(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    await SelectListThreadBts.show(
      context,
      selectedList: null,
      onListSelected: (list) {
        cubit.sendListMessage(list.name ?? '', listId: list.uuid);
      },
      title: 'Select List',
    );
  }

  Future<void> _handleTask(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    final result = await SelectTaskThreadBTS.show(
      context,
      initialSelectedTaskUuids: [],
      initialListUuid: null,
      title: 'Select Task',
    );
    if (result != null && result.isNotEmpty) {
      if (result.length == 1) {
        final task = result.first;
        cubit.sendTaskMessage(task.name ?? '', taskId: task.uuid);
      } else {
        final taskNames = result
            .map((t) => t.name ?? '')
            .where((n) => n.isNotEmpty)
            .join(', ');
        final taskIds = result
            .map((t) => t.uuid ?? '')
            .where((id) => id.isNotEmpty)
            .toList();
        cubit.sendTaskMessage(
            taskNames.isNotEmpty ? taskNames : 'Multiple tasks',
            taskIds: taskIds);
      }
    }
  }

  Future<void> _handlePoll(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    final state = cubit.state;
    final threadUuid = (state.threadDetail ?? cubit.threadFamily).uuid;
    final result = await ThreadDetailVotingUpsertBts.show(context, threadUuid!);
    if (result == true) {
      await cubit.onFetchThreadDetail(threadUuid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 180),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Input Row
          InputRow(
            controller: widget.controller,
            focusNode: widget.focusNode,
            showMenu: widget.showMenu,
            onToggleMenu: widget.onToggleMenu,
            onSend: widget.onSend,
          ),
          // Menu or Gallery Picker
          if (widget.showMenu) ...[
            Divider(color: appTheme.borderColorV2),
            _showGalleryPicker
                ? MessengerGalleryPicker(
                    onFilesSelected: _handleFilesSelected,
                    onClose: _handleCloseGalleryPicker,
                  )
                : InputAreaMenuGrid(
                    onCamera: () {
                      context.read<ThreadDetailCubit>().pickImageFromCamera();
                    },
                    onGallery: _handleShowGalleryPicker,
                    onFile: () {
                      context.read<ThreadDetailCubit>().pickMultipleFiles();
                    },
                    onLocation: () => _handleLocation(context),
                    onEvent: () => _handleEvent(context),
                    onList: () => _handleList(context),
                    onTask: () => _handleTask(context),
                    onPoll: () => _handlePoll(context),
                  ),
          ],
        ],
      ),
    );
  }

  @override
  void didUpdateWidget(covariant InputAreaWithMenu oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!widget.showMenu && oldWidget.showMenu) {
      setState(() {
        _showGalleryPicker = false;
      });
    }
  }
}
