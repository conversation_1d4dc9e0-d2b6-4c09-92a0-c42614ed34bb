import 'package:family_app/base/widget/cubit/base_state.dart';

enum ThreadDetailVotingUpsertStatus { initial, loading, success, error, done }

class ThreadDetailVotingUpsertState extends BaseState {
  final ThreadDetailVotingUpsertStatus status;
  final String threadId;
  final String? name;
  final bool isNameValid;
  final List<Map<String, dynamic>> options;
  final bool isOptionsValid;
  final DateTime? fromTime;
  final bool isFromTimeValid;
  final DateTime? toTime;
  final bool isToTimeValid;
  final bool allowMultipleChoice;
  final bool timeLimit3Days;

  ThreadDetailVotingUpsertState({
    this.status = ThreadDetailVotingUpsertStatus.initial,
    required this.threadId,
    this.name,
    this.isNameValid = false,
    this.options = const [],
    this.isOptionsValid = false,
    this.fromTime,
    this.isFromTimeValid = true,
    this.toTime,
    this.isToTimeValid = true,
    this.allowMultipleChoice = false,
    this.timeLimit3Days = false,
  });

  @override
  List<Object?> get props => [status, threadId, name, isNameValid, options, fromTime, toTime, isOptionsValid, isFromTimeValid, isToTimeValid, allowMultipleChoice, timeLimit3Days];

  ThreadDetailVotingUpsertState copyWith({
    ThreadDetailVotingUpsertStatus? status,
    String? threadId,
    String? name,
    bool? isNameValid,
    List<Map<String, dynamic>>? options,
    bool? isOptionsValid,
    DateTime? fromTime,
    bool? isFromTimeValid,
    DateTime? toTime,
    bool? isToTimeValid,
    bool? allowMultipleChoice,
    bool? timeLimit3Days,
  }) {
    return ThreadDetailVotingUpsertState(
      status: status ?? this.status,
      threadId: threadId ?? this.threadId,
      name: name ?? this.name,
      isNameValid: isNameValid ?? this.isNameValid,
      options: options ?? this.options,
      isOptionsValid: isOptionsValid ?? this.isOptionsValid,
      fromTime: fromTime ?? this.fromTime,
      isFromTimeValid: isFromTimeValid ?? this.isFromTimeValid,
      toTime: toTime ?? this.toTime,
      isToTimeValid: isToTimeValid ?? this.isToTimeValid,
      allowMultipleChoice: allowMultipleChoice ?? this.allowMultipleChoice,
      timeLimit3Days: timeLimit3Days ?? this.timeLimit3Days,
    );
  }
}
