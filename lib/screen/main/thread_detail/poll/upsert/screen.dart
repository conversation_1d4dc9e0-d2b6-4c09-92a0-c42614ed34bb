import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/flash/flash.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/dotted_border/dotted_border.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/switch.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:family_app/widget/textfield/title_text_field_v3.dart';
import 'package:flutter/material.dart';

import '../../../thread/widget/rotating_check_icon_success.dart';
import '../../../thread/widget/thread_app_bar.dart';
import 'cubit.dart';
import 'parameter.dart';
import 'state.dart';

class ThreadDetailVotingUpsertBts extends BaseBlocProvider<ThreadDetailVotingUpsertState, ThreadDetailVotingUpsertCubit> {
  final ThreadDetailVotingUpsertParameter parameter;

  const ThreadDetailVotingUpsertBts({required this.parameter, super.key});

  static Future<bool?> show(BuildContext context, String threadId) async {
    return BottomSheetUtils.showHeightReturnBool(
      context,
      height: 0.9,
      child: ThreadDetailVotingUpsertBts(
        parameter: ThreadDetailVotingUpsertParameter(threadId),
      ),
    );
  }

  @override
  Widget buildPage() => const _ThreadDetailVotingUpsertScreen();

  @override
  ThreadDetailVotingUpsertCubit createCubit() => ThreadDetailVotingUpsertCubit(parameter);
}

class _ThreadDetailVotingUpsertScreen extends StatefulWidget {
  const _ThreadDetailVotingUpsertScreen();

  @override
  State<_ThreadDetailVotingUpsertScreen> createState() => _ThreadDetailVotingUpsertScreenState();
}

class _ThreadDetailVotingUpsertScreenState extends BaseBlocPageState<_ThreadDetailVotingUpsertScreen, ThreadDetailVotingUpsertState, ThreadDetailVotingUpsertCubit> {
  @override
  bool listenWhen(ThreadDetailVotingUpsertState previous, ThreadDetailVotingUpsertState current) {
    switch (current.status) {
      case ThreadDetailVotingUpsertStatus.loading:
        showLoading();
        break;
      case ThreadDetailVotingUpsertStatus.success:
        dismissLoading();
        showFullScreenFlash(
          context,
          child: const RotatingCheckIconSuccess(text: "Poll Created"),
        );
        Navigator.of(context).pop(true);
        break;
      default:
        dismissLoading();
        break;
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        _buildHeader(context, cubit, state),
        _buildBody(context, cubit, state),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return Padding(
      padding: const EdgeInsets.only(top: 12.0, bottom: 8.0),
      child: ThreadAppBar(
        title: 'Poll',
        actions: [
          ButtonIcon(
            Assets.icons.check.path,
            size: 44.h2,
            sizeIcon: 24.h2,
            colorIcon: (state.isNameValid && state.isOptionsValid) ? appTheme.primaryColorV2 : appTheme.grayV2,
            () {
              if (state.isNameValid && state.isOptionsValid) cubit.formHandler.onSubmit();
            },
            bg: appTheme.transparentColor,
          )
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return Expanded(
      child: SingleChildScrollView(
        child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildQuestion(context, cubit, state),
                const SizedBox(height: 16),
                _buildOptions(context, cubit, state),
                const SizedBox(height: 16),
                _buildSettings(context, cubit, state),
              ],
            )),
      ),
    );
  }

  Widget _buildTextBox(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return ValueListenableBuilder(
      valueListenable: cubit.formHandler.formValidated,
      builder: (context, isValidate, _) => TitleTextFieldV2(fieldNode: cubit.handlerTitle, validatedForm: isValidate),
    );
  }

  Widget _buildQuestion(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return _buildTextBox(context, cubit, state);
  }

  Widget _buildOptions(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildOptionList(context, cubit, state),
        Row(
          spacing: 4.0,
          children: [
            const SizedBox(
              width: 32,
              height: 32,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: InkWell(
                  child: DottedBorder(
                    radius: const Radius.circular(8),
                    borderType: BorderType.RRect,
                    color: appTheme.borderColorV2,
                    dashPattern: const [4, 4],
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    child: Text(
                      'Add option ...',
                      style: AppStyle.regular14(color: appTheme.grayV2),
                    ),
                  ),
                  onTap: () => cubit.addOption(),
                ),
              ),
            ),
            const SizedBox(
              width: 32,
              height: 32,
            ),
          ],
        )
      ],
    );
  }

  Widget _buildOptionList(
    BuildContext context,
    ThreadDetailVotingUpsertCubit cubit,
    ThreadDetailVotingUpsertState state,
  ) {
    return ReorderableListView(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        for (final (index, option) in state.options.indexed)
          ListTile(
            key: ValueKey('option-$index'),
            leading: const SizedBox(
              width: 32,
              height: 32,
              child: Icon(Icons.menu, size: 24),
            ),
            title: ValueListenableBuilder(
              valueListenable: cubit.formHandler.formValidated,
              builder: (context, isValidate, _) => TitleTextFieldV3(
                fieldNode: option['handler'],
                validatedForm: isValidate,
                hintStyle: AppStyle.regular14(color: appTheme.grayV2),
              ),
            ),
            trailing: SizedBox(
              width: 32,
              height: 32,
              child: IconButton(
                padding: EdgeInsets.zero,
                icon: ImageAssetCustom(imagePath: Assets.icons.iconDelete.path),
                onPressed: () => cubit.removeOption(index),
              ),
            ),
            contentPadding: const EdgeInsets.all(4),
          ),
      ],
      onReorder: (int oldIndex, int newIndex) {
        cubit.onReorder(oldIndex, newIndex);
      },
    );
  }

  Widget _buildSettings(BuildContext context, ThreadDetailVotingUpsertCubit cubit, ThreadDetailVotingUpsertState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Allow Multiple Choice', style: AppStyle.regular16(color: Colors.black)),
            SwitchCustom(state.allowMultipleChoice, cubit.updateAllowMultipleChoice),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Time limit of 3 days', style: AppStyle.regular16(color: Colors.black)),
            SwitchCustom(state.timeLimit3Days, cubit.updateTimeLimit3Days),
          ],
        ),
      ],
    );
  }
}
