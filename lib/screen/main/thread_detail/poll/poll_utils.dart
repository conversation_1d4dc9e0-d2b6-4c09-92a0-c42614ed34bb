import 'package:flutter/material.dart';
import 'package:family_app/main.dart';

class PollUtils {
  static final List<Color> defaultVotedColors = [
    appTheme.orangeColorV2,
    appTheme.grayColor.withAlpha(140),
  ];

  static List<Color?> getVotedColorsForOptions(
    List<int> voteCounts, [
    List<Color>? votedColors,
  ]) {
    final colors = votedColors ?? defaultVotedColors;
    int votedColorIdx = 0;
    return voteCounts.map((count) {
      if (count > 0) {
        final color = colors[votedColorIdx % colors.length];
        votedColorIdx++;
        return color;
      }
      return null;
    }).toList();
  }
}
