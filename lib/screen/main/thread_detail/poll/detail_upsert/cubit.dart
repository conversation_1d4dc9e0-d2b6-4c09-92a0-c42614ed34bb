import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:family_app/data/repository/thread_poll/ithread_poll_repository.dart';
import 'package:family_app/data/repository/thread_poll/model/thread_poll_parameter.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:flutter/widgets.dart';
import 'state.dart';

class VotingDetailUpsertCubit extends BaseCubit<VotingDetailUpsertState> {
  final IThreadPollRepository threadPollRepository;

  VotingDetailUpsertCubit()
      : threadPollRepository = locator<IThreadPollRepository>(),
        super(VotingDetailUpsertState());

  void updateSelectedtemUuid(String uuid, {bool multiple = false}) {
    if (multiple) {
      final current = List<String>.from(state.selectedItemUuids);
      if (current.contains(uuid)) {
        current.remove(uuid);
      } else {
        current.add(uuid);
      }
      emit(state.copyWith(selectedItemUuids: current, isActive: current.isNotEmpty));
    } else {
      emit(state.copyWith(selectedItemUuid: uuid, isActive: true));
    }
  }

  void validateForm({bool multiple = false}) {
    if (multiple) {
      emit(state.copyWith(isActive: state.selectedItemUuids.isNotEmpty));
    } else {
      emit(state.copyWith(isActive: state.selectedItemUuid != null));
    }
  }

  Future<void> onSubmit(String pollId, {bool multiple = false}) async {
    try {
      emit(state.copyWith(status: VotingDetailUpsertStatus.loading));
      final pollItems = multiple ? state.selectedItemUuids : [state.selectedItemUuid!];
      final parameter = ThreadPollParameter(pollItems: pollItems);
      await threadPollRepository.threadPollVote(pollId, parameter);
      emit(state.copyWith(status: VotingDetailUpsertStatus.success));
    } catch (e) {
      emit(state.copyWith(status: VotingDetailUpsertStatus.error));
    }
  }

  Future<void> createEvent(BuildContext context, ThreadMessageRelatedData relatedData, List<Account> memberList) async {
    final itemWithBestTotalCountVote = relatedData.getItemWithBestTotalCountVoteInfo();

    await context.pushRoute(UpsertEventRoute(
      upsertEventParameter: UpsertEventParameter(
        model: EventModels(
          name: relatedData.name,
          description: itemWithBestTotalCountVote?.name,
          members: memberList,
        ),
      ),
    ));
  }
}
