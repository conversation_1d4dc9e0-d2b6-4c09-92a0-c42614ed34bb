import 'package:family_app/base/widget/cubit/base_state.dart';

enum VotingDetailUpsertStatus { initial, loading, success, error, done }

class VotingDetailUpsertState extends BaseState {
  final VotingDetailUpsertStatus status;
  final String? selectedItemUuid;
  final List<String> selectedItemUuids;
  final bool isActive;

  VotingDetailUpsertState({
    this.status = VotingDetailUpsertStatus.initial,
    this.selectedItemUuid,
    this.selectedItemUuids = const [],
    this.isActive = false,
  });

  @override
  List<Object?> get props => [status, selectedItemUuid, selectedItemUuids, isActive];

  VotingDetailUpsertState copyWith({
    VotingDetailUpsertStatus? status,
    String? selectedItemUuid,
    List<String>? selectedItemUuids,
    bool? isActive,
  }) {
    return VotingDetailUpsertState(
      status: status ?? this.status,
      selectedItemUuid: selectedItemUuid ?? this.selectedItemUuid,
      selectedItemUuids: selectedItemUuids ?? this.selectedItemUuids,
      isActive: isActive ?? this.isActive,
    );
  }
}
