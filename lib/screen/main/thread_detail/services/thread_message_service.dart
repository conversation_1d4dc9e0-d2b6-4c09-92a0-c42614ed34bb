import 'dart:convert';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/data/repository/thread/model/message_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';

/// Service class responsible for handling all message-related operations
/// in the thread detail screen.
class ThreadMessageService {
  final IThreadRepository _threadRepository;

  ThreadMessageService({
    required IThreadRepository threadRepository,
    required AccountService accountService,
  }) : _threadRepository = threadRepository;

  /// Fetch thread messages for a given thread ID
  Future<List<ThreadMessage>> fetchThreadMessages(String threadId) async {
    try {
      final result = await _threadRepository.getThreadMessage(threadId);
      final List<ThreadMessage> messages = [];
      for (final raw in result) {
        try {
          // ignore: unnecessary_type_check
          final msg = raw is ThreadMessage
              ? raw
              : ThreadMessage.fromJson(raw as Map<String, dynamic>);
          messages.add(msg);
        } catch (e, st) {
          AppLogger.e('Error deserializing ThreadMessage: $e\nRaw: $raw\n$st');
        }
      }
      return messages;
    } catch (e) {
      AppLogger.e('Error fetching thread messages: $e');
      rethrow;
    }
  }

  /// Send a generic message with specified type and content
  Future<void> sendMessage({
    required String threadId,
    required String messageType,
    String? message,
    String? messageTypeId,
    Map<String, dynamic>? optionalData,
  }) async {
    if (!ThreadMessageType.isValidType(messageType)) {
      throw ArgumentError("Invalid message type: $messageType");
    }

    try {
      final MessageParameter messageParameter = _createMessageParameter(
        messageType: messageType,
        message: message,
        messageTypeId: messageTypeId,
        optionalData: optionalData,
      );

      await _threadRepository.createThreadMessage(
          messageParameter, threadId);
    } catch (e) {
      AppLogger.e('Error sending message: $e');
      rethrow;
    }
  }

  /// Utility to build default attachment message for attachments and special types
  String buildDefaultAttachmentMessage({
    required String
        type, // e.g. 'Image', 'Video', 'File', 'Location', 'Event', etc.
    int count = 1,
    String? name, // For event/list/task/poll
    bool includeName = false,
  }) {
    if (includeName && name != null && name.isNotEmpty) {
      return '[$type: $name]';
    }
    if (count > 1) {
      return '[$count ${type}s]';
    }
    return '[$type]';
  }

  /// Send a text message
  Future<void> sendTextMessage(String threadId, String message) async {
    if (message.trim().isEmpty) return;
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.TEXT,
      message: message,
    );
  }

  /// Send an image message using StorageModel
  Future<void> sendImageMessage(
      String threadId, List<StorageModel> storageModels,
      {String? userMessage}) async {
    if (storageModels.isEmpty) return;
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'Image', count: storageModels.length);
    try {
      if (storageModels.length == 1) {
        final storageModel = storageModels.first;
        await sendMessage(
          threadId: threadId,
          messageType: ThreadMessageType.IMAGE,
          messageTypeId: storageModel.uuid,
          message:
              userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
        );
      } else {
        final uuids = storageModels.map((model) => model.uuid).toList();
        await sendMessage(
          threadId: threadId,
          messageType: ThreadMessageType.IMAGE,
          message:
              userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
          messageTypeId: uuids.join(','),
        );
      }
    } catch (e) {
      AppLogger.e('Error sending image message: $e');
      rethrow;
    }
  }

  /// Send a file message
  Future<void> sendFileMessage(String threadId, StorageModel storageModel,
      {String? userMessage}) async {
    final defaultMsg = buildDefaultAttachmentMessage(type: 'File');
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.FILE,
      messageTypeId: storageModel.uuid,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
    );
  }

  /// Send a video message
  Future<void> sendVideoMessage(String threadId, StorageModel storageModel,
      {String? userMessage}) async {
    final defaultMsg = buildDefaultAttachmentMessage(type: 'Video');
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.VIDEO,
      messageTypeId: storageModel.uuid,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
    );
  }

  /// Send an audio message
  Future<void> sendAudioMessage(String threadId, StorageModel storageModel,
      {String? userMessage}) async {
    final defaultMsg = buildDefaultAttachmentMessage(type: 'Audio');
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.AUDIO,
      messageTypeId: storageModel.uuid,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
    );
  }

  /// Send multiple files message
  Future<void> sendMultipleFilesMessage(
      String threadId, List<StorageModel> storageModels,
      {String? userMessage}) async {
    if (storageModels.isEmpty) return;
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'File', count: storageModels.length);
    final uuids = storageModels.map((model) => model.uuid).toList();
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.FILE,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
      messageTypeId: uuids.join(','),
    );
  }

  /// Send multiple videos message
  Future<void> sendMultipleVideosMessage(
      String threadId, List<StorageModel> storageModels,
      {String? userMessage}) async {
    if (storageModels.isEmpty) return;
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'Video', count: storageModels.length);
    final uuids = storageModels.map((model) => model.uuid).toList();
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.VIDEO,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
      messageTypeId: uuids.join(','),
    );
  }

  /// Send multiple audios message
  Future<void> sendMultipleAudiosMessage(
      String threadId, List<StorageModel> storageModels,
      {String? userMessage}) async {
    if (storageModels.isEmpty) return;
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'Audio', count: storageModels.length);
    final uuids = storageModels.map((model) => model.uuid).toList();
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.AUDIO,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
      messageTypeId: uuids.join(','),
    );
  }

  /// Send a location message (always requires extraData)
  Future<void> sendLocationMessage(
      String threadId, Map<String, dynamic> extraData,
      {String? userMessage}) async {
    final defaultMsg = buildDefaultAttachmentMessage(type: 'Location');
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.LOCATION,
      message: (userMessage != null && userMessage.trim().isNotEmpty)
          ? userMessage
          : defaultMsg,
      optionalData: extraData,
    );
  }

  /// Send an event message
  Future<void> sendEventMessage(String threadId, String eventName,
      {String? eventId, String? userMessage, bool includeName = false}) async {
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'Event', name: eventName, includeName: includeName);
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.EVENT,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
      messageTypeId: eventId,
    );
  }

  /// Send a task message
  Future<void> sendTaskMessage(String threadId, String taskData,
      {String? taskId,
      List<String>? taskIds,
      String? userMessage,
      String? taskName,
      bool includeName = false}) async {
    String? messageTypeId;
    if (taskIds != null && taskIds.isNotEmpty) {
      messageTypeId = taskIds.join(',');
    } else {
      messageTypeId = taskId;
    }
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'Task',
        name: taskName,
        includeName: includeName,
        count: taskIds?.length ?? 1);
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.TASK,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
      messageTypeId: messageTypeId,
    );
  }

  /// Send a poll message
  Future<void> sendPollMessage(String threadId, String pollData,
      {String? pollId,
      String? pollName,
      String? userMessage,
      bool includeName = false}) async {
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'Poll', name: pollName, includeName: includeName);
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.POLL,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
      messageTypeId: pollId,
    );
  }

  /// Send a list message
  Future<void> sendListMessage(String threadId, String listName,
      {String? listId, String? userMessage, bool includeName = false}) async {
    final defaultMsg = buildDefaultAttachmentMessage(
        type: 'List', name: listName, includeName: includeName);
    await sendMessage(
      threadId: threadId,
      messageType: ThreadMessageType.LIST,
      message:
          userMessage?.trim().isNotEmpty == true ? userMessage : defaultMsg,
      messageTypeId: listId,
    );
  }

  /// Delete a message
  Future<void> deleteMessage(String messageId) async {
    try {
      await _threadRepository.deleteThreadMessage(messageId);
    } catch (e) {
      AppLogger.e('Error deleting message: $e');
      rethrow;
    }
  }

  /// Create MessageParameter based on message type
  MessageParameter _createMessageParameter({
    required String messageType,
    String? message,
    String? messageTypeId,
    Map<String, dynamic>? optionalData,
  }) {
    // Convert data map to JSON string if not empty
    String? dataString;
    if (optionalData != null && optionalData.isNotEmpty) {
      try {
        dataString = jsonEncode(optionalData);
      } catch (e) {
        AppLogger.e('Error encoding data to JSON: $e');
        dataString = null;
      }
    }

    return MessageParameter(
      message: message?.trim() ?? '',
      messageType: messageType,
      messageTypeId: messageTypeId ?? "",
      data: dataString,
    );
  }
}
