import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/data/repository/thread/model/thread_parameter.dart';
import 'package:family_app/router/app_route.dart';

/// Service class responsible for handling all member-related operations
/// in the thread detail screen.
class ThreadMemberService {
  final IThreadRepository _threadRepository;
  final AccountService _accountService;

  ThreadMemberService({
    required IThreadRepository threadRepository,
    required AccountService accountService,
  })  : _threadRepository = threadRepository,
        _accountService = accountService;

  /// Get all members in the current thread
  List<Account> getMembersInThread(ThreadFamily threadFamily) {
    try {
      final data = threadFamily;
      final members = data.members ?? [];
      final result = _accountService.memberInFamily.value
          .where((member) =>
              members.any((item) => member.familyMemberUuid == item.uuid))
          .toList();

      return result;
    } catch (e) {
      return [];
    }
  }

  /// Get members available for chat (excluding current user)
  List<Account> getMembersAvailableForChat(ThreadFamily threadFamily) {
    try {
      final data = threadFamily;
      final members = data.members ?? [];
      final result = _accountService.memberInFamily.value
          .where((member) =>
              members.any((item) => member.familyMemberUuid == item.uuid) &&
              member.familyMemberUuid !=
                  _accountService.account?.uuid?.toString())
          .toList();

      return result;
    } catch (e) {
      return [];
    }
  }

  /// Update selected members in a thread
  Future<ThreadFamily> updateSelectedMembers(
      List<String> selectedMemberUuids, String threadId) async {
    try {
      final result = await _threadRepository.updateMemberThread(
          selectedMemberUuids, threadId);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// Create a new thread with a specific member
  Future<ThreadFamily?> createThreadWithMember(Account account) async {
    try {
      List<String> members = ["${account.familyMemberUuid}"];
      final result = await _threadRepository.createThread(
        ThreadParameter(
          familyId: _accountService.familyId,
          name: account.fullName,
          members: members,
          color: "",
          image: "",
          setting: [],
        ),
      );

      if (result.uuid != null) {
        return result;
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// Get navigation route for a thread
  ThreadDetailRoute getThreadDetailRoute(ThreadFamily threadFamily) {
    return ThreadDetailRoute(parameter: threadFamily);
  }
}
