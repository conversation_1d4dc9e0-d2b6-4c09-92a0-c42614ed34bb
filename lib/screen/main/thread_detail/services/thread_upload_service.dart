import 'dart:io';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/utils/upload.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';

/// Represents a file that has been picked but not yet uploaded
class PickedFile {
  final String id;
  final String fileName;
  final String filePath;
  final bool isImage;
  final bool isVideo;
  final File file;

  const PickedFile({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.isImage,
    required this.isVideo,
    required this.file,
  });
}

/// Service class responsible for handling all upload-related operations
/// in the thread detail screen.
class ThreadUploadService {
  final String _familyId;

  ThreadUploadService({
    required AccountService accountService,
  }) : _familyId = accountService.familyId;

  /// Upload a single image file
  Future<StorageModel> uploadImage(File imageFile) async {
    try {
      final upload = Upload(familyId: _familyId);
      final storageModel = await upload.uploadImage(imageFile, null);

      if (storageModel.uuid != null) {
        return storageModel;
      } else {
        return const StorageModel();
      }
    } catch (e) {
      return const StorageModel();
    }
  }

  /// Upload multiple images in parallel
  Future<List<StorageModel>> uploadImages(List<File> imageFiles) async {
    if (imageFiles.isEmpty) return [];

    try {
      // Upload all images in parallel using Future.wait
      final uploadFutures =
          imageFiles.map((imageFile) => uploadImage(imageFile));
      final storageModels = await Future.wait(uploadFutures);

      // Filter out failed uploads (those with null uuid)
      final successfulUploads =
          storageModels.where((model) => model.uuid != null).toList();

      return successfulUploads;
    } catch (e) {
      return [];
    }
  }

  /// Pick and upload a single image from gallery
  Future<StorageModel?> pickAndUploadSingleImage() async {
    try {
      final picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        final storageModel = await uploadImage(File(image.path));

        if (storageModel.uuid != null) {
          return storageModel;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// Pick and upload multiple images from gallery
  Future<List<StorageModel>> pickAndUploadMultipleImages() async {
    try {
      final picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage();

      if (images.isNotEmpty) {
        // Convert XFiles to Files
        final imageFiles = <File>[];
        for (final image in images) {
          imageFiles.add(File(image.path));
        }

        // Upload all images in parallel
        return await uploadImages(imageFiles);
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  /// Check and request camera permission
  Future<bool> _checkCameraPermission() async {
    try {
      final status = await Permission.camera.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        final result = await Permission.camera.request();
        return result.isGranted;
      }

      if (status.isPermanentlyDenied) {
        return false;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Pick and upload a single image from camera
  Future<StorageModel?> pickAndUploadFromCamera() async {
    try {
      // Check camera permission first
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) {
        return null;
      }

      final picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80, // Compress image to reduce upload size
      );

      if (image != null) {
        final storageModel = await uploadImage(File(image.path));

        if (storageModel.uuid != null) {
          return storageModel;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// Upload a single file
  Future<StorageModel> uploadFile(File file) async {
    try {
      final upload = Upload(familyId: _familyId);
      final storageModel = await upload.uploadFile(file, null);

      if (storageModel.uuid != null) {
        return storageModel;
      } else {
        return const StorageModel();
      }
    } catch (e) {
      return const StorageModel();
    }
  }

  /// Upload multiple files in parallel
  Future<List<StorageModel>> uploadFiles(List<File> files) async {
    if (files.isEmpty) return [];

    try {
      // Upload all files in parallel using Future.wait
      final uploadFutures = files.map((file) => uploadFile(file));
      final storageModels = await Future.wait(uploadFutures);

      // Filter out failed uploads (those with null uuid)
      final successfulUploads =
          storageModels.where((model) => model.uuid != null).toList();

      return successfulUploads;
    } catch (e) {
      return [];
    }
  }

  /// Pick images and videos and return file information immediately (without uploading)
  Future<List<PickedFile>> pickImages(
      {bool allowMultiple = false, bool useCamera = false}) async {
    try {
      if (useCamera) {
        final cameraImage = await _pickImageFromCamera();
        return cameraImage != null ? [cameraImage] : [];
      } else if (allowMultiple) {
        return await _pickMultipleMedia();
      } else {
        final singleMedia = await _pickSingleMedia();
        return singleMedia != null ? [singleMedia] : [];
      }
    } catch (e) {
      return [];
    }
  }

  /// Pick files and return file information immediately (without uploading)
  Future<List<PickedFile>> pickFiles({bool allowMultiple = false, List<String>? allowedExtensions}) async {
    try {
      if (allowMultiple) {
        return await _pickMultipleFiles(allowedExtensions: allowedExtensions);
      } else {
        final singleFile = await _pickSingleFile(allowedExtensions: allowedExtensions);
        return singleFile != null ? [singleFile] : [];
      }
    } catch (e) {
      return [];
    }
  }

  /// Upload a picked file and return StorageModel
  Future<StorageModel?> uploadPickedFile(PickedFile pickedFile) async {
    try {
      if (pickedFile.isImage) {
        return await uploadImage(pickedFile.file);
      } else {
        return await uploadFile(pickedFile.file);
      }
    } catch (e) {
      return null;
    }
  }

  /// Upload multiple picked files in parallel
  Future<List<StorageModel>> uploadPickedFiles(
      List<PickedFile> pickedFiles) async {
    if (pickedFiles.isEmpty) return [];

    try {
      final uploadFutures =
          pickedFiles.map((pickedFile) => uploadPickedFile(pickedFile));
      final results = await Future.wait(uploadFutures);

      final successfulUploads = results
          .where((result) => result != null)
          .cast<StorageModel>()
          .toList();

      return successfulUploads;
    } catch (e) {
      return [];
    }
  }

  // Private methods for picking files/images
  Future<PickedFile?> _pickSingleMedia() async {
    try {
      // Use file_picker for better support on iOS
      // This allows picking either images or videos
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'bmp',
          'webp',
          'mp4',
          'mov',
          'avi',
          'mkv',
          'webm'
        ],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final platformFile = result.files.first;
        if (platformFile.path != null) {
          final file = File(platformFile.path!);
          final isImage = isImageFile(platformFile.extension);
          final isVideo = isVideoFile(platformFile.extension);
          return PickedFile(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            fileName: platformFile.name,
            filePath: platformFile.path!,
            isImage: isImage,
            isVideo: isVideo,
            file: file,
          );
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  Future<List<PickedFile>> _pickMultipleMedia() async {
    try {
      // Use file_picker for better multi-selection support on iOS
      // This allows picking both images and videos in one operation
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'jpg',
          'jpeg',
          'png',
          'gif',
          'bmp',
          'webp',
          'mp4',
          'mov',
          'avi',
          'mkv',
          'webm'
        ],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        return result.files
            .where((platformFile) => platformFile.path != null)
            .map((platformFile) {
          final file = File(platformFile.path!);
          final isImage = isImageFile(platformFile.extension);
          final isVideo = isVideoFile(platformFile.extension);
          return PickedFile(
            id: '${DateTime.now().millisecondsSinceEpoch}_${platformFile.name}',
            fileName: platformFile.name,
            filePath: platformFile.path!,
            isImage: isImage,
            isVideo: isVideo,
            file: file,
          );
        }).toList();
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  Future<PickedFile?> _pickImageFromCamera() async {
    try {
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) {
        return null;
      }

      final picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );
      if (image != null) {
        return PickedFile(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          fileName: image.name,
          filePath: image.path,
          isImage: true,
          isVideo: false,
          file: File(image.path),
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<PickedFile?> _pickSingleFile({List<String>? allowedExtensions}) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: allowedExtensions != null ? FileType.custom : FileType.any,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );
      if (result != null && result.files.isNotEmpty) {
        final platformFile = result.files.first;
        if (platformFile.path != null) {
          final file = File(platformFile.path!);
          final isImage = isImageFile(platformFile.extension);
          final isVideo = isVideoFile(platformFile.extension);
          return PickedFile(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            fileName: platformFile.name,
            filePath: platformFile.path!,
            isImage: isImage,
            isVideo: isVideo,
            file: file,
          );
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<List<PickedFile>> _pickMultipleFiles({List<String>? allowedExtensions}) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: allowedExtensions != null ? FileType.custom : FileType.any,
        allowedExtensions: allowedExtensions,
        allowMultiple: true,
      );
      if (result != null && result.files.isNotEmpty) {
        return result.files
            .where((platformFile) => platformFile.path != null)
            .map((platformFile) {
          final file = File(platformFile.path!);
          final isImage = isImageFile(platformFile.extension);
          final isVideo = isVideoFile(platformFile.extension);
          return PickedFile(
            id: '${DateTime.now().millisecondsSinceEpoch}_${platformFile.name}',
            fileName: platformFile.name,
            filePath: platformFile.path!,
            isImage: isImage,
            isVideo: isVideo,
            file: file,
          );
        }).toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  bool isImageFile(String? extension) {
    final ext = extension?.toLowerCase();
    return ext == 'jpg' ||
        ext == 'jpeg' ||
        ext == 'png' ||
        ext == 'gif' ||
        ext == 'bmp' ||
        ext == 'webp';
  }

  bool isVideoFile(String? extension) {
    final ext = extension?.toLowerCase();
    return ext == 'mp4' ||
        ext == 'mov' ||
        ext == 'avi' ||
        ext == 'mkv' ||
        ext == 'webm';
  }
}
