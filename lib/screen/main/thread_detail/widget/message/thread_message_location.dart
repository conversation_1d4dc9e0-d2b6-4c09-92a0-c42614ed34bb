import 'dart:convert';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'dart:math';
import 'package:family_app/gen/assets.gen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform;

const double locationCardHorizontalPadding = 72;
const double locationCardMaxWidth = 400;

class ThreadMessageLocation extends StatelessWidget {
  final ThreadMessage message;
  final CrossAxisAlignment crossAxisAlignment;
  final String? currentUserId;

  const ThreadMessageLocation({
    super.key,
    required this.message,
    required this.crossAxisAlignment,
    this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = message.userId == currentUserId;
    // Get user name from userLog
    String userName = 'User';
    if (message.userLog != null) {
      try {
        final userInfo = jsonDecode(message.userLog!);
        userName = userInfo["full_name"]?.toString() ?? 'User';
      } catch (e) {
        AppLogger.e('ThreadMessageLocation: Error parsing userLog: $e');
      }
    }

    // Extract location data from message.relatedData (fields are direct properties)
    double? latitude;
    double? longitude;
    String? photoUrl;
    bool hasValidLocation = false;
    final data = message.relatedData;
    if (data != null) {
      latitude = (data.latitude as num?)?.toDouble();
      longitude = (data.longitude as num?)?.toDouble();
      photoUrl = data.photoUrl?.toString();
      // Debug prints
      AppLogger.d('ThreadMessageLocation: latitude = '
          '[33m$latitude[0m, longitude = [33m$longitude[0m');
      hasValidLocation = latitude != null &&
          longitude != null &&
          latitude >= -90 &&
          latitude <= 90 &&
          longitude >= -180 &&
          longitude <= 180 &&
          (latitude != 0.0 || longitude != 0.0);
    }

    final cardWidth = min(
      context.screenSize.width - (locationCardHorizontalPadding * 2),
      locationCardMaxWidth,
    );

    // Use photoUrl for preview
    Widget mapPreview;
    if (photoUrl != null && photoUrl.isNotEmpty) {
      mapPreview = Image.network(
        photoUrl,
        width: double.infinity,
        height: 120,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _mapPlaceholder(),
      );
    } else {
      mapPreview = _mapPlaceholder();
    }

    return _AlignedRow(
      isCurrentUser: isCurrentUser,
      child: Container(
        padding: const EdgeInsets.all(12),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          shadows: [
            const BoxShadow(
              color: Color(0x19000000),
              blurRadius: 12,
              offset: Offset(0, 4),
              spreadRadius: 0,
            ),
          ],
        ),
        child: SizedBox(
          width: cardWidth,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Text(
                '$userName shared a location',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w700,
                  height: 1.50,
                ),
              ),
              const SizedBox(height: 8),
              // Map preview with avatar/icon overlay or placeholder
              Stack(
                alignment: Alignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: mapPreview,
                  ),
                  // Show icon overlay ONLY if valid location and NO photoUrl
                  if (hasValidLocation &&
                      (photoUrl == null || photoUrl.isEmpty))
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(20),
                            blurRadius: 4,
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(8),
                      child: Assets.icons.icLocation.svg(
                        width: 32,
                        height: 32,
                        colorFilter: const ColorFilter.mode(
                          Color(0xFF4E46B4),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),
              // View button
              SizedBox(
                width: double.infinity,
                child: GestureDetector(
                  onTap: hasValidLocation
                      ? () async {
                          String url;
                          if (!kIsWeb && Platform.isIOS) {
                            url =
                                'http://maps.apple.com/?ll=$latitude,$longitude&q=Shared%20Location';
                          } else {
                            url =
                                'https://www.google.com/maps/place/$latitude,$longitude';
                          }
                          if (await canLaunchUrl(Uri.parse(url))) {
                            await launchUrl(Uri.parse(url),
                                mode: LaunchMode.externalApplication);
                          }
                        }
                      : () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Location data unavailable'),
                            ),
                          );
                        },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: ShapeDecoration(
                      color: hasValidLocation
                          ? const Color(0xFF4E46B4).withAlpha(31)
                          : const Color(0xFF4E46B4).withAlpha(10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      'View',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: hasValidLocation
                            ? const Color(0xFF4E46B4)
                            : Colors.grey,
                        fontSize: 14,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                        height: 1.71,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _mapPlaceholder() {
    return Container(
      width: double.infinity,
      height: 120,
      color: const Color(0xFFE0E0E0),
      child: const Center(
        child: Icon(Icons.map, color: Colors.white, size: 48),
      ),
    );
  }
}

class _AlignedRow extends StatelessWidget {
  final bool isCurrentUser;
  final Widget child;

  const _AlignedRow({
    required this.isCurrentUser,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.center,
      children: [Flexible(child: child)],
    );
  }
}
