import 'dart:convert';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/screen/main/thread_detail/widget/message_poll/poll_card_voter_list.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/service/thread_event_service.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:auto_route/auto_route.dart';
import 'dart:math';
import 'package:family_app/main.dart';

const double eventCardHorizontalPadding =
    72; // 72*2 = 144 total, matches previous logic
const double eventCardMaxWidth = 400; // Optional: cap for large screens

class ThreadMessageEvent extends StatefulWidget {
  final ThreadMessage message;
  final CrossAxisAlignment crossAxisAlignment;
  final String? currentUserId;

  const ThreadMessageEvent({
    super.key,
    required this.message,
    required this.crossAxisAlignment,
    this.currentUserId,
  });

  @override
  State<ThreadMessageEvent> createState() => _ThreadMessageEventState();
}

class _ThreadMessageEventState extends State<ThreadMessageEvent> {
  EventModels? event;
  bool isLoading = true;
  bool isNotFound = false;

  @override
  void initState() {
    super.initState();
    // Use event data from relatedData if available
    final related = widget.message.relatedData;
    if (related != null && related.uuid != null && related.name != null) {
      event = EventModels(
        uuid: related.uuid,
        name: related.name,
        description: related.description,
        fromDate: related.fromDate,
        toDate: related.toDate,
        color: related.extraData != null && related.extraData['color'] != null
            ? related.extraData['color']
            : null,
        // Add more fields as needed from relatedData
      );
      isLoading = false;
      isNotFound = false;
    } else {
      _fetchEvent();
    }
  }

  Future<void> _fetchEvent() async {
    setState(() {
      isLoading = true;
      isNotFound = false;
    });

    try {
      final uuid = widget.message.messageTypeId ?? '';
      if (uuid.isNotEmpty) {
        AppLogger.d('ThreadMessageEvent: Fetching event for UUID: $uuid');
        final result = await threadEventService.getEventById(uuid);
        if (result != null) {
          setState(() {
            event = result;
            isLoading = false;
          });
          AppLogger.d('ThreadMessageEvent: Event loaded successfully');
          return;
        }
      }
      setState(() {
        isNotFound = true;
        isLoading = false;
      });
      AppLogger.d('ThreadMessageEvent: Event not found');
    } catch (e) {
      AppLogger.e('ThreadMessageEvent: Error fetching event: $e');
      setState(() {
        isNotFound = true;
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final message = widget.message;
    final isCurrentUser = message.userId == widget.currentUserId;

    // Get user name from userLog
    String userName = 'User';
    if (message.userLog != null) {
      try {
        final userInfo = jsonDecode(message.userLog!);
        userName = userInfo["full_name"]?.toString() ?? 'User';
      } catch (e) {
        AppLogger.e('ThreadMessageEvent: Error parsing userLog: $e');
      }
    }

    return _AlignedRow(
      isCurrentUser: isCurrentUser,
      child: Container(
        padding: const EdgeInsets.all(12),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          shadows: [
            const BoxShadow(
              color: Color(0x19000000),
              blurRadius: 12,
              offset: Offset(0, 4),
              spreadRadius: 0,
            ),
          ],
        ),
        child: isLoading
            ? _buildSkeleton()
            : isNotFound
                ? _buildNotFound(userName)
                : _buildEventCard(context, userName, event!),
      ),
    );
  }

  Widget _buildSkeleton() {
    final cardWidth = min(
      context.screenSize.width - (eventCardHorizontalPadding * 2),
      eventCardMaxWidth,
    );
    return SizedBox(
      width: cardWidth,
      height: 180,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildNotFound(String userName) {
    final cardWidth = min(
      context.screenSize.width - (eventCardHorizontalPadding * 2),
      eventCardMaxWidth,
    );
    return SizedBox(
      width: cardWidth,
      height: 180,
      child: const Center(
        child: Text('Event not found', style: TextStyle(color: Colors.black54)),
      ),
    );
  }

  Widget _buildEventCard(
      BuildContext context, String userName, EventModels event) {
    final cardWidth = min(
      context.screenSize.width - (eventCardHorizontalPadding * 2),
      eventCardMaxWidth,
    );
    return GestureDetector(
      onTap: () {
        context.pushRoute(DetailEventRoute(
            parameter: DetailEventParameter(uuid: event.uuid ?? '')));
      },
      child: SizedBox(
        width: cardWidth,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Text(
              '$userName shared an event',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w700,
                height: 1.50,
              ),
            ),
            const SizedBox(height: 8),

            // Event content container
            Container(
              decoration: ShapeDecoration(
                color: const Color(0xFF158FAE),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Top section with activity tag and members
                  Padding(
                    padding:
                        const EdgeInsets.only(top: 12, left: 16, right: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(9999),
                              ),
                            ),
                            child: Text(
                              event.activity?.name ?? 'Event',
                              style: const TextStyle(
                                color: Color(0xFF158FAE),
                                fontSize: 12,
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w700,
                                height: 1.33,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        if (event.members != null && event.members!.isNotEmpty)
                          SizedBox(
                            width: 74,
                            height: 24,
                            child: PollCardVoterList(
                              voters: event.members!,
                              voterSize: 24,
                              optionVerticalPadding: 0,
                              totalVoterDisplayConfig: 3,
                              voterOverlapWidth: 12,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Main content section
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: IntrinsicHeight(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Event details
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  event.name ?? 'Event',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w700,
                                    height: 1.50,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (event.description != null &&
                                    event.description!.isNotEmpty)
                                  Text(
                                    event.description!,
                                    style: const TextStyle(
                                      color: Color(0xFFF5F5F5),
                                      fontSize: 12,
                                      fontFamily: 'Poppins',
                                      fontWeight: FontWeight.w400,
                                      height: 2,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                Text(
                                  _formatEventTime(
                                      event.fromDate, event.toDate),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontFamily: 'Poppins',
                                    fontWeight: FontWeight.w400,
                                    height: 2,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 12),

                          // Date display
                          Container(
                            width: 60,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: ShapeDecoration(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  event.fromDate != null
                                      ? _getDayFromDate(event.fromDate!)
                                      : '--',
                                  style: const TextStyle(
                                    color: Color(0xFF158FAE),
                                    fontSize: 20,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    height: 1.20,
                                  ),
                                ),
                                Text(
                                  event.fromDate != null
                                      ? _getMonthFromDate(event.fromDate!)
                                      : '---',
                                  style: const TextStyle(
                                    color: Color(0xFF158FAE),
                                    fontSize: 13,
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w600,
                                    height: 1.85,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),

            // View button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: ShapeDecoration(
                color: appTheme.primaryColorV2.withAlpha(31),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text(
                'View',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0xFF4E46B4),
                  fontSize: 14,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w400,
                  height: 1.71,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatEventTime(String? fromDate, String? toDate) {
    if (fromDate == null && toDate == null) {
      return 'No time specified';
    }

    try {
      if (fromDate != null && toDate != null) {
        final from = fromDate.toLocalDT;
        final to = toDate.toLocalDT;
        return '${from.HH_mm} - ${to.HH_mm}';
      } else if (fromDate != null) {
        final from = fromDate.toLocalDT;
        return from.HH_mm;
      } else if (toDate != null) {
        final to = toDate.toLocalDT;
        return 'Until ${to.HH_mm}';
      }
    } catch (e) {
      AppLogger.e('ThreadMessageEvent: Error formatting time: $e');
    }

    return 'Time not specified';
  }

  String _getDayFromDate(String dateString) {
    try {
      final date = dateString.toLocalDT;
      return date.day.toString().padLeft(2, '0');
    } catch (e) {
      AppLogger.e('ThreadMessageEvent: Error parsing day: $e');
      return '--';
    }
  }

  String _getMonthFromDate(String dateString) {
    try {
      final date = dateString.toLocalDT;
      return date.MMM.toUpperCase();
    } catch (e) {
      AppLogger.e('ThreadMessageEvent: Error parsing month: $e');
      return '---';
    }
  }
}

/// Aligns a child widget to the start or end of a row based on user.
class _AlignedRow extends StatelessWidget {
  final bool isCurrentUser;
  final Widget child;

  const _AlignedRow({
    required this.isCurrentUser,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.center,
      children: [Flexible(child: child)],
    );
  }
}
