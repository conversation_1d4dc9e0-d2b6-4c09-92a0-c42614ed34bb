import 'dart:convert';
import 'dart:math';
import 'package:family_app/config/service/app_service.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/router/app_route.dart';
import 'package:auto_route/auto_route.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/screen/main/thread_detail/widget/message_poll/poll_card_voter_list.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/main.dart';
import 'package:family_app/data/model/account.dart';
import 'package:intl/intl.dart';

const double eventCardHorizontalPadding = 72;
const double eventCardMaxWidth = 400;

class ThreadMessageTaskCard extends StatefulWidget {
  final ThreadMessage message;
  final CrossAxisAlignment crossAxisAlignment;
  final String? currentUserId;

  const ThreadMessageTaskCard({
    super.key,
    required this.message,
    required this.crossAxisAlignment,
    this.currentUserId,
  });

  @override
  State<ThreadMessageTaskCard> createState() => _ThreadMessageTaskCardState();
}

class _ThreadMessageTaskCardState extends State<ThreadMessageTaskCard> {
  List<MapEntry<ListItem, Item>> tasksWithList = [];
  bool isLoading = true;
  bool isNotFound = false;

  final IListRepository listRepository = locator.get();
  final AccountService accountService = locator.get();

  @override
  void initState() {
    super.initState();
    _fetchTasksWithParentList();
  }

  Future<void> _fetchTasksWithParentList() async {
    setState(() {
      isLoading = true;
      isNotFound = false;
    });
    try {
      final ids = (widget.message.messageTypeId ?? '')
          .split(',')
          .where((e) => e.isNotEmpty)
          .toList();
      if (ids.isNotEmpty) {
        final lists =
            await listRepository.getListByFamilyId(accountService.familyId);
        final result = <MapEntry<ListItem, Item>>[];
        for (final list in lists) {
          final items =
              list.items ?? await listRepository.getAllItemInList(list.uuid!);
          for (final item in items) {
            if (ids.contains(item.uuid)) {
              result.add(MapEntry(list, item));
            }
          }
        }
        if (result.isNotEmpty) {
          setState(() {
            tasksWithList = result;
            isLoading = false;
          });
          return;
        }
      }
      setState(() {
        isNotFound = true;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isNotFound = true;
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final message = widget.message;
    String userName = 'User';
    if (message.userLog != null) {
      try {
        final userInfo = jsonDecode(message.userLog!);
        userName = userInfo["full_name"]?.toString() ?? 'User';
      } catch (_) {}
    }
    final cardWidth = min(
      context.screenSize.width - (eventCardHorizontalPadding * 2),
      eventCardMaxWidth,
    );
    return _AlignedRow(
      isCurrentUser: message.userId == widget.currentUserId,
      child: isLoading
          ? _buildSkeleton(cardWidth)
          : isNotFound
              ? _buildNotFound(cardWidth)
              : _buildTaskCards(context, userName, tasksWithList, cardWidth),
    );
  }

  Widget _buildSkeleton(double cardWidth) {
    return SizedBox(
      width: cardWidth,
      height: 200,
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildNotFound(double cardWidth) {
    return SizedBox(
      width: cardWidth,
      height: 200,
      child: const Center(
        child: Text('Task not found', style: TextStyle(color: Colors.black54)),
      ),
    );
  }

  Widget _buildTaskCards(BuildContext context, String userName,
      List<MapEntry<ListItem, Item>> tasksWithList, double cardWidth) {
    // If all tasks are from the same list, group them
    if (tasksWithList.isNotEmpty) {
      final listUuid = tasksWithList.first.key.uuid;
      final sameList = tasksWithList.every((e) => e.key.uuid == listUuid);
      if (sameList) {
        final list = tasksWithList.first.key;
        final items = tasksWithList.map((e) => e.value).toList();
        return _buildTaskCard(context, userName, list, items, cardWidth);
      }
    }
    // Otherwise, show each task with its parent list
    return Column(
      children: tasksWithList
          .map((entry) => _buildTaskCard(
              context, userName, entry.key, [entry.value], cardWidth))
          .toList(),
    );
  }

  // Helper to patch voters with full Account data from list.includedMembers
  List<Account> _patchVoters(
      List<Account>? rawVoters, List<Account>? fullListMembers) {
    if (rawVoters == null || rawVoters.isEmpty) return [];
    if (fullListMembers == null || fullListMembers.isEmpty) return rawVoters;
    return rawVoters.map((voter) {
      if ((voter.fullName == null || voter.fullName!.isEmpty) &&
          (voter.photoUrl == null || voter.photoUrl!.isEmpty)) {
        final match = fullListMembers.firstWhere(
          (m) => m.uuid != null && voter.uuid != null && m.uuid == voter.uuid,
          orElse: () => voter,
        );
        return match;
      }
      return voter;
    }).toList();
  }

  Widget _buildTaskCard(BuildContext context, String userName, ListItem list,
      List<Item> items, double cardWidth) {
    final mainItem = items.first;
    // Patch voters to ensure full Account data
    final rawVoters = (mainItem.includedMembers ?? []);
    final patchedVoters = _patchVoters(rawVoters, list.includedMembers);
    // Due date logic
    String dueText = '';
    if (mainItem.due_date != null && mainItem.due_date!.isNotEmpty) {
      try {
        final dt = DateTime.parse(mainItem.due_date!);
        dueText =
            'Due by ${DateFormat('dd MMM HH:mm').format(dt).toUpperCase()}';
      } catch (_) {}
    }
    final color = list.color?.toColor ?? appTheme.primaryColorV2;
    final headerText = (list.categoryId ?? 'Task');
    final capitalizedHeader = headerText.isNotEmpty
        ? headerText[0].toUpperCase() + headerText.substring(1).toLowerCase()
        : headerText;
    String time = '';
    if (mainItem.due_date != null && mainItem.due_date!.length >= 16) {
      time = mainItem.due_date!.substring(11, 16);
    }
    return SizedBox(
      width: cardWidth,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(18),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header: User shared a task
            Text(
              '$userName shared a task',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
            ),
            if (dueText.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                dueText,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Color(0xFF6C5CE7),
                  fontSize: 12,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w400,
                  height: 1.33,
                ),
              ),
            ],
            const SizedBox(height: 8),
            // Colored card with task info
            Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(18),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        capitalizedHeader,
                        style: AppStyle.bold12V2(color: color),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          mainItem.name ?? list.name ?? '',
                          style: AppStyle.bold16V2(color: Colors.white),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Show only selected tasks from messageTypeId
                  for (final item in items) ...[
                    Row(
                      children: [
                        Icon(
                          item.isDone
                              ? Icons.check_circle
                              : Icons.radio_button_unchecked,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            item.name ?? '',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(77),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.access_time,
                                color: Colors.white, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              time,
                              style: AppStyle.regular12(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      SizedBox(
                        width: 74,
                        height: 24,
                        child: PollCardVoterList(
                          voters: patchedVoters,
                          voterSize: 24,
                          optionVerticalPadding: 0,
                          totalVoterDisplayConfig: 3,
                          voterOverlapWidth: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Progress bar
                  Stack(
                    children: [
                      Container(
                        height: 6,
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(77),
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                      FractionallySizedBox(
                        widthFactor: items.isNotEmpty
                            ? items.where((item) => item.isDone).length /
                                items.length
                            : 0.0,
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      items.isNotEmpty
                          ? '${((items.where((item) => item.isDone).length / items.length) * 100).round()}%'
                          : '0%',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () {
                context.pushRoute(
                  ListDetailRoute(
                      parameter: ListDetailParameter(uuid: list.uuid ?? '')),
                );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: ShapeDecoration(
                  color: appTheme.primaryColorV2.withAlpha(31),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text(
                  'View',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF4E46B4),
                    fontSize: 14,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 1.71,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AlignedRow extends StatelessWidget {
  final bool isCurrentUser;
  final Widget child;

  const _AlignedRow({
    required this.isCurrentUser,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.center,
      children: [Flexible(child: child)],
    );
  }
}
