import 'dart:convert';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/screen/main/thread/widget/thread_avatar.dart';
import 'thread_message_image.dart';
import 'package:family_app/screen/main/thread_detail/widget/poll/thread_message_poll.dart';
import 'thread_message_file.dart';
import 'thread_message_video.dart';
import 'thread_message_event.dart';
import 'package:family_app/screen/main/thread_detail/widget/popup/message_options_widget.dart';
import 'package:family_app/screen/main/thread_detail/widget/message/thread_message_list_card.dart';
import 'package:family_app/screen/main/thread_detail/widget/message/thread_message_task_card.dart';
import 'thread_message_location.dart';

/// Displays a single message row in a threaded conversation with comprehensive
/// formatting rules for grouping, timestamps, avatars, and message types.
///
/// # Overview
/// This widget handles the display of individual messages in a chat thread,
/// designed to work with `reverse: true` ChatList (newest messages at bottom).
///
/// # Message Types Supported
/// - **Text**: Regular text messages with bubble styling
/// - **Image**: Photo attachments with viewer support
/// - **Video**: Video files with player controls
/// - **File**: Document attachments with download
/// - **Audio**: Voice messages with playback
/// - **Poll**: Interactive voting polls
/// - **Event**: Calendar event information
/// - **Task**: Task assignments and checklists
/// - **List**: Structured list content
///
/// # Core Display Rules
///
/// ## 1. Message Grouping
/// Messages from the same user are grouped together when:
/// - Same user sends consecutive messages
/// - Messages are within 5 minutes of each other
/// - Messages are on the same date
///
/// **Group Behavior:**
/// - First message in group: Shows avatar, name, and timestamp
/// - Middle messages: No avatar, no name, no timestamp
/// - Last message in group: No avatar, no name, no timestamp
///
/// ## 2. Date Label Rules
/// Timestamps are shown above messages in these cases:
/// - **Last message in thread** (no next message)
/// - **First message in a group** (different user than next message)
/// - **Date change** (different date than next message)
/// - **Time gap** (5+ minutes difference from next message)
///
/// **Timestamp Format:**
/// - **Today**: "14:30" or "2:30 PM"
/// - **Yesterday**: "Yesterday, 14:30"
/// - **This week**: "Monday, 14:30"
/// - **This year**: "Dec 15, 14:30"
/// - **Older**: "Dec 15, 2024, 14:30"
///
/// ## 3. Avatar Display Rules
/// User avatars are shown when:
/// - Message is NOT from current user
/// - AND one of these conditions:
///   - First message in thread (no next message)
///   - Different user than next message
///   - Different date than next message
///
/// ## 4. Message Bubble Styling
/// Bubbles have different border radius based on grouping:
/// - **Single message**: Fully rounded (16px radius)
/// - **First in group**: Rounded top, pointed bottom
/// - **Middle in group**: Pointed top and bottom
/// - **Last in group**: Pointed top, rounded bottom
///
/// ## 5. Message Alignment
/// - **Current user messages**: Right-aligned with primary color
/// - **Other user messages**: Left-aligned with white background
/// - **Media content**: Follows same alignment rules
///
/// ## 6. Interactive Features
/// - **Long press**: Shows message options (delete, copy, reply)
/// - **Image tap**: Opens full-screen image viewer
/// - **Video tap**: Starts video playback
/// - **File tap**: Initiates download
/// - **Poll tap**: Opens voting interface
///
/// ## 7. Message Options
/// Available on long press:
/// - **Delete**: Remove message (if user has permission)
/// - **Copy**: Copy message text to clipboard
/// - **Reply**: Quote message in reply (future feature)
///
/// # Technical Notes
/// - Uses `AutomaticKeepAliveClientMixin` for scroll performance
/// - Implements `RepaintBoundary` for rendering optimization
/// - Supports 12/24 hour time format via `use24HourFormat` parameter
/// - Handles reverse chronological order (newest at bottom)
/// - Optimized for performance with large message lists
class ThreadMessageRow extends StatefulWidget {
  /// The message to display.
  final ThreadMessage message;

  /// The previous message in the list, or null if this is the first.
  final ThreadMessage? previousMessage;

  /// The next message in the list, or null if this is the last.
  final ThreadMessage? nextMessage;

  /// True if this is the last message in the thread.
  final bool isLastMessage;

  /// List of all thread members.
  final List<Account> memberList;

  /// The current user's ID.
  final String? currentUserId;

  /// Callback for showing voting UI.
  final void Function(ThreadMessage message)? onShowVoting;

  /// Callback when image viewer opens
  final VoidCallback? onImageViewerOpen;

  /// Callback when image viewer closes
  final VoidCallback? onImageViewerClose;

  /// Callback for deleting a message
  final void Function(String messageId)? onDeleteMessage;

  /// Callback for copying message text
  final VoidCallback? onCopyMessage;

  /// Callback for replying to a message
  final VoidCallback? onReplyMessage;

  /// If true, use 24-hour time format for date labels. Default is true.
  final bool use24HourFormat;

  /// Creates a message row with comprehensive formatting rules.
  /// See class-level documentation for detailed behavior specifications.
  const ThreadMessageRow({
    super.key,
    required this.message,
    this.previousMessage,
    this.nextMessage,
    required this.isLastMessage,
    required this.memberList,
    required this.currentUserId,
    this.onShowVoting,
    this.onImageViewerOpen,
    this.onImageViewerClose,
    this.onDeleteMessage,
    this.onCopyMessage,
    this.onReplyMessage,
    this.use24HourFormat = true,
  });

  @override
  State<ThreadMessageRow> createState() => _ThreadMessageRowState();
}

class _ThreadMessageRowState extends State<ThreadMessageRow>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive =>
      true; // Keep this widget alive when scrolled out of view

  /// Returns true if a date label should be shown above this message.
  /// Date label is shown if:
  /// 1. Next message is null (last message)
  /// 2. Next message is from a different user (first message in group)
  /// 3. Next message is from different date
  /// 4. 5+ minutes have passed between messages (even for same user)
  /// Note: With reverse: true, nextMessage is chronologically newer, previousMessage is chronologically older.
  bool _shouldShowTimestamp(ThreadMessage current) {
    final chronologicallyNewerMessage = widget.nextMessage;
    if (chronologicallyNewerMessage == null) {
      return true;
    }

    final newerCreatedAt =
        chronologicallyNewerMessage.createdAt.toThreadDateTime ??
            DateTime.now();
    final currentCreatedAt =
        current.createdAt.toThreadDateTime ?? DateTime.now();

    final isDifferentUser =
        chronologicallyNewerMessage.userId != current.userId;
    final isDifferentDate = DateFormat('yyyy-MM-dd').format(newerCreatedAt) !=
        DateFormat('yyyy-MM-dd').format(currentCreatedAt);
    final timeDifference =
        (currentCreatedAt.difference(newerCreatedAt).inMinutes).abs();
    final isMoreThan5Minutes = timeDifference >= 5;

    return isDifferentUser || isDifferentDate || isMoreThan5Minutes;
  }

  /// Returns a formatted date label for the message, including time.
  /// See class-level docs for formatting rules.
  String _getDateLabel(DateTime createdAt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDay = DateTime(createdAt.year, createdAt.month, createdAt.day);

    final diff = today.difference(messageDay).inDays;
    final timeStr = widget.use24HourFormat
        ? DateFormat('HH:mm').format(createdAt)
        : DateFormat('h:mm a').format(createdAt);

    if (diff == 0) {
      return timeStr;
    } else if (diff == 1) {
      return "Yesterday, $timeStr";
    } else if (diff < 7 &&
        messageDay.isAfter(today.subtract(Duration(days: now.weekday - 1)))) {
      return "${DateFormat('EEEE').format(createdAt)}, $timeStr";
    } else if (createdAt.year == now.year) {
      return "${DateFormat('MMM d').format(createdAt)}, $timeStr";
    } else {
      return "${DateFormat('MMM d, yyyy').format(createdAt)}, $timeStr";
    }
  }

  /// Parses the user log JSON into a map.
  Map<String, dynamic> _parseUserLog(dynamic userLog) {
    try {
      return jsonDecode(userLog.toString());
    } catch (_) {
      return {};
    }
  }

  /// Returns true if the avatar should be shown for this message.
  /// Avatar is shown if next message is from a different user or day, and not current user.
  /// Note: With reverse: true, nextMessage is chronologically newer, previousMessage is chronologically older.
  bool _shouldShowAvatar(
    bool isCurrentUser,
    String userId, {
    required DateTime messageCreatedAt,
  }) {
    if (isCurrentUser) return false;

    // With reverse: true, we need to check against the chronologically newer message
    // which is nextMessage (not previousMessage)
    final chronologicallyNewerMessage = widget.nextMessage;
    if (chronologicallyNewerMessage == null) return true;

    final newerCreatedAt =
        chronologicallyNewerMessage.createdAt.toThreadDateTime ??
            DateTime.now();
    final isDifferentUser = chronologicallyNewerMessage.userId != userId;
    final isDifferentDate = DateFormat('yyyy-MM-dd').format(messageCreatedAt) !=
        DateFormat('yyyy-MM-dd').format(newerCreatedAt);
    return isDifferentUser || isDifferentDate;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final userInfo = _parseUserLog(widget.message.userLog);
    final isCurrentUser = widget.message.userId == widget.currentUserId;
    final messageCreatedAt =
        widget.message.createdAt.toThreadDateTime ?? DateTime.now();

    final shouldShowAvatar = _shouldShowAvatar(
      isCurrentUser,
      widget.message.userId ?? "",
      messageCreatedAt: messageCreatedAt,
    );

    final shouldShowDateLabel = _shouldShowTimestamp(widget.message);
    final dateLabel = _getDateLabel(messageCreatedAt);

    // Determine message type and content
    final messageType = widget.message.messageType;
    final isTextType = messageType == ThreadMessageType.TEXT;
    final isPollType = messageType == ThreadMessageType.POLL;
    final isImageType = messageType == ThreadMessageType.IMAGE;
    final isFileType = messageType == ThreadMessageType.FILE;
    final isVideoType = messageType == ThreadMessageType.VIDEO;
    final isEventType = messageType == ThreadMessageType.EVENT;
    final isTaskType = messageType == ThreadMessageType.TASK;
    final isListType = messageType == ThreadMessageType.LIST;
    final isLocationType = messageType == ThreadMessageType.LOCATION;

    final shouldShowText = isTextType &&
        widget.message.message != null &&
        widget.message.message!.isNotEmpty;
    final shouldShowVote = isPollType && widget.message.relatedData != null;
    final shouldShowImage = isImageType &&
        widget.message.messageTypeId != null &&
        widget.message.messageTypeId!.isNotEmpty;
    final shouldShowFile = isFileType &&
        widget.message.messageTypeId != null &&
        widget.message.messageTypeId!.isNotEmpty;
    final shouldShowVideo = isVideoType &&
        widget.message.messageTypeId != null &&
        widget.message.messageTypeId!.isNotEmpty;
    final shouldShowEvent = isEventType &&
        widget.message.messageTypeId != null &&
        widget.message.messageTypeId!.isNotEmpty;
    final shouldShowLocation = isLocationType;

    return RepaintBoundary(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (shouldShowDateLabel && dateLabel.isNotEmpty)
            _DateLabel(dateLabel: dateLabel),
          if (shouldShowText)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _AvatarColumn(
                  shouldShowAvatar: shouldShowAvatar,
                  userInfo: userInfo,
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: _MessageContentColumn(
                    shouldShowAvatar: shouldShowAvatar,
                    userInfo: userInfo,
                    shouldShowText: shouldShowText,
                    isCurrentUser: isCurrentUser,
                    message: widget.message,
                    previousMessage: widget.previousMessage,
                    nextMessage: widget.nextMessage,
                    onDeleteMessage: widget.onDeleteMessage,
                    onCopyMessage: widget.onCopyMessage,
                    onReplyMessage: widget.onReplyMessage,
                    currentUserId: widget.currentUserId,
                  ),
                ),
              ],
            ),
          if (shouldShowVote)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: _AlignedRow(
                isCurrentUser: isCurrentUser,
                child: ThreadMessagePoll(
                  message: widget.message,
                  memberList: widget.memberList,
                  onShowVoting: widget.onShowVoting != null
                      ? () => widget.onShowVoting!(widget.message)
                      : null,
                  currentUserId: widget.currentUserId,
                ),
              ),
            ),
          if (shouldShowImage)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: GestureDetector(
                onLongPress: () {
                  MessageOptionsWidget.show(
                    context,
                    message: widget.message,
                    currentUserId: widget.currentUserId,
                    onDelete: widget.onDeleteMessage,
                    onCopy: widget.onCopyMessage,
                  );
                },
                child: ThreadMessageImage(
                  message: widget.message,
                  onShowVoting: widget.onShowVoting != null
                      ? () => widget.onShowVoting!(widget.message)
                      : null,
                  crossAxisAlignment: isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
                  currentUserId: widget.currentUserId,
                  onImageViewerOpen: widget.onImageViewerOpen,
                  onImageViewerClose: widget.onImageViewerClose,
                ),
              ),
            ),
          if (shouldShowFile)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: GestureDetector(
                onLongPress: () {
                  MessageOptionsWidget.show(
                    context,
                    message: widget.message,
                    currentUserId: widget.currentUserId,
                    onDelete: widget.onDeleteMessage,
                    onCopy: widget.onCopyMessage,
                  );
                },
                child: ThreadMessageFile(
                  message: widget.message,
                  crossAxisAlignment: isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.center,
                  currentUserId: widget.currentUserId,
                ),
              ),
            ),
          if (shouldShowLocation)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: ThreadMessageLocation(
                message: widget.message,
                crossAxisAlignment: isCurrentUser
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.center,
                currentUserId: widget.currentUserId,
              ),
            ),
          if (shouldShowVideo)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: GestureDetector(
                onLongPress: () {
                  MessageOptionsWidget.show(
                    context,
                    message: widget.message,
                    currentUserId: widget.currentUserId,
                    onDelete: widget.onDeleteMessage,
                    onCopy: widget.onCopyMessage,
                  );
                },
                child: ThreadMessageVideo(
                  message: widget.message,
                  crossAxisAlignment: isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.center,
                  currentUserId: widget.currentUserId,
                  onImageViewerOpen: widget.onImageViewerOpen,
                  onImageViewerClose: widget.onImageViewerClose,
                ),
              ),
            ),
          if (shouldShowEvent)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: GestureDetector(
                onLongPress: () {
                  MessageOptionsWidget.show(
                    context,
                    message: widget.message,
                    currentUserId: widget.currentUserId,
                    onDelete: widget.onDeleteMessage,
                    onCopy: widget.onCopyMessage,
                  );
                },
                child: ThreadMessageEvent(
                  message: widget.message,
                  crossAxisAlignment: isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.center,
                  currentUserId: widget.currentUserId,
                ),
              ),
            ),
          if (isTaskType)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: GestureDetector(
                onLongPress: () {
                  MessageOptionsWidget.show(
                    context,
                    message: widget.message,
                    currentUserId: widget.currentUserId,
                    onDelete: widget.onDeleteMessage,
                    onCopy: widget.onCopyMessage,
                  );
                },
                child: ThreadMessageTaskCard(
                  message: widget.message,
                  crossAxisAlignment: isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.center,
                  currentUserId: widget.currentUserId,
                ),
              ),
            ),
          if (isListType)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: GestureDetector(
                onLongPress: () {
                  MessageOptionsWidget.show(
                    context,
                    message: widget.message,
                    currentUserId: widget.currentUserId,
                    onDelete: widget.onDeleteMessage,
                    onCopy: widget.onCopyMessage,
                  );
                },
                child: ThreadMessageListCard(
                  message: widget.message,
                  crossAxisAlignment: isCurrentUser
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.center,
                  currentUserId: widget.currentUserId,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Displays the avatar for a message group if required.
class _AvatarColumn extends StatelessWidget {
  final bool shouldShowAvatar;
  final Map<String, dynamic> userInfo;

  const _AvatarColumn({
    required this.shouldShowAvatar,
    required this.userInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48.0,
      alignment: Alignment.topCenter,
      child: shouldShowAvatar
          ? SingleAvatar(
              photoUrl: userInfo["photo_url"]?.toString() ?? "",
              fullname: userInfo["full_name"]?.toString() ?? "",
            )
          : const SizedBox.shrink(),
    );
  }
}

/// Displays the message content, including sender name and message bubble.
class _MessageContentColumn extends StatelessWidget {
  final bool shouldShowAvatar;
  final Map<String, dynamic> userInfo;
  final bool shouldShowText;
  final bool isCurrentUser;
  final ThreadMessage message;
  final ThreadMessage? previousMessage;
  final ThreadMessage? nextMessage;
  final void Function(String messageId)? onDeleteMessage;
  final VoidCallback? onCopyMessage;
  final VoidCallback? onReplyMessage;
  final String? currentUserId;

  const _MessageContentColumn({
    required this.shouldShowAvatar,
    required this.userInfo,
    required this.shouldShowText,
    required this.isCurrentUser,
    required this.message,
    required this.previousMessage,
    required this.nextMessage,
    this.onDeleteMessage,
    this.onCopyMessage,
    this.onReplyMessage,
    this.currentUserId,
  });

  /// Returns true if this is the first message in a group.
  /// Note: With reverse: true, first message in group has no next message from same user.
  bool _isFirstMessageInGroup() {
    return nextMessage == null || nextMessage!.userId != message.userId;
  }

  /// Returns true if this is the last message in a group.
  /// Note: With reverse: true, last message in group has no previous message from same user.
  bool _isLastMessageInGroup() {
    return previousMessage == null || previousMessage!.userId != message.userId;
  }

  @override
  Widget build(BuildContext context) {
    final double topPadding = _isFirstMessageInGroup() ? 10.0 : 0.0;
    final double bottomPadding = _isLastMessageInGroup() ? 10.0 : 0.0;

    return Padding(
      padding: EdgeInsets.only(top: topPadding, bottom: bottomPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (shouldShowAvatar)
            Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Text(
                userInfo["full_name"]?.toString() ?? "",
                style: AppStyle.regular12(color: appTheme.blackTextV2),
              ),
            ),
          if (shouldShowText)
            _AlignedRow(
              isCurrentUser: isCurrentUser,
              child: GestureDetector(
                onLongPress: () {
                  MessageOptionsWidget.show(
                    context,
                    message: message,
                    currentUserId: currentUserId,
                    onDelete: onDeleteMessage,
                    onCopy: onCopyMessage,
                  );
                },
                child: _MessageBubble(
                  isCurrentUser: isCurrentUser,
                  text: message.message.toString(),
                  isFirstInGroup: _isFirstMessageInGroup(),
                  isLastInGroup: _isLastMessageInGroup(),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Displays a message bubble with appropriate border radius for grouping.
class _MessageBubble extends StatelessWidget {
  final bool isCurrentUser;
  final String text;
  final bool isFirstInGroup;
  final bool isLastInGroup;
  final bool useGroupRadius;

  const _MessageBubble({
    required this.isCurrentUser,
    required this.text,
    required this.isFirstInGroup,
    required this.isLastInGroup,
    this.useGroupRadius = false,
  });

  @override
  Widget build(BuildContext context) {
    BorderRadius borderRadius;

    if (!useGroupRadius) {
      borderRadius = BorderRadius.circular(16);
    } else if (isFirstInGroup && isLastInGroup) {
      borderRadius = BorderRadius.circular(16);
    } else if (isFirstInGroup) {
      borderRadius = isCurrentUser
          ? const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(4),
            )
          : const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
              bottomLeft: Radius.circular(4),
              bottomRight: Radius.circular(16),
            );
    } else if (isLastInGroup) {
      borderRadius = isCurrentUser
          ? const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(0),
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(16),
            )
          : const BorderRadius.only(
              topLeft: Radius.circular(0),
              topRight: Radius.circular(16),
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(16),
            );
    } else {
      borderRadius = isCurrentUser
          ? const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(4),
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(4),
            )
          : const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(16),
              bottomLeft: Radius.circular(4),
              bottomRight: Radius.circular(16),
            );
    }

    return Align(
      alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 2.0),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isCurrentUser
              ? appTheme.primaryColorV2
              : appTheme.backgroundWhite,
          borderRadius: borderRadius,
          boxShadow: [
            BoxShadow(
              color: appTheme.blackTextV2.withAlpha(26),
              offset: const Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Text(
          text,
          style: AppStyle.regular12(
              color: isCurrentUser ? Colors.white : Colors.black),
        ),
      ),
    );
  }
}

/// Aligns a child widget to the start or end of a row based on user.
class _AlignedRow extends StatelessWidget {
  final bool isCurrentUser;
  final Widget child;

  const _AlignedRow({
    required this.isCurrentUser,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.center,
      children: [Flexible(child: child)],
    );
  }
}

/// Displays the date label above a message group.
class _DateLabel extends StatelessWidget {
  final String dateLabel;

  const _DateLabel({required this.dateLabel});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0, bottom: 10.0),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
              color: appTheme.borderColorV2,
              borderRadius: BorderRadius.circular(16)),
          child: Text(dateLabel, style: AppStyle.medium12()),
        ),
      ),
    );
  }
}
