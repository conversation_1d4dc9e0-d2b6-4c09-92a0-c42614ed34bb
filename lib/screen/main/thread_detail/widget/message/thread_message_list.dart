import 'package:flutter/material.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/widget/chat_list.dart';
import 'thread_message_row.dart';
import 'package:family_app/screen/main/thread_detail/model/pending_message.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';

class ThreadMessageList extends StatefulWidget {
  final List<ThreadMessage> messages;
  final List<Account> memberList;
  final ScrollController scrollController;
  final String? currentUserId;
  final void Function(ThreadMessage message)? onShowVoting;
  final bool autoScrollToBottom;
  final VoidCallback? onImageViewerOpen;
  final VoidCallback? onImageViewerClose;
  final List<PendingMessage> pendingMessages;
  final void Function(String pendingId)? onRetryPendingMessage;
  final void Function(String pendingId)? onCancelPendingMessage;
  final void Function(String messageId)? onDeleteMessage;
  final VoidCallback? onCopyMessage;
  final VoidCallback? onReplyMessage;

  const ThreadMessageList({
    super.key,
    required this.messages,
    required this.memberList,
    required this.scrollController,
    required this.currentUserId,
    this.onShowVoting,
    this.autoScrollToBottom = true,
    this.onImageViewerOpen,
    this.onImageViewerClose,
    this.pendingMessages = const [],
    this.onRetryPendingMessage,
    this.onCancelPendingMessage,
    this.onDeleteMessage,
    this.onCopyMessage,
    this.onReplyMessage,
  });

  @override
  State<ThreadMessageList> createState() => _ThreadMessageListState();
}

class _ThreadMessageListState extends State<ThreadMessageList> {
  @override
  Widget build(BuildContext context) {
    // Combine messages and pending messages
    final allItems = <dynamic>[];

    // When reverse: true, we want pending messages to appear at the bottom
    // Since the list is reversed, we add them first so they appear at the bottom
    for (final pending in widget.pendingMessages) {
      allItems.add(_PendingMessageItem(pendingMessage: pending));
    }

    // Add regular messages
    allItems.addAll(widget.messages);

    return ChatList<dynamic>(
      key: ValueKey(
          'thread_messages_${widget.messages.length}_pending_${widget.pendingMessages.length}'),
      items: allItems,
      controller: widget.scrollController,
      padding: const EdgeInsets.all(20.0),
      physics: const ClampingScrollPhysics(),
      cacheExtent: 1000,
      reverse: true,
      itemBuilder: (context, item, index, previousItem, nextItem) {
        if (item is ThreadMessage) {
          return ThreadMessageRow(
            key: ValueKey('message_${item.uuid}'),
            message: item,
            previousMessage: index == 0
                ? null
                : previousItem is ThreadMessage
                    ? previousItem
                    : null,
            nextMessage: nextItem is ThreadMessage ? nextItem : null,
            isLastMessage: nextItem == null ||
                (nextItem is ThreadMessage && nextItem.userId != item.userId) ||
                nextItem is _PendingMessageItem,
            memberList: widget.memberList,
            currentUserId: widget.currentUserId,
            onShowVoting: widget.onShowVoting,
            onImageViewerOpen: () {
              widget.onImageViewerOpen?.call();
            },
            onImageViewerClose: () {
              widget.onImageViewerClose?.call();
            },
            onDeleteMessage: widget.onDeleteMessage,
            onCopyMessage: widget.onCopyMessage,
            onReplyMessage: widget.onReplyMessage,
          );
        } else if (item is _PendingMessageItem) {
          return _SimplePendingMessageBubble(
            key: ValueKey('pending_message_${item.pendingMessage.id}'),
            pendingMessage: item.pendingMessage,
            onRetry: widget.onRetryPendingMessage != null
                ? () => widget.onRetryPendingMessage!(item.pendingMessage.id)
                : null,
            onCancel: widget.onCancelPendingMessage != null
                ? () => widget.onCancelPendingMessage!(item.pendingMessage.id)
                : null,
          );
        }

        return const SizedBox.shrink();
      },
      shouldRebuild: (item) {
        if (item is _PendingMessageItem) return true;
        return false;
      },
    );
  }
}

/// Simple pending message bubble that looks like a real message
class _SimplePendingMessageBubble extends StatelessWidget {
  final PendingMessage pendingMessage;
  final VoidCallback? onRetry;
  final VoidCallback? onCancel;

  const _SimplePendingMessageBubble({
    super.key,
    required this.pendingMessage,
    this.onRetry,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end, // Current user's messages
        children: [
          Flexible(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 2.0),
              padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              decoration: BoxDecoration(
                color: appTheme.primaryColorV2,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: appTheme.blackTextV2.withAlpha(26),
                    offset: const Offset(0, 4),
                    blurRadius: 12,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: _buildContent(),
                  ),
                  const SizedBox(width: 8),
                  _buildStatusIndicator(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    switch (pendingMessage.type) {
      case PendingMessageType.text:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text(
                pendingMessage.text ?? '',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.image:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.image, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Image',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.video:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.videocam, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Video',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.file:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.attach_file, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                pendingMessage.fileName ?? 'File',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.audio:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.audiotrack, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Audio',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.location:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.location_on, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Location',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.poll:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.poll, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Poll',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.event:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.event, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Event',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.task:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.task, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'Task',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
      case PendingMessageType.list:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.list, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                'List',
                style: AppStyle.regular12(color: Colors.white),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        );
    }
  }

  Widget _buildStatusIndicator() {
    if (pendingMessage.isSending && pendingMessage.error == null) {
      return const SizedBox(
        width: 8,
        height: 8,
        child: CircularProgressIndicator(
          strokeWidth: 1,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    } else if (pendingMessage.error != null && !pendingMessage.isSending) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.error_outline, color: Colors.white, size: 20),
          if (onRetry != null) ...[
            const SizedBox(width: 4),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh, color: Colors.white, size: 20),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
          ],
        ],
      );
    }
    
    return const SizedBox.shrink();
  }
}

/// Wrapper class for pending messages to make them compatible with ChatList
class _PendingMessageItem {
  final PendingMessage pendingMessage;

  const _PendingMessageItem({required this.pendingMessage});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _PendingMessageItem &&
          runtimeType == other.runtimeType &&
          pendingMessage == other.pendingMessage;

  @override
  int get hashCode => pendingMessage.hashCode;
}
