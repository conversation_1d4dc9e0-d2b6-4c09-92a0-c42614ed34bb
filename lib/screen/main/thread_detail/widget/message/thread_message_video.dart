import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/config/service/image_cache_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:url_launcher/url_launcher.dart';

class ThreadMessageVideo extends StatefulWidget {
  final ThreadMessage message;
  final CrossAxisAlignment crossAxisAlignment;
  final String? currentUserId;
  final VoidCallback? onImageViewerOpen;
  final VoidCallback? onImageViewerClose;

  const ThreadMessageVideo({
    super.key,
    required this.message,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    required this.currentUserId,
    this.onImageViewerOpen,
    this.onImageViewerClose,
  });

  @override
  State<ThreadMessageVideo> createState() => _ThreadMessageVideoState();
}

class _ThreadMessageVideoState extends State<ThreadMessageVideo> {
  bool _isLoading = true;
  String? _error;
  final Map<String, String?> _cachedUrls = {};

  @override
  void initState() {
    super.initState();
    _loadVideoUrls();
  }

  Future<void> _loadVideoUrls() async {
    try {
      final String? messageTypeId = widget.message.messageTypeId;
      if (messageTypeId == null || messageTypeId.isEmpty) {
        setState(() {
          _isLoading = false;
          _error = 'No video data available';
        });
        return;
      }

      // Parse comma-separated UUIDs from messageTypeId
      final List<String> videoUuids = messageTypeId
          .split(',')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();

      if (videoUuids.isEmpty) {
        setState(() {
          _isLoading = false;
          _error = 'Invalid video data';
        });
        return;
      }

      // Load all video URLs in parallel
      final futures = videoUuids.map((uuid) async {
        if (_cachedUrls.containsKey(uuid) && _cachedUrls[uuid] != null) {
          return MapEntry(uuid, _cachedUrls[uuid]);
        }

        final url = await imageCacheService.getImageUrl(uuid);
        return MapEntry(uuid, url);
      });

      final results = await Future.wait(futures);
      final newCachedUrls = Map.fromEntries(results);

      if (mounted) {
        setState(() {
          _cachedUrls.addAll(newCachedUrls);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final String? messageTypeId = widget.message.messageTypeId;

    if (messageTypeId == null || messageTypeId.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('No video available'),
      );
    }

    // Parse comma-separated UUIDs from messageTypeId
    final List<String> videoUuids = messageTypeId
        .split(',')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();

    if (videoUuids.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('Invalid video data'),
      );
    }

    if (_isLoading) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: widget.crossAxisAlignment,
          children: [
            for (final videoUuid in videoUuids)
              _VideoItemLoading(videoUuid: videoUuid),
          ],
        ),
      );
    }

    if (_error != null) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: widget.crossAxisAlignment,
          children: [
            for (final videoUuid in videoUuids)
              _VideoItemError(videoUuid: videoUuid, error: _error!),
          ],
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: widget.crossAxisAlignment,
        children: [
          for (final videoUuid in videoUuids)
            _VideoItem(
              videoUuid: videoUuid,
              videoUrl: _cachedUrls[videoUuid],
              videoTitle: widget.message.message ?? 'Video',
              isMultiple: videoUuids.length > 1,
              onImageViewerOpen: widget.onImageViewerOpen,
              onImageViewerClose: widget.onImageViewerClose,
            ),
        ],
      ),
    );
  }
}

class _VideoItemLoading extends StatelessWidget {
  final String videoUuid;

  const _VideoItemLoading({required this.videoUuid});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      constraints: const BoxConstraints(maxWidth: 250),
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: appTheme.blackTextV2.withAlpha(26),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: appTheme.primaryColorV2.withAlpha(31),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Loading video...',
                    style: AppStyle.medium14(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Please wait',
                    style: AppStyle.regular12(color: appTheme.grayV2),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _VideoItemError extends StatelessWidget {
  final String videoUuid;
  final String error;

  const _VideoItemError({required this.videoUuid, required this.error});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      constraints: const BoxConstraints(maxWidth: 250),
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: appTheme.blackTextV2.withAlpha(26),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: appTheme.errorV2.withAlpha(31),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Icon(
                  Icons.error_outline,
                  color: appTheme.errorV2,
                  size: 20,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Error loading video',
                    style: AppStyle.medium14(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Tap to retry',
                    style: AppStyle.regular12(color: appTheme.grayV2),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _VideoItem extends StatelessWidget {
  final String videoUuid;
  final String? videoUrl;
  final String videoTitle;
  final bool isMultiple;
  final VoidCallback? onImageViewerOpen;
  final VoidCallback? onImageViewerClose;

  const _VideoItem({
    required this.videoUuid,
    required this.videoUrl,
    required this.videoTitle,
    required this.isMultiple,
    this.onImageViewerOpen,
    this.onImageViewerClose,
  });

  @override
  Widget build(BuildContext context) {
    final hasValidUrl = videoUrl != null && videoUrl!.isNotEmpty;

    if (!hasValidUrl) {
      return Container(
        margin: const EdgeInsets.only(bottom: 8.0),
        constraints: const BoxConstraints(maxWidth: 250),
        decoration: BoxDecoration(
          color: appTheme.backgroundWhite,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: appTheme.blackTextV2.withAlpha(26),
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: appTheme.grayV2.withAlpha(31),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Icon(
                    Icons.error_outline,
                    color: appTheme.grayV2,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Video not available',
                      style: AppStyle.medium14(),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Show video thumbnail with play button overlay
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: GestureDetector(
        onTap: () => _openVideo(context, videoUrl!),
        child: Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            color: appTheme.backgroundWhite,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: appTheme.blackTextV2.withAlpha(26),
                offset: const Offset(0, 2),
                blurRadius: 8,
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              children: [
                // Video thumbnail
                Container(
                  width: 200,
                  height: 200,
                  color: Colors.black.withAlpha(180),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.videocam,
                          color: Colors.white,
                          size: 32,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Video',
                          style: AppStyle.regular12(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
                // Play button overlay
                Center(
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(120),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _openVideo(BuildContext context, String videoUrl) async {
    try {
      final uri = Uri.parse(videoUrl);

      // Try to launch the URL
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webViewConfiguration: const WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
          ),
        );
      } else {
        showErrorToast('Unable to open video. Please try again.');
      }
    } catch (e) {
      showErrorToast('Error opening video: ${e.toString()}');
    }
  }
}
