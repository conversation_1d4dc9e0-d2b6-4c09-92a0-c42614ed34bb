import 'dart:convert';
import 'package:family_app/config/service/app_service.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/router/app_route.dart';
import 'package:auto_route/auto_route.dart';
import 'package:intl/intl.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'dart:math';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';

const double eventCardHorizontalPadding =
    72; // 72*2 = 144 total, matches event card
const double eventCardMaxWidth = 400; // Optional: cap for large screens

class ThreadMessageListCard extends StatefulWidget {
  final ThreadMessage message;
  final CrossAxisAlignment crossAxisAlignment;
  final String? currentUserId;

  const ThreadMessageListCard({
    super.key,
    required this.message,
    required this.crossAxisAlignment,
    this.currentUserId,
  });

  @override
  State<ThreadMessageListCard> createState() => _ThreadMessageListCardState();
}

class _ThreadMessageListCardState extends State<ThreadMessageListCard> {
  ListItem? listItem;
  List<Item> items = [];
  bool isLoading = true;
  bool isNotFound = false;

  final IListRepository _listRepository = locator.get();

  @override
  void initState() {
    super.initState();
    _fetchList();
  }

  Future<void> _fetchList() async {
    setState(() {
      isLoading = true;
      isNotFound = false;
    });
    try {
      final uuid = widget.message.messageTypeId ?? '';
      if (uuid.isNotEmpty) {
        final result = await _listRepository.getListDetail(uuid);
        final listItems = await _listRepository.getAllItemInList(uuid);
        if (result != null) {
          setState(() {
            listItem = result;
            items = listItems;
            isLoading = false;
          });
          return;
        }
      }
      setState(() {
        isNotFound = true;
        isLoading = false;
      });
    } catch (e) {
      AppLogger.e('ThreadMessageListCard: Error fetching list: $e');
      setState(() {
        isNotFound = true;
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final message = widget.message;
    // Get user name from userLog
    String userName = 'User';
    if (message.userLog != null) {
      try {
        final userInfo = jsonDecode(message.userLog!);
        userName = userInfo["full_name"]?.toString() ?? 'User';
      } catch (e) {
        AppLogger.e('ThreadMessageListCard: Error parsing userLog: $e');
      }
    }
    return _AlignedRow(
      isCurrentUser: message.userId == widget.currentUserId,
      child: isLoading
          ? _buildSkeleton()
          : isNotFound
              ? _buildNotFound(userName)
              : _buildListCard(context, userName, listItem!, items),
    );
  }

  Widget _buildSkeleton() {
    return const SizedBox(
      width: 320,
      height: 200,
      child: Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildNotFound(String userName) {
    return const SizedBox(
      width: 320,
      height: 200,
      child: Center(
        child: Text('List not found', style: TextStyle(color: Colors.black54)),
      ),
    );
  }

  Widget _buildListCard(
      BuildContext context, String userName, ListItem list, List<Item> items) {
    final dueDate = list.planDate;
    String dueText = '';
    if (dueDate != null && dueDate.isNotEmpty) {
      try {
        final dt = DateTime.parse(dueDate);
        dueText =
            'Due by ${DateFormat('dd MMM HH:mm').format(dt).toUpperCase()}';
      } catch (_) {}
    }
    final cardWidth = min(
      context.screenSize.width - (eventCardHorizontalPadding * 2),
      eventCardMaxWidth,
    );
    return SizedBox(
      width: cardWidth,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(31),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              '$userName shared the list',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '“${list.name ?? ''}”',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontFamily: 'Inter',
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
            ),
            if (dueText.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8, bottom: 8),
                child: Text(
                  dueText,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Color(0xFF6C5CE7),
                    fontSize: 12,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 1.33,
                  ),
                ),
              ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F7FA),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  for (final item in items) ...[
                    Row(
                      children: [
                        Icon(
                          item.isDone ? Icons.check_circle : Icons.radio_button_unchecked,
                          color: Colors.black,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            item.name ?? '',
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ]
                ],
              ),
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () {
                context.pushRoute(
                  ListDetailRoute(
                      parameter: ListDetailParameter(uuid: list.uuid ?? '')),
                );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: ShapeDecoration(
                  color: appTheme.primaryColorV2.withAlpha(31),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text(
                  'View',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF4E46B4),
                    fontSize: 14,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.w400,
                    height: 1.71,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AlignedRow extends StatelessWidget {
  final bool isCurrentUser;
  final Widget child;

  const _AlignedRow({
    required this.isCurrentUser,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.center,
      children: [Flexible(child: child)],
    );
  }
}
