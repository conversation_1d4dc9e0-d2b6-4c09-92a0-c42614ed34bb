import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/widget/image/thread_image_gallery.dart';

class ThreadMessageImage extends StatelessWidget {
  final ThreadMessage message;
  final VoidCallback? onShowVoting;
  final CrossAxisAlignment crossAxisAlignment;
  final String? currentUserId;
  final VoidCallback? onImageViewerOpen;
  final VoidCallback? onImageViewerClose;

  const ThreadMessageImage({
    super.key,
    required this.message,
    this.onShowVoting,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    required this.currentUserId,
    this.onImageViewerOpen,
    this.onImageViewerClose,
  });

  @override
  Widget build(BuildContext context) {
    final String? messageTypeId = message.messageTypeId;

    if (messageTypeId == null || messageTypeId.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('No image available'),
      );
    }

    // Parse comma-separated UUIDs from messageTypeId
    final List<String> imageUuids = messageTypeId
        .split(',')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();

    if (imageUuids.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('Invalid image data'),
      );
    }

    // Create a stable key based on the message UUID and image UUIDs
    final stableKey =
        ValueKey('image_gallery_${message.uuid}_${imageUuids.join('_')}');

    return ThreadImageGallery(
      key: stableKey,
      imageUuids: imageUuids,
      isMultipleImages: imageUuids.length > 1,
      crossAxisAlignment: crossAxisAlignment,
      maxWidth: 200,
      height: 200,
      borderRadius: 8.0,
      enableImageViewer: true,
      imageViewerTitle: 'Message Image',
      imageViewerDescription: message.message ?? '',
      onImageViewerOpen: onImageViewerOpen,
      onImageViewerClose: onImageViewerClose,
    );
  }
}
