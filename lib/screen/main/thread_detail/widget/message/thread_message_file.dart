import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/config/service/image_cache_service.dart';

class ThreadMessageFile extends StatefulWidget {
  final ThreadMessage message;
  final CrossAxisAlignment crossAxisAlignment;
  final String? currentUserId;

  const ThreadMessageFile({
    super.key,
    required this.message,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    required this.currentUserId,
  });

  @override
  State<ThreadMessageFile> createState() => _ThreadMessageFileState();
}

class _ThreadMessageFileState extends State<ThreadMessageFile> {
  bool _isLoading = true;
  String? _error;
  final Map<String, String?> _cachedUrls = {};

  @override
  void initState() {
    super.initState();
    _loadFileUrls();
  }

  Future<void> _loadFileUrls() async {
    try {
      final String? messageTypeId = widget.message.messageTypeId;
      if (messageTypeId == null || messageTypeId.isEmpty) {
        setState(() {
          _isLoading = false;
          _error = 'No file data available';
        });
        return;
      }

      // Parse comma-separated UUIDs from messageTypeId
      final List<String> fileUuids = messageTypeId
          .split(',')
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .toList();

      if (fileUuids.isEmpty) {
        setState(() {
          _isLoading = false;
          _error = 'Invalid file data';
        });
        return;
      }

      AppLogger.d('ThreadMessageFile: Loading ${fileUuids.length} file URLs');

      // Load all file URLs in parallel using the same service as images
      final futures = fileUuids.map((uuid) async {
        if (_cachedUrls.containsKey(uuid) && _cachedUrls[uuid] != null) {
          return MapEntry(uuid, _cachedUrls[uuid]);
        }

        AppLogger.d('ThreadMessageFile: Fetching URL for UUID: $uuid');
        final url = await imageCacheService.getImageUrl(uuid);
        AppLogger.d(
            'ThreadMessageFile: Got URL for UUID $uuid: ${url?.substring(0, url.length > 50 ? 50 : url.length)}...');
        return MapEntry(uuid, url);
      });

      final results = await Future.wait(futures);
      final newCachedUrls = Map.fromEntries(results);

      if (mounted) {
        setState(() {
          _cachedUrls.addAll(newCachedUrls);
          _isLoading = false;
        });
        AppLogger.d(
            'ThreadMessageFile: Loaded ${newCachedUrls.length} file URLs successfully');
      }
    } catch (e) {
      AppLogger.e('ThreadMessageFile: Error loading file URLs: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final String? messageTypeId = widget.message.messageTypeId;
    AppLogger.d(
        'ThreadMessageFile: build called - message UUID: ${widget.message.uuid}, messageTypeId: $messageTypeId');

    if (messageTypeId == null || messageTypeId.isEmpty) {
      AppLogger.d('ThreadMessageFile: No messageTypeId found');
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('No file available'),
      );
    }

    // Parse comma-separated UUIDs from messageTypeId
    final List<String> fileUuids = messageTypeId
        .split(',')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();

    AppLogger.d(
        'ThreadMessageFile: Parsed ${fileUuids.length} file UUIDs: $fileUuids');

    if (fileUuids.isEmpty) {
      AppLogger.d('ThreadMessageFile: No valid file UUIDs found');
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('Invalid file data'),
      );
    }

    if (_isLoading) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: widget.crossAxisAlignment,
          children: [
            for (final fileUuid in fileUuids)
              _FileItemLoading(fileUuid: fileUuid),
          ],
        ),
      );
    }

    if (_error != null) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 10.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: widget.crossAxisAlignment,
          children: [
            for (final fileUuid in fileUuids)
              _FileItemError(fileUuid: fileUuid, error: _error!),
          ],
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: widget.crossAxisAlignment,
        children: [
          for (final fileUuid in fileUuids)
            _FileItem(
              fileUuid: fileUuid,
              fileUrl: _cachedUrls[fileUuid],
              fileName: widget.message.message ?? 'File',
              isMultiple: fileUuids.length > 1,
            ),
        ],
      ),
    );
  }
}

class _FileItemLoading extends StatelessWidget {
  final String fileUuid;

  const _FileItemLoading({required this.fileUuid});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      constraints: const BoxConstraints(maxWidth: 250),
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: appTheme.blackTextV2.withAlpha(26),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: appTheme.primaryColorV2.withAlpha(31),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Loading file...',
                    style: AppStyle.medium14(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Please wait',
                    style: AppStyle.regular12(color: appTheme.grayV2),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FileItemError extends StatelessWidget {
  final String fileUuid;
  final String error;

  const _FileItemError({required this.fileUuid, required this.error});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      constraints: const BoxConstraints(maxWidth: 250),
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: appTheme.blackTextV2.withAlpha(26),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: appTheme.errorV2.withAlpha(31),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Icon(
                  Icons.error_outline,
                  color: appTheme.errorV2,
                  size: 20,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Error loading file',
                    style: AppStyle.medium14(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Tap to retry',
                    style: AppStyle.regular12(color: appTheme.grayV2),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FileItem extends StatelessWidget {
  final String fileUuid;
  final String? fileUrl;
  final String fileName;
  final bool isMultiple;

  const _FileItem({
    required this.fileUuid,
    required this.fileUrl,
    required this.fileName,
    required this.isMultiple,
  });

  @override
  Widget build(BuildContext context) {
    final hasValidUrl = fileUrl != null && fileUrl!.isNotEmpty;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      constraints: const BoxConstraints(maxWidth: 250),
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: appTheme.blackTextV2.withAlpha(26),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: hasValidUrl ? () => _openFile(context, fileUrl!) : null,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // File icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: hasValidUrl
                        ? appTheme.primaryColorV2.withAlpha(31)
                        : appTheme.grayV2.withAlpha(31),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: ImageAssetCustom(
                      imagePath: Assets.icons.file.path,
                      width: 24,
                      height: 24,
                      color: hasValidUrl
                          ? appTheme.primaryColorV2
                          : appTheme.grayV2,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // File info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        isMultiple ? fileName : _getFileName(fileName),
                        style: AppStyle.medium14(),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        hasValidUrl ? 'Tap to open' : 'File not available',
                        style: AppStyle.regular12(color: appTheme.grayV2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // Download/Open icon
                Icon(
                  hasValidUrl ? Icons.download : Icons.error_outline,
                  color:
                      hasValidUrl ? appTheme.primaryColorV2 : appTheme.grayV2,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getFileName(String fullName) {
    // Extract just the filename from the full path/name
    final parts = fullName.split('/');
    return parts.last;
  }

  void _openFile(BuildContext context, String fileUrl) async {
    try {
      AppLogger.d(
          'ThreadMessageFile: Opening file with URL: ${fileUrl.substring(0, fileUrl.length > 50 ? 50 : fileUrl.length)}...');

      final uri = Uri.parse(fileUrl);

      // Try to launch the URL
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webViewConfiguration: const WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
          ),
        );
        AppLogger.d('ThreadMessageFile: File opened successfully');
      } else {
        AppLogger.e('ThreadMessageFile: Could not open file URL');
        showErrorToast('Unable to open file. Please try again.');
      }
    } catch (e) {
      AppLogger.e('ThreadMessageFile: Error opening file: $e');
      showErrorToast('Error opening file: ${e.toString()}');
    }
  }
}
