import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:family_app/main.dart';

class MessengerGalleryPicker extends StatefulWidget {
  final Function(List<File>) onFilesSelected;
  final ValueChanged<String>? onSendMessage;
  final VoidCallback? onClose;

  const MessengerGalleryPicker({
    Key? key,
    required this.onFilesSelected,
    this.onSendMessage,
    this.onClose,
  }) : super(key: key);

  @override
  State<MessengerGalleryPicker> createState() => _MessengerGalleryPickerState();
}

class _MessengerGalleryPickerState extends State<MessengerGalleryPicker> {
  List<AssetPathEntity> _albums = [];
  AssetPathEntity? _currentAlbum;
  List<AssetEntity> _assets = [];
  final Set<String> _selectedIds = {};
  bool _loading = true;
  bool _sending = false;
  PermissionState? _cachedPermissionState;

  @override
  void initState() {
    super.initState();
    _initializeGallery();
  }

  Future<void> _initializeGallery() async {
    try {
      setState(() => _loading = true);
      await _fetchAlbums();
    } catch (e) {
      setState(() => _loading = false);
    }
  }

  Future<void> _fetchAlbums() async {
    try {
      PermissionState permitted;
      if (_cachedPermissionState != null && _cachedPermissionState!.hasAccess) {
        permitted = _cachedPermissionState!;
      } else {
        permitted = await PhotoManager.requestPermissionExtend();
        _cachedPermissionState = permitted;
      }

      if (!permitted.hasAccess) {
        setState(() => _loading = false);
        return;
      }

      if (permitted.isAuth) {
        try {
          await PhotoManager.releaseCache();
          await Future.delayed(const Duration(milliseconds: 500));

          if (Platform.isAndroid) {
            List<AssetEntity> androidAssets = [];

            try {
              final immediateAssets =
                  await PhotoManager.getAssetListPaged(page: 0, pageCount: 1);
              androidAssets = immediateAssets;
            } catch (e) {
              // Debug log
            }

            if (androidAssets.isEmpty) {
              try {
                final alternativeAssets = await PhotoManager.getAssetListPaged(
                    page: 0, pageCount: 10);
                androidAssets = alternativeAssets;
              } catch (e) {
                // Debug log
              }
            }

            if (androidAssets.isNotEmpty) {
              setState(() {
                _assets = androidAssets;
                _loading = false;
              });
              return;
            }
          }
        } catch (e) {
          // Debug log
        }
      }

      final albums = await PhotoManager.getAssetPathList(
        type: RequestType.all,
        filterOption: FilterOptionGroup(
          imageOption: const FilterOption(
            sizeConstraint: SizeConstraint(ignoreSize: true),
          ),
          videoOption: const FilterOption(
            durationConstraint: DurationConstraint(allowNullable: true),
          ),
        ),
      );

      setState(() {
        _albums = albums;
        _currentAlbum = albums.isNotEmpty ? albums.first : null;
        _loading = false;
      });

      if (_currentAlbum != null) {
        await _fetchAssets();

        if (_assets.isEmpty && albums.length > 1) {
          for (int i = 1; i < albums.length; i++) {
            final album = albums[i];
            if (await album.assetCountAsync > 0) {
              setState(() {
                _currentAlbum = album;
                _loading = true;
              });
              await _fetchAssets();
              break;
            }
          }
        }

        if (_assets.isEmpty) {
          try {
            if (albums.isNotEmpty) {
              final firstAlbum = albums.first;
              final allAssets =
                  await firstAlbum.getAssetListRange(start: 0, end: 200);
              if (allAssets.isNotEmpty) {
                setState(() {
                  _assets = allAssets;
                });
              }
            }
          } catch (e) {
            // Debug log
          }
        }

        if (_assets.isEmpty && Platform.isAndroid && albums.length > 1) {
          for (final album in albums) {
            final albumName = album.name.toLowerCase();
            if (albumName.contains('recent') ||
                albumName.contains('camera') ||
                albumName.contains('dcim')) {
              try {
                final albumAssets =
                    await album.getAssetListRange(start: 0, end: 200);
                if (albumAssets.isNotEmpty) {
                  setState(() {
                    _currentAlbum = album;
                    _assets = albumAssets;
                  });
                  break;
                }
              } catch (e) {
                // Debug log
              }
            }
          }
        }
      } else {
        // Debug log
      }

      if (_assets.isEmpty && !_loading) {
        setState(() {
          _assets = [];
          _loading = false;
        });
      }
    } catch (e) {
      setState(() => _loading = false);
    }
  }

  Future<void> _fetchAssets() async {
    if (_currentAlbum == null) return;
    setState(() {
      _loading = true;
    });
    try {
      final assetCount = await _currentAlbum!.assetCountAsync;
      List<AssetEntity> assets = [];
      if (assetCount > 0) {
        assets = await _currentAlbum!.getAssetListPaged(page: 0, size: 200);
      } else {
        // Debug log
      }
      setState(() {
        _assets = assets;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _assets = [];
        _loading = false;
      });
    }
  }

  void _onAssetTap(AssetEntity asset) {
    setState(() {
      final id = asset.id;
      if (_selectedIds.contains(id)) {
        _selectedIds.remove(id);
      } else {
        _selectedIds.add(id);
      }
    });
  }

  Future<void> _onSend() async {
    if (_selectedIds.isEmpty || _sending) return;

    setState(() {
      _sending = true;
    });

    try {
      final files = <File>[];
      final selectedAssets =
          _assets.where((a) => _selectedIds.contains(a.id)).toList();

      for (final asset in selectedAssets) {
        try {
          final file = await asset.file;
          if (file != null && await file.exists()) {
            files.add(file);
          }
        } catch (e) {
          continue;
        }
      }

      if (files.isNotEmpty) {
        widget.onFilesSelected(files);
        if (widget.onClose != null) widget.onClose!();
      }
    } catch (e) {
      // Handle error if needed
    } finally {
      if (mounted) {
        setState(() {
          _sending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 8, bottom: 8),
                child: Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.symmetric(vertical: 4),
                height: 48,
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back_ios,
                          color: Colors.black87),
                      onPressed: widget.onClose,
                    ),
                    Expanded(
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Align(
                            alignment: Alignment.center,
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<AssetPathEntity>(
                                value: _currentAlbum,
                                icon: const SizedBox.shrink(),
                                isDense: true,
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                items: _albums.map((album) {
                                  return DropdownMenuItem(
                                    value: album,
                                    child: Text(
                                      album.name,
                                      style: const TextStyle(
                                        color: Colors.black87,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  );
                                }).toList(),
                                selectedItemBuilder: (context) {
                                  return _albums.map((album) {
                                    return Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          album.name,
                                          style: const TextStyle(
                                            color: Colors.black87,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        const Icon(Icons.arrow_drop_down,
                                            size: 24, color: Colors.black87),
                                      ],
                                    );
                                  }).toList();
                                },
                                onChanged: (album) {
                                  setState(() {
                                    _currentAlbum = album;
                                    _assets = [];
                                    _loading = true;
                                  });
                                  _fetchAssets();
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  height: 260,
                  child: _loading
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(color: Colors.blue),
                              SizedBox(height: 16),
                              Text(
                                'Loading gallery...',
                                style: TextStyle(
                                    color: Colors.black54, fontSize: 14),
                              ),
                            ],
                          ),
                        )
                      : _assets.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.photo_library,
                                      color: Colors.black26, size: 48),
                                  const SizedBox(height: 16),
                                  Text(
                                    _albums.isEmpty
                                        ? 'No albums found'
                                        : Platform.isAndroid
                                            ? 'No media found. Please check your gallery permissions.'
                                            : 'No media found in this album',
                                    style: const TextStyle(
                                        color: Colors.black54, fontSize: 16),
                                  ),
                                  if (_albums.isNotEmpty) ...[
                                    const SizedBox(height: 8),
                                    Text(
                                      Platform.isAndroid
                                          ? 'Make sure you have photos or videos in your gallery'
                                          : 'Try selecting a different album',
                                      style: const TextStyle(
                                          color: Colors.black38, fontSize: 14),
                                    ),
                                  ],
                                  const SizedBox(height: 16),
                                  TextButton(
                                    onPressed: () {
                                      setState(() {
                                        _cachedPermissionState = null;
                                        _loading = true;
                                      });
                                      _initializeGallery();
                                    },
                                    child: const Text('Retry'),
                                  ),
                                ],
                              ),
                            )
                          : GridView.builder(
                              padding: const EdgeInsets.all(2),
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 2,
                                mainAxisSpacing: 2,
                              ),
                              itemCount: _assets.length,
                              itemBuilder: (context, index) {
                                final asset = _assets[index];
                                final isSelected =
                                    _selectedIds.contains(asset.id);
                                return _GalleryGridItem(
                                  asset: asset,
                                  isSelected: isSelected,
                                  selectionIndex: isSelected
                                      ? _selectedIds
                                              .toList()
                                              .indexOf(asset.id) +
                                          1
                                      : null,
                                  onTap: () => _onAssetTap(asset),
                                );
                              },
                            ),
                ),
              ),
            ],
          ),
        ),
        if (_selectedIds.isNotEmpty)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: ElevatedButton(
                onPressed: _sending ? null : _onSend,
                style: ElevatedButton.styleFrom(
                  backgroundColor: appTheme.primaryColorV2,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _sending
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Send ${_selectedIds.length} ${_selectedIds.length == 1 ? 'item' : 'items'}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
              ),
            ),
          ),
      ],
    );
  }
}

class _GalleryGridItem extends StatefulWidget {
  final AssetEntity asset;
  final bool isSelected;
  final int? selectionIndex;
  final VoidCallback onTap;

  const _GalleryGridItem({
    required this.asset,
    required this.isSelected,
    required this.selectionIndex,
    required this.onTap,
  });

  @override
  State<_GalleryGridItem> createState() => _GalleryGridItemState();
}

class _GalleryGridItemState extends State<_GalleryGridItem> {
  Future<Uint8List?>? _thumbnailFuture;

  @override
  void initState() {
    super.initState();
    _loadThumbnail();
  }

  @override
  void didUpdateWidget(_GalleryGridItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.asset.id != widget.asset.id) {
      _loadThumbnail();
    }
  }

  void _loadThumbnail() {
    _thumbnailFuture = widget.asset
        .thumbnailDataWithSize(
      const ThumbnailSize(200, 200),
      format: ThumbnailFormat.jpeg,
      quality: 80,
    )
        .catchError((error) {
      return null;
    });
  }

  String _formatDuration(Duration d) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(d.inMinutes.remainder(60));
    final seconds = twoDigits(d.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: _thumbnailFuture,
      builder: (context, snapshot) {
        final thumb = snapshot.data != null
            ? Image.memory(snapshot.data!, fit: BoxFit.cover)
            : Container(color: Colors.grey[200]);
        return GestureDetector(
          onTap: widget.onTap,
          child: Stack(
            fit: StackFit.expand,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: widget.isSelected
                      ? Border.all(color: appTheme.primaryColorV2, width: 3)
                      : null,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: thumb,
                ),
              ),
              if (widget.asset.type == AssetType.video)
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    color: Colors.black54,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    child: Text(
                      _formatDuration(widget.asset.videoDuration),
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                ),
              if (widget.isSelected)
                Positioned(
                  top: 4,
                  right: 4,
                  child: CircleAvatar(
                    radius: 12,
                    backgroundColor: appTheme.primaryColorV2,
                    child: Text(
                      widget.selectionIndex?.toString() ?? '',
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
