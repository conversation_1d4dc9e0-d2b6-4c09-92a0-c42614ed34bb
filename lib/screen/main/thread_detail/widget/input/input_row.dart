import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';

class InputRow extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final bool showMenu;
  final VoidCallback onToggleMenu;
  final VoidCallback onSend;

  const InputRow({
    required this.controller,
    required this.focusNode,
    required this.showMenu,
    required this.onToggleMenu,
    required this.onSend,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Plus button
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: appTheme.primaryColorV2.withAlpha(31),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon<PERSON>utton(
              icon: Icon(Icons.add, color: appTheme.primaryColorV2, size: 20),
              onPressed: onToggleMenu,
              splashRadius: 24,
            ),
          ),
          const SizedBox(width: 8),
          // TextField
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: appTheme.transparentWhiteColor,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: controller,
                      focusNode: focusNode,
                      style: AppStyle.regular16V2(),
                      decoration: InputDecoration(
                        hintText: 'Type here',
                        hintStyle: AppStyle.regular14(color: appTheme.grayV2),
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      minLines: 1,
                      maxLines: 3,
                      keyboardType: TextInputType.multiline,
                      textCapitalization: TextCapitalization.sentences,
                      onSubmitted: (_) => onSend(),
                    ),
                  ),
                  // Emoji
                  IconButton(
                      icon: SvgPicture.asset(Assets.icons.iconSmile.path),
                      onPressed: () {},
                      iconSize: 24),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Dynamic button area - only rebuilds when text changes
          ValueListenableBuilder<TextEditingValue>(
            valueListenable: controller,
            builder: (context, value, child) {
              final hasText = value.text.trim().isNotEmpty;

              if (hasText) {
                // Send button
                return Container(
                  decoration: BoxDecoration(
                      color: appTheme.primaryColorV2.withAlpha(31),
                      borderRadius: BorderRadius.circular(24)),
                  child: IconButton(
                    icon: SvgPicture.asset(
                      Assets.icons.iconSend.path,
                      colorFilter: ColorFilter.mode(
                          appTheme.primaryColorV2, BlendMode.srcIn),
                    ),
                    onPressed: onSend,
                  ),
                );
              } else {
                // Mic button
                return Container(
                  decoration: BoxDecoration(
                      color: appTheme.primaryColorV2.withAlpha(31),
                      borderRadius: BorderRadius.circular(24)),
                  child: IconButton(
                    icon: SvgPicture.asset(
                      Assets.icons.iconMicrophone.path,
                      colorFilter: ColorFilter.mode(
                          appTheme.primaryColorV2, BlendMode.srcIn),
                    ),
                    onPressed: () {},
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }
} 