import 'dart:io';
import 'package:flutter/material.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:family_app/screen/main/thread_detail/widget/input/messenger_gallery_picker.dart';
import 'package:family_app/screen/main/thread_detail/widget/input/input_row.dart';
import 'package:family_app/screen/main/thread_detail/widget/input/input_area_menu_grid.dart';
import 'package:family_app/screen/main/thread_detail/widget/popup/select_event_thread_bts.dart';
import 'package:family_app/screen/main/thread_detail/widget/popup/select_list_thread_bts.dart';
import 'package:family_app/screen/main/thread_detail/widget/popup/select_task_thread_bts.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/screen/main/thread_detail/poll/upsert/screen.dart';
import 'package:family_app/main.dart';
import 'package:auto_route/auto_route.dart';
import 'package:family_app/router/app_route.dart';

class InputAreaWithMenu extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final bool showMenu;
  final VoidCallback onToggleMenu;
  final VoidCallback onSend;

  const InputAreaWithMenu({
    required this.controller,
    required this.focusNode,
    required this.showMenu,
    required this.onToggleMenu,
    required this.onSend,
    Key? key,
  }) : super(key: key);

  @override
  State<InputAreaWithMenu> createState() => _InputAreaWithMenuState();
}

class _InputAreaWithMenuState extends State<InputAreaWithMenu> {
  bool _showGalleryPicker = false;

  void _handleFilesSelected(List<File> files) {
    context.read<ThreadDetailCubit>().handleFilesSelectedFromGallery(files);
    _handleCloseGalleryPicker();
  }

  void _handleShowGalleryPicker() {
    setState(() {
      _showGalleryPicker = true;
    });
  }

  void _handleCloseGalleryPicker() {
    setState(() {
      _showGalleryPicker = false;
    });
  }

  Future<void> _handleLocation(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    final result = await AutoRouter.of(context).push(
      PlaceMapSelectionRoute(
        parameter: PlaceUpsertParameter(
          tripId: cubit.threadFamily.uuid ?? '',
          dayIndex: 0,
          activityIndex: null,
          place: null,
          viewMode: false,
          allActivities: const [],
          initialLocation: null,
        ),
      ),
    );
    if (result is PlaceUpsertParameter &&
        result.place != null &&
        (result.place!.activities?.isNotEmpty ?? false)) {
      final activity = result.place!.activities!.first;
      if (activity.latitude != null && activity.longitude != null) {
        final extraData = <String, dynamic>{
          'latitude': activity.latitude,
          'longitude': activity.longitude,
          if (activity.venue != null) 'address': activity.venue,
          if (activity.activityImage != null)
            'photoUrl': activity.activityImage,
          if (activity.city != null) 'city': activity.city,
          'time': activity.time,
          if (activity.description?.isNotEmpty ?? false)
            'description': activity.description,
        };
        await cubit.sendLocationMessage(extraData);
      }
    }
  }

  Future<void> _handleEvent(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    await SelectEventThreadBts.show(
      context,
      selectedEvents: [],
      onEventsSelected: (events) {
        final event = events.first;
        cubit.sendEventMessage(event.name ?? '', eventId: event.uuid);
      },
      title: 'Select Event',
    );
  }

  Future<void> _handleList(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    await SelectListThreadBts.show(
      context,
      selectedList: null,
      onListSelected: (list) {
        cubit.sendListMessage(list.name ?? '', listId: list.uuid);
      },
      title: 'Select List',
    );
  }

  Future<void> _handleTask(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    final result = await SelectTaskThreadBTS.show(
      context,
      initialSelectedTaskUuids: [],
      initialListUuid: null,
      title: 'Select Task',
    );
    if (result != null && result.isNotEmpty) {
      if (result.length == 1) {
        final task = result.first;
        cubit.sendTaskMessage(task.name ?? '', taskId: task.uuid);
      } else {
        final taskNames = result
            .map((t) => t.name ?? '')
            .where((n) => n.isNotEmpty)
            .join(', ');
        final taskIds = result
            .map((t) => t.uuid ?? '')
            .where((id) => id.isNotEmpty)
            .toList();
        cubit.sendTaskMessage(
            taskNames.isNotEmpty ? taskNames : 'Multiple tasks',
            taskIds: taskIds);
      }
    }
  }

  Future<void> _handlePoll(BuildContext context) async {
    final cubit = context.read<ThreadDetailCubit>();
    final state = cubit.state;
    final threadUuid = (state.threadDetail ?? cubit.threadFamily).uuid;
    final result = await ThreadDetailVotingUpsertBts.show(context, threadUuid!);
    if (result == true) {
      await cubit.onFetchThreadDetail(threadUuid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 180),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Input Row
          InputRow(
            controller: widget.controller,
            focusNode: widget.focusNode,
            showMenu: widget.showMenu,
            onToggleMenu: widget.onToggleMenu,
            onSend: widget.onSend,
          ),
          // Menu or Gallery Picker
          if (widget.showMenu) ...[
            Divider(color: appTheme.borderColorV2),
            _showGalleryPicker
                ? MessengerGalleryPicker(
                    onFilesSelected: _handleFilesSelected,
                    onClose: _handleCloseGalleryPicker,
                  )
                : InputAreaMenuGrid(
                    onCamera: () {
                      context.read<ThreadDetailCubit>().pickImageFromCamera();
                    },
                    onGallery: _handleShowGalleryPicker,
                    onFile: () {
                      context.read<ThreadDetailCubit>().pickMultipleFiles();
                    },
                    onLocation: () => _handleLocation(context),
                    onEvent: () => _handleEvent(context),
                    onList: () => _handleList(context),
                    onTask: () => _handleTask(context),
                    onPoll: () => _handlePoll(context),
                  ),
          ],
        ],
      ),
    );
  }

  @override
  void didUpdateWidget(covariant InputAreaWithMenu oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!widget.showMenu && oldWidget.showMenu) {
      setState(() {
        _showGalleryPicker = false;
      });
    }
  }
} 