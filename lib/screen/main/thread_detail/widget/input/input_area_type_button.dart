import 'package:flutter/material.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/popup/popup.dart';

class InputAreaTypeButton extends StatelessWidget {
  final VoidCallback? onCamera;
  final VoidCallback? onPhoto;
  final VoidCallback? onAttachFile;
  final VoidCallback? onVoice;
  final VoidCallback? onLocation;
  final Future<void> Function()? onVoting;
  final VoidCallback? onAction;

  const InputAreaTypeButton({
    super.key,
    this.onCamera,
    this.onPhoto,
    this.onAttachFile,
    this.onVoice,
    this.onLocation,
    this.onVoting,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    List<_MenuItem> menuItems = [
      _MenuItem(
        label: 'Camera',
        iconPath: Assets.icons.icCamera32.path,
        onTap: () {
          Navigator.pop(context);
          if (onCamera != null) onCamera!();
        },
      ),
      _MenuItem(
        label: 'Photo',
        iconPath: Assets.icons.picture.path,
        onTap: () {
          Navigator.pop(context);
          if (onPhoto != null) onPhoto!();
        },
      ),
      _MenuItem(
        label: 'Attach file',
        iconPath: Assets.icons.file.path,
        onTap: () {
          Navigator.pop(context);
          if (onAttachFile != null) onAttachFile!();
        },
      ),
      _MenuItem(
        label: 'Voice',
        iconPath: Assets.icons.voice.path,
        onTap: () {
          Navigator.pop(context);
          if (onVoice != null) onVoice!();
        },
      ),
      _MenuItem(
        label: 'Location',
        iconPath: Assets.icons.location.path,
        onTap: () {
          Navigator.pop(context);
          if (onLocation != null) onLocation!();
        },
      ),
      _MenuItem(
        label: 'Voting',
        iconPath: Assets.icons.iconTrailling.path,
        onTap: () async {
          Navigator.pop(context);
          if (onVoting != null) {
            await onVoting!();
            if (onAction != null) onAction!();
          }
        },
      ),
    ];

    return CustomPopup(
      showArrow: false,
      content: Container(
        width: 188,
        padding: const EdgeInsets.all(4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: menuItems
              .map((item) => InkWell(
                    onTap: item.onTap,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(child: Text(item.label, style: AppStyle.textSmR)),
                          ImageAssetCustom(imagePath: item.iconPath),
                        ],
                      ),
                    ),
                  ))
              .toList(),
        ),
      ),
      contentPadding: const EdgeInsets.all(0),
      child: ImageAssetCustom(
        imagePath: Assets.icons.buttonMenu.path,
        width: 40,
      ),
    );
  }
}

class _MenuItem {
  final String label;
  final String iconPath;
  final VoidCallback onTap;
  _MenuItem({required this.label, required this.iconPath, required this.onTap});
}
