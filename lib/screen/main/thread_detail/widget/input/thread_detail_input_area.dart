import 'package:flutter/material.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/screen/main/thread_detail/widget/input/input_area_type_button.dart';
import 'package:family_app/gen/assets.gen.dart';

class ThreadDetailInputArea extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final VoidCallback onSend;
  final VoidCallback onAction;
  final VoidCallback? onCamera;
  final VoidCallback? onPhoto;
  final VoidCallback? onAttachFile;
  final VoidCallback? onVoice;
  final VoidCallback? onLocation;
  final Future<void> Function()? onVoting;

  const ThreadDetailInputArea({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onSend,
    required this.onAction,
    this.onCamera,
    this.onPhoto,
    this.onAttachFile,
    this.onVoice,
    this.onLocation,
    this.onVoting,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        child: Row(
          children: [
            InputAreaTypeButton(
              onCamera: onCamera,
              onPhoto: onPhoto,
              onAttachFile: onAttachFile,
              onVoice: onVoice,
              onLocation: onLocation,
              onVoting: onVoting,
              onAction: onAction,
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextField(
                  focusNode: focusNode,
                  controller: controller,
                  decoration: const InputDecoration(
                    hintText: 'Aa',
                    border: InputBorder.none,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 10),
            InkWell(
              onTap: onSend,
              child: ImageAssetCustom(
                imagePath: Assets.icons.buttonSend.path,
                width: 40,
              ),
            )
          ],
        ),
      ),
    );
  }
}
