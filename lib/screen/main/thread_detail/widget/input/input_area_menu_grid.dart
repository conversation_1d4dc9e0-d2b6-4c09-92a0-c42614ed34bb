import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';

class InputAreaMenuGrid extends StatelessWidget {
  final VoidCallback? onCamera;
  final VoidCallback? onGallery;
  final VoidCallback? onFile;
  final VoidCallback? onLocation;
  final VoidCallback? onEvent;
  final VoidCallback? onList;
  final VoidCallback? onTask;
  final Future<void> Function()? onPoll;

  const InputAreaMenuGrid({
    this.onCamera,
    this.onGallery,
    this.onFile,
    this.onLocation,
    this.onEvent,
    this.onList,
    this.onTask,
    this.onPoll,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final items = [
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconCameraOutlined.path),
        label: 'Camera',
        onTap: onCamera,
      ),
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconGalleryOutlined.path),
        label: 'Gallery',
        onTap: onGallery,
      ),
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconFileOutlined.path),
        label: 'Files',
        onTap: onFile,
      ),
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconLocationOutlined.path),
        label: 'Location',
        onTap: onLocation,
      ),
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconCalendarOutlined.path),
        label: 'Event',
        onTap: onEvent,
      ),
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconListOutlined.path),
        label: 'List',
        onTap: onList,
      ),
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconTaskOutlined.path),
        label: 'Task',
        onTap: onTask,
      ),
      _MenuGridItem(
        icon: SvgPicture.asset(Assets.icons.iconPollOutlined.path),
        label: 'Poll',
        onTap: onPoll,
      ),
    ];
    return Container(
      padding: const EdgeInsets.only(
          left: 16.0, right: 16.0, top: 16.0, bottom: 20.0),
      child: GridView.count(
        shrinkWrap: true,
        crossAxisCount: 4,
        mainAxisSpacing: 20,
        crossAxisSpacing: 8,
        childAspectRatio: 0.85,
        physics: const NeverScrollableScrollPhysics(),
        children: items.map((item) {
          return GestureDetector(
            onTap: item.onTap,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                      color: appTheme.borderColorV2,
                      borderRadius: BorderRadius.circular(16)),
                  child: Center(child: item.icon),
                ),
                const SizedBox(height: 2),
                Text(item.label,
                    style: AppStyle.regular16(color: appTheme.grayV2),
                    textAlign: TextAlign.center),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}

class _MenuGridItem {
  final Widget icon;
  final String label;
  final VoidCallback? onTap;
  const _MenuGridItem({required this.icon, required this.label, this.onTap});
} 