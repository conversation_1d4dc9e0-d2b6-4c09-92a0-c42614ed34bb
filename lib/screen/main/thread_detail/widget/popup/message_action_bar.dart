import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/dialog.dart';
import 'message_options_widget.dart';

class MessageActionBar extends StatelessWidget {
  final VoidCallback? onCopy;
  final VoidCallback? onDelete;
  final bool showCopy;

  const MessageActionBar({
    super.key,
    this.onCopy,
    this.onDelete,
    this.showCopy = false,
  });

  void _handleDelete(BuildContext context) {
    MessageOptionsWidget.hide();
    DialogUtils.showDeleteDialog(
      navigatorKey.currentContext ?? context,
      title: 'Delete message',
      content: 'Are you sure you want to delete this message?',
      confirmText: 'Delete',
      onConfirm: () {
        Navigator.of(navigatorKey.currentContext ?? context).pop();
        onDelete?.call();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 12,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (showCopy)
              _ActionButton(
                icon: Icons.copy_outlined,
                label: 'Copy',
                color: appTheme.grayV2,
                onTap: onCopy,
              ),
            _ActionButton(
              icon: Icons.delete_outline,
              label: 'Delete',
              color: appTheme.errorV2,
              onTap: () => _handleDelete(context),
            ),
          ],
        ),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback? onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
              color: color.withAlpha(18),
              shape: BoxShape.circle,
            ),
            padding: const EdgeInsets.all(16),
            child: Icon(
              icon,
              size: 28,
              color: color,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: AppStyle.medium14(color: color),
          ),
        ],
      ),
    );
  }
} 