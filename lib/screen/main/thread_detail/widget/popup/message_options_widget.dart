import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'message_action_bar.dart';

class MessageOptionsWidget {
  static OverlayEntry? _currentEntry;

  static void show(
    BuildContext context, {
    required ThreadMessage message,
    required String? currentUserId,
    void Function(String messageId)? onDelete,
    VoidCallback? onCopy,
  }) {
    // Remove any existing overlay
    _currentEntry?.remove();
    _currentEntry = null;

    final isTextMessage = message.messageType == 'text' &&
        message.message != null &&
        message.message!.isNotEmpty;

    _currentEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Dismiss area
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                _currentEntry?.remove();
                _currentEntry = null;
              },
              behavior: HitTestBehavior.translucent,
              child: Container(),
            ),
          ),
          // Action bar at bottom
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: MessageActionBar(
              showCopy: isTextMessage,
              onCopy: () {
                _currentEntry?.remove();
                _currentEntry = null;
                onCopy?.call();
              },
              onDelete: () {
                _currentEntry?.remove();
                _currentEntry = null;
                onDelete?.call(message.uuid ?? '');
              },
            ),
          ),
        ],
      ),
    );
    final overlay = Overlay.of(context, rootOverlay: true);
    overlay.insert(_currentEntry!);
  }

  static void hide() {
    if (_currentEntry != null) {
      _currentEntry?.remove();
      _currentEntry = null;
    }
  }
}
