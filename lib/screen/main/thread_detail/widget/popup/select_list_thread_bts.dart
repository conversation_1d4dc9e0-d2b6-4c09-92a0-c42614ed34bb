import 'package:family_app/config/service/app_service.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/textfield/title_text_field_v3.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';

class SelectListThreadBts extends StatefulWidget {
  final ListItem? selectedList;
  final Function(ListItem)? onListSelected;
  final String? title;

  const SelectListThreadBts({
    super.key,
    this.selectedList,
    this.onListSelected,
    this.title,
  });

  static Future<ListItem?> show(
    BuildContext context, {
    ListItem? selectedList,
    Function(ListItem)? onListSelected,
    String? title,
  }) async {
    return showModalBottomSheet<ListItem>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) => FractionallySizedBox(
        heightFactor: 0.55,
        child: SelectListThreadBts(
          selectedList: selectedList,
          onListSelected: onListSelected,
          title: title,
        ),
      ),
    );
  }

  @override
  State<SelectListThreadBts> createState() => _SelectListThreadBtsState();
}

class _SelectListThreadBtsState extends State<SelectListThreadBts> {
  final AccountService _accountService = locator.get();
  final IListRepository _listRepository = locator.get();

  List<ListItem> _lists = [];
  bool _isLoading = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  String? _selectedListId;

  @override
  void initState() {
    super.initState();
    _selectedListId = widget.selectedList?.uuid;
    _loadLists();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.trim();
    });
    _loadLists();
  }

  Future<void> _loadLists() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final lists =
          await _listRepository.getListByFamilyId(_accountService.familyId);
      setState(() {
        _lists = _searchQuery.isEmpty
            ? lists
            : lists
                .where((l) =>
                    l.name
                        ?.toLowerCase()
                        .contains(_searchQuery.toLowerCase()) ??
                    false)
                .toList();
      });
    } catch (e) {
      AppLogger.e('SelectListThreadBts: Error loading lists: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onSelect(ListItem list) {
    setState(() {
      _selectedListId = list.uuid;
    });
    widget.onListSelected?.call(list);
    Navigator.of(context).pop(list);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.0),
          topRight: Radius.circular(24.0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TitleTextFieldV3(
              controller: _searchController,
              prefix: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: SvgPicture.asset(Assets.icons.icSearch.path,
                    width: 16, height: 16),
              ),
              prefixIconConstraints: const BoxConstraints(maxWidth: 32),
              suffixIcon: _searchController.text.isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                        _loadLists();
                      },
                      child: Icon(
                        Icons.clear,
                        size: 16,
                        color: appTheme.grayV2,
                      ),
                    )
                  : null,
              hintText: "Search lists",
              hintStyle: AppStyle.regular12(color: appTheme.grayV2),
              radius: BorderRadius.circular(32),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              filled: true,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _lists.isEmpty
                    ? const Center(child: Text('No lists found'))
                    : ListView.builder(
                        itemCount: _lists.length,
                        itemBuilder: (context, index) {
                          final list = _lists[index];
                          final isSelected = list.uuid == _selectedListId;
                          return ListTile(
                            title: Text(list.name ?? 'Unnamed List'),
                            selected: isSelected,
                            onTap: () => _onSelect(list),
                            trailing: isSelected
                                ? const Icon(Icons.check, color: Colors.blue)
                                : null,
                          );
                        },
                      ),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 12.0),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
          Text(
            widget.title ?? 'Select List',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 44), // Placeholder for symmetry
        ],
      ),
    );
  }
}
