import 'package:flutter/material.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';

import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'dart:async';
import 'package:family_app/utils/flash/toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/textfield/title_text_field_v3.dart';
import 'package:family_app/widget/event_card_widget.dart';
import 'package:flutter_svg/svg.dart';

class SelectEventThreadBts extends StatefulWidget {
  final List<EventModels>? selectedEvents;
  final Function(List<EventModels>)? onEventsSelected;
  final String? title;

  const SelectEventThreadBts({
    super.key,
    this.selectedEvents,
    this.onEventsSelected,
    this.title,
  });

  static Future<bool?> show(
    BuildContext context, {
    List<EventModels>? selectedEvents,
    Function(List<EventModels>)? onEventsSelected,
    String? title,
  }) async {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) => FractionallySizedBox(
        heightFactor: 0.55,
        child: SelectEventThreadBts(
          selectedEvents: selectedEvents,
          onEventsSelected: onEventsSelected,
          title: title,
        ),
      ),
    );
  }

  @override
  State<SelectEventThreadBts> createState() => _SelectEventThreadBtsState();
}

class _SelectEventThreadBtsState extends State<SelectEventThreadBts> {
  final AccountService _accountService = locator.get();
  final EventService _eventService = locator.get();

  final BaseListStreamController<EventModels> _events =
      BaseListStreamController<EventModels>([]);
  final StreamController<String> _selectedEventIdController =
      StreamController<String>.broadcast();
  String _selectedEventId = '';
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false;
  String _searchQuery = '';
  static const int _pageSize = 1000;

  DateTime? _fromDate;
  DateTime? _toDate;

  // Debounce timer for search
  Timer? _debounceTimer;
  Map<String, List<EventModels>>? _groupedEvents;

  @override
  void initState() {
    super.initState();
    _initializeDates();
    _initializeSelectedEvents();
    _loadEvents();
    _searchController.addListener(_onSearchChanged);
    _searchController.addListener(() {
      setState(() {}); // Rebuild to show/hide clear icon
    });
  }

  void _initializeDates() {
    final now = DateTime.now();
    _fromDate = DateTime(now.year, 1, 1, 0, 0, 0);
    _toDate = DateTime(now.year, 12, 31, 23, 59, 59);
  }

  void _initializeSelectedEvents() {
    if (widget.selectedEvents != null && widget.selectedEvents!.isNotEmpty) {
      final firstEvent = widget.selectedEvents!.first;
      if (firstEvent.uuid != null && firstEvent.uuid!.isNotEmpty) {
        _selectedEventId = firstEvent.uuid!;
        _selectedEventIdController.add(_selectedEventId);
      }
    }
  }

  // Debounced search function - triggers API call with search parameter
  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (!mounted) return; // Check if widget is still mounted

      final newQuery = _searchController.text.trim();
      if (newQuery != _searchQuery) {
        setState(() {
          _searchQuery = newQuery;
        });
        _loadEvents(refresh: true);
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _events.dispose();
    _selectedEventIdController.close();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents({bool refresh = false}) async {
    if (_isLoading) return;

    try {
      if (!mounted) return; // Check if widget is still mounted

      setState(() {
        _isLoading = true;
      });

      final events = await _eventService.getEventsByFamilyId(
        _accountService.familyId,
        from: _fromDate,
        to: _toDate,
        limit: _pageSize,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      if (!mounted) return; // Check again after async operation

      // Filter out duplicate events by UUID (for recurring events)
      final uniqueEvents = _deduplicateEventsByUuid(events);

      _events.value = uniqueEvents;

      // Group events by date
      _groupEventsByDate(_events.value);
    } catch (e) {
      if (mounted) showErrorToast('Failed to load events: ${e.toString()}');
    } finally {
      if (mounted)
        setState(() {
          _isLoading = false;
        });
    }
  }

  void _groupEventsByDate(List<EventModels> events) {
    if (!mounted) return; // Check if widget is still mounted

    final grouped = <String, List<EventModels>>{};
    for (final event in events) {
      try {
        // Use fromDate if available, otherwise use createdAt, otherwise use current date
        String dateString = event.fromDate ??
            event.createdAt ??
            DateTime.now().toIso8601String();

        // Handle different date formats
        DateTime eventDate;
        if (dateString.contains('T')) {
          // ISO format
          eventDate = DateTime.parse(dateString);
        } else if (dateString.contains(' ')) {
          // "yyyy-MM-dd HH:mm:ss" format
          eventDate = DateFormat("yyyy-MM-dd HH:mm:ss").parse(dateString);
        } else {
          // "yyyy-MM-dd" format
          eventDate = DateFormat("yyyy-MM-dd").parse(dateString);
        }

        final dateKey = DateFormat('EEEE, MMMM d, yyyy').format(eventDate);
        grouped.putIfAbsent(dateKey, () => []).add(event);
      } catch (e) {
        // Use current date as fallback
        final dateKey = DateFormat('EEEE, MMMM d, yyyy').format(DateTime.now());
        grouped.putIfAbsent(dateKey, () => []).add(event);
      }
    }
    setState(() {
      _groupedEvents = grouped;
    });
  }

  Future<void> _refreshEvents() async {
    await _loadEvents(refresh: true);
  }

  /// Deduplicate events by UUID, keeping the most recent instance of each event
  List<EventModels> _deduplicateEventsByUuid(List<EventModels> events) {
    final Map<String, EventModels> uniqueEvents = {};

    for (final event in events) {
      final uuid = event.uuid;
      if (uuid == null || uuid.isEmpty) continue;

      // If we haven't seen this UUID before, or if this event is more recent
      if (!uniqueEvents.containsKey(uuid) ||
          _isMoreRecentEvent(event, uniqueEvents[uuid]!)) {
        uniqueEvents[uuid] = event;
      }
    }

    return uniqueEvents.values.toList();
  }

  /// Compare two events and return true if the first event is more recent
  bool _isMoreRecentEvent(EventModels event1, EventModels event2) {
    // Compare by fromDate first
    final date1 = event1.fromDate?.toLocalDT;
    final date2 = event2.fromDate?.toLocalDT;

    if (date1 != null && date2 != null) {
      return date1.isAfter(date2);
    }

    // If fromDate is not available, compare by createdAt
    final created1 = event1.createdAt?.toLocalDT;
    final created2 = event2.createdAt?.toLocalDT;

    if (created1 != null && created2 != null) {
      return created1.isAfter(created2);
    }

    // If neither is available, keep the first one
    return false;
  }

  void _toggleEventSelection(EventModels event) {
    final eventId = event.uuid;
    if (eventId == null || eventId.isEmpty) return;

    if (_selectedEventId == eventId) {
      // Deselect if already selected
      _selectedEventId = '';
    } else {
      // Select new event (single selection)
      _selectedEventId = eventId;
    }
    _selectedEventIdController.add(_selectedEventId);
  }

  void _onSave() {
    final selectedEvent = _events.value
        .where((event) => event.uuid == _selectedEventId)
        .firstOrNull;
    if (selectedEvent != null) {
      widget.onEventsSelected?.call([selectedEvent]);
    }
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.0),
          topRight: Radius.circular(24.0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          const SizedBox(height: 8),
          Expanded(
            child: Column(
              children: [
                const SizedBox(height: 20),
                _buildSearchBar(),
                const SizedBox(height: 20),
                Expanded(
                  child: _buildEventsList(),
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 12.0),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ButtonIcon(
            Assets.icons.arrowLeft.path,
            size: 44.h2,
            sizeIcon: 24.h2,
            colorIcon: appTheme.grayV2,
            () => Navigator.of(context).pop(),
            bg: appTheme.transparentColor,
          ),
          Text(
            widget.title ?? LocaleKeys.select_event.tr(),
            style: AppStyle.textLgS,
          ),
          StreamBuilder<String>(
            stream: _selectedEventIdController.stream,
            initialData: _selectedEventId,
            builder: (context, selectedIdSnapshot) {
              final isSaveEnabled = selectedIdSnapshot.data?.isNotEmpty == true;
              return ButtonIcon(
                Assets.icons.check.path,
                () {
                  if (isSaveEnabled) _onSave();
                },
                size: 44.h2,
                sizeIcon: 24.h2,
                colorIcon:
                    isSaveEnabled ? appTheme.primaryColorV2 : Colors.grey,
                bg: appTheme.transparentColor,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: TitleTextFieldV3(
        controller: _searchController,
        prefix: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: SvgPicture.asset(Assets.icons.icSearch.path,
              width: 16, height: 16),
        ),
        prefixIconConstraints: const BoxConstraints(maxWidth: 32),
        suffixIcon: _searchController.text.isNotEmpty
            ? GestureDetector(
                onTap: () {
                  _searchController.clear();
                  setState(() {
                    _searchQuery = '';
                  });
                  _loadEvents(refresh: true);
                },
                child: Icon(
                  Icons.clear,
                  size: 16,
                  color: appTheme.grayV2,
                ),
              )
            : null,
        hintText: "Search events",
        hintStyle: AppStyle.regular12(color: appTheme.grayV2),
        radius: BorderRadius.circular(32),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        filled: true,
      ),
    );
  }

  Widget _buildEventsList() {
    return StreamBuilder<List<EventModels>>(
      stream: _events.stream,
      initialData: _events.value,
      builder: (context, eventSnapshot) {
        return StreamBuilder<String>(
          stream: _selectedEventIdController.stream,
          initialData: _selectedEventId,
          builder: (context, selectedIdSnapshot) {
            final allEvents = eventSnapshot.data ?? [];
            final selectedId = selectedIdSnapshot.data ?? '';

            if (_isLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (allEvents.isEmpty) {
              return _buildEmptyState();
            }

            final filteredEvents = _events.value;
            if (filteredEvents.isEmpty && _searchQuery.isNotEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.search_off,
                      size: 64,
                      color: appTheme.grayV2,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No events match your search',
                      style: AppStyle.regular16(color: appTheme.grayV2),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: _refreshEvents,
              child: ListView.builder(
                controller: _scrollController,
                shrinkWrap: true,
                itemCount:
                    _buildGroupedEventList(filteredEvents, selectedId).length,
                itemBuilder: (context, index) {
                  return _buildGroupedEventList(
                      filteredEvents, selectedId)[index];
                },
              ),
            );
          },
        );
      },
    );
  }

  List<Widget> _buildGroupedEventList(
      List<EventModels> events, String selectedId) {
    final widgets = <Widget>[];

    if (_groupedEvents == null || events.isEmpty) {
      return widgets;
    }

    // Filter grouped events to only show events that match the current filter
    final filteredGroupedEvents = <String, List<EventModels>>{};
    for (final entry in _groupedEvents!.entries) {
      final filteredEventsInGroup = entry.value
          .where((event) => events.any((e) => e.uuid == event.uuid))
          .toList();
      if (filteredEventsInGroup.isNotEmpty) {
        filteredGroupedEvents[entry.key] = filteredEventsInGroup;
      }
    }

    for (final entry in filteredGroupedEvents.entries) {
      final date = entry.key;
      final eventsInGroup = entry.value;

      widgets.add(
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                date,
                style: AppStyle.regular14(color: appTheme.labelColor),
              ),
            ),
            ...eventsInGroup.map((event) {
              final isSelected = event.uuid == selectedId;
              return EventCardWidget(
                event: event,
                isSelected: isSelected,
                onTap: () => _toggleEventSelection(event),
                showSelectionBorder: true,
              );
            }).toList(),
            const SizedBox(height: 8),
          ],
        ),
      );
    }

    return widgets;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: appTheme.grayV2,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty
                ? 'No events found'
                : 'No events match your search',
            style: AppStyle.regular16(color: appTheme.grayV2),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Try a different search term',
              style: AppStyle.regular14(color: appTheme.hintColor),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
