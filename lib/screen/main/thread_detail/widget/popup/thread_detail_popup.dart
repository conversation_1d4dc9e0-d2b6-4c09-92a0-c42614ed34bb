import 'package:family_app/main.dart';
import 'package:family_app/widget/image/avatar.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/popup/popup.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/extension.dart';

import 'add_member_bts.dart';

class ThreadDetailPopup extends StatelessWidget {
  final List<Account> memberList;
  final List<Account> memberListChat;
  final void Function(List<String> selectedMembers) onUpdateSelectedMember;
  final void Function(Account member) onChatWithMember;

  const ThreadDetailPopup({
    super.key,
    required this.memberList,
    required this.memberListChat,
    required this.onUpdateSelectedMember,
    required this.onChatWithMember,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPopup(
      content: Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                Navigator.pop(context);
                AddMemberThreadBts.show(
                  context,
                  selectedMembers: memberList.map((member) => member.familyMemberUuid ?? '').toList(),
                  onSelected: (selectedAccounts) {
                    onUpdateSelectedMember(selectedAccounts.map((a) => a.familyMemberUuid ?? '').toList());
                  },
                );
              },
              child: Row(
                children: [
                  Text(LocaleKeys.add_or_remove_member.tr()),
                ],
              ),
            ),
            const Divider(),
            SingleChildScrollView(child: _showMemberInThread(context)),
          ],
        ),
      ),
      child: SizedBox(
        width: 44,
        height: 44,
        child: Center(
          child: ImageAssetCustom(
            imagePath: Assets.icons.iconTablerPencil.path,
            width: 24,
            height: 24,
            color: appTheme.grayV2,
          ),
        ),
      ),
    );
  }

  Widget _showMemberInThread(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: padding(horizontal: 0),
      separatorBuilder: (context, index) => Divider(color: appTheme.borderColor),
      itemCount: memberListChat.length,
      itemBuilder: (context, index) => _buildMember(context, index, memberListChat),
    );
  }

  Widget _buildMember(BuildContext context, int index, List<Account> membersList) {
    final member = membersList[index];
    return GestureDetector(
      onTap: () => onChatWithMember(member),
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: padding(top: 5, bottom: 5),
        child: Row(
          children: [
            Avatar(member.photoUrl, name: member.fullName,  size: 25),
            const SizedBox(width: 12),
            Expanded(child: Text(member.fullName ?? '', style: AppStyle.regular14())),
            const SizedBox(width: 12),
          ],
        ),
      ),
    );
  }
}
