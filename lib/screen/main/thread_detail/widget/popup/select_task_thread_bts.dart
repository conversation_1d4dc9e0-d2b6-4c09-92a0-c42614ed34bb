import 'package:flutter/material.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/main.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/textfield/title_text_field_v3.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SelectTaskThreadBTS extends StatefulWidget {
  final List<String>? initialSelectedTaskUuids;
  final String? initialListUuid;
  final Function(List<Item> selectedTasks) onSelected;

  const SelectTaskThreadBTS({
    Key? key,
    this.initialSelectedTaskUuids,
    this.initialListUuid,
    required this.onSelected,
  }) : super(key: key);

  static Future<List<Item>?> show(
    BuildContext context, {
    List<String>? initialSelectedTaskUuids,
    String? initialListUuid,
    String? title,
  }) async {
    List<Item>? result;
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return FractionallySizedBox(
          heightFactor: 0.55,
          child: Material(
            color: Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            child: SelectTaskThreadBTS(
              initialSelectedTaskUuids: initialSelectedTaskUuids,
              initialListUuid: initialListUuid,
              onSelected: (tasks) {
                result = tasks;
              },
            ),
          ),
        );
      },
    );
    return result;
  }

  @override
  State<SelectTaskThreadBTS> createState() => _SelectTaskThreadBTSState();
}

class _SelectTaskThreadBTSState extends State<SelectTaskThreadBTS> {
  final IListRepository listRepository = locator.get();
  final AccountService accountService = locator.get();

  List<Item> _allTasks = [];
  Map<String, List<Item>> _groupedTasks = {};
  Map<String, String> _groupNames = {}; // listUuid -> parent_name
  List<String> _selectedTaskUuids = [];
  bool _loadingTasks = true;
  String _search = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedTaskUuids = widget.initialSelectedTaskUuids ?? [];
    _fetchAllTasks();
    _searchController.addListener(() {
      _onSearchChanged(_searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchAllTasks() async {
    setState(() => _loadingTasks = true);
    final lists =
        await listRepository.getListByFamilyId(accountService.familyId);
    List<Item> allItems = [];
    Map<String, String> groupNames = {};
    for (final list in lists) {
      final items =
          list.items ?? await listRepository.getAllItemInList(list.uuid!);
      for (final item in items) {
        if (item.uuid != null) {
          allItems.add(item);
          groupNames[item.listUuid ?? list.uuid!] =
              item.parentName ?? list.name ?? '';
        }
      }
    }
    setState(() {
      _allTasks = allItems;
      _groupNames = groupNames;
      _groupedTasks = _groupByListUuid(_applySearch(allItems, _search));
      _loadingTasks = false;
    });
  }

  Map<String, List<Item>> _groupByListUuid(List<Item> items) {
    final map = <String, List<Item>>{};
    for (final item in items) {
      final key = item.listUuid ?? '';
      map.putIfAbsent(key, () => []).add(item);
    }
    return map;
  }

  List<Item> _applySearch(List<Item> items, String search) {
    if (search.isEmpty) return items;
    return items
        .where((item) =>
            (item.name ?? '').toLowerCase().contains(search.toLowerCase()))
        .toList();
  }

  void _onSearchChanged(String value) {
    setState(() {
      _search = value;
      _groupedTasks = _groupByListUuid(_applySearch(_allTasks, value));
    });
  }

  void _onTaskTap(Item item) {
    final group = item.listUuid ?? '';
    final selectedGroup = _selectedTaskUuids.isNotEmpty
        ? _allTasks
                .firstWhere((i) => _selectedTaskUuids.contains(i.uuid))
                .listUuid ??
            ''
        : null;
    setState(() {
      if (_selectedTaskUuids.contains(item.uuid)) {
        _selectedTaskUuids.remove(item.uuid);
      } else {
        // Only allow selecting items in the same group as the first selected item
        if (selectedGroup == null || selectedGroup == group) {
          _selectedTaskUuids.add(item.uuid!);
        }
      }
    });
  }

  bool _isGroupDisabled(String group) {
    if (_selectedTaskUuids.isEmpty) return false;
    final selectedGroup = _allTasks
            .firstWhere((i) => _selectedTaskUuids.contains(i.uuid))
            .listUuid ??
        '';
    return group != selectedGroup;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header like event selection
          Padding(
            padding:
                const EdgeInsets.only(top: 12, left: 16, right: 16, bottom: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ButtonIcon(
                  Assets.icons.icClose.path,
                  size: 44,
                  sizeIcon: 24,
                  colorIcon: appTheme.grayV2,
                  () => Navigator.of(context).pop(),
                  bg: appTheme.transparentColor,
                ),
                Text('Select Task', style: AppStyle.textLgS),
                ButtonIcon(
                  Assets.icons.check.path,
                  size: 44,
                  sizeIcon: 24,
                  colorIcon: _selectedTaskUuids.isNotEmpty
                      ? appTheme.primaryColorV2
                      : appTheme.grayV2,
                  () {
                    if (_selectedTaskUuids.isNotEmpty) {
                      final selected = _allTasks
                          .where(
                              (item) => _selectedTaskUuids.contains(item.uuid))
                          .toList();
                      widget.onSelected(selected);
                      Navigator.of(context).pop();
                    }
                  },
                  bg: appTheme.transparentColor,
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: TitleTextFieldV3(
              controller: _searchController,
              prefix: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: SvgPicture.asset(Assets.icons.icSearch.path,
                    width: 16, height: 16),
              ),
              prefixIconConstraints: const BoxConstraints(maxWidth: 32),
              hintText: 'Search tasks',
              hintStyle: AppStyle.regular12(color: appTheme.grayV2),
              radius: BorderRadius.circular(32),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              filled: true,
            ),
          ),
          Expanded(
            child: _loadingTasks
                ? const Center(child: CircularProgressIndicator())
                : _groupedTasks.isEmpty
                    ? const Center(child: Text('No tasks found'))
                    : ListView(
                        children: _groupedTasks.entries.expand((entry) {
                          final group = entry.key;
                          final items = entry.value;
                          final groupDisabled = _isGroupDisabled(group);
                          final groupName = _groupNames[group] ?? '';
                          return [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              child: Text(groupName,
                                  style: AppStyle.regular14(
                                      color: appTheme.labelColor)),
                            ),
                            ...items.map((item) {
                              final selected =
                                  _selectedTaskUuids.contains(item.uuid);
                              final disabled = groupDisabled && !selected;
                              return ListTile(
                                onTap: disabled ? null : () => _onTaskTap(item),
                                leading: Checkbox(
                                  value: selected,
                                  onChanged:
                                      disabled ? null : (_) => _onTaskTap(item),
                                ),
                                title: Text(item.name ?? '',
                                    style: AppStyle.regular14(
                                        color: disabled
                                            ? appTheme.grayColor
                                            : null)),
                                subtitle: item.due_date != null &&
                                        item.due_date!.isNotEmpty
                                    ? Text('Due:  ${item.due_date}',
                                        style: AppStyle.regular12(
                                            color: disabled
                                                ? appTheme.grayColor
                                                : null))
                                    : null,
                                enabled: !disabled,
                              );
                            }).toList(),
                          ];
                        }).toList(),
                      ),
          ),
        ],
      ),
    );
  }
}
