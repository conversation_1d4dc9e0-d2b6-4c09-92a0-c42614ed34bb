import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data_summary.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/thread_message.dart';
import 'package:family_app/screen/main/thread_detail/widget/message_poll/poll_card.dart';

class ThreadMessagePoll extends StatelessWidget {
  final ThreadMessage message;
  final List<Account> memberList;
  final VoidCallback? onShowVoting;
  final String? currentUserId;

  const ThreadMessagePoll({
    super.key,
    required this.message,
    required this.memberList,
    this.onShowVoting,
    required this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    final ThreadMessageRelatedData relatedData = message.relatedData!;
    final bool isVoted =
        relatedData.hasUserVoted(currentUserId); // Use currentUserId
    final bool isEnd = relatedData.isEnded();
    final ThreadMessageRelatedDataSummary relatedDataSummary =
        relatedData.getDecodedSummary();
    final relatedDataItems = relatedData.getItemsWithVoters(memberList);

    void showVoting() {
      if (onShowVoting != null) {
        onShowVoting!();
      }
    }

    return PollCard(
      relatedData: relatedData,
      relatedDataSummary: relatedDataSummary,
      relatedDataItems: relatedDataItems,
      isVoted: isVoted,
      isEnd: isEnd,
      showVoting: showVoting,
    );
  }
}
