import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';

import 'package:family_app/screen/main/thread_detail/widget/message_poll/poll_card_voter_list.dart';

class VoteCardOption extends StatelessWidget {
  const VoteCardOption({
    Key? key,
    required this.isVoted,
    required this.option,
    required this.countVote,
    required this.totalCountVote,
    required this.voteCardOptionWidth,
    this.votedColor,
  }) : super(key: key);

  final bool isVoted;
  final ItemWithVoters option;
  final int countVote;
  final int totalCountVote;
  final double voteCardOptionWidth;
  final Color? votedColor;

  static const double voterSize = 18.0;
  static const double optionVerticalPadding = 8.0;
  static const int totalVoterDisplayConfig = 3;

  @override
  Widget build(BuildContext context) {
    double getWidthOfVotedOption(int countVote, int totalCountVote) {
      if (totalCountVote == 0) return 0.0;
      return ((countVote / totalCountVote) * voteCardOptionWidth).roundToDouble();
    }

    final widthOfVotedOption = getWidthOfVotedOption(countVote, totalCountVote);

    double getOptionRightPadding() {
      final voters = option.voters ?? [];
      final bool isShowMore = voters.length > totalVoterDisplayConfig;
      final int totalVoterDisplayed = isShowMore ? totalVoterDisplayConfig : voters.length;
      final int totalItemDisplayed = isShowMore ? totalVoterDisplayed + 1 : totalVoterDisplayed;

      return voterSize * totalItemDisplayed;
    }

    return Stack(
      children: [
        if (isVoted)
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: Container(width: widthOfVotedOption, decoration: BoxDecoration(color: votedColor ?? appTheme.orangeColorV2)),
          ),
        Container(
          padding: EdgeInsets.only(
            left: 12,
            right: getOptionRightPadding(),
            top: optionVerticalPadding,
            bottom: optionVerticalPadding,
          ),
          child: _buildOptionName(
            option.name,
          ),
        ),
        PollCardVoterList(
          voters: option.voters ?? [],
          totalVoterDisplayConfig: totalVoterDisplayConfig,
          voterSize: voterSize,
          optionVerticalPadding: optionVerticalPadding,
        ),
      ],
    );
  }

  Widget _buildOptionName(String name) {
    return Container(
      alignment: Alignment.centerLeft,
      constraints: const BoxConstraints(minHeight: voterSize),
      child: Text(name, style: AppStyle.regular12(color: Colors.black)),
    );
  }
}
