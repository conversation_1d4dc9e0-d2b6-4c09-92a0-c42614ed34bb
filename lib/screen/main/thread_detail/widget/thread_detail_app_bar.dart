import 'package:family_app/screen/main/thread/widget/thread_app_bar.dart';
import 'package:family_app/screen/main/thread/widget/thread_avatar.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/thread_family.dart';

class ThreadDetailAppBar extends StatelessWidget {
  final List<Members> members;
  final String? threadName;
  final String? currentUserId;
  final VoidCallback? onBack;
  final List<Widget>? actions;

  const ThreadDetailAppBar({
    super.key,
    required this.members,
    required this.threadName,
    required this.currentUserId,
    this.actions,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return ThreadAppBar(
      titleView: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ThreadAvatar(
              members: members,
              currentUserId: currentUserId,
            ),
            const SizedBox(width: 16),
            Flexible(
              child: Text(
                threadName ?? '',
                style: AppStyle.medium16(),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
      onBack: onBack,
      actions: actions,
    );
  }
}
