import 'dart:io';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';

/// Enhanced video preview widget that supports thumbnails and playback
class VideoPreviewWidget extends StatefulWidget {
  final String videoPath;
  final double width;
  final double height;
  final double borderRadius;
  final bool showPlayButton;
  final bool autoPlay;
  final bool loop;
  final VoidCallback? onTap;
  final bool showLoadingOverlay;

  const VideoPreviewWidget({
    super.key,
    required this.videoPath,
    this.width = 200,
    this.height = 200,
    this.borderRadius = 12,
    this.showPlayButton = true,
    this.autoPlay = false,
    this.loop = false,
    this.onTap,
    this.showLoadingOverlay = false,
  });

  @override
  State<VideoPreviewWidget> createState() => _VideoPreviewWidgetState();
}

class _VideoPreviewWidgetState extends State<VideoPreviewWidget> {
  VideoPlayerController? _controller;
  String? _thumbnailPath;
  bool _isLoading = true;
  bool _isPlaying = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      // Generate thumbnail first
      await _generateThumbnail();

      // Initialize video controller
      _controller = VideoPlayerController.file(File(widget.videoPath));
      await _controller!.initialize();

      if (widget.autoPlay) {
        _controller!.play();
        _controller!.setLooping(widget.loop);
      }

      _controller!.addListener(_onVideoStateChanged);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  Future<void> _generateThumbnail() async {
    try {
      // Generate thumbnail first
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: widget.videoPath,
        thumbnailPath:
            (await Directory.systemTemp.createTemp('video_thumbnails')).path,
        imageFormat: ImageFormat.JPEG,
        quality: 80,
        maxWidth: widget.width.toInt(),
        maxHeight: widget.height.toInt(),
      );

      if (mounted && thumbnailPath != null) {
        setState(() {
          _thumbnailPath = thumbnailPath;
        });
      }
    } catch (e) {
      AppLogger.e('Error generating thumbnail: $e');
    }
  }

  void _onVideoStateChanged() {
    if (mounted && _controller != null) {
      setState(() {
        _isPlaying = _controller!.value.isPlaying;
      });
    }
  }

  void _togglePlayPause() {
    if (_controller == null) return;

    if (_isPlaying) {
      _controller!.pause();
    } else {
      _controller!.play();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap ?? _togglePlayPause,
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          color: appTheme.backgroundWhite,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: [
            BoxShadow(
              color: appTheme.blackTextV2.withAlpha(26),
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Stack(
            children: [
              // Video content
              _buildVideoContent(),

              // Play button overlay
              if (widget.showPlayButton &&
                  !_isPlaying &&
                  !_isLoading &&
                  !_hasError)
                _buildPlayButton(),

              // Loading overlay
              if (_isLoading || widget.showLoadingOverlay)
                _buildLoadingOverlay(),

              // Error overlay
              if (_hasError) _buildErrorOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoContent() {
    if (_hasError) {
      return _buildErrorPlaceholder();
    }

    if (_controller != null && _controller!.value.isInitialized) {
      return AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: VideoPlayer(_controller!),
      );
    }

    // Show thumbnail while loading
    if (_thumbnailPath != null) {
      return Image.file(
        File(_thumbnailPath!),
        width: widget.width,
        height: widget.height,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildErrorPlaceholder();
        },
      );
    }

    return _buildErrorPlaceholder();
  }

  Widget _buildErrorPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.black.withAlpha(180),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.videocam_off,
              color: Colors.white,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              'Video not available',
              style: AppStyle.regular12(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayButton() {
    return Center(
      child: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(120),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.play_arrow,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(80),
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: const Center(
        child: SizedBox(
          width: 36,
          height: 36,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorOverlay() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(120),
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: appTheme.errorV2,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Failed to load video',
              style: AppStyle.regular12(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}

/// Simple video thumbnail widget for static display
class VideoThumbnailWidget extends StatefulWidget {
  final String videoPath;
  final double width;
  final double height;
  final double borderRadius;
  final Widget? placeholder;

  const VideoThumbnailWidget({
    super.key,
    required this.videoPath,
    this.width = 200,
    this.height = 200,
    this.borderRadius = 12,
    this.placeholder,
  });

  @override
  State<VideoThumbnailWidget> createState() => _VideoThumbnailWidgetState();
}

class _VideoThumbnailWidgetState extends State<VideoThumbnailWidget> {
  String? _thumbnailPath;
  bool _isLoading = true;
  final bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _generateThumbnail();
  }

  Future<void> _generateThumbnail() async {
    try {
      // Generate thumbnail first
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: widget.videoPath,
        thumbnailPath:
            (await Directory.systemTemp.createTemp('video_thumbnails')).path,
        imageFormat: ImageFormat.JPEG,
        quality: 80,
        maxWidth: widget.width.toInt(),
        maxHeight: widget.height.toInt(),
      );

      if (mounted) {
        setState(() {
          _thumbnailPath = thumbnailPath;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.e('Error generating thumbnail: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow: [
          BoxShadow(
            color: appTheme.blackTextV2.withAlpha(26),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: Stack(
          children: [
            // Thumbnail image
            if (_thumbnailPath != null && !_hasError)
              Image.file(
                File(_thumbnailPath!),
                width: widget.width,
                height: widget.height,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholder();
                },
              )
            else
              _buildPlaceholder(),

            // Play icon overlay
            Center(
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(120),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),

            // Loading overlay
            if (_isLoading)
              Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  color: Colors.black.withAlpha(80),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.black.withAlpha(180),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.videocam,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 4),
            Text(
              'Video',
              style: AppStyle.regular12(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
