import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';

class PollCTAButton extends StatelessWidget {
  final String text;
  final GestureTapCallback showVoting;

  const PollCTAButton({
    Key? key,
    required this.text,
    required this.showVoting,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: showVoting,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: appTheme.primaryColorV2.withAlpha(31),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: AppStyle.regular14(color: appTheme.primaryColorV2),
          ),
        ),
      ),
    );
  }
}
