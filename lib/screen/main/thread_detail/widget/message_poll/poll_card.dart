import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data.dart';
import 'package:family_app/data/model/thread_message/thread_message_related_data_summary.dart';
import 'package:family_app/screen/main/thread_detail/widget/message_poll/poll_cta_button.dart';
import 'package:family_app/screen/main/thread_detail/widget/message_poll/poll_title_info.dart';
import 'package:family_app/extension.dart';

import '../../poll/poll_utils.dart';
import 'poll_card_option.dart';

class PollCard extends StatelessWidget {
  final ThreadMessageRelatedData relatedData;
  final ThreadMessageRelatedDataSummary relatedDataSummary;
  final List<ItemWithVoters> relatedDataItems;
  final bool isVoted;
  final bool isEnd;
  final GestureTapCallback showVoting;

  const PollCard({
    Key? key,
    required this.relatedData,
    required this.relatedDataSummary,
    required this.relatedDataItems,
    required this.isVoted,
    required this.isEnd,
    required this.showVoting,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    const double voteCardPadding = 60;
    const double voteCardOptionPadding = 12;
    final double voteCardWidth = context.screenSize.width - (voteCardPadding * 2);
    final double voteCardOptionWidth = voteCardWidth - (voteCardOptionPadding * 2);
    final voteCounts = relatedDataItems.map((item) => relatedData.getVoteCountByItemUuid(item.uuid!)).toList();
    final optionVotedColors = PollUtils.getVotedColorsForOptions(voteCounts);

    return Container(
      width: voteCardWidth,
      padding: const EdgeInsets.all(12),
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: appTheme.blackTextV2.withAlpha(26),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: SizedBox(
        width: voteCardOptionWidth,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            PollTitleInfo(
              voteName: relatedData.name ?? '',
              isVoted: isVoted,
              isEnded: isEnd,
              fromDate: relatedData.fromDate,
              toDate: relatedData.toDate,
            ),
            const SizedBox(height: 8.0),
            SizedBox(
              width: double.infinity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int i = 0; i < relatedDataItems.length; i++) ...[
                    Container(
                      width: double.infinity,
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        color: appTheme.transparentWhiteColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(99),
                        ),
                      ),
                      child: PollCardOption(
                        isVoted: isVoted,
                        option: relatedDataItems[i],
                        countVote: voteCounts[i],
                        totalCountVote: relatedDataSummary.total,
                        voteCardOptionWidth: voteCardOptionWidth,
                        votedColor: optionVotedColors[i],
                      ),
                    ),
                    if (i != relatedDataItems.length - 1) const SizedBox(height: 4),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 8.0),
            PollCTAButton(
              text: isVoted || isEnd ? LocaleKeys.view.tr() : LocaleKeys.voting.tr(),
              showVoting: showVoting,
            ),
          ],
        ),
      ),
    );
  }
}
