import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';

class PollTitleInfo extends StatelessWidget {
  final String voteName;
  final bool isVoted;
  final bool isEnded;
  final bool isEnableInfo;
  final String? fromDate;
  final String? toDate;

  const PollTitleInfo({
    Key? key,
    required this.voteName,
    required this.isVoted,
    this.isEnded = false,
    this.isEnableInfo = true,
    this.fromDate,
    this.toDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool showInfo = isEnableInfo && (toDate != null);
    final String endTimeString = toDate != null && toDate.toThreadDateTime != null ? DateFormat('dd MMM HH:mm').format(toDate!.toThreadDateTime!) : '';
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(voteName, textAlign: TextAlign.center, style: AppStyle.bold16()),
        ),
        if (showInfo && !isEnded)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              'Voting: ends at $endTimeString',
              textAlign: TextAlign.center,
              style: AppStyle.regular12(color: appTheme.primaryColorV2),
            ),
          ),
        if (isEnded)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              LocaleKeys.ended.tr(),
              textAlign: TextAlign.center,
              style: AppStyle.textSmS.copyWith(color: appTheme.errorV2),
            ),
          ),
      ],
    );
  }
}
