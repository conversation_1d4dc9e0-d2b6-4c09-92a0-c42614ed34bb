import 'package:flutter/material.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/config/theme/style/style_theme.dart';

class PollCardVoterList extends StatelessWidget {
  const PollCardVoterList({
    Key? key,
    required this.voters,
    this.votersIndent = 8.0,
    required this.voterSize,
    required this.optionVerticalPadding,
    this.totalVoterDisplayConfig = 3,
    this.voterOverlapWidth = 10,
  }) : super(key: key);

  final List<Account> voters;
  final double votersIndent;
  final double voterSize;
  final double optionVerticalPadding;
  final int totalVoterDisplayConfig;
  final double voterOverlapWidth;

  @override
  Widget build(BuildContext context) {
    final bool isShowMore = voters.length > totalVoterDisplayConfig;
    final int totalVoterMore = voters.length - totalVoterDisplayConfig;

    Color getRandomSelectionColor(int index) {
      final colors = themeUtil.selectionColor();
      return colors[index >= colors.length ? 0 : index];
    }

    double getVoterPosition(int voterIndex) {
      final int totalVoter = voters.length;
      final int totalVoterDisplayed = isShowMore ? totalVoterDisplayConfig : totalVoter;
      final int totalItemDisplayed = isShowMore ? totalVoterDisplayed + 1 : totalVoterDisplayed;
      final isOnly = totalVoter == 1;

      if (isOnly) {
        return votersIndent;
      }
      return (totalItemDisplayed - voterIndex) * voterOverlapWidth;
    }

    return SizedBox(
      height: voterSize + (optionVerticalPadding * 2),
      child: Stack(
        children: [
          ...voters
              .asMap()
              .entries
              .map((entry) {
                final int voterIndex = entry.key;
                final Account voter = entry.value;
                return Positioned(
                  top: optionVerticalPadding,
                  right: getVoterPosition(voterIndex),
                  child: SizedBox(
                    child: CircleAvatarCustom(
                      size: voterSize,
                      imageUrl: voter.photoUrl ?? '',
                      borderColor: voterIndex % 2 == 0 ? appTheme.borderColorV2 : Colors.white,
                      borderWidth: voterIndex % 2 == 0 ? 0 : 1,
                      color: appTheme.whiteText,
                      defaultWidget: CircleItem(
                        size: 1,
                        backgroundColor: getRandomSelectionColor(voterIndex),
                        child: Center(
                          child: Text(
                            ((voter.fullName ?? '').isNotEmpty ? (voter.fullName?.substring(0, 1) ?? '') : '').toUpperCase(),
                            style: AppStyle.text2XsR.copyWith(color: Colors.white, fontSize: 8),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              })
              .toList()
              .take(totalVoterDisplayConfig),
          if (isShowMore)
            Positioned(
              top: optionVerticalPadding,
              right: 8,
              child: SizedBox(
                width: voterSize,
                height: voterSize,
                child: Container(
                  decoration: ShapeDecoration(
                    color: Colors.black.withAlpha(14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(99),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                      bottom: 1.0,
                      right: 1.0,
                    ),
                    child: Center(
                      child: Text(
                        '+$totalVoterMore',
                        textAlign: TextAlign.center,
                        style: AppStyle.textXsS.copyWith(fontSize: 8, color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
