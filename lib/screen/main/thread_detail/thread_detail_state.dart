import 'model/pending_message.dart';
import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/data/model/thread_message.dart';

enum ThreadDetailStatus { none, loading, success, error }

enum ThreadInputCommand { voting }

class ThreadDetailState extends BaseState {
  final ThreadDetailStatus status;
  final ThreadFamily? threadDetail;
  final List<ThreadMessage> messages;
  final bool isWaitingForResponse;
  final String? errorMessage;
  final List<Account> memberListChat;
  final List<Account> memberList;
  final List<PendingMessage> pendingMessages;

  ThreadDetailState({
    this.status = ThreadDetailStatus.none,
    this.threadDetail,
    this.messages = const [],
    this.isWaitingForResponse = false,
    this.errorMessage,
    this.memberListChat = const [],
    this.memberList = const [],
    this.pendingMessages = const [],
  });

  ThreadDetailState copyWith({
    ThreadDetailStatus? status,
    ThreadFamily? threadDetail,
    List<ThreadMessage>? messages,
    bool? isWaitingForResponse,
    String? errorMessage,
    List<Account>? memberListChat,
    List<Account>? memberList,
    List<PendingMessage>? pendingMessages,
  }) {
    return ThreadDetailState(
      status: status ?? this.status,
      threadDetail: threadDetail ?? this.threadDetail,
      messages: messages ?? this.messages,
      isWaitingForResponse: isWaitingForResponse ?? this.isWaitingForResponse,
      errorMessage: errorMessage ?? this.errorMessage,
      memberListChat: memberListChat ?? this.memberListChat,
      memberList: memberList ?? this.memberList,
      pendingMessages: pendingMessages ?? this.pendingMessages,
    );
  }

  @override
  List<Object?> get props => [status, threadDetail, messages, isWaitingForResponse, errorMessage, memberListChat, memberList, pendingMessages];
}
