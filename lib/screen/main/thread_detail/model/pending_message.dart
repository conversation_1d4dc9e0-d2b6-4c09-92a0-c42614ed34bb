import 'dart:io';

/// Enum for all pending message types
enum PendingMessageType { text, image, video, file, audio, location, poll, event, task, list }

/// Represents a pending message (optimistic UI) for any type
class PendingMessage {
  final String id;
  final PendingMessageType type;
  final String? text;
  final File? file;
  final String? fileName;
  final String? filePath;
  final bool isImage;
  final bool isVideo;
  final DateTime createdAt;
  final bool isSending;
  final String? error;
  final Map<String, dynamic>? extraData; // for poll, event, etc.

  const PendingMessage({
    required this.id,
    required this.type,
    this.text,
    this.file,
    this.fileName,
    this.filePath,
    this.isImage = false,
    this.isVideo = false,
    required this.createdAt,
    this.isSending = true,
    this.error,
    this.extraData,
  });

  PendingMessage copyWith({
    String? id,
    PendingMessageType? type,
    String? text,
    File? file,
    String? fileName,
    String? filePath,
    bool? isImage,
    bool? isVideo,
    DateTime? createdAt,
    bool? isSending,
    String? error,
    Map<String, dynamic>? extraData,
  }) {
    return PendingMessage(
      id: id ?? this.id,
      type: type ?? this.type,
      text: text ?? this.text,
      file: file ?? this.file,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      isImage: isImage ?? this.isImage,
      isVideo: isVideo ?? this.isVideo,
      createdAt: createdAt ?? this.createdAt,
      isSending: isSending ?? this.isSending,
      error: error ?? this.error,
      extraData: extraData ?? this.extraData,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PendingMessage &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
} 