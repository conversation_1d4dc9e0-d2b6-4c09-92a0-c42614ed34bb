import 'dart:convert';
import 'dart:io';

import 'package:dartx/dartx_io.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/file_model.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/upload.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:image_picker/image_picker.dart';

class TripTransferUpsertCubit extends BaseCubit<TripTransferUpsertState> {
  final IActivityRepository activityRepository;
  final TripTransferUpsertParameter parameter;
  final IFamilyRepository familyRepository = locator.get<IFamilyRepository>();

  TripTransferUpsertCubit({required this.activityRepository, required this.parameter})
      : super(TripTransferUpsertState(activity: parameter.activity));

  final AccountService accountService = locator.get();

  @override
  void onInit() {
    super.onInit();
    initData();
  }

  void initData() {
    final selectedTransport = state.transports.first;
    final startTime = parameter.activity.fromDate?.toDateTime();
    // Departure date and time = start time + dayIndex days
    final departureDate = startTime?.add(Duration(days: parameter.dayIndex));
    final arrivalDate = departureDate;
    
    // If editing existing transfer, populate fields
    if (!parameter.addNew && parameter.activityIndex != null) {
      final itineraries = parameter.activity.itinerary ?? [];
      if (parameter.dayIndex < itineraries.length) {
        final selectedItinerary = itineraries[parameter.dayIndex];
        final activities = selectedItinerary.activities ?? [];
        if (parameter.activityIndex! < activities.length) {
          final activity = activities[parameter.activityIndex!];
          final transfer = activity.transfer;
          if (transfer != null) {
            emit(state.copyWith(
              selectedTransport: state.transports.firstWhere(
                (t) => t.name.toLowerCase() == transfer.type?.toLowerCase(),
                orElse: () => selectedTransport,
              ),
              ticketNo: transfer.ticketNo,
              departureLocation: transfer.fromLocation,
              arrivalLocation: transfer.toLocation,
              departureDate: transfer.fromTime?.toLocalDT,
              arrivalDate: transfer.toTime?.toLocalDT,
              departureTime: transfer.fromTime != null 
                  ? TimeOfDay.fromDateTime(transfer.fromTime!.toLocalDT)
                  : null,
              arrivalTime: transfer.toTime != null 
                  ? TimeOfDay.fromDateTime(transfer.toTime!.toLocalDT)
                  : null,
              selectedCurrency: transfer.currency ?? 'USD',
              quantity: transfer.quantity ?? 1,
              price: transfer.price ?? 0.0,
              note: transfer.note,
              dayIndex: parameter.dayIndex,
              isTicketNoValid: transfer.ticketNo?.isNotEmpty ?? false,
              isDepartureLocationValid: transfer.fromLocation?.isNotEmpty ?? false,
              isArrivalLocationValid: transfer.toLocation?.isNotEmpty ?? false,
              files: transfer.attachments ?? [],
            ));
            return;
          }
        }
      }
      // If no real transfer found, but initialTransfer is provided, use it
      if (parameter.initialTransfer != null) {
        final transfer = parameter.initialTransfer!;
        emit(state.copyWith(
          selectedTransport: state.transports.firstWhere(
            (t) => t.name.toLowerCase() == transfer.type?.toLowerCase(),
            orElse: () => selectedTransport,
          ),
          ticketNo: transfer.ticketNo,
          departureLocation: transfer.fromLocation,
          arrivalLocation: transfer.toLocation,
          departureDate: transfer.fromTime?.toLocalDT,
          arrivalDate: transfer.toTime?.toLocalDT,
          departureTime: transfer.fromTime != null 
              ? TimeOfDay.fromDateTime(transfer.fromTime!.toLocalDT)
              : null,
          arrivalTime: transfer.toTime != null 
              ? TimeOfDay.fromDateTime(transfer.toTime!.toLocalDT)
              : null,
          selectedCurrency: transfer.currency ?? 'USD',
          quantity: transfer.quantity ?? 1,
          price: transfer.price ?? 0.0,
          note: transfer.note,
          dayIndex: parameter.dayIndex,
          isTicketNoValid: transfer.ticketNo?.isNotEmpty ?? false,
          isDepartureLocationValid: transfer.fromLocation?.isNotEmpty ?? false,
          isArrivalLocationValid: transfer.toLocation?.isNotEmpty ?? false,
          files: transfer.attachments ?? [],
        ));
        return;
      }
    }

    // Default initialization for new transfer
    emit(state.copyWith(
        selectedTransport: selectedTransport,
        departureDate: departureDate,
        arrivalDate: arrivalDate,
        dayIndex: parameter.dayIndex));
  }

  void updateSelectedCurrency(String currency) {
    emit(state.copyWith(selectedCurrency: currency));
  }

  void updateQuantity(int quantity) {
    emit(state.copyWith(quantity: quantity));
  }

  void updatePrice(double price) {
    emit(state.copyWith(price: price));
  }

  void updateNote(String note) {
    emit(state.copyWith(note: note));
  }

  void updateTicketNo(String ticketNo) {
    emit(state.copyWith(ticketNo: ticketNo, isTicketNoValid: ticketNo.isNotEmpty));
  }

  void updateDepartureLocation(String location) {
    emit(state.copyWith(departureLocation: location, isDepartureLocationValid: location.isNotEmpty));
  }

  void updateArrivalLocation(String location) {
    emit(state.copyWith(arrivalLocation: location, isArrivalLocationValid: location.isNotEmpty));
  }

  void updateSelectedTransport(Transport transport) {
    emit(state.copyWith(selectedTransport: transport));
  }

  Future<void> removeFile(int index, BuildContext context) async {
    final files = List<FileModel>.from(state.files);
    if (index < 0 || index >= files.length) {
      AppLogger.e('Remove file failed: invalid index $index');
      return;
    }
    final file = files[index];
    // Confirm if remote
    if (file.fileType != 'local' && file.fileUuid != null) {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (ctx) => AlertDialog(
          title: const Text('Delete File'),
          content: const Text(
              'Are you sure you want to delete this file from the server?'),
          actions: [
            TextButton(
                onPressed: () => Navigator.of(ctx).pop(false),
                child: const Text('No')),
            TextButton(
                onPressed: () => Navigator.of(ctx).pop(true),
                child: const Text('Yes')),
          ],
        ),
      );
      if (confirm != true) return;
    }
    await _removeAndPersistFile(index);
  }

  Future<void> _removeAndPersistFile(int index) async {
    final files = List<FileModel>.from(state.files);
    if (index < 0 || index >= files.length) {
      AppLogger.e('Remove file failed: invalid index $index');
      return;
    }
    final file = files[index];
    try {
      bool isRemote = file.fileType != 'local' && file.fileUuid != null;
      files.removeAt(index);
      emit(state.copyWith(files: files));
      // Only persist to server if editing and file is remote
      if (isRemote) {
        try {
          await familyRepository.removeStorageFile(file.fileUuid!);
        } catch (e) {
          AppLogger.e('Failed to delete remote file: $e');
          showSimpleToast('Failed to delete remote file');
          return;
        }
      }
    } catch (e) {
      AppLogger.e('Error removing file: $e');
      showSimpleToast('Error removing file');
    } finally {
    }
  }

  Future<void> selectDate(BuildContext context, bool isDeparture) async {
    final date = isDeparture ? state.departureDate : state.arrivalDate;
    final DateTime? datePicked = await showDatePicker(
      context: context,
      initialDate: date ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (datePicked != null) {
      if (isDeparture) {
        // Handle departure date selection
        emit(state.copyWith(departureDate: datePicked, isDepartureDateValid: true));
      } else {
        // Handle arrival date selection
        emit(state.copyWith(arrivalDate: datePicked, isArrivalDateValid: true));
      }
    } else {
      if (isDeparture) {
        emit(state.copyWith(isDepartureDateValid: false));
      } else {
        emit(state.copyWith(isArrivalDateValid: false));
      }
    }
  }

  Future<void> selectTime(BuildContext context, bool isDeparture) async {
    final time = isDeparture ? state.departureTime : state.arrivalTime;
    final TimeOfDay? timePicked = await showTimePicker(
      context: context,
      initialTime: time ?? TimeOfDay.now(),
    );
    if (timePicked != null) {
      if (isDeparture) {
        // Handle departure time selection
        emit(state.copyWith(departureTime: timePicked, isDepartureTimeValid: true));
      } else {
        // Handle arrival time selection
        emit(state.copyWith(arrivalTime: timePicked, isArrivalTimeValid: true));
      }
    } else {
      if (isDeparture) {
        emit(state.copyWith(isDepartureTimeValid: false));
      } else {
        emit(state.copyWith(isArrivalTimeValid: false));
      }
    }
  }

  bool _validateFields() {
    bool isTicketNoValid = false;
    bool isDepartureLocationValid = false;
    bool isDepartureDateValid = false;
    bool isDepartureTimeValid = false;
    bool isArrivalLocationValid = false;
    bool isArrivalDateValid = false;
    bool isArrivalTimeValid = false;

    if (state.ticketNo?.isEmpty ?? true) {
      AppLogger.d('Ticket no is empty');
      isTicketNoValid = false;
    } else {
      isTicketNoValid = true;
    }

    if (state.departureLocation?.isEmpty ?? true) {
      AppLogger.d('Departure location is empty');
      isDepartureLocationValid = false;
    } else {
      isDepartureLocationValid = true;
    }
    isDepartureDateValid = state.departureDate != null;
    if (!isDepartureDateValid) {
      AppLogger.d('Departure date is empty');
    }
    isDepartureTimeValid = state.departureTime != null;
    if (!isDepartureTimeValid) {
      AppLogger.d('Departure time is empty');
    }

    if (state.arrivalLocation?.isEmpty ?? true) {
      AppLogger.d('Arrival location is empty');
      isArrivalLocationValid = false;
    } else {
      isArrivalLocationValid = true;
    }
    isArrivalDateValid = state.arrivalDate != null;
    if (!isArrivalDateValid) {
      AppLogger.d('Arrival date is empty');
    }
    isArrivalTimeValid = state.arrivalTime != null;
    if (!isArrivalTimeValid) {
      AppLogger.d('Arrival time is empty');
    }
    emit(state.copyWith(
      isTicketNoValid: isTicketNoValid,
      isDepartureLocationValid: isDepartureLocationValid,
      isDepartureDateValid: isDepartureDateValid,
      isDepartureTimeValid: isDepartureTimeValid,
      isArrivalLocationValid: isArrivalLocationValid,
      isArrivalDateValid: isArrivalDateValid,
      isArrivalTimeValid: isArrivalTimeValid,
    ));
    return isTicketNoValid &&
        isDepartureLocationValid &&
        isDepartureDateValid &&
        isDepartureTimeValid &&
        isArrivalLocationValid &&
        isArrivalDateValid &&
        isArrivalTimeValid;
  }

  Future<void> pickFileOrImage(BuildContext context) async {
    try {
      // emit(state.copyWith(status: TripTransferUpsertStatus.loading));
      
      // Show dialog to choose between image and document
      final String? choice = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select File Type'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.image),
                  title: const Text('Image from Photos'),
                  subtitle: const Text('Pick from your photo gallery'),
                  onTap: () => Navigator.of(context).pop('image'),
                ),
                ListTile(
                  leading: const Icon(Icons.description),
                  title: const Text('Document from Files'),
                  subtitle: const Text('Pick PDF'),
                  onTap: () => Navigator.of(context).pop('document'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          );
        },
      );

      if (choice == null) {
        // emit(state.copyWith(status: TripTransferUpsertStatus.error));
        return;
      }

      if (choice == 'image') {
        await _pickImage();
      } else if (choice == 'document') {
        await _pickDocument();
      }
    } catch (e) {
      AppLogger.e('Error picking file: $e');
      showSimpleToast('Error selecting file');
      // emit(state.copyWith(status: TripTransferUpsertStatus.error));
    }
  }

  Future<void> _pickImage() async {
    emit(state.copyWith(status: TripTransferUpsertStatus.loading));
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        File file = File(image.path);
        String fileName = path.basename(file.path);
        AppLogger.d('Picked image: ${file.path}');
        final List<FileModel> currentFiles = List.from(state.files);
        // Mark local files with fileUuid = null, fileType = 'local'
        currentFiles.add(FileModel(
          fileUuid: null,
          fileName: fileName,
          fileUrl: file.path,
          fileType: 'local',
          fileSize: null,
        ));
        emit(state.copyWith(
          files: currentFiles,
          hasSelectedFile: true,
          status: TripTransferUpsertStatus.error,
        ));
      } else {
        emit(state.copyWith(status: TripTransferUpsertStatus.error));
      }
    } catch (e) {
      AppLogger.e('Error picking image: $e');
      showSimpleToast('Error selecting image');
      emit(state.copyWith(status: TripTransferUpsertStatus.error));
    }
  }

  Future<void> _pickDocument() async {
    emit(state.copyWith(status: TripTransferUpsertStatus.loading));
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom, // Use custom for documents to access Files app
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'xls',
          'xlsx',
          'txt',
        ],
      );
      if (result != null) {
        File file = File(result.files.single.path!);
        String fileName = path.basename(file.path);
        AppLogger.d('Picked document: ${file.path}');
        final List<FileModel> currentFiles = List.from(state.files);
        // Mark local files with fileUuid = null, fileType = 'local'
        currentFiles.add(FileModel(
          fileUuid: null,
          fileName: fileName,
          fileUrl: file.path,
          fileType: 'local',
          fileSize: null,
        ));
        emit(state.copyWith(
          files: currentFiles,
          hasSelectedFile: true,
          status: TripTransferUpsertStatus.error
        ));
      } else {
        emit(state.copyWith(status: TripTransferUpsertStatus.error));
      }
    } catch (e) {
      AppLogger.e('Error picking file: $e');
      showSimpleToast('Error selecting file');
      emit(state.copyWith(status: TripTransferUpsertStatus.error));
    }
  }

  Future<void> saveActivity() async {
    if (!_validateFields()) {
      return;
    }
    emit(state.copyWith(status: TripTransferUpsertStatus.loading));
    try {
      // Upload local files (fileType == 'local'), keep remote files as is
      final uploader = Upload(familyId: accountService.familyId);
      List<FileModel> finalFiles = [];
      for (final file in state.files) {
        if (file.fileType == 'local') {
          // Upload local file
          final localFile = File(file.fileUrl);

          final storage = await uploader.uploadFile(localFile, file.fileName);

          finalFiles.add(FileModel(
            fileUuid: storage.uuid,
            fileName: storage.fileName ?? '',
            fileUrl: storage.filePath ?? '',
            fileType: storage.extraData,
            fileSize: storage.fileSize,
          ));
        } else {
          // Already remote
          finalFiles.add(file);
        }
      }

      // Create transfer model
      final transfer = TransferModel(
        type: state.selectedTransport!.name.toLowerCase(),
        ticketNo: state.ticketNo,
        fromLocation: state.departureLocation,
        fromTime: getDateTimeString(state.departureDate, state.departureTime),
        toLocation: state.arrivalLocation,
        toTime: getDateTimeString(state.arrivalDate, state.arrivalTime),
        currency: state.selectedCurrency,
        quantity: state.quantity,
        price: state.price,
        note: state.note,
        attachments: finalFiles,
      );

      emit(state.copyWith(status: TripTransferUpsertStatus.success, transferResult: transfer));
      return;
    } catch (e) {
      AppLogger.e('Error saving transfer: $e');
    }
    emit(state.copyWith(status: TripTransferUpsertStatus.error));
  }

  String getDateTimeString(DateTime? date, TimeOfDay? time) {
    if (date != null && time != null) {
      final dateTime = DateTime(
        date.year,
        date.month,
        date.day,
        time.hour,
        time.minute,
      ).toUtc(); // Convert to UTC
      return dateTime.toIso8601String(); // DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    }
    return '';
  }


}
