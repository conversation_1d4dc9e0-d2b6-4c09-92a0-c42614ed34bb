import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/in_app_purchase_service.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/trip/place/place_map_selection_cubit.dart';
import 'package:family_app/screen/main/trip/place/place_map_selection_state.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/utils/content_provider.dart';

import '../../../../router/app_route.dart';

@RoutePage()
class PlaceMapSelectionPage
    extends BaseBlocProvider<PlaceMapSelectionState, PlaceMapSelectionCubit> {
  const PlaceMapSelectionPage({super.key, required this.parameter});

  final PlaceUpsertParameter parameter;

  @override
  Widget buildPage() => PlaceMapSelectionView(parameter: parameter);

  @override
  PlaceMapSelectionCubit createCubit() =>
      PlaceMapSelectionCubit(parameter: parameter, isPremium: locator.get<InAppPurchaseService>().isPremium);
}

class PlaceMapSelectionView extends StatefulWidget {
  const PlaceMapSelectionView({super.key, required this.parameter});

  final PlaceUpsertParameter parameter;

  @override
  State<PlaceMapSelectionView> createState() => _PlaceMapSelectionViewState();
}

class _PoiCategory {
  final String label;
  final String type;
  final IconData icon;
  const _PoiCategory(this.label, this.type, this.icon);
}

class _PlaceMapSelectionViewState extends BaseBlocPageState<
    PlaceMapSelectionView, PlaceMapSelectionState, PlaceMapSelectionCubit> {
  final TextEditingController _searchController = TextEditingController();
  GoogleMapController? _mapController;
  String? _mapStyle;

  Timer? _debounce; // Debounce timer for search
  String _lastQuery = '';
  final Map<String, List<PlaceSuggestion>> _searchCache = {};

  int _searchCount =
      0; // Counts how many times user has searched in this session
  Timer? _resetSearchCountTimer; // Timer to reset _searchCount after inactivity

  bool _isDebouncing = false; // Track if debounce is active
  // Tracks the last selected suggestion name to suppress suggestion list after selection
  String? _lastSelectedSuggestionName;

  final FocusNode _searchFocusNode = FocusNode();
  int _suggestionListKey = 0;

  late final bool _isPremium;

  /// Handles all logic for when a suggestion is selected
  Future<void> _handleSuggestionSelection(
      PlaceSuggestion suggestion, PlaceMapSelectionCubit cubit) async {
    AppLogger.d('Suggestion tapped: \\${suggestion.name}');
    // Fetch photo using correct provider
    final photoProvider = getPhotoProvider(isPremium: _isPremium);
    final photoUrl = await photoProvider.fetchImageUrl(suggestion.name, location: suggestion.location);
    // PlaceSuggestion does not have copyWith, so create a new instance
    final updatedSuggestion = PlaceSuggestion(
      placeId: suggestion.placeId,
      name: suggestion.name,
      address: suggestion.address,
      location: suggestion.location,
      photoReference: photoUrl,
    );
    await cubit.onSelectSuggestion(updatedSuggestion);
    _searchController.removeListener(_onSearchChanged);
    setState(() {
      _lastSelectedSuggestionName = suggestion.name;
      _searchController.text = suggestion.name;
      AppLogger.d('Set searchController.text to: \\${suggestion.name}');
      FocusScope.of(context).unfocus();
    });
    _searchController.addListener(_onSearchChanged);
    // Clear suggestions after selection to avoid duplicates
    cubit.emit(
      cubit.state.copyWith(suggestions: []),
    );
    AppLogger.d('Suggestions cleared after selection.');
    _animateToLocation(suggestion.location);
  }

  // Resets all local and Cubit state to initial values
  void _resetAllState() {
    _searchController.clear();
    _selectedCategoryIndex = -1;
    _categoryMode = false;
    _lastQuery = '';
    _searchCache.clear();
    _searchCount = 0; // Reset searchCount when leaving screen
  }

  // Handles debounced search logic and cache with progressive debounce
  void _onSearchChanged() {
    AppLogger.d(
        'onSearchChanged triggered. Current text: \\${_searchController.text}');
    final rawValue = _searchController.text;
    final value = rawValue.trim();
    if (rawValue.isEmpty) {
      _searchCount = 0;
    }
    // If less than 3 characters, clear suggestions/loading and do not debounce
    if (value.length < 3) {
      _debounce?.cancel();
      _resetSearchCountTimer?.cancel();
      setState(() {
        _isDebouncing = false;
      });
      _handleSearchQuery(value); // This will clear suggestions/loading
      return;
    }
    final isRealSearch = value.isNotEmpty && value != _lastQuery;
    // Progressive debounce: base 500ms + (searchCount x 500ms), max 3000ms
    final debounceDuration =
        Duration(milliseconds: (500 + (_searchCount * 500)).clamp(500, 3000));
    AppLogger.d(
        'Debounce duration: \\${debounceDuration.inMilliseconds}ms (searchCount: \\$_searchCount)');
    _resetSearchCountTimer?.cancel();
    // Reset searchCount if user stops typing for 7 seconds
    _resetSearchCountTimer = Timer(const Duration(seconds: 7), () {
      _searchCount = 0;
    });
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    setState(() {
      _isDebouncing = true;
      _suggestionListKey++;
    });
    _debounce = Timer(debounceDuration, () {
      _updateCategoryMode(value);
      _handleSearchQuery(value);
      if (isRealSearch) _searchCount++;
      setState(() {
        _isDebouncing = false;
      });
      AppLogger.d(
          'Debounce finished. Suggestions: \\${context.read<PlaceMapSelectionCubit>().state.suggestions.length}');
    });
  }

  // Updates category mode state
  void _updateCategoryMode(String value) {
    setState(() {
      if (value.isEmpty) {
        _selectedCategoryIndex = -1;
        _categoryMode = false;
      } else if (_selectedCategoryIndex != -1 &&
          value == _categories[_selectedCategoryIndex].label) {
        _categoryMode = true;
      } else {
        _categoryMode = false;
      }
    });
  }

  // Handles the search query, cache, and API call
  void _handleSearchQuery(String query) {
    if (query.length < 3) {
      _lastQuery = '';
      context.read<PlaceMapSelectionCubit>().onSearchLocation('');
      return;
    }
    if (query == _lastQuery) return; // Prevent duplicate API call
    _lastQuery = query;

    // Local filtering: check all cached results for substring matches, avoid duplicates by placeId
    final Map<String, PlaceSuggestion> uniqueMatches = {};
    _searchCache.forEach((cachedQuery, suggestions) {
      for (final suggestion in suggestions) {
        if ((suggestion.name.toLowerCase().contains(query.toLowerCase()) ||
                suggestion.address
                    .toLowerCase()
                    .contains(query.toLowerCase())) &&
            !uniqueMatches.containsKey(suggestion.placeId)) {
          uniqueMatches[suggestion.placeId] = suggestion;
        }
      }
    });
    final List<PlaceSuggestion> localMatches = uniqueMatches.values.toList();
    if (localMatches.isNotEmpty) {
      context.read<PlaceMapSelectionCubit>().emit(
            context.read<PlaceMapSelectionCubit>().state.copyWith(
                  suggestions: localMatches,
                  isLoading: false,
                ),
          );
      return;
    }

    // If no local matches, call API and cache results
    context.read<PlaceMapSelectionCubit>().onSearchLocation(query).then((_) {
      final state = context.read<PlaceMapSelectionCubit>().state;
      _searchCache[query] = state.suggestions;
    });
  }

  @override
  void initState() {
    final iapService = locator.get<InAppPurchaseService>();
    _isPremium = iapService.isPremium;

    super.initState();
    _searchCount = 0; // Reset searchCount when entering screen
    _loadMapStyle();
    _loadCustomPoiIcons();
    _resolveInitialCameraTarget();
    _searchController.addListener(_onSearchChanged);
    if (viewMode && _displayActivities.length == 1) {
      // _selectedActivity = _displayActivities.first;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (viewMode && allActivities.isNotEmpty) {
        _fitAllMarkers();
      }
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _resetSearchCountTimer?.cancel();
    _searchController.removeListener(_onSearchChanged);
    _resetAllState();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  bool get viewMode => widget.parameter.viewMode;
  List<Activity> get allActivities => widget.parameter.allActivities;

  List<Activity> get _displayActivities {
    // If only one activity, show only that place (accessed from itinerary)
    // If more than one, show all (accessed from appbar)
    if (allActivities.length == 1) return allActivities;
    // Defensive: filter out activities without coordinates
    return allActivities
        .where((a) => a.latitude != null && a.longitude != null)
        .toList();
  }

  final List<_PoiCategory> _categories = [
    _PoiCategory('Restaurant', 'restaurant', Icons.restaurant),
    _PoiCategory('Hotel', 'hotel', Icons.hotel),
    _PoiCategory('Coffee', 'cafe', Icons.local_cafe),
    _PoiCategory('Shopping', 'shopping_mall', Icons.shopping_bag),
    _PoiCategory('ATM', 'atm', Icons.atm),
    _PoiCategory('Gas', 'gas_station', Icons.local_gas_station),
    _PoiCategory('Pharmacy', 'pharmacy', Icons.local_pharmacy),
    _PoiCategory('Attraction', 'tourist_attraction', Icons.attractions),
  ];
  int _selectedCategoryIndex = -1; // No category selected by default
  bool _categoryMode =
      false; // Track if a category is selected and input is filled

  final Map<String, BitmapDescriptor> _customPoiIcons = {};
  bool _poiIconsLoaded = false;

  LatLng? _initialCameraTarget;
  bool _initialCameraReady = false;

  void _onPoiMarkerTapped(PoiMarker poi) async {
    // Update the cubit state to reflect the selected POI as the main selected location
    final cubit = context.read<PlaceMapSelectionCubit>();
    await cubit.onSelectSuggestion(
      PlaceSuggestion(
        placeId: poi.placeId,
        name: poi.name,
        address: poi.address ?? '',
        location: poi.location,
        photoReference: poi.photoReference,
      ),
    );
  }

  void _onMarkerTapped(Activity activity, PlaceMapSelectionCubit cubit) {
    cubit.onSelectActivity(activity);
  }

  void _onCategorySelected(int index, PlaceMapSelectionCubit cubit) {
    setState(() {
      _selectedCategoryIndex = index;
      _categoryMode = true;
      _searchController.text = _categories[index].label;
      FocusScope.of(context).unfocus();
    });
    final state = context.read<PlaceMapSelectionCubit>().state;
    final center =
        state.selectedLocation ?? const LatLng(10.762622, 106.660172);
    cubit.fetchNearbyPois(
      location: center,
      types: [_categories[index].type],
    );
  }

  Future<void> _resolveInitialCameraTarget() async {
    final state = context.read<PlaceMapSelectionCubit>().state;
    if (state.selectedLocation != null) {
      setState(() {
        _initialCameraTarget = state.selectedLocation!;
        _initialCameraReady = true;
      });
      return;
    }
    final InitialLocation? initialLocation = widget.parameter.initialLocation;
    AppLogger.d(
        'Resolving initial camera target latLng: ${initialLocation?.latLng}');
    AppLogger.d(
        'Resolving initial camera target name: ${initialLocation?.name}');
    if (initialLocation != null) {
      if (initialLocation.latLng != null) {
        setState(() {
          _initialCameraTarget = initialLocation.latLng;
          _initialCameraReady = true;
        });
        return;
      } else if (initialLocation.name != null &&
          initialLocation.name!.trim().isNotEmpty) {
        final latLng = await _geocodeLocationName(initialLocation.name!.trim());
        if (latLng != null) {
          setState(() {
            _initialCameraTarget = latLng;
            _initialCameraReady = true;
          });
          return;
        }
      }
    }
    // Defensive fallback: use default location if all else fails
    setState(() {
      _initialCameraTarget = const LatLng(10.762622, 106.660172);
      _initialCameraReady = true;
    });
  }

  Future<void> _loadMapStyle() async {
    _mapStyle = await rootBundle.loadString('assets/map_style.json');
  }

  Future<void> _loadCustomPoiIcons() async {
    final iconMap = <String, String>{
      'restaurant': 'assets/markers/marker_restaurant.png',
      'hotel': 'assets/markers/marker_hotel.png',
      'cafe': 'assets/markers/marker_cafe.png',
      'shopping_mall': 'assets/markers/marker_shopping.png',
      'atm': 'assets/markers/marker_atm.png',
      'gas_station': 'assets/markers/marker_gas.png',
      'pharmacy': 'assets/markers/marker_pharmacy.png',
      'tourist_attraction': 'assets/markers/marker_attraction.png',
    };
    for (final entry in iconMap.entries) {
      try {
        final bitmap = await BitmapDescriptor.fromAssetImage(
          const ImageConfiguration(size: Size(64, 64)),
          entry.value,
        );
        _customPoiIcons[entry.key] = bitmap;
      } catch (_) {
        // If asset not found, skip
      }
    }
    setState(() {
      _poiIconsLoaded = true;
    });
  }

  void _fitAllMarkers() async {
    if (_mapController == null) return;
    final validActivities = _displayActivities
        .where((a) => a.latitude != null && a.longitude != null)
        .toList();
    if (validActivities.isEmpty) return;
    if (validActivities.length == 1) {
      final LatLng pos =
          LatLng(validActivities[0].latitude!, validActivities[0].longitude!);
      await _mapController!.animateCamera(CameraUpdate.newLatLngZoom(pos, 15));
      return;
    }
    LatLngBounds bounds = _createBounds(validActivities);
    await _mapController!
        .animateCamera(CameraUpdate.newLatLngBounds(bounds, 60));
  }

  LatLngBounds _createBounds(List<Activity> activities) {
    // Defensive: filter out activities without lat/lng
    final filtered = activities
        .where((a) => a.latitude != null && a.longitude != null)
        .toList();
    if (filtered.isEmpty) {
      // Fallback to a default location if no valid activities
      return LatLngBounds(
        southwest: const LatLng(10.762622, 106.660172),
        northeast: const LatLng(10.762622, 106.660172),
      );
    }
    final lats = filtered.map((a) => a.latitude!).toList();
    final lngs = filtered.map((a) => a.longitude!).toList();
    final southwest = LatLng(lats.reduce((a, b) => a < b ? a : b),
        lngs.reduce((a, b) => a < b ? a : b));
    final northeast = LatLng(lats.reduce((a, b) => a > b ? a : b),
        lngs.reduce((a, b) => a > b ? a : b));
    return LatLngBounds(southwest: southwest, northeast: northeast);
  }

  BitmapDescriptor _getPoiMarkerIcon(String type) {
    if (_poiIconsLoaded && _customPoiIcons.containsKey(type)) {
      return _customPoiIcons[type]!;
    }
    return BitmapDescriptor.defaultMarker;
  }

  Future<void> _animateToLocation(LatLng location) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(location, 16),
      );
    }
  }

  /// Geocode a location name (city, country, etc) to LatLng
  Future<LatLng?> _geocodeLocationName(String locationName) async {
    try {
      final locations = await locationFromAddress(locationName);
      if (locations.isNotEmpty) {
        final loc = locations.first;
        return LatLng(loc.latitude, loc.longitude);
      }
    } catch (e) {
      print('Geocoding error for $locationName: $e');
    }
    return null;
  }

  @override
  bool? get isBottomSafeArea => false;

  @override
  Widget buildAppBar(BuildContext context, PlaceMapSelectionCubit cubit,
      PlaceMapSelectionState state) {
    return CustomAppBar2(
      title: viewMode ? 'View All Places' : 'Select Location',
      showBack: true,
      actions: viewMode
          ? [
          GestureDetector(
            onTap: () {
              context.pushRoute(PlaceUpsertRoute(
                parameter: PlaceUpsertParameter(
                  tripId: cubit.parameter.tripId,
                  dayIndex: cubit.parameter.dayIndex,
                  activityIndex: cubit.parameter.activityIndex,
                  place: cubit.parameter.place,
                  initialLocation: cubit.parameter.initialLocation,
                ),
              ));
            },
            behavior: HitTestBehavior.opaque,
            child: CircleItem(
              backgroundColor: appTheme.backgroundV2,
              padding: padding(all: 7),
              child: Assets.icons.iconEdit.svg(),
            ),
          )
      ]
          : [
              if (state.selectedLocation != null)
                GestureDetector(
                  onTap: () => cubit.onConfirm(context),
                  behavior: HitTestBehavior.opaque,
                  child: CircleItem(
                    backgroundColor: appTheme.backgroundV2,
                    padding: padding(all: 7),
                    child: Assets.icons.icActionCheck.svg(),
                  ),
                )
            ],
    );
  }

  @override
  Widget buildBody(BuildContext context, PlaceMapSelectionCubit cubit,
      PlaceMapSelectionState state) {
    final isSearching = _isDebouncing || state.isLoading;
    final bool shouldShowSuggestions = !viewMode &&
        state.suggestions.isNotEmpty &&
        _searchController.text != _lastSelectedSuggestionName;
    if (!_initialCameraReady || _initialCameraTarget == null) {
      return const Center(child: CircularProgressIndicator());
    }
    Set<Marker> markers = {};
    if (viewMode) {
      final validActivities = _displayActivities
          .where((a) => a.latitude != null && a.longitude != null)
          .toList();
      if (validActivities.isNotEmpty) {
        markers.addAll(validActivities.map((a) => Marker(
              markerId: MarkerId(a.description ?? ''),
              position: LatLng(a.latitude!, a.longitude!),
              onTap: () => _onMarkerTapped(a, cubit),
            )));
      } else if (_initialCameraTarget != null) {
        // No activities with lat/lng, but we have an initial camera target (from initialLocation)
        markers.add(Marker(
          markerId: const MarkerId('initial_location'),
          position: _initialCameraTarget!,
        ));
      }
    } else {
      if (state.selectedLocation != null) {
        markers.add(Marker(
          markerId: const MarkerId('selected'),
          position: state.selectedLocation!,
        ));
      }
      markers.addAll(state.poiMarkers.map((poi) => Marker(
            markerId: MarkerId('poi_${poi.placeId}'),
            position: poi.location,
            icon: _getPoiMarkerIcon(poi.type ?? ''),
            onTap: () => _onPoiMarkerTapped(poi),
          )));
      // If no POIs loaded yet, auto-fetch for all default categories around initial camera
      // if (state.poiMarkers.isEmpty && _initialCameraTarget != null && _poiIconsLoaded) {
      //   // Only fetch once per session
      //   WidgetsBinding.instance.addPostFrameCallback((_) {
      //     final cubit = context.read<PlaceMapSelectionCubit>();
      //     cubit.fetchNearbyPois(location: _initialCameraTarget!);
      //   });
      // }
    }
    return Stack(
      children: [
        GoogleMap(
          initialCameraPosition: CameraPosition(
            target: _initialCameraTarget!,
            zoom: 15,
          ),
          onMapCreated: (controller) {
            _mapController = controller;
            if (_mapStyle != null) {
              try {
                _mapController!.setMapStyle(_mapStyle!);
              } catch (e) {}
            }
            if (viewMode && _displayActivities.isNotEmpty) {
              _fitAllMarkers();
            }
          },
          markers: markers,
          myLocationEnabled: !viewMode,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: true,
          tiltGesturesEnabled: true,
          rotateGesturesEnabled: true,
          zoomGesturesEnabled: true,
          scrollGesturesEnabled: true,
          padding: const EdgeInsets.only(top: 120, bottom: 40),
          onTap: viewMode
              ? null
              : (LatLng latLng) async {
                  await cubit.onMapTap(latLng);
                  _animateToLocation(latLng);
                },
        ),
        if (!viewMode)
          Positioned(
            bottom: 24,
            right: 16,
            child: FloatingActionButton(
              heroTag: 'myLocation',
              backgroundColor: Colors.white,
              mini: true,
              elevation: 4,
              onPressed: () async {
                // Center map on user's current location
                // You may want to add permission checks here
                Position? position;
                try {
                  position = await Geolocator.getCurrentPosition(
                      desiredAccuracy: LocationAccuracy.high);
                } catch (_) {}
                if (position != null) {
                  final latLng = LatLng(position.latitude, position.longitude);
                  _animateToLocation(latLng);
                }
              },
              child: const Icon(Icons.my_location, color: Colors.black87),
            ),
          ),
        if (state.selectedPhotoUrl != null ||
            state.selectedDescription != null ||
            state.selectedCity != null ||
            state.selectedTime != null ||
            state.selectedAddress != null)
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Material(
              color: Colors.transparent,
              child: LocationQuickViewCard(
                photoUrl: state.selectedPhotoUrl,
                description: state.selectedDescription,
                city: state.selectedCity,
                time: state.selectedTime,
                address: state.selectedAddress,
              ),
            ),
          ),
        if (!viewMode)
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _SearchBar(
                  controller: _searchController,
                  isSearching: isSearching,
                  onClear: () {
                    setState(() {
                      _searchController.clear();
                      _selectedCategoryIndex = -1;
                      _categoryMode = false;
                    });
                    context.read<PlaceMapSelectionCubit>().onSearchLocation('');
                  },
                  focusNode: _searchFocusNode,
                  onChanged: (value) {},
                ),
                if (!_categoryMode && _searchController.text.isEmpty) ...[
                  const SizedBox(height: 10),
                  SizedBox(
                    height: 40,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: _categories.length,
                      separatorBuilder: (_, __) => const SizedBox(width: 8),
                      itemBuilder: (context, index) {
                        final cat = _categories[index];
                        final selected = index == _selectedCategoryIndex;
                        return ChoiceChip(
                          label: Row(
                            children: [
                              Icon(cat.icon,
                                  size: 18,
                                  color:
                                      selected ? Colors.white : Colors.black54),
                              const SizedBox(width: 4),
                              Text(cat.label),
                            ],
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24.0),
                          ),
                          selected: selected,
                          onSelected: (_) => _onCategorySelected(index, cubit),
                          selectedColor: Theme.of(context).primaryColor,
                          backgroundColor: Colors.white,
                          labelStyle: TextStyle(
                              color: selected ? Colors.white : Colors.black87),
                          elevation: selected ? 4 : 0,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 0),
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),
        if (shouldShowSuggestions)
          Positioned(
            key: ValueKey(_suggestionListKey),
            top: 72,
            left: 16,
            right: 16,
            child: _SuggestionList(
              suggestions: state.suggestions,
              query: _searchController.text,
              onSuggestionTap: (s) => _handleSuggestionSelection(s, cubit),
              apiKey: cubit.apiKey,
              isPremium: _isPremium,
            ),
          ),
      ],
    );
  }
}

class _SearchBar extends StatefulWidget {
  final TextEditingController controller;
  final bool isSearching;
  final VoidCallback onClear;
  final FocusNode? focusNode;
  final ValueChanged<String>? onChanged;
  const _SearchBar({
    required this.controller,
    required this.isSearching,
    required this.onClear,
    this.focusNode,
    this.onChanged,
  });

  @override
  State<_SearchBar> createState() => _SearchBarState();
}

class _SearchBarState extends State<_SearchBar> {
  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(30.0),
      child: TextField(
        controller: widget.controller,
        focusNode: widget.focusNode,
        decoration: InputDecoration(
          hintText: 'Search for a place...',
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30.0),
            borderSide: BorderSide.none,
          ),
          prefixIcon: const Icon(Icons.search),
          suffixIcon: widget.isSearching
              ? const Padding(
                  padding: EdgeInsets.all(10.0),
                  child: SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
              : widget.controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: widget.onClear,
                      tooltip: 'Clear search',
                    )
                  : null,
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
        onChanged: widget.onChanged,
        textInputAction: TextInputAction.search,
        autofocus: false,
        autocorrect: false,
        enableSuggestions: false,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }
}

class _SuggestionList extends StatefulWidget {
  final List<PlaceSuggestion> suggestions;
  final String query;
  final void Function(PlaceSuggestion) onSuggestionTap;
  final String apiKey;
  final bool isPremium;
  const _SuggestionList({
    required this.suggestions,
    required this.query,
    required this.onSuggestionTap,
    required this.apiKey,
    required this.isPremium,
  });

  @override
  State<_SuggestionList> createState() => _SuggestionListState();
}

class _SuggestionListState extends State<_SuggestionList> {
  int _highlightedIndex = 0;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  void _onKey(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
        setState(() {
          _highlightedIndex =
              (_highlightedIndex + 1) % widget.suggestions.length;
        });
      } else if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
        setState(() {
          _highlightedIndex =
              (_highlightedIndex - 1 + widget.suggestions.length) %
                  widget.suggestions.length;
        });
      } else if (event.logicalKey == LogicalKeyboardKey.enter) {
        widget.onSuggestionTap(widget.suggestions[_highlightedIndex]);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: _focusNode,
      onKeyEvent: (event) => _onKey(event),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxHeight: 320, // Adjust as needed for your UI
        ),
        child: Card(
          elevation: 6,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            itemCount: widget.suggestions.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final suggestion = widget.suggestions[index];
              final isHighlighted = index == _highlightedIndex;
              return Semantics(
                label: 'Suggestion: ${suggestion.name}, ${suggestion.address}',
                selected: isHighlighted,
                child: ListTile(
                  tileColor: isHighlighted
                      ? Theme.of(context).primaryColor.withOpacity(0.08)
                      : null,
                  leading: const Icon(Icons.place, color: Colors.blueAccent),
                  title: _highlightMatch(
                      suggestion.name, widget.query, context, isHighlighted),
                  subtitle: _highlightMatch(
                      suggestion.address, widget.query, context, isHighlighted,
                      isSubtitle: true),
                  onTap: () => widget.onSuggestionTap(suggestion),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _highlightMatch(
      String text, String query, BuildContext context, bool isHighlighted,
      {bool isSubtitle = false}) {
    if (query.isEmpty)
      return Text(text,
          maxLines: isSubtitle ? 1 : null,
          overflow: isSubtitle ? TextOverflow.ellipsis : TextOverflow.visible);
    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final start = lowerText.indexOf(lowerQuery);
    if (start < 0)
      return Text(text,
          maxLines: isSubtitle ? 1 : null,
          overflow: isSubtitle ? TextOverflow.ellipsis : TextOverflow.visible);
    final end = start + query.length;
    final style = isSubtitle
        ? Theme.of(context).textTheme.bodySmall
        : Theme.of(context)
            .textTheme
            .bodyMedium
            ?.copyWith(fontWeight: FontWeight.bold);
    final highlightStyle =
        style?.copyWith(color: Theme.of(context).colorScheme.primary);
    return RichText(
      text: TextSpan(
        style: style,
        children: [
          TextSpan(text: text.substring(0, start)),
          TextSpan(text: text.substring(start, end), style: highlightStyle),
          TextSpan(text: text.substring(end)),
        ],
      ),
      maxLines: isSubtitle ? 1 : null,
      overflow: isSubtitle ? TextOverflow.ellipsis : TextOverflow.visible,
    );
  }
}

class LocationQuickViewCard extends StatelessWidget {
  final String? photoUrl;
  final String? description;
  final String? city;
  final String? time;
  final String? address;
  const LocationQuickViewCard({
    super.key,
    this.photoUrl,
    this.description,
    this.city,
    this.time,
    this.address,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (photoUrl != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.network(
                  photoUrl!,
                  height: 180,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    height: 180,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: const Icon(Icons.broken_image,
                        size: 48, color: Colors.grey),
                  ),
                ),
              ),
            if (photoUrl != null) const SizedBox(height: 14),
            Text(
              address ?? 'Selected Location',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            if (city != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.location_city,
                    size: 16,
                    color: appTheme.grayV2,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    city!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: appTheme.grayV2,
                        ),
                  ),
                ],
              ),
            ],
            if (time != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: appTheme.grayV2,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    time!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: appTheme.grayV2,
                        ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 12),
            Text(
              description ?? '',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
