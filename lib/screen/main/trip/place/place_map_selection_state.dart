import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class PoiMarker {
  final String placeId;
  final String name;
  final LatLng location;
  final String? type;
  final String? address;
  final String? photoReference;

  PoiMarker({
    required this.placeId,
    required this.name,
    required this.location,
    this.type,
    this.address,
    this.photoReference,
  });
}

/// Represents a place suggestion from the Google Places API
class PlaceSuggestion {
  final String placeId;
  final String name;
  final String address;
  final LatLng location;
  final String? photoReference;

  PlaceSuggestion({
    required this.placeId,
    required this.name,
    required this.address,
    required this.location,
    this.photoReference,
  });

  @override
  String toString() => 'PlaceSuggestion(name: $name, address: $address)';
}

class PlaceMapSelectionState extends BaseState {
  final List<PlaceSuggestion> suggestions;
  final LatLng? selectedLocation;
  final String? selectedAddress;
  final String? selectedCity;
  final String? selectedPhotoUrl;
  final String? selectedTime;
  final String? selectedDescription;
  final String? selectedOpeningHours;
  final List<PoiMarker> poiMarkers;

  PlaceMapSelectionState({
    super.isLoading = false,
    this.suggestions = const [],
    this.selectedLocation,
    this.selectedAddress,
    this.selectedCity,
    this.selectedPhotoUrl,
    this.selectedTime,
    this.selectedDescription,
    this.selectedOpeningHours,
    this.poiMarkers = const [],
  });

  PlaceMapSelectionState copyWith({
    bool? isLoading,
    List<PlaceSuggestion>? suggestions,
    LatLng? selectedLocation,
    String? selectedAddress,
    String? selectedCity,
    String? selectedPhotoUrl,
    String? selectedTime,
    String? selectedDescription,
    String? selectedOpeningHours,
    List<PoiMarker>? poiMarkers,
  }) {
    return PlaceMapSelectionState(
      isLoading: isLoading ?? this.isLoading,
      suggestions: suggestions ?? this.suggestions,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      selectedAddress: selectedAddress ?? this.selectedAddress,
      selectedCity: selectedCity ?? this.selectedCity,
      selectedPhotoUrl: selectedPhotoUrl ?? this.selectedPhotoUrl,
      selectedTime: selectedTime ?? this.selectedTime,
      selectedDescription: selectedDescription ?? this.selectedDescription,
      selectedOpeningHours: selectedOpeningHours ?? this.selectedOpeningHours,
      poiMarkers: poiMarkers ?? this.poiMarkers,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        suggestions,
        selectedLocation,
        selectedAddress,
        selectedCity,
        selectedPhotoUrl,
        selectedTime,
        selectedDescription,
        selectedOpeningHours,
        poiMarkers,
      ];

  @override
  String toString() => 'PlaceMapSelectionState('
      'isLoading: $isLoading, '
      'selectedLocation: $selectedLocation, '
      'selectedAddress: $selectedAddress, '
      'selectedDescription: $selectedDescription, '
      'suggestions: ${suggestions.length}, '
      'selectedPhotoUrl: $selectedPhotoUrl, '
      'selectedCity: $selectedCity, '
      'selectedOpeningHours: $selectedOpeningHours, '
      'selectedTime: $selectedTime)';
}
