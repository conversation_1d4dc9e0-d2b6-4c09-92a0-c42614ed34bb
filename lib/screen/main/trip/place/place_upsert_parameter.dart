import 'package:family_app/data/model/trip_model.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class InitialLocation {
  final String? name;
  final LatLng? latLng;

  const InitialLocation({
    this.name,
    this.latLng,
  });
}

/// Parameter class for the place upsert screen
class PlaceUpsertParameter {
  final String tripId;
  final int dayIndex;
  final int? activityIndex;
  final Itinerary? place;
  final bool viewMode;
  final List<Activity> allActivities;
  final InitialLocation? initialLocation;

  const PlaceUpsertParameter({
    required this.tripId,
    required this.dayIndex,
    this.activityIndex,
    this.place,
    this.viewMode = false,
    this.allActivities = const [],
    this.initialLocation,
  });

  @override
  String toString() => 'PlaceUpsertParameter('
      'tripId: $tripId, '
      'dayIndex: $dayIndex, '
      'activityIndex: $activityIndex, '
      'place: $place, '
      'viewMode: $viewMode, '
      'allActivities: $allActivities, '
      'initialLocation: $initialLocation)';
}
