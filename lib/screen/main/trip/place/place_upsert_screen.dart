import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_cubit.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/place/place_upsert_state.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/services.dart';

@RoutePage()
class PlaceUpsertPage
    extends BaseBlocProvider<PlaceUpsertState, PlaceUpsertCubit> {
  const PlaceUpsertPage({required this.parameter, super.key});

  final PlaceUpsertParameter parameter;

  @override
  PlaceUpsertCubit createCubit() => PlaceUpsertCubit(
        parameter: parameter,
        activityRepository: locator.get(),
      );

  @override
  Widget buildPage() => Builder(
        builder: (context) {
          final cubit = context.read<PlaceUpsertCubit>();
          cubit.router ??= AutoRouter.of(context);
          return const PlaceUpsertView();
        },
      );
}

class PlaceUpsertView extends StatefulWidget {
  const PlaceUpsertView({super.key});

  @override
  State<PlaceUpsertView> createState() => _PlaceUpsertViewState();
}

class _PlaceUpsertViewState extends BaseBlocPageState<PlaceUpsertView,
    PlaceUpsertState, PlaceUpsertCubit> {
  final _formKey = GlobalKey<FormState>();

  final _descriptionHandler = TextFieldHandler(
    field: 'description',
    title: 'Description',
    hintText: 'Enter a description',
    maxLength: 200,
    isFieldValid: (value) => true,
  );

  final _venueHandler = TextFieldHandler(
    field: 'venue',
    title: 'Venue',
    hintText: 'Enter venue',
    isRequired: true,
    isFieldValid: (value) => value.isNotEmpty,
  );

  final _cityHandler = TextFieldHandler(
    field: 'city',
    title: 'City',
    hintText: 'Enter city',
    isRequired: true,
    isFieldValid: (value) => value.isNotEmpty,
  );

  final _timeHandler = TextFieldHandler(
    field: 'time',
    title: 'Duration',
    hintText: 'Enter duration in minutes',
    isRequired: true,
    isFieldValid: (value) {
      if (value.isEmpty) return false;
      // final timeRegex = RegExp(
      //     r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]\s*-\s*([0-1]?[0-9]|2[0-3]):[0-5][0-9]$');
      return true;
    },
  );

  final _activityImageHandler = TextFieldHandler(
    field: 'activityImage',
    title: 'Activity Image URL',
    hintText: 'Enter image URL',
    isFieldValid: (value) {
      if (value.isEmpty) return true;
      final urlRegex = RegExp(r'^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$');
      return urlRegex.hasMatch(value);
    },
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeControllers();
    });
  }

  void _initializeControllers() {
    final state = context.read<PlaceUpsertCubit>().state;
    if (state.place != null && state.place!.activities?.isNotEmpty == true) {
      final activity = state.place!.activities!.first;
      _descriptionHandler.text = activity.description ?? '';
      _venueHandler.text = activity.venue ?? '';
      _cityHandler.text = activity.city ?? '';
      _timeHandler.text = state.duration.toString();
      _activityImageHandler.text = state.activityImage ?? '';
    }
  }

  @override
  void didUpdateWidget(covariant PlaceUpsertView oldWidget) {
    super.didUpdateWidget(oldWidget);
    _syncControllersWithState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _syncControllersWithState();
  }

  void _syncControllersWithState() {
    final state = context.read<PlaceUpsertCubit>().state;
    if (state.description != _descriptionHandler.text) {
      _descriptionHandler.text = state.description;
    }
    if (state.venue != _venueHandler.text) {
      _venueHandler.text = state.venue;
    }
    if (state.city != _cityHandler.text) {
      _cityHandler.text = state.city;
    }
    if (state.duration != _timeHandler.text) {
      _timeHandler.text = state.duration.toString();
    }
    if (state.activityImage != _activityImageHandler.text) {
      _activityImageHandler.text = state.activityImage ?? '';
    }
  }

  @override
  Widget buildAppBar(
      BuildContext context, PlaceUpsertCubit cubit, PlaceUpsertState state) {
    return CustomAppBar2(
      title: state.place == null ? 'Add Place' : 'Edit Place',
      showBack: true,
      actions: [
        GestureDetector(
          onTap: () => _savePlace(cubit),
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: appTheme.backgroundV2,
            padding: padding(all: 7),
            child: state.isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Assets.icons.icActionCheck.svg(),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(
      BuildContext context, PlaceUpsertCubit cubit, PlaceUpsertState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: paddingV2(top: 8),
      child: Card(
        margin: const EdgeInsets.only(left: 8, right: 8, top: 0, bottom: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        elevation: 2,
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (state.activityImage != null) ...[
                  _buildImageSection(context, cubit, state),
                  const SizedBox(height: 16),
                ],
                _buildLocationSection(context, cubit, state),
                const SizedBox(height: 16),
                _buildFormFields(context, cubit, state),
                if (state.error != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            state.error!,
                            style: TextStyle(color: Colors.red.shade700),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormFields(
      BuildContext context, PlaceUpsertCubit cubit, PlaceUpsertState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleTextFieldV2(
          fieldNode: _descriptionHandler,
          showTitle: true,
          maxLine: 3,
          inputAction: TextInputAction.next,
        ),
        const SizedBox(height: 16),
        TitleTextFieldV2(
          fieldNode: _venueHandler,
          showTitle: true,
          inputAction: TextInputAction.next,
        ),
        const SizedBox(height: 16),
        TitleTextFieldV2(
          fieldNode: _cityHandler,
          showTitle: true,
          inputAction: TextInputAction.next,
        ),
        const SizedBox(height: 16),
        // _buildTime(context, cubit, state),
        Row(
          children: [
            _buildTime(context, cubit, state),
          ],
        ),
        const SizedBox(height: 16),
        TitleTextFieldV2(
          fieldNode: _timeHandler,
          showTitle: true,
          keyboardType: TextInputType.datetime,
          //inputFormatters: [
          //  // FilteringTextInputFormatter.digitsOnly,
          //  _TimeInputFormatter(),
          //],
          inputAction: TextInputAction.done,
        ),
      ],
    );
  }

  Widget _buildTime(
      BuildContext context, PlaceUpsertCubit cubit, PlaceUpsertState state) {
    final time =  state.arrivalTime;
    return Expanded(
      flex: 40,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Time',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
          ),
          const SizedBox(height: 4),
          InkWell(
              onTap: () => cubit.selectTime(context),
              child: Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  border: Border.all(color: appTheme.borderGreyColor),
                  // border: Border.all(color: isValid ? appTheme.borderGreyColor : Colors.red),
                  borderRadius: BorderRadius.circular(5.0),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(time != null ? _formatTimeOfDay(time) : 'Select Time'),
                    const SizedBox(width: 4),
                    SvgPicture.asset(Assets.icons.icClock.path),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hours = time.hour.toString().padLeft(2, '0'); // Ensures two digits
    final minutes = time.minute.toString().padLeft(2, '0');
    return "$hours:$minutes";
  }

  Widget _buildImageSection(
      BuildContext context, PlaceUpsertCubit cubit, PlaceUpsertState state) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: AspectRatio(
          aspectRatio: 16.0 / 9.0,
          child: Stack(
            fit: StackFit.expand,
            children: [
              if (state.activityImage?.isNotEmpty == true)
                CachedNetworkImage(
                  imageUrl: state.activityImage!,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) =>
                      _buildPlaceholderImage(),
                )
              else
                _buildPlaceholderImage(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.image, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 8),
          Text(
            'Add a place image',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection(
      BuildContext context, PlaceUpsertCubit cubit, PlaceUpsertState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PrimaryButtonV2(
          text: 'Select Location on Map',
          icon: Assets.icons.icMapWhite.path,
          onTap: () => _selectLocation(context, cubit),
        ),
        if (state.selectedLocation != null) ...[
          const SizedBox(height: 8),
          Text(
            'Selected Location: ${state.selectedLocation!.latitude}, ${state.selectedLocation!.longitude}',
            style: AppStyle.regular14V2(color: appTheme.grayV2),
          ),
        ],
      ],
    );
  }

  Future<void> _selectLocation(
      BuildContext context, PlaceUpsertCubit cubit) async {
    final currentState = cubit.state;
    final currentActivity = Activity(
      description: currentState.description,
      venue: currentState.venue,
      city: currentState.city,
      time: currentState.time,
      latitude: currentState.selectedLocation?.latitude,
      longitude: currentState.selectedLocation?.longitude,
      activityImage: currentState.activityImage,
    );

    final result = await context.router.push(
      PlaceMapSelectionRoute(
        parameter: PlaceUpsertParameter(
          tripId: cubit.parameter.tripId,
          dayIndex: cubit.parameter.dayIndex,
          activityIndex: cubit.parameter.activityIndex,
          place: Itinerary(activities: [currentActivity]),
          initialLocation: cubit.parameter.initialLocation,
        ),
      ),
    );

    if (result != null &&
        result is PlaceUpsertParameter &&
        result.place?.activities?.isNotEmpty == true) {
      final activity = result.place!.activities!.first;
      _descriptionHandler.text = activity.description ?? '';
      _venueHandler.text = activity.venue ?? '';
      _cityHandler.text = activity.city ?? '';
      cubit.updateFromPlaceDetails(
        location: LatLng(activity.latitude!, activity.longitude!),
        venue: activity.venue ?? '',
        city: activity.city ?? '',
        activityImage: activity.activityImage,
        time: activity.time,
      );
    }
  }

  Future<void> _savePlace(PlaceUpsertCubit cubit) async {
    if (_formKey.currentState?.validate() ?? false) {
      await cubit.savePlace(Activity(
        description: _descriptionHandler.text,
        venue: _venueHandler.text,
        city: _cityHandler.text,
        time: _timeHandler.text,
        activityImage: _activityImageHandler.text,
        latitude: cubit.state.selectedLocation?.latitude,
        longitude: cubit.state.selectedLocation?.longitude,
      ));
    }
  }

  @override
  void dispose() {
    _descriptionHandler.dispose();
    _venueHandler.dispose();
    _cityHandler.dispose();
    _timeHandler.dispose();
    _activityImageHandler.dispose();
    super.dispose();
  }
}

class _TimeInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final text = newValue.text;

    if (text.isEmpty) {
      return newValue;
    }

    // Remove all non-digit characters
    final digits = text.replaceAll(RegExp(r'[^\d]'), '');

    if (digits.isEmpty) {
      return newValue;
    }

    // Format the time
    String formatted = '';
    if (digits.length <= 2) {
      formatted = digits;
    } else if (digits.length <= 4) {
      formatted = '${digits.substring(0, 2)}:${digits.substring(2)}';
    } else {
      formatted =
          '${digits.substring(0, 2)}:${digits.substring(2, 4)} - ${digits.substring(4, 6)}:${digits.substring(6)}';
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
