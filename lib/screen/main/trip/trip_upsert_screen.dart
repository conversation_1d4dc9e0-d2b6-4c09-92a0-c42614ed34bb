import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/country.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:family_app/utils/loading.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'trip_upsert_cubit.dart';
import 'trip_upsert_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class TripUpsertPage extends BaseBlocProvider<TripUpsertState, TripUpsertCubit> {
  final String? tripId;
  const TripUpsertPage({this.tripId, super.key});

  @override
  Widget buildPage() => TripUpsertView(tripId: tripId);

  @override
  TripUpsertCubit createCubit() => TripUpsertCubit(activityRepository: locator.get(), familyRepository: locator.get());
}

class TripUpsertView extends StatefulWidget {
  final String? tripId;
  const TripUpsertView({this.tripId, super.key});

  @override
  State<TripUpsertView> createState() => _TripUpsertViewState();
}

class _TripUpsertViewState extends BaseBlocPageState<TripUpsertView, TripUpsertState, TripUpsertCubit> {
  late TextEditingController _tripNameController;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _tripNameController = TextEditingController();
    final cubit = context.read<TripUpsertCubit>();
    if (widget.tripId != null) {
      cubit.init(tripId: widget.tripId);
    } else {
      cubit.init();
    }
  }

  @override
  void dispose() {
    _tripNameController.dispose();
    super.dispose();
  }

  @override
  bool? get isBottomSafeArea => false;

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool listenWhen(TripUpsertState previous, TripUpsertState current) {
    if (current.status == TripUpsertStatus.loading) {
      showLoading();
    } else {
      dismissLoading();
    }
    if (current.status == TripUpsertStatus.success) {
      Navigator.of(context).pop(true);
    }
    if (previous.tripName != current.tripName) {
      _tripNameController.text = current.tripName ?? '';
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return CustomAppBar2(
      title: widget.tripId != null ? 'Edit trip' : 'New trip',
      showBack: true,
      actions: [
        GestureDetector(
          onTap: () {
            if (_formKey.currentState?.validate() ?? false) {
              cubit.saveTrip(isEdit: widget.tripId != null, tripId: widget.tripId);
            }
          },
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: appTheme.backgroundV2,
            padding: padding(all: 8),
            child: SvgPicture.asset(Assets.icons.icActionCheck.path),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Form(
      key: _formKey,
      child: _buildBodyContainer(context, cubit, state),
    );
  }

  Widget _buildBodyContainer(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.0),
      ),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _buildCardBody(context, cubit, state),
      ),
    );
  }

  Widget _buildCardBody(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCover(context, cubit, state),
        const SizedBox(height: 24),
        _buildTripName(context, cubit, state),
        const SizedBox(height: 24),
        _buildFromDate(context, cubit, state),
        const SizedBox(height: 24),
        _buildToDate(context, cubit, state),
        const SizedBox(height: 24),
        _buildCountries(context, cubit, state),
      ],
    );
  }

  Widget _buildCover(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Colors.white,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: AspectRatio(
          aspectRatio: 16.0 / 9.0,
          child: Stack(
            children: [
              Center(
                child: state.coverImageFilePath != null
                    ? Image.file(
                        File(state.coverImageFilePath!),
                        fit: BoxFit.cover,
                        width: double.infinity,
                      )
                    : Image.asset(
                        Assets.images.birthdayRegistryCover.path,
                        fit: BoxFit.cover,
                        width: double.infinity,
                      ),
              ),
              Positioned(
                left: 8,
                bottom: 8,
                child: state.coverImageFilePath != null
                    ? _buildEditCover(context, cubit, state)
                    : _buildUploadCover(context, cubit, state),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUploadCover(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          showImagePicker(context, cubit, state);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: .56),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            LocaleKeys.upload_cover.tr(),
            style: AppStyle.regular14V2(color: appTheme.whiteText),
          ),
        ),
      ),
    );
  }

  Widget _buildEditCover(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Material(
      color: Colors.transparent,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(
            onTap: () {
              showImagePicker(context, cubit, state);
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(.56),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                LocaleKeys.change.tr(),
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () {
              cubit.updateCoverImageFile(null);
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(.56),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                LocaleKeys.remove.tr(),
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void showImagePicker(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      cubit.updateCoverImageFile(image.path);
    }
  }

  Widget _buildTripName(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: TextFormField(
        controller: _tripNameController,
        decoration: InputDecoration(
          hintText: 'Untitled trip',
          hintStyle: TextStyle(color: appTheme.borderColorV2),
          border: InputBorder.none,
        ),
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        textCapitalization: TextCapitalization.sentences,
        validator: (value) {
          if (value?.isEmpty ?? true) {
            return 'Please enter a trip name';
          }
          return null;
        },
        onChanged: (value) {
          cubit.updateTripName(value);
        },
      ),
    );
  }

  Widget _buildFromDate(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            LocaleKeys.start_date.tr(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
            ),
          ),
          InkWell(
            onTap: () async {
              final date = state.fromDate;
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: date ?? DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2101),
              );
              if (picked != null) {
                cubit.updateFromDate(picked);
              }
            },
            child: Container(
              padding: EdgeInsets.zero,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    state.fromDate != null
                        ? DateFormat('dd/MM/yyyy').format(state.fromDate!)
                        : 'dd / mm / yyyy',
                    style: TextStyle(
                      fontWeight: FontWeight.normal,
                      color: state.fromDate != null
                          ? Colors.black
                          : appTheme.grayV2,
                    ),
                  ),
                  const SizedBox(width: 8),
                  SvgPicture.asset(
                    Assets.icons.icCalendar.path,
                    width: 24.w2,
                    colorFilter:
                        ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToDate(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            LocaleKeys.end_date.tr(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
            ),
          ),
          InkWell(
            onTap: () async {
              final date = state.toDate;
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: date ?? DateTime.now(),
                firstDate: DateTime(2000),
                lastDate: DateTime(2101),
              );
              if (picked != null) {
                cubit.updateToDate(picked);
              }
            },
            child: Container(
              padding: EdgeInsets.zero,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    state.toDate != null
                        ? DateFormat('dd/MM/yyyy').format(state.toDate!)
                        : 'dd / mm / yyyy',
                    style: TextStyle(
                      fontWeight: FontWeight.normal,
                      color: state.toDate != null
                          ? Colors.black
                          : appTheme.grayV2,
                    ),
                  ),
                  const SizedBox(width: 8),
                  SvgPicture.asset(
                    Assets.icons.icCalendar.path,
                    width: 24.w2,
                    colorFilter:
                        ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountries(BuildContext context, TripUpsertCubit cubit, TripUpsertState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Country(s) to visit',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: appTheme.hintColor),
          ),
          child: _CountryDropdownMultiSelect(
            selectedCountryCode: state.countryCode,
            onChanged: (selected) => cubit.emit(state.copyWith(countryCode: selected)),
          ),
        ),
      ],
    );
  }
}

class _CountryDropdownMultiSelect extends StatelessWidget {
  final String? selectedCountryCode;
  final ValueChanged<String?> onChanged;

  const _CountryDropdownMultiSelect({
    required this.selectedCountryCode,
    required this.onChanged,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownSearch<String>(
      items: (String filter, _) async => CountryConstants.countryCodes,
      selectedItem: selectedCountryCode,
      mode: Mode.custom,
      onChanged: onChanged,
      itemAsString: (String countryCode) => CountryConstants.getLocalizedCountryName(countryCode),
      dropdownBuilder: (context, selectedItem) {
        if (selectedItem == null || selectedItem.isEmpty) {
          return Text(
            'Select country',
            style: AppStyle.regular14(color: appTheme.hintColor),
          );
        }
        return Chip(
          label: Text(CountryConstants.getLocalizedCountryName(selectedItem)),
          backgroundColor: appTheme.primaryColorV2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.w),
          ),
          side: BorderSide(color: appTheme.primaryColorV2),
          labelStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: appTheme.whiteText,
            height: 1.2,
          ),
          labelPadding: const EdgeInsets.symmetric(horizontal: 2, vertical: 0),
          padding: paddingV2(left: 4, right: 0),
          deleteIconColor: appTheme.whiteText,
          onDeleted: () {
            onChanged(null);
          },
        );
      },
      popupProps: PopupProps.menu(
        showSearchBox: true,
        searchFieldProps: TextFieldProps(
          cursorColor: appTheme.primaryColor,
          decoration: InputDecoration(
            filled: true,
            fillColor: appTheme.transparentWhiteColor,
            hintText: 'Search country',
            hintStyle: AppStyle.regular14(color: appTheme.hintColor),
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide.none,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
}
