import 'dart:io';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/document_model.dart';
import 'package:family_app/data/model/file_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/main/trip/document/document_upsert_parameter.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/upload.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:image_picker/image_picker.dart';

import 'package:auto_route/auto_route.dart';

import 'document_upsert_state.dart';

class DocumentUpsertCubit extends BaseCubit<DocumentUpsertState> {
  final IActivityRepository activityRepository;
  final DocumentUpsertParameter parameter;
  final IFamilyRepository familyRepository = locator.get<IFamilyRepository>();

  DocumentUpsertCubit({
    required this.activityRepository,
    required this.parameter,
    DocumentModel? document,
  }) : super(DocumentUpsertState(
          isEditing: document != null,
          document: document,
          title: document?.title ?? '',
          description: document?.description ?? '',
          fileName: (document != null && document.files.isNotEmpty) ? document.files.first.fileName : '',
          filePath: (document != null && document.files.isNotEmpty) ? document.files.first.fileUrl : '',
          files: document?.files ?? [],
        ));

  final AccountService accountService = locator.get();

  void onInitDocument(DocumentModel? document) {
    if (document != null) {
      final file = document.files.isNotEmpty ? document.files.first : null;
      emit(state.copyWith(
        isEditing: true,
        document: document,
        title: document.title,
        description: document.description,
        fileName: file?.fileName ?? '',
        filePath: file?.fileUrl ?? '',
        files: document.files,
      ));
    }
  }

  void setTitle(String title) {
    emit(state.copyWith(title: title));
  }

  void setDescription(String description) {
    emit(state.copyWith(description: description));
  }

  Future<void> pickFileOrImage(BuildContext context) async {
    try {
      emit(state.copyWith(isLoading: true));
      
      // Show dialog to choose between image and document
      final String? choice = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select File Type'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.image),
                  title: const Text('Image from Photos'),
                  subtitle: const Text('Pick from your photo gallery'),
                  onTap: () => Navigator.of(context).pop('image'),
                ),
                ListTile(
                  leading: const Icon(Icons.description),
                  title: const Text('Document from Files'),
                  subtitle: const Text('Pick PDF'),
                  onTap: () => Navigator.of(context).pop('document'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          );
        },
      );

      if (choice == null) {
        emit(state.copyWith(isLoading: false));
        return;
      }

      if (choice == 'image') {
        await _pickImage();
      } else if (choice == 'document') {
        await _pickDocument();
      }
    } catch (e) {
      AppLogger.e('Error picking file: $e');
      showSimpleToast('Error selecting file');
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        File file = File(image.path);
        String fileName = path.basename(file.path);
        AppLogger.d('Picked image: ${file.path}');
        final List<FileModel> currentFiles = List.from(state.files);
        // Mark local files with fileUuid = null, fileType = 'local'
        currentFiles.add(FileModel(
          fileUuid: null,
          fileName: fileName,
          fileUrl: file.path,
          fileType: 'local',
          fileSize: null,
        ));
        emit(state.copyWith(
          fileName: fileName,
          filePath: file.path,
          files: currentFiles,
          hasSelectedFile: true,
          isLoading: false,
        ));
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      AppLogger.e('Error picking image: $e');
      showSimpleToast('Error selecting image');
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> _pickDocument() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom, // Use custom for documents to access Files app
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'xls',
          'xlsx',
          'txt',
        ],
      );
      if (result != null) {
        File file = File(result.files.single.path!);
        String fileName = path.basename(file.path);
        AppLogger.d('Picked document: ${file.path}');
        final List<FileModel> currentFiles = List.from(state.files);
        // Mark local files with fileUuid = null, fileType = 'local'
        currentFiles.add(FileModel(
          fileUuid: null,
          fileName: fileName,
          fileUrl: file.path,
          fileType: 'local',
          fileSize: null,
        ));
        emit(state.copyWith(
          fileName: fileName,
          filePath: file.path,
          files: currentFiles,
          hasSelectedFile: true,
          isLoading: false,
        ));
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      AppLogger.e('Error picking file: $e');
      showSimpleToast('Error selecting file');
      emit(state.copyWith(isLoading: false));
    }
  }
  Future<void> removeFile(int index, BuildContext context) async {
    final files = List<FileModel>.from(state.files);
    if (index < 0 || index >= files.length) {
      AppLogger.e('Remove file failed: invalid index $index');
      return;
    }
    final file = files[index];
    // Confirm if remote
    if (file.fileType != 'local' && file.fileUuid != null) {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (ctx) => AlertDialog(
          title: const Text('Delete File'),
          content: const Text('Are you sure you want to delete this file from the server?'),
          actions: [
            TextButton(onPressed: () => Navigator.of(ctx).pop(false), child: const Text('No')),
            TextButton(onPressed: () => Navigator.of(ctx).pop(true), child: const Text('Yes')),
          ],
        ),
      );
      if (confirm != true) return;
    }
    await _removeAndPersistFile(index);
  }

  Future<void> _removeAndPersistFile(int index) async {
    final files = List<FileModel>.from(state.files);
    if (index < 0 || index >= files.length) {
      AppLogger.e('Remove file failed: invalid index $index');
      return;
    }
    final file = files[index];
    try {
      emit(state.copyWith(isLoading: true));
      bool isRemote = file.fileType != 'local' && file.fileUuid != null;
      files.removeAt(index);
      emit(state.copyWith(files: files));
      // Only persist to server if editing and file is remote
      if (state.isEditing && isRemote) {
        try {
          await familyRepository.removeStorageFile(file.fileUuid!);
        } catch (e) {
          AppLogger.e('Failed to delete remote file: $e');
          showSimpleToast('Failed to delete remote file');
          return;
        }
      }
    } catch (e) {
      AppLogger.e('Error removing file: $e');
      showSimpleToast('Error removing file');
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<DocumentModel?> saveDocument(BuildContext context,
      {required String title, required String description}) async {
    emit(state.copyWith(isLoading: true));
    try {
      // Upload local files (fileType == 'local'), keep remote files as is
      final uploader = Upload(familyId: accountService.familyId);
      List<FileModel> finalFiles = [];
      for (final file in state.files) {
        if (file.fileType == 'local') {
          // Upload local file
          final localFile = File(file.fileUrl);

          final storage = await uploader.uploadFile(localFile, file.fileName);

          finalFiles.add(FileModel(
            fileUuid: storage.uuid,
            fileName: storage.fileName ?? '',
            fileUrl: storage.filePath ?? '',
            fileType: storage.extraData,
            fileSize: storage.fileSize,
          ));
        } else {
          // Already remote
          finalFiles.add(file);
        }
      }

      final doc = DocumentModel(
        title: title,
        description: description,
        files: finalFiles,
      );
      emit(state.copyWith(isLoading: false, document: doc));

      // context.maybePop(doc);
      return doc;
    } catch (e, stackTrace) {
      AppLogger.e('Error saving document: $e \n $stackTrace');
      showSimpleToast('Error saving document');
      emit(state.copyWith(status: TripDocumentUpsertStatus.error));
      return null;
    }
  }

  bool validateFields() {
    return state.title.isNotEmpty || (state.hasSelectedFile || state.files.isNotEmpty || state.isEditing);
  }
}
