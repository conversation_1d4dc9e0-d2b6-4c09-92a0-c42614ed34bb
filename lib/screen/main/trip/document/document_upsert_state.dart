import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/document_model.dart';
import 'package:family_app/data/model/file_model.dart';

enum TripDocumentUpsertStatus { initial, loading, success, error }


class DocumentUpsertState extends BaseState {
  final DocumentModel? document;
  final String title;
  final String description;
  final String fileName;
  final String filePath;
  final List<FileModel> files;
  final TripDocumentUpsertStatus status;
  final bool isEditing;
  final bool hasSelectedFile;
  final String? error;

  DocumentUpsertState({
    super.isLoading = false,
    this.document,
    this.title = '',
    this.description = '',
    this.fileName = '',
    this.filePath = '',
    this.files = const [],
    this.status = TripDocumentUpsertStatus.initial,
    this.isEditing = false,
    this.hasSelectedFile = false,
    this.error,
  });

  bool get isValid => title.isNotEmpty || (hasSelectedFile || files.isNotEmpty || isEditing);

  DocumentUpsertState copyWith({
    bool? isLoading,
    DocumentModel? document,
    String? title,
    String? description,
    String? fileName,
    String? filePath,
    List<FileModel>? files,
    TripDocumentUpsertStatus? status,
    bool? isEditing,
    bool? hasSelectedFile,
    String? error,
  }) {
    return DocumentUpsertState(
      isLoading: isLoading ?? this.isLoading,
      document: document ?? this.document,
      title: title ?? this.title,
      description: description ?? this.description,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      files: files ?? this.files,
      status: status ?? this.status,
      isEditing: isEditing ?? this.isEditing,
      hasSelectedFile: hasSelectedFile ?? this.hasSelectedFile,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    document,
    title,
    description,
    fileName,
    filePath,
    files,
    status,
    isEditing,
    hasSelectedFile,
    error,
  ];
}
