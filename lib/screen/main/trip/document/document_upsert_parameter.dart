import 'package:family_app/data/model/activity.dart';

class DocumentUpsertParameter {
  final ActivityModel activity;
  final int? dayIndex;
  final int? activityIndex; // Index of the activity in the day's activities list
  final bool addNew;
  final bool isRootDocument;

  DocumentUpsertParameter({
    required this.activity,
    this.dayIndex,
    this.activityIndex,
    this.addNew = false,
    this.isRootDocument = false,
  });
}
