import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/image_cache_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/document_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/model/file_model.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/document/document_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/config/service/log_service.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/custom_easy_image_viewer/easy_image_viewer.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;

import 'document_upsert_cubit.dart';
import 'document_upsert_state.dart';
import 'package:family_app/widget/pdf_viewer_screen.dart';

@RoutePage()
class DocumentUpsertPage
    extends BaseBlocProvider<DocumentUpsertState, DocumentUpsertCubit> {
  final DocumentModel? document;

  const DocumentUpsertPage({
    required this.parameter,
    super.key,
    this.document,
  });

  final DocumentUpsertParameter parameter;

  @override
  Widget buildPage() => DocumentUpsertView();

  @override
  DocumentUpsertCubit createCubit() {
    final cubit = DocumentUpsertCubit(
      activityRepository: locator.get<IActivityRepository>(),
      parameter: parameter,
      document: document,
    );
    cubit.onInitDocument(document);
    return cubit;
  }
}

class DocumentUpsertView extends StatefulWidget {
  const DocumentUpsertView({super.key});
  @override
  State<DocumentUpsertView> createState() => _DocumentUpsertViewState();
}

class _DocumentUpsertViewState extends BaseBlocPageState<DocumentUpsertView,
    DocumentUpsertState, DocumentUpsertCubit> {
  final _formKey = GlobalKey<FormState>();
  late final TextFieldHandler _titleHandler;
  late final TextFieldHandler _descriptionHandler;

  @override
  String get title => context.read<DocumentUpsertCubit>().state.isEditing
      ? 'Edit Document'
      : 'Add Document';

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool listenWhen(DocumentUpsertState previous, DocumentUpsertState current) {
    if (current.status == TripDocumentUpsertStatus.loading) {
      logd("Show Loading!");
      showLoading();
    } else if (current.status == TripDocumentUpsertStatus.success && current.document != null) {
      dismissLoading();
      AppLogger.d('Document upsert success: \\${current.document}');
      // context.router.maybePop(current.document);
      context.router
          .popUntil((route) => route.settings.name == TripDetailRoute.name);
    } else if (current.status == TripDocumentUpsertStatus.error) {
      dismissLoading();
      // Optionally show error feedback
    }
    return super.listenWhen(previous, current);
  }

  @override
  void initState() {
    super.initState();
    _titleHandler = TextFieldHandler(
      field: 'title',
      title: 'Title',
      hintText: 'Enter document title',
      isRequired: true,
      isFieldValid: (value) => value.trim().isNotEmpty,
    );
    _descriptionHandler = TextFieldHandler(
      field: 'description',
      title: 'Description (optional)',
      hintText: 'Enter document description',
      isFieldValid: (value) => true,
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeControllers();
    });
  }

  void _initializeControllers() {
    final state = context.read<DocumentUpsertCubit>().state;
    _titleHandler.text = state.title;
    _descriptionHandler.text = state.description;
  }

  @override
  void didUpdateWidget(covariant DocumentUpsertView oldWidget) {
    super.didUpdateWidget(oldWidget);
    _syncControllersWithState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _syncControllersWithState();
  }

  void _syncControllersWithState() {
    final state = context.read<DocumentUpsertCubit>().state;
    if (state.title != _titleHandler.text) {
      _titleHandler.text = state.title;
    }
    if (state.description != _descriptionHandler.text) {
      _descriptionHandler.text = state.description;
    }
  }

  @override
  Widget buildAppBar(BuildContext context, DocumentUpsertCubit cubit,
      DocumentUpsertState state) {
    return CustomAppBar2(
      title: title,
      showBack: true,
    );
  }

  @override
  Widget buildBody(BuildContext context, DocumentUpsertCubit cubit, DocumentUpsertState state) {
    if (state.status == TripDocumentUpsertStatus.loading || state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
    return BlocBuilder<DocumentUpsertCubit, DocumentUpsertState>(
      builder: (context, state) {
        
        if (state.status == TripDocumentUpsertStatus.error) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Error saving document. Please check your input.')),
            );
          });
        }
        return SingleChildScrollView(
          padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 8),
          child: Card(
            margin: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.0),
            ),
            elevation: 2,
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormFields(context, cubit, state),
                    const SizedBox(height: 24),
                    _buildFileSection(state, cubit),
                    if (state.error != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline, color: Colors.red.shade700),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                state.error!,
                                style: TextStyle(color: Colors.red.shade700),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    const SizedBox(height: 48),
                    _buildSaveButton(cubit, state),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormFields(BuildContext context, DocumentUpsertCubit cubit,
      DocumentUpsertState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleTextFieldV2(
          fieldNode: _titleHandler,
          showTitle: true,
          inputAction: TextInputAction.next,
        ),
        const SizedBox(height: 16),
        TitleTextFieldV2(
          fieldNode: _descriptionHandler,
          showTitle: true,
          maxLine: 3,
          inputAction: TextInputAction.done,
        ),
      ],
    );
  }

  Widget _buildFileSection(DocumentUpsertState state, DocumentUpsertCubit cubit)  {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Documents',
          style: AppStyle.medium16().copyWith(color: appTheme.blackText),
        ),
        const SizedBox(height: 12),
        _buildDocumentPicker(state, cubit),
      ],
    );
  }

  Widget _buildDocumentPicker(DocumentUpsertState state, DocumentUpsertCubit cubit) {
    return Wrap(
      spacing: 8.0, // Space between items
      runSpacing: 8.0, // Space between rows
      children: [
        for (int i = 0; i < state.files.length; i++)
          _buildFileModelItem(cubit, state.files[i], i),
        _buildAddFileButton(cubit),
      ],
    );
  }
  Future<void> _onFileTap(FileModel file) async {
      final fileName = file.fileName;
    final extension = path.extension(fileName).toLowerCase();
    final isImage = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(extension);

    // For images, show image viewer
    if (isImage) {
      await _showImageViewer(file);
      return;
    }
      
    // For documents, open in external viewer
    await _openDocument(file);
  }
    


  Widget _buildFileModelItem(DocumentUpsertCubit cubit, FileModel file, int index) {
   
    final fileName = file.fileName;
    final extension = path.extension(fileName).toLowerCase();
    IconData iconData;
    Color iconColor = appTheme.primaryColor;
    if (['.pdf'].contains(extension)) {
      iconData = Icons.picture_as_pdf;
      iconColor = Colors.red;
    } else if (['.doc', '.docx'].contains(extension)) {
      iconData = Icons.description;
      iconColor = Colors.blue;
    } else if (['.xls', '.xlsx'].contains(extension)) {
      iconData = Icons.table_chart;
      iconColor = Colors.green;
    } else if (['.jpg', '.jpeg', '.png'].contains(extension)) {
      iconData = Icons.image;
      iconColor = Colors.purple;
    } else {
      iconData = Icons.insert_drive_file;
    }
    return Stack(
      children: [
        GestureDetector(
          onTap: () => {_onFileTap(file)},
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: appTheme.background,
              borderRadius: BorderRadius.circular(10.0),
              border: Border.all(color: appTheme.borderColor),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  iconData,
                  color: iconColor,
                  size: 32,
                ),
                const SizedBox(height: 4),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Text(
                    fileName,
                    style: AppStyle.regular10(color: appTheme.fadeTextColor),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => cubit.removeFile(index, context),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _showImageViewer(FileModel file) async {
    try {
      String imageUrl;

      if (file.fileType == 'local') {
        // Local file - use file path directly
        imageUrl = file.fileUrl;
      } else {
        // Remote file - get URL from cache service
        final url = await imageCacheService.getImageUrl(file.fileUuid!);
        if (url == null) {
          showSimpleToast('Failed to load image');
          return;
        }
        imageUrl = url;
      }

      // Show image viewer using the existing pattern from your codebase
      showImageViewer(
        context,
        imageUrl.startsWith('http') ? NetworkImage(imageUrl) : FileImage(File(imageUrl)) as ImageProvider,
        doubleTapZoomable: true,
        swipeDismissible: true,
      );
    } catch (e) {
      AppLogger.e('Error showing image viewer: $e');
      showSimpleToast('Failed to open image');
    }
  }

  Future<void> _openDocument(FileModel file) async {
    try {
      String documentUrl;

      if (file.fileType == 'local') {
        documentUrl = file.fileUrl;
      } else {
        final url = await imageCacheService.getImageUrl(file.fileUuid!);
        if (url == null) {
          showSimpleToast('Failed to load document');
          return;
        }
        documentUrl = url;
      }

      final extension = path.extension(file.fileName).toLowerCase();
      if (extension == '.pdf') {
        if (!mounted) return;
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => PdfViewerScreen(pdfUrl: documentUrl),
          ),
        );
        return;
      }

      // For other documents, open in external viewer
      final uri = Uri.parse(documentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        showSimpleToast('Unable to open document');
      }
    } catch (e) {
      AppLogger.e('Error opening document: $e');
      showSimpleToast('Failed to open document');
    }
  }

  Widget _buildAddFileButton( DocumentUpsertCubit cubit) {
    return InkWell(
      onTap: () =>  cubit.pickFileOrImage(context),
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: appTheme.background,
          borderRadius: BorderRadius.circular(10.0),
          border: Border.all(
            color: appTheme.borderColor,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              color: appTheme.fadeTextColor,
              size: 32,
            ),
            const SizedBox(height: 4),
            Text(
              'Add File',
              style: AppStyle.regular10(color: appTheme.fadeTextColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton(DocumentUpsertCubit cubit, DocumentUpsertState state) {
    return PrimaryButtonV2(
      text: state.isEditing ? 'Save Changes' : 'Add Document',
      isLoading: state.status == TripDocumentUpsertStatus.loading,
      onTap: () {
        if (_formKey.currentState?.validate() ?? false) {
          cubit
              .saveDocument(
            context,
            title: _titleHandler.text,
            description: _descriptionHandler.text,
          )
              .then((value) {
            //value = DocumentModel?
            if (value != null) {
              //assuming save is done
              logd("Got value is \\${value.runtimeType}.. poping now ");
              context.maybePop((value));
            } else {
              logd("SaveDocument return NULL");
            }
          });
        }
      },
    );
  }
}
