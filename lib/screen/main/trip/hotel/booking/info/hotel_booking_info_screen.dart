import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/booking_personal_info.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/booking/info/hotel_booking_info_cubit.dart';
import 'package:family_app/screen/main/trip/hotel/booking/info/hotel_booking_info_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/booking/info/hotel_booking_info_state.dart';
import 'package:family_app/screen/main/trip/hotel/widgets/input_card_widget.dart';
import 'package:family_app/screen/main/trip/hotel/widgets/input_guest_widget.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:flutter/material.dart';

@RoutePage()
class HotelBookingInfoPage extends BaseBlocProvider<HotelBookingInfoState, HotelBookingInfoCubit> {
  const HotelBookingInfoPage({required this.parameter, super.key});

  final HotelBookingInfoParameter parameter;

  @override
  Widget buildPage() => const HotelBookingInfoView();

  @override
  HotelBookingInfoCubit createCubit() => HotelBookingInfoCubit(
        usecase: locator.get(),
        activityRepository: locator.get(),
        accountService: locator.get(),
        activityUsecase: locator.get(),
        localStorage: locator.get(),
        parameter: parameter,
      );
}

class HotelBookingInfoView extends StatefulWidget {
  const HotelBookingInfoView({super.key});

  @override
  State<HotelBookingInfoView> createState() => _HotelBookingInfoViewState();
}

class _HotelBookingInfoViewState
    extends BaseBlocPageState<HotelBookingInfoView, HotelBookingInfoState, HotelBookingInfoCubit> {
  @override
  Widget buildAppBar(BuildContext context, HotelBookingInfoCubit cubit, HotelBookingInfoState state) {
    return CustomAppBar2(
      title: LocaleKeys.booking_info.tr(),
      showBack: true,
      actions: [],
    );
  }

  @override
  bool listenWhen(HotelBookingInfoState previous, HotelBookingInfoState current) {
    if (current.status == HotelBookingInfoStatus.success) {
      // Navigate back to trip detail screen using route name instead of count
      // This is more reliable than using a fixed count
      context.router.popUntil((route) =>
          route.settings.name == TripDetailRoute.name);
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, HotelBookingInfoCubit cubit, HotelBookingInfoState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(8.0),
            child: Form(
              key: cubit.formKey,
              child: Column(
                children: [
                  InputGuestWidget(
                      guest: state.personalInfo ?? BookingPersonalInfo(id: 1),
                      onChanged: (newGuest) {
                        cubit.updateGuest(1, newGuest);
                      }),
                  InputCardWidget(card: state.card, onChanged: cubit.onCardChanged)
                ],
              ),
            ),
          ),
        ),
        if (state.error != null && state.error!.isNotEmpty)
          Container(
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: Text(
              state.error!,
              style: AppStyle.regular12V2(color: appTheme.redColor),
            ),
          ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
          decoration: BoxDecoration(
            color: appTheme.whiteText
          ),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(child: Text(LocaleKeys.total.tr(), style: AppStyle.bold16V2())),
                  Text("${state.totalPrice.currency} ${state.currency}", style: AppStyle.bold18V2(color: appTheme.primaryColorV2)),
                ],
              ),
              const SizedBox(height: 8),
              PrimaryButtonV2(
                text: LocaleKeys.pay.tr(),
                onTap: () {
                  cubit.onNext(context);
                },
              ),
            ],
          ),
        )
      ],
    );
  }
}
