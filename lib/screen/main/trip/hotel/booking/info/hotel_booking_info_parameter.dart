import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/hotel_model.dart';

class HotelBookingInfoParameter {
  final ActivityModel activity;
  final List<HotelOfferModel> offers;
  final String currency;
  final HotelModel hotel;
  final int dayIndex;
  final String? location;
  final DateTime checkInDate;
  final DateTime checkOutDate;

  HotelBookingInfoParameter({
    required this.activity,
    required this.offers,
    required this.hotel,
    required this.currency,
    required this.checkInDate,
    required this.checkOutDate,
    this.location,
    this.dayIndex = 0,
  });
}
