import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/booking_personal_info.dart';
import 'package:family_app/data/model/card_info.dart';

enum HotelBookingInfoStatus { initial, loading, success, error }

class HotelBookingInfoState extends BaseState {
  BookingPersonalInfo? personalInfo;
  CardInfo? card;
  double totalPrice;
  int updateCount;
  String? currency;
  String? error;
  final HotelBookingInfoStatus status;

  HotelBookingInfoState({
    super.isLoading = true,
    this.personalInfo,
    this.card,
    this.status = HotelBookingInfoStatus.initial,
    this.currency = "USD",
    this.updateCount = 0,
    this.totalPrice = 0.0,
    this.error,
  });

  HotelBookingInfoState copyWith({
    bool? isLoading,
    BookingPersonalInfo? personalInfo,
    CardInfo? card,
    HotelBookingInfoStatus? status,
    double? totalPrice,
    String? currency,
    error,
  }) {
    return HotelBookingInfoState(
      isLoading: isLoading ?? this.isLoading,
      personalInfo: personalInfo ?? this.personalInfo,
      card: card ?? this.card,
      status: status ?? this.status,
      totalPrice: totalPrice ?? this.totalPrice,
      currency: currency ?? this.currency,
      updateCount: updateCount + 1,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        personalInfo,
        card,
        status,
        totalPrice,
        currency,
        error,
        updateCount,
      ];
}
