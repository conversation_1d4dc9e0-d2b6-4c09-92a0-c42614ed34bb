import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/search_custom_filed.dart';
import 'package:flutter/material.dart';

import 'hotel_list_cubit.dart';
import 'hotel_list_parameter.dart';
import 'hotel_list_state.dart';
import 'widgets/hotel_item_widget.dart';

@RoutePage()
class HotelListPage extends BaseBlocProvider<HotelListState, HotelListCubit> {
  const HotelListPage({required this.parameter, super.key});

  final HotelListParameter parameter;

  @override
  Widget buildPage() => const HotelListView();

  @override
  HotelListCubit createCubit() => HotelListCubit(
        activityRepository: locator.get(),
        useCase: locator.get(),
        parameter: parameter,
      );
}

class HotelListView extends StatefulWidget {
  const HotelListView({super.key});

  @override
  State<HotelListView> createState() => _HotelListViewState();
}

class _HotelListViewState
    extends BaseBlocPageState<HotelListView, HotelListState, HotelListCubit> {
  @override
  Widget buildAppBar(
      BuildContext context, HotelListCubit cubit, HotelListState state) {
    return CustomAppBar2(
      title: LocaleKeys.select_new_hotel.tr(),
      showBack: true,
      actions: [],
    );
  }

  @override
  Widget buildBody(
      BuildContext context, HotelListCubit cubit, HotelListState state) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.only(left: 10, right: 10, top: 16),
          child: SearchCustomField(
            radius: 30,
            hintText: LocaleKeys.search_by_hotel.tr(),
            backgroundColor: appTheme.whiteText,
            onGetSearchValue: (text) => cubit.onGetSearchValue(text),
          ),
        ),
        Expanded(
          child: state.isLoading
              ? const Center(child: CircularProgressIndicator())
              : state.isError
                  ? Container(
                      margin: const EdgeInsets.all(20),
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: appTheme.whiteText,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 1,
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Error Title
                          Text(
                            "No Hotels Found",
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: appTheme.blackText,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 12),

                          // Location Info
                          if (state.city != null && state.city!.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: appTheme.primaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.location_on_outlined,
                                    size: 16,
                                    color: appTheme.primaryColor,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    state.city! +
                                        (state.country != null
                                            ? ", ${state.country}"
                                            : ""),
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: appTheme.primaryColor,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          const SizedBox(height: 16),

                          // Error Message
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey.shade200),
                            ),
                            child: Text(
                              state.errorMessage ??
                                  "Unable to find hotels for this location",
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                                height: 1.4,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Container(
                      margin:
                          const EdgeInsets.only(top: 16, left: 10, right: 10),
                      decoration: BoxDecoration(
                        color: appTheme.whiteText,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: ListView.builder(
                        itemCount: state.searchHotels?.isNotEmpty == true && state.textSearch != null
                            ? state.searchHotels?.length
                            : state.hotels?.length ?? 0,
                        padding: const EdgeInsets.all(8),
                        itemBuilder: (context, index) {
                          var hotelItem = state.searchHotels?.isNotEmpty == true
                              ? state.searchHotels![index]
                              : state.hotels![index];
                          return HotelItemWidget(
                              hotel: hotelItem,
                              onTap: (hotel) {
                                cubit.onHotelTap(context, hotel);
                              });
                        },
                      ),
                    ),
        )
      ],
    );
  }
}
