import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/amadeus/iamadeus_repository.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_state.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';

import 'details/hotel_detail_parameter.dart';

class HotelListCubit extends BaseCubit<HotelListState> {
  final _tag = "HotelListCubit";

  final IActivityRepository activityRepository;
  final HotelBookingUseCase useCase;
  final HotelListParameter parameter;

  String? keyword;

  // bool isBooking = false;
  // int pageItem = 10;
  // int maxPage = 10;
  int currentPage = 0;

  // List<AmadeusHotelModel> hotelList = [];

  HotelListCubit({
    required this.activityRepository,
    required this.useCase,
    required this.parameter,
  }) : super(HotelListState());

  @override
  void onInit() {
    _init();
    super.onInit();
  }

  _init() async {
    try {
      emit(state.copyWith(
          isLoading: true,
          city: parameter.activity.city,
          country: parameter.activity.country));
      List<int>? ratings;
      if (parameter.activity.hotelPreferences?.starRating != null) {
        ratings = [parameter.activity.hotelPreferences!.starRating!];
      }

      // Get the city code for hotel search
      String? cityCodeForSearch = await _getCityCodeForSearch();

      if (cityCodeForSearch == null || cityCodeForSearch.isEmpty) {
        logd("No valid city code found for hotel search", tag: _tag);

        String errorDetails = "";
        if (parameter.activity.city == null ||
            parameter.activity.city!.isEmpty) {
          errorDetails = "City name is missing from trip details.";
        } else if (parameter.activity.countryCode == null ||
            parameter.activity.countryCode!.isEmpty) {
          errorDetails = "Country information is missing from trip details.";
        } else {
          errorDetails =
              "Unable to find a valid city code for '${parameter.activity.city}' in ${parameter.activity.countryCode}.";
        }

        emit(state.copyWith(
            isLoading: false,
            isError: true,
            errorMessage:
                "Unable to search hotels: $errorDetails Please ensure your trip has complete location information."));
        return;
      }

      List<HotelModel> hotelList = await useCase.searchHotels(cityCodeForSearch,
          ratings: ratings,
          amenities: parameter.activity.hotelPreferences?.amenities);
      hotelList = hotelList.map((e) {
        e.city = parameter.activity.city;
        e.country = parameter.activity.country;
        return e;
      }).toList();
      emit(state.copyWith(hotels: hotelList, isLoading: false));
      logd("message length ${hotelList.length}");
    } catch (e) {
      logd("_init $e", tag: _tag);
      emit(state.copyWith(
          isLoading: false,
          isError: true,
          errorMessage: "Failed to load hotels: ${e.toString()}"));
    }
  }

  /// Get city code for hotel search with fallback logic
  Future<String?> _getCityCodeForSearch() async {
    // 1. Try to use existing cityCode from activity
    if (parameter.activity.cityCode != null &&
        parameter.activity.cityCode!.isNotEmpty) {
      String cityCode = parameter.activity.cityCode!.trim().toUpperCase();
      // Validate that it looks like an IATA code (3 letters)
      if (RegExp(r'^[A-Z]{3}$').hasMatch(cityCode)) {
        logd("Using existing cityCode: $cityCode", tag: _tag);
        return cityCode;
      } else {
        logd("Invalid cityCode format: $cityCode, will try alternatives",
            tag: _tag);
      }
    }

    // 2. Try to get city code from hotel preferences location
    if (parameter.activity.hotelPreferences?.location != null &&
        parameter.activity.hotelPreferences!.location!.isNotEmpty) {
      String location =
          parameter.activity.hotelPreferences!.location!.trim().toUpperCase();
      // Validate that it looks like an IATA code (3 letters)
      if (RegExp(r'^[A-Z]{3}$').hasMatch(location)) {
        logd("Using hotel preferences location as cityCode: $location",
            tag: _tag);
        return location;
      } else {
        logd("Hotel preferences location is not a valid IATA code: $location",
            tag: _tag);
      }
    }

    // 3. Try to search for city code using city name and country
    if (parameter.activity.city != null &&
        parameter.activity.city!.isNotEmpty &&
        parameter.activity.countryCode != null &&
        parameter.activity.countryCode!.isNotEmpty) {
      try {
        logd(
            "Searching city code for: ${parameter.activity.city}, ${parameter.activity.countryCode}",
            tag: _tag);
        // Use amadeus repository to get city code
        final repository = locator.get<IAmadeusRepository>();
        final cities = await repository.getCityByName(
          parameter.activity.city!,
          parameter.activity.countryCode!,
        );

        if (cities.isNotEmpty) {
          String? cityCode = cities.first.iataCode;
          if (cityCode != null && cityCode.isNotEmpty) {
            cityCode = cityCode.trim().toUpperCase();
            // Validate that it's a proper IATA code
            if (RegExp(r'^[A-Z]{3}$').hasMatch(cityCode)) {
              logd(
                  "Found valid city code: $cityCode for ${parameter.activity.city}",
                  tag: _tag);
              return cityCode;
            } else {
              logd("Invalid IATA code format returned: $cityCode", tag: _tag);
            }
          }
        } else {
          logd(
              "No cities found for ${parameter.activity.city}, ${parameter.activity.countryCode}",
              tag: _tag);
        }
      } catch (e) {
        logd("Failed to search city code: $e", tag: _tag);
      }
    }

    // 4. No valid city code found
    logd(
        "No valid IATA city code found. fake TOKYO for testing.Available data: city=${parameter.activity.city}, country=${parameter.activity.countryCode}, cityCode=${parameter.activity.cityCode}",
        tag: _tag);
    return null; //'TYO';
  }

  // _getHotelPage(int i) async {
  //   if(kDebugMode && (state.hotels?.length ?? 0) > 50){
  //     return;
  //   }
  //   try {
  //     logd("fetch page $i / $maxPage", tag: _tag);
  //     currentPage = i;
  //     int startIndex = i * pageItem;
  //     if(startIndex >= hotelList.length) {
  //       emit(state.copyWith(isLoading: false));
  //       return;
  //     }
  //     int endIndex = min(startIndex + pageItem, hotelList.length);
  //     var hotels = hotelList.sublist(startIndex, endIndex);
  //     var hotelIds = hotels.map((e) => e.hotelId).toSet().join(",");
  //     var hotelsOffers = await amadeusRepository.getHotelOffers(
  //         "[$hotelIds]", parameter.activity.hotelPreferences?.numberOfGuests ?? 1);
  //
  //     for (var offer in hotelsOffers) {
  //       if (offer.hotel != null && offer.offers.isNotEmpty) {
  //         AmadeusHotelModel hotel = offer.hotel!;
  //         hotel.city = parameter.activity.city;
  //         hotel.country = parameter.activity.country;
  //         hotel.offers = offer.offers;
  //         List<AmadeusHotelModel> currentHotelList = [...state.hotels ?? []];
  //         currentHotelList.add(hotel);
  //         emit(state.copyWith(hotels: currentHotelList, isLoading: currentHotelList.isEmpty));
  //       }
  //     }
  //     if(isClose || isBooking) return;
  //
  //     if (currentPage < maxPage - 1) {
  //       currentPage++;
  //       _getHotelPage(currentPage);
  //     } else {
  //       logd("fetch page done", tag: _tag);
  //       emit(state.copyWith(isLoading: false));
  //     }
  //   } catch (e) {
  //     logd("_getHotelPage $e", tag: _tag);
  //   }
  // }

  onGetSearchValue(String text) {
    keyword = text;
    if (keyword != null && keyword!.isNotEmpty) {
      emit(state.copyWith(isLoading: true));
      List<AmadeusHotelModel> tmpHotelList = [];
      for (var hotel in state.hotels ?? []) {
        if (hotel.name?.toLowerCase().contains(keyword!.toLowerCase()) ?? false) {
          tmpHotelList.add(hotel);
        }
      }
      tmpHotelList.add(AmadeusHotelModel(
        name: "Add manually ...",
        hotelId: "add_new_hotel",
      )); // Add "Add New Hotel" option
      emit(state.copyWith(textSearch: keyword, searchHotels: tmpHotelList, isLoading: false));
    } else {
      emit(state.copyWith(searchHotels: [], isLoading: false));
    }
  }

  Future<void> onHotelTap(BuildContext context, HotelModel hotel) async {
    final addNew = parameter.addNew;

    dynamic result = await context.pushRoute(HotelDetailRoute(
        parameter: HotelDetailParameter(
      hotelId: hotel.id ?? "",
      hotel: hotel,
      activity: parameter.activity,
      dayIndex: parameter.dayIndex,
      addNew: addNew,
    )));

    if (result != null) {
      logd("Selected 1 hotel, go back 1 more. since we are done ");
      final selectedHotelBooking = result as HotelBookingModel;

      context.router.maybePop(selectedHotelBooking);
    } else {
      // NULL, Fail to select the hotel.

      //Stay here for now.
    }
  }
}
