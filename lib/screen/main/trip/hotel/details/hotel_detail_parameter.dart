import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/hotel_model.dart';

class HotelDetailParameter {
  final String hotelId;
  final HotelModel? hotel;
  final String? location;
  final ActivityModel activity;
  final bool
      addNew; //indicate if this Adding new (TRUE) or update an exsiting hotel..
  final int dayIndex;

  HotelDetailParameter({
    required this.hotelId,
    this.hotel,
    this.location,
    required this.activity,
    this.dayIndex = 0,
    this.addNew = false,
  });
}
