import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/widgets/hotel_room_widget.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/utils/formatter/currency_formatter.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/bottom_sheet/date_picker_bottom_sheet.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/image/custom_network_image.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/primary_outline_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'hotel_detail_cubit.dart';
import 'hotel_detail_state.dart';

@RoutePage()
class HotelDetailPage
    extends BaseBlocProvider<HotelDetailState, HotelDetailCubit> {
  const HotelDetailPage({required this.parameter, super.key});

  final HotelDetailParameter parameter;

  @override
  Widget buildPage() => const HotelDetailView();

  @override
  HotelDetailCubit createCubit() => HotelDetailCubit(
        parameter: parameter,
        activityRepository: locator.get(),
        usecase: locator.get(),
      );
}

class HotelDetailView extends StatefulWidget {
  const HotelDetailView({super.key});

  @override
  State<StatefulWidget> createState() => _HotelDetailViewState();
}

class _HotelDetailViewState extends BaseBlocPageState<HotelDetailView,
    HotelDetailState, HotelDetailCubit> {
  TextEditingController? _hotelNameController;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant HotelDetailView oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _hotelNameController?.dispose();
    super.dispose();
  }

  @override
  Widget buildAppBar(
      BuildContext context, HotelDetailCubit cubit, HotelDetailState state) {
    return CustomAppBar2(
      title: cubit.parameter.addNew
          ? LocaleKeys.add_new_hotel.tr()
          : LocaleKeys.edit_hotel.tr(),
      showBack: true,
      actions: [
        // GestureDetector(
        //   onTap: () => cubit.onSaveToTrip(context),
        //   behavior: HitTestBehavior.opaque,
        //   child: CircleItem(
        //     backgroundColor: appTheme.backgroundV2,
        //     padding: padding(all: 7),
        //     child: Assets.icons.icActionCheck.svg(),
        //   ),
        // )
      ],
    );
  }

  @override
  bool listenWhen(HotelDetailState previous, HotelDetailState current) {
    if (current.status == HotelDetailStatus.success) {
      // Navigate back to trip detail screen using route name instead of count
      // This is more reliable than using a fixed count
      context.router
          .popUntil((route) => route.settings.name == TripDetailRoute.name);
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(
      BuildContext context, HotelDetailCubit cubit, HotelDetailState state) {
    final bool isAddManualHotel = cubit.parameter.hotelId == 'add_new_hotel';
    if (isAddManualHotel && (_hotelNameController == null || _hotelNameController!.text != (state.hotel?.name ?? ''))) {
      _hotelNameController?.dispose();
      _hotelNameController = TextEditingController(text: state.hotel?.name ?? '');
    }
    return Container(
      margin: const EdgeInsets.only(left: 10, right: 10, top: 16),
      child: state.hotel == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: appTheme.whiteText,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: _buildBasicInfo(cubit, state, isAddManualHotel)),
                      const SizedBox(height: 8),
                      Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: appTheme.whiteText,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: _buildBookingInfo(cubit, state)),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                // Show Save to Trip and Book Now buttons for hotel selection
                // @hotel here could be from the original booking or from the Hotel list, it
                ...[
                  Row(
                    children: [
                      Expanded(
                        child: PrimaryButtonV2(
                          text: cubit.parameter.addNew
                              ? LocaleKeys.save_to_trip.tr()
                              : LocaleKeys.change_hotel.tr(),
                          color: appTheme.primaryColorV2,
                          bg: appTheme.whiteText,
                          border: Border.all(color: appTheme.primaryColorV2),
                          onTap: () {
                            if (cubit.parameter.addNew) {
                              if (isAddManualHotel) {
                                // Update the hotel name before saving
                                state.hotel?.name = _hotelNameController?.text;
                              }
                              cubit.onSaveToTrip(context);
                            } else {
                              cubit.onChangeHotel(context);
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: PrimaryButtonV2(
                          text: LocaleKeys.book_now.tr(),
                          border: Border.all(color: appTheme.primaryColorV2),
                          onTap: () {
                            cubit.onBookHotel(context);
                          },
                        ),
                      ),
                    ],
                  ),
                ]
              ],
            ),
    );
  }

  Widget _buildBasicInfo(HotelDetailCubit cubit, HotelDetailState state, bool isAddManualHotel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10.0),
          child: Container(
            width: double.infinity,
            color: appTheme.backgroundV2,
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: state.hotel!.imageURL != null &&
                      state.hotel!.imageURL!.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: state.hotel!.imageURL!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: appTheme.backgroundV2,
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      errorWidget: (context, url, error) =>
                          _buildFallbackImage(),
                    )
                  : FutureBuilder<String?>(
                      future: state.hotel!.fetchImage(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return Container(
                            color: appTheme.backgroundV2,
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        } else if (snapshot.connectionState ==
                            ConnectionState.done) {
                          if (snapshot.hasData &&
                              snapshot.data != null &&
                              snapshot.data!.isNotEmpty) {
                            return CachedNetworkImage(
                              imageUrl: snapshot.data!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: appTheme.backgroundV2,
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                              errorWidget: (context, url, error) =>
                                  _buildFallbackImage(),
                            );
                          } else {
                            return _buildFallbackImage();
                          }
                        } else {
                          return _buildFallbackImage();
                        }
                      }),
            ),
          ),
        ),
        const SizedBox(height: 10),
        isAddManualHotel
            ? TextField(
                controller: _hotelNameController,
                decoration: InputDecoration(
                  labelText: 'Hotel Name',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  state.hotel?.name = value;
                },
              )
            : Text(
                state.hotel?.name ?? "",
                style: AppStyle.bold16V2(),
              ),
        if (state.minPrice != null && state.maxPrice != null)
          Text(
            " ${state.minPrice ?? 0} - ${state.maxPrice ?? 0} ${state.currency}",
            style: AppStyle.bold16V2(color: appTheme.primaryColorV2),
          ),
        if (state.hotel?.city != null && state.hotel?.country != null && state.hotel?.city != 'null' && state.hotel?.country != 'null')
        Row(
          children: [
            Assets.icons.icLocation.svg(
              width: 16,
              height: 16,
              colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcIn),
            ),
            const SizedBox(width: 4),
            Text(
              cubit.parameter.location ??
                  "${state.hotel?.city}, ${state.hotel?.country}",
              style: AppStyle.regular12V2(color: appTheme.grayV2),
            ),
          ],
        ),
        // Text(
        //   LocaleKeys.description.tr(),
        //   style: AppStyle.bold12V2(),
        // ),
        // const SizedBox(height: 4),
        // if (state.offers.isNotEmpty)
        //   Text(
        //     state.offers[0].description?["text"] as String? ?? "",
        //     style: AppStyle.regular12V2(color: appTheme.grayV2),
        //   ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildBookingInfo(HotelDetailCubit cubit, HotelDetailState state) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.booking_info.tr(),
          style: AppStyle.bold14V2(color: appTheme.grayV2),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
                child: _buildSelectionDate(
              title: LocaleKeys.check_in.tr(),
              dateTime: state.checkInDate,
              disable: true,
              firstDate: state.tripStartDate ?? DateTime.now(),
              lastDate: state.tripEndDate ?? DateTime.now(),
              onSelected: (dateTime) {
                cubit.onCheckInDateSelected(dateTime);
              },
            )),
            const SizedBox(width: 8),
            Expanded(
                child: _buildSelectionDate(
              title: LocaleKeys.check_out.tr(),
              dateTime: state.checkOutDate,
              firstDate: state.tripStartDate?.add(const Duration(days: 1)) ??
                  DateTime.now(),
              lastDate: state.tripEndDate ?? DateTime.now(),
              onSelected: (dateTime) {
                cubit.onCheckOutDateSelected(dateTime);
              },
            ))
          ],
        ),
        // const SizedBox(height: 8),
        // Row(
        //   children: [
        //     Expanded(
        //         child: _buildInputMoney(
        //       title: LocaleKeys.budget.tr(),
        //       money: state.budget,
        //       currency: state.currency,
        //       onChanged: (value) => cubit.onBudgetChanged(value),
        //     )),
        //     const SizedBox(width: 8),
        //     Expanded(
        //         child: _buildInputMoney(
        //       title: LocaleKeys.cost.tr(),
        //       money: state.cost,
        //       currency: state.currency,
        //       onChanged: (value) => cubit.onCostChanged(value),
        //     ))
        //   ],
        // ),
        const SizedBox(height: 8),
        if (state.offers.isNotEmpty)
          ...List.generate(state.offers.length, (index) {
            final offer = state.offers[index];
            if (offer is AmadeusHotelOfferModel) {
              return HotelRoomWidget(
                offer: offer,
                index: index,
                state: state,
                onChangedQuantity: (offer, index, privQty) =>
                    cubit.onOfferQuantityChanged(offer, index, privQty),
              );
            }
            return const SizedBox();
          })
        else if (state.isLoading)
          const Center(
            child: CircularProgressIndicator(),
          )
        else
          const Center(
            child: Text(
              "No offers available",
              style: TextStyle(color: Colors.red),
            ),
          )
      ],
    );
  }

  Widget _buildSelectionDate(
      {bool disable = false,
      required String title,
      DateTime? dateTime,
      required DateTime firstDate,
      required DateTime lastDate,
      required Function(DateTime) onSelected}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppStyle.regular12V2(),
        ),
        const SizedBox(height: 4),
        Container(
          padding: padding(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            color: appTheme.whiteText,
            borderRadius: const BorderRadius.all(Radius.circular(4)),
            border: Border.all(
              color: appTheme.borderColorV2,
              width: 1,
            ),
          ),
          child: InkWell(
            onTap: disable
                ? null
                : () async {
                    final DateTime? datePicked = await showDatePicker(
                      context: context,
                      initialDate: dateTime ?? firstDate,
                      firstDate: firstDate,
                      lastDate: lastDate,
                    );
                    if (datePicked != null) {
                      onSelected(datePicked);
                    }
                  },
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    dateTime?.ddMMyy ?? 'dd / MM / yyyy',
                    style: AppStyle.regular14V2(),
                  ),
                ),
                SvgPicture.asset(
                  Assets.icons.icCalendar32.path,
                  width: 24.w,
                  height: 24.h,
                  colorFilter:
                      ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Builds a fallback image widget when hotel image is unavailable
  Widget _buildFallbackImage() {
    return Container(
      color: appTheme.backgroundV2,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.hotel,
            size: 48,
            color: appTheme.grayV2,
          ),
          const SizedBox(height: 8),
          Text(
            "No image available",
            style: AppStyle.regular12V2(color: appTheme.grayV2),
          ),
        ],
      ),
    );
  }
}
