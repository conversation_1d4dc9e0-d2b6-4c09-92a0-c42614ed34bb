import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/booking/info/hotel_booking_info_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_cubit.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/material.dart';

import 'hotel_detail_state.dart';

class HotelDetailCubit extends BaseCubit<HotelDetailState> {
  final String tag = "HotelDetailCubit";
  final HotelDetailParameter parameter;
  final HotelBookingUseCase usecase;
  final IActivityRepository activityRepository;

  HotelDetailCubit({
    required this.parameter,
    required this.activityRepository,
    required this.usecase,
  }) : super(HotelDetailState());

  @override
  void onInit() {
    super.onInit();
    _initializeDates();
    _loadHotelData();
  }

  /// Initialize check-in/out dates and trip date boundaries
  void _initializeDates() {
    final fromDate = parameter.activity.fromDate?.toLocalDT ?? DateTime.now();
    final toDate = parameter.activity.toDate?.toLocalDT ?? DateTime.now();

    logd("Trip dates - From: $fromDate, To: $toDate", tag: tag);

    emit(state.copyWith(
      checkInDate: fromDate.add(Duration(days: parameter.dayIndex)),
      checkOutDate: fromDate.add(Duration(days: parameter.dayIndex + 1)),
      tripStartDate: fromDate,
      tripEndDate: toDate,
    ));
  }

  /// Load hotel data and fetch offers
  Future<void> _loadHotelData() async {
    try {
      await _loadHotelInfo();
      fetchOffers();
    } catch (e) {
      logd("Error loading hotel data: $e", tag: tag);
      emit(state.copyWith(
        isLoading: false,
        error: "Failed to load hotel data",
      ));
    }
  }

  /// Load hotel information from parameter or fetch by ID
  Future<void> _loadHotelInfo() async {
    if (parameter.hotel != null) {
      //get hotel from parameter -> state
      emit(state.copyWith(hotel: parameter.hotel));
      return;
    }

    if (parameter.hotelId.isEmpty) {
      throw Exception("Hotel ID is required when hotel object is not provided");
    }

    final hotel = await usecase.getHotelById(parameter.hotelId);
    if (hotel != null) {
      emit(state.copyWith(hotel: hotel));
    } else {
      throw Exception("Hotel not found for ID: ${parameter.hotelId}");
    }
  }

  /// Fetch hotel offers for different guest counts
  Future<void> fetchOffers() async {
    if (state.hotel == null) {
      logd("Cannot fetch offers: hotel is null", tag: tag);
      return;
    }

    logd("Fetching offers for hotel: ${state.hotel!.name}", tag: tag);
    emit(state.copyWith(isLoading: true, offers: [], error: null));

    try {
      final guestCount =
          parameter.activity.hotelPreferences?.numberOfGuests ?? 1;
      final List<HotelOfferModel> allOffers = [];

      // Fetch offers for different guest counts (1 to max guests)
      for (int guests = 1; guests <= guestCount; guests++) {
        try {
          final offers = await _fetchOffersForGuests(guests);
          allOffers.addAll(offers);
        } catch (e) {
          logd("Failed to fetch offers for $guests guests: $e", tag: tag);
          // Continue with other guest counts instead of failing completely
        }
      }

      if (allOffers.isNotEmpty) {
        _processFetchedOffers(allOffers);
      } else {
        emit(state.copyWith(
          isLoading: false,
          error: "No offers available for the selected dates",
        ));
      }
    } catch (e) {
      logd("Error fetching offers: $e", tag: tag);
      emit(state.copyWith(
        isLoading: false,
        error: "Failed to fetch hotel offers",
      ));
    }
  }

  /// Fetch offers for a specific number of guests
  Future<List<HotelOfferModel>> _fetchOffersForGuests(int guests) async {
    return await usecase.getHotelOffers(
      parameter.hotelId,
      guests,
      checkInDate: state.checkInDate?.yyyy_MM_dd,
      checkOutDate: state.checkOutDate?.yyyy_MM_dd,
      roomQuantity: 1,
    );
  }

  /// Process and update state with fetched offers
  void _processFetchedOffers(List<HotelOfferModel> offers) {
    final priceRange = _calculatePriceRange(offers);

    emit(state.copyWith(
      offers: offers,
      minPrice: priceRange.min,
      maxPrice: priceRange.max,
      currency: offers.isNotEmpty ? offers.first.currency : "USD",
      offerCount: offers.length,
      isLoading: false,
    ));

    logd(
        "Fetched ${offers.length} offers with price range: ${priceRange.min} - ${priceRange.max}",
        tag: tag);
  }

  /// Calculate price range from offers
  ({double min, double max}) _calculatePriceRange(
      List<HotelOfferModel> offers) {
    if (offers.isEmpty) return (min: 0.0, max: 0.0);

    double minPrice = offers.first.price;
    double maxPrice = offers.first.price;

    for (final offer in offers) {
      if (offer.price < minPrice) minPrice = offer.price;
      if (offer.price > maxPrice) maxPrice = offer.price;
    }

    return (min: minPrice, max: maxPrice);
  }

  Future<List<HotelOfferModel>> fetchHotelOffers({
    required String hotelIds,
    required int adults,
    required int roomQuantity,
  }) {
    try {
      return usecase.getHotelOffers(
        hotelIds,
        adults,
        checkInDate: state.checkInDate?.yyyy_MM_dd,
        checkOutDate: state.checkOutDate?.yyyy_MM_dd,
        roomQuantity: roomQuantity,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Update check-in date and validate against trip constraints
  void onCheckInDateSelected(DateTime dateTime) {
    if (!_isDateWithinTripBounds(dateTime)) {
      showSimpleToast("Check-in date must be within trip dates");
      return;
    }

    // Ensure check-out is after check-in
    DateTime newCheckOut =
        state.checkOutDate ?? dateTime.add(const Duration(days: 1));
    if (newCheckOut.isBefore(dateTime.add(const Duration(days: 1)))) {
      newCheckOut = dateTime.add(const Duration(days: 1));
    }

    emit(state.copyWith(
      checkInDate: dateTime,
      checkOutDate: newCheckOut,
    ));

    logd("Updated check-in: $dateTime, check-out: $newCheckOut", tag: tag);
  }

  /// Update check-out date, validate, and refresh offers
  void onCheckOutDateSelected(DateTime dateTime) {
    if (!_isDateWithinTripBounds(dateTime)) {
      showSimpleToast("Check-out date must be within trip dates");
      return;
    }

    final checkIn = state.checkInDate ?? DateTime.now();
    if (dateTime.isBefore(checkIn.add(const Duration(days: 1)))) {
      showSimpleToast("Check-out must be at least 1 day after check-in");
      return;
    }

    emit(state.copyWith(checkOutDate: dateTime));
    logd("Updated check-out: $dateTime", tag: tag);

    // Refresh offers with new dates
    fetchOffers();
  }

  /// Validate if date is within trip bounds
  bool _isDateWithinTripBounds(DateTime date) {
    final tripStart = state.tripStartDate;
    final tripEnd = state.tripEndDate;

    if (tripStart == null || tripEnd == null) return true;

    return date.isAfter(tripStart.subtract(const Duration(days: 1))) &&
        date.isBefore(tripEnd.add(const Duration(days: 1)));
  }

  /// Navigate to hotel booking with selected offers
  Future<void> onBookHotel(BuildContext context) async {
    showSimpleToast(LocaleKeys.coming_soon.tr());

    //     // Validate hotel availability
    // if (state.hotel == null) {
    //   showSimpleToast(LocaleKeys.not_found.tr());
    //   return;
    // }

    // // Validate offer selection
    // final selectedOffers =
    //     state.offers.where((offer) => offer.quantity > 0).toList();
    // if (selectedOffers.isEmpty) {
    //   showSimpleToast(LocaleKeys.please_select_one_offer.tr());
    //   return;
    // }

    // // Validate dates
    // if (state.checkInDate == null || state.checkOutDate == null) {
    //   showSimpleToast("Please select valid check-in and check-out dates");
    //   return;
    // }

    // try {
    //   final bookingParameter = HotelBookingInfoParameter(
    //     activity: parameter.activity,
    //     offers: selectedOffers,
    //     currency: state.currency ?? "USD",
    //     hotel: state.hotel!,
    //     dayIndex: parameter.dayIndex,
    //     checkInDate: state.checkInDate!,
    //     checkOutDate: state.checkOutDate!,
    //     location: parameter.location,
    //   );

    //   await context
    //       .pushRoute(HotelBookingInfoRoute(parameter: bookingParameter));
    // } catch (e) {
    //   logd("Error navigating to booking: $e", tag: tag);
    //   showSimpleToast("Failed to proceed with booking");
    // }
  }

  /// Update offer quantity and refresh pricing
  Future<void> onOfferQuantityChanged(
      HotelOfferModel offer, int index, int previousQuantity) async {
    if (index < 0 || index >= state.offers.length) {
      logd("Invalid offer index: $index, offers length: ${state.offers.length}",
          tag: tag);
      return;
    }

    logd(
        "Updating offer quantity - Index: $index, ID: ${offer.id}, New quantity: ${offer.quantity}",
        tag: tag);

    final updatedOffers = List<HotelOfferModel>.from(state.offers);
    showLoading();

    try {
      // Fetch updated pricing for the new quantity
      final refreshedOffers = await _fetchOffersForGuests(offer.adults);

      if (refreshedOffers.isNotEmpty) {
        // Update the offer with new pricing but preserve the quantity
        updatedOffers[index] = refreshedOffers.first;
        updatedOffers[index].quantity = offer.quantity;

        emit(state.copyWith(offers: updatedOffers));
        logd("Successfully updated offer quantity and pricing", tag: tag);
      } else {
        // Revert to previous quantity if no offers available
        updatedOffers[index].quantity = previousQuantity;
        emit(state.copyWith(offers: updatedOffers));
        showSimpleToast("No offers available for selected quantity");
      }
    } catch (e) {
      // Revert to previous quantity on error
      updatedOffers[index].quantity = previousQuantity;
      emit(state.copyWith(offers: updatedOffers));

      logd("Error updating offer quantity: $e", tag: tag);
      showSimpleToast("Failed to update offer");
    } finally {
      dismissLoading();
    }
  }

  /// Save hotel booking to trip (handles both add and update)
  Future<void> onSaveToTrip(BuildContext context) async {
    if (!_validateSaveToTrip()) return;

    showLoading();

    try {
      final hotelBooking = _createHotelBooking();
      // Pop the result to the previous screen
      logd("create booking and return");

      context.router.maybePop(hotelBooking);
      // emit(state.copyWith(status: HotelDetailStatus.success));
    } catch (e) {
      logd("Error saving hotel to trip: $e", tag: tag);
      emit(state.copyWith(
        status: HotelDetailStatus.error,
        error: LocaleKeys.save_trip_failed.tr(),
      ));
      showSimpleToast("Failed to save hotel to trip");
    } finally {
      dismissLoading();
    }
  }

  /// Validate requirements for saving to trip
  bool _validateSaveToTrip() {
    if (state.hotel == null) {
      showSimpleToast(LocaleKeys.not_found.tr());
      return false;
    }

    if (state.checkInDate == null || state.checkOutDate == null) {
      showSimpleToast("Please select valid check-in and check-out dates");
      return false;
    }

    return true;
  }

  /// Create hotel booking model from current state
  HotelBookingModel _createHotelBooking() {
    return HotelBookingModel(
      hotelId: state.hotel!.id,
      hotelName: state.hotel!.name,
      imageUrl: state.hotel!.imageURL,
      location:
          parameter.location ?? "${state.hotel!.city}, ${state.hotel!.country}",
      provider: "Amadeus",
      checkInDate: state.checkInDate!.yyyy_MM_dd,
      checkOutDate: state.checkOutDate!.yyyy_MM_dd,
      bookingResults: [
        {
          "dayIndex": parameter.dayIndex,
          "savedAt": DateTime.now().toIso8601String(),
          "offerCount": state.offers.length,
        }
      ],
    );
  }

  // /// Save activity to repository
  // Future<void> _saveActivityToRepository(dynamic activity) async {
  //   // In preview mode, don't save to repository since there's no UUID yet
  //   if (parameter.isPreviewMode) {
  //     logd("Preview mode: skipping save to repository", tag: tag);
  //     return;
  //   }

  //   // Check if activity has a valid UUID
  //   if (activity.uuid.isEmpty) {
  //     logd("No valid UUID found, cannot save to repository", tag: tag);
  //     throw Exception("Cannot save activity without valid UUID");
  //   }

  //   final activityParameters = activity.toCreateActivityParameter();
  //   final result = await activityRepository.updateActivity(
  //     activity.uuid,
  //     activityParameters,
  //   );

  //   if (result.uuid.isEmpty) {
  //     throw Exception("Failed to save activity to repository");
  //   }
  // }

//  /// Notify trip detail cubit of changes (for view mode - triggers API call)
//  void _notifyTripDetailCubit() {
//    if (locator.isRegistered<TripDetailCubit>()) {
//      locator.get<TripDetailCubit>().fetchTripDetail();
//    }
//  }

  /// Navigate to hotel list for changing the current hotel
  Future<void> onChangeHotel(BuildContext context) async {
    if (state.hotel == null) {
      showSimpleToast(LocaleKeys.not_found.tr());
      return;
    }

    await context
        .pushRoute(HotelListRoute(
      parameter: HotelListParameter(
        parameter.activity,
        parameter.dayIndex,
        addNew: true, // XXX  since we want to update to a new hotel.
      ),
    ))
        .then((value) {
      if (value != null && value is HotelBookingModel) {
        //we've got a new booking, just go back now with it. pass along the value
        context.maybePop(value);
      } else {
        //seems no hotel is selected or change of mind. keep them here.
      }
    });
  }
}
