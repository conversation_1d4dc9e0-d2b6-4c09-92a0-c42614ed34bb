import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
enum HotelDetailStatus {initial, loading, done, success, error}
class HotelDetailState extends BaseState {
  final HotelModel? hotel;
  final List<HotelOfferModel> offers;
  final DateTime? checkInDate;
  final DateTime? checkOutDate;

  final DateTime? tripStartDate;
  final DateTime? tripEndDate;

  final double? minPrice;
  final double? maxPrice;
  final String? currency;
  final int offerCount;
  final int updateCount;
  final String? error;
  final HotelDetailStatus status;

  HotelDetailState({
    super.isLoading = true,
    this.hotel,
    this.offers = const [],
    this.checkInDate,
    this.checkOutDate,
    this.tripStartDate,
    this.tripEndDate,
    this.minPrice,
    this.maxPrice,
    this.currency,
    this.offerCount = 0,
    this.updateCount = 0,
    this.status = HotelDetailStatus.initial,
    this.error,
  });

  HotelDetailState copyWith({
    bool? isLoading,
    HotelModel? hotel,
    List<HotelOfferModel>? offers,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    DateTime? tripStartDate,
    DateTime? tripEndDate,
    double? minPrice,
    double? maxPrice,
    String? currency,
    int? offerCount,
    HotelDetailStatus? status,
    String? error,
  }) {
    return HotelDetailState(
      isLoading: isLoading ?? this.isLoading,
      hotel: hotel ?? this.hotel,
      offers: offers ?? this.offers,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      tripStartDate: tripStartDate ?? this.tripStartDate,
      tripEndDate: tripEndDate ?? this.tripEndDate,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      currency: currency ?? this.currency,
      offerCount: offerCount ?? this.offerCount,
      status: status ?? this.status,
      error: error ?? this.error,
      updateCount: updateCount + 1,
    );
  }

  @override
  List<Object?> get props => [
    ...super.props,
    hotel,
    minPrice,
    maxPrice,
    currency,
    offers,
    tripStartDate,
    tripEndDate,
    checkInDate,
    checkOutDate,
    updateCount,
  ];


}