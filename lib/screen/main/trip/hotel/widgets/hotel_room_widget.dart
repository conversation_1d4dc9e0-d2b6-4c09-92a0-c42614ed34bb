import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/amadeus/hotel_offer_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/widget/image/custom_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../details/hotel_detail_state.dart';

class HotelRoomWidget extends StatelessWidget {
  final int index;
  final HotelOfferModel offer;
  final HotelDetailState state;
  final Function(HotelOfferModel, int, int) onChangedQuantity;

  const HotelRoomWidget({
    Key? key,
    required this.index,
    required this.offer,
    required this.state,
    required this.onChangedQuantity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: appTheme.backgroundV2,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(10.0),
                  child: SizedBox(
                    width: 96,
                    height: 74,
                    child: offer.imageURL != null && offer.imageURL!.isNotEmpty == true
                        ? CustomNetworkImage(
                            imageUrl: offer.imageURL!,
                            boxFit: BoxFit.cover,
                            defaultImage: Container(
                              color: appTheme.backgroundV2,
                              alignment: Alignment.center,
                              child: Text("${index + 1}", style: AppStyle.bold20V2()),
                            ),
                          )
                        : FutureBuilder(
                            future: offer.fetchRoomImage(
                                "room ${offer.description} hotel ${state.hotel?.name} room for ${offer.adults} adults"),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState == ConnectionState.done &&
                                  snapshot.hasData &&
                                  snapshot.data != null) {
                                return CustomNetworkImage(
                                  imageUrl: snapshot.data as String,
                                  boxFit: BoxFit.cover,
                                  defaultImage: Container(
                                    color: appTheme.backgroundV2,
                                    alignment: Alignment.center,
                                    child: Text("${index + 1}", style: AppStyle.bold20V2()),
                                  ),
                                );
                              } else if (snapshot.connectionState == ConnectionState.waiting) {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              } else {
                                return const SizedBox();
                              }
                            }),
                  )),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: appTheme.grayA8Color),
                  const SizedBox(width: 4),
                  Text(
                    "${offer.adults}",
                    style: AppStyle.regular12V2(),
                  ),
                  if (offer.beds?.isNotEmpty == true) ...[
                    const SizedBox(width: 8),
                    Icon(Icons.king_bed, size: 16, color: appTheme.grayA8Color),
                    const SizedBox(width: 4),
                    Text(
                      offer.beds ?? "",
                      style: AppStyle.regular12V2(),
                    ),
                  ]
                ],
              ),
            ],
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  (offer.description ?? "").split("\n").first,
                  style: AppStyle.bold12V2(),
                ),
                const SizedBox(height: 4),
                Text(
                  (offer.description ?? "").split("\n").skip(1).join("\n"),
                  style: AppStyle.regular12V2(),
                ),
                const SizedBox(height: 4),

                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Text(
                        "${((offer.price) * (offer.quantity > 1 ? offer.quantity : 1)).currency} ${offer.currency}",
                        style: AppStyle.bold16V2(color: appTheme.primaryColorV2),
                      ),
                    ),
                    InkWell(
                        onTap: () {
                          if(offer.quantity > 0) {
                            int privQty = offer.quantity;
                            offer.quantity--;
                            onChangedQuantity(offer, index, privQty);
                          }
                        },
                        child: Container(
                            height: 35,
                            width: 34,
                            decoration: BoxDecoration(
                              color: appTheme.primaryColorV2,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                bottomLeft: Radius.circular(4),
                              ),
                            ),
                            child: Icon(Icons.remove, color: appTheme.whiteText))),
                    SizedBox(
                      width: 50,
                      child: TextFormField(
                        textAlign: TextAlign.center,
                        controller: TextEditingController(text: "${offer.quantity}"),
                        // initialValue: "${offer.quantity}",
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(vertical: 4),
                          isDense: true,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(0),
                            borderSide: BorderSide(color: appTheme.primaryColorV2),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(0),
                            borderSide: BorderSide(color: appTheme.primaryColorV2),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(0),
                            borderSide: BorderSide(color: appTheme.primaryColorV2),
                          ),
                        ),
                        style: AppStyle.regular14V2(),
                      ),
                    ),
                    InkWell(
                        onTap: () {
                          // Handle add action
                          var privQty = offer.quantity;
                          offer.quantity++;
                          onChangedQuantity(offer, index, privQty);
                        },
                        child: Container(
                            height: 35,
                            width: 34,
                            decoration: BoxDecoration(
                              color: appTheme.primaryColorV2,
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(4),
                                bottomRight: Radius.circular(4),
                              ),
                            ),
                            child: Icon(Icons.add, color: appTheme.whiteText)))
                  ],
                ),
                const SizedBox(height: 4),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
