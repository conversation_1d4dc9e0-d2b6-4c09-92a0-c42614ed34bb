import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class BookingInfoTextField extends StatefulWidget {
  const BookingInfoTextField({
    super.key,
    required this.label,
    required this.onChanged,
    required this.text,
    required this.validator,
    this.keyboardType,
    this.isRequired = true,
  });

  final String label;
  final String text;
  final TextInputType? keyboardType;
  final bool isRequired;
  final FormFieldValidator validator;
  final Function(String) onChanged;

  @override
  State<BookingInfoTextField> createState() => _BookingInfoTextFieldState();
}

class _BookingInfoTextFieldState extends State<BookingInfoTextField> {
  final _textController = TextEditingController();
  @override
  void initState() {
    _textController.text = widget.text;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant BookingInfoTextField oldWidget) {
    if(widget.text != oldWidget.text) {
      WidgetsBinding.instance.addPostFrameCallback((_){
        _textController.text = widget.text;
      });

    }
    super.didUpdateWidget(oldWidget);
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding(bottom: 12),
      child: TextFormField(
        controller: _textController,
        // initialValue: widget.text,
        textAlign: TextAlign.start,
        onChanged: widget.onChanged,
        validator: widget.validator,
        keyboardType: widget.keyboardType,
        decoration: InputDecoration(
          labelText: "${widget.label}${widget.isRequired ? "(*)" : ""}",
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(
              color: appTheme.borderColorV2,
              width: 1,
            ),
          ),
        ),
      ),
    );
  }
}