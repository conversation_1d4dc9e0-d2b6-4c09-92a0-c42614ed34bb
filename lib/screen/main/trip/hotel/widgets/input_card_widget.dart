import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/card_info.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/cupertino.dart';

import 'booking_info_textfield.dart';

class InputCardWidget extends StatefulWidget {
  const InputCardWidget({
    super.key,
    this.card,
    required this.onChanged,
  });

  final CardInfo? card;
  final Function(CardInfo) onChanged;

  @override
  State<InputCardWidget> createState() => _InputCardWidgetState();
}

class _InputCardWidgetState extends State<InputCardWidget> {
  late CardInfo card;

  @override
  void initState() {
    card = widget.card ?? CardInfo();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant InputCardWidget oldWidget) {
    if(widget.card != oldWidget.card) {
      card = widget.card ?? CardInfo();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.onChanged(card);
    super.dispose();
  }

  void _onChanged() {
    widget.onChanged(card);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding(horizontal: 8, vertical: 12),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: appTheme.whiteText,
        borderRadius: const BorderRadius.all(Radius.circular(20)),
        border: Border.all(
          color: appTheme.borderColorV2,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.payment_info.tr(),
            style: AppStyle.bold14V2(),
          ),
          const SizedBox(height: 8),
          BookingInfoTextField(
            label: LocaleKeys.card_name.tr(),
            text: card.cardHolderName ?? "",
            onChanged: (newValue) {
              card.cardHolderName = newValue;
              _onChanged();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return LocaleKeys.please_input_last_name.tr();
              }
              return null;
            },
          ),
          BookingInfoTextField(
            label: LocaleKeys.card_number.tr(),
            text: card.cardNumber ?? "",
            keyboardType: TextInputType.number,
            onChanged: (newValue) {
              card.cardNumber = newValue;
              _onChanged();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return LocaleKeys.please_input_last_name.tr();
              }
              return null;
            },
          ),
          Row(
            children: [
              Expanded(
                  flex: 2,
                  child: BookingInfoTextField(
                    label: LocaleKeys.card_expiration_date.tr(),
                    text: card.expiryDate ?? "",
                    onChanged: (newValue) {
                      card.expiryDate = newValue;
                      _onChanged();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocaleKeys.please_input_last_name.tr();
                      }
                      return null;
                    },
                  )),
              const SizedBox(width: 8),
              Expanded(
                  flex: 1,
                  child: BookingInfoTextField(
                    label: LocaleKeys.card_cvc.tr(),
                    text: card.cvc ?? "",
                    keyboardType: TextInputType.number,
                    onChanged: (newValue) {
                      card.cvc = newValue;
                      _onChanged();
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocaleKeys.please_input_last_name.tr();
                      }
                      return null;
                    },
                  ))
            ],
          )
        ],
      ),
    );
  }
}
