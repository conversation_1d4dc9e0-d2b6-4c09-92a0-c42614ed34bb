import 'dart:math';

import 'package:family_app/extension.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:flutter/material.dart';

class HotelItemWidget extends StatelessWidget {
  final HotelModel hotel;
  final Function(HotelModel hotel) onTap;

  const HotelItemWidget({super.key, required this.hotel, required this.onTap});

  @override
  Widget build(BuildContext context) {
    if (hotel.id == 'add_new_hotel') {
      return Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 10),
        child: ElevatedButton.icon(
          icon: const Icon(Icons.add, size: 22, color: Colors.white),
          label: Text(hotel.name ?? '', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          style: ElevatedButton.styleFrom(
            backgroundColor: appTheme.primaryColorV2,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 18),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 2,
          ),
          onPressed: () => onTap(hotel),
        ),
      );
    }
    return Container(
      padding: const EdgeInsets.all(8.0),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: appTheme.backgroundV2,
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        title: Text(
          hotel.name ?? "",
          style: AppStyle.bold14V2(),
        ),
        subtitle: (hotel.city != null && hotel.country != null)
            ? Row(
                children: [
                  Assets.icons.icLocation.svg(
                    width: 16,
                    height: 16,
                    colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcIn),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    "${hotel.city}, ${hotel.country}",
                    style: AppStyle.regular12V2(color: appTheme.grayV2),
                  ),
                ],
              )
            : null,
        trailing: CircleItem(
          onTap: () => _openLocationInMap(context, hotel),
          padding: padding(all: 10),
          backgroundColor: appTheme.primaryColorV2.withValues(alpha: 0.12),
          child: Transform.rotate(
            angle: pi,
            child: Assets.icons.icMap.svg(
              width: 18.w,
              height: 18.w,
              colorFilter:
                  ColorFilter.mode(appTheme.primaryColorV2, BlendMode.srcIn),
            ),
          ),
        ),
        onTap: () => onTap(hotel),
      ),
    );
  }

  Future<void> _openLocationInMap(BuildContext context, HotelModel hotel) async {
    final name = hotel.name ?? '';
    final city = hotel.city ?? '';
    final country = hotel.country ?? '';
    String loc = name;
    if (city.isNotEmpty) loc += ', $city';
    if (country.isNotEmpty) loc += ', $country';
    if (loc.trim().isNotEmpty) {
      final encoded = Uri.encodeComponent(loc);
      final url = 'https://www.google.com/maps/search/?api=1&query=$encoded';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open Google Maps')),
        );
      }
    }
  }

  _buildRating(double rating) {
    if (rating <= 0) {
      return const SizedBox.shrink();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: appTheme.grayV2.withOpacity(0.2),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Assets.icons.icStarRating.svg(),
          const SizedBox(width: 2),
          Text(
            rating.toStringAsFixed(1),
            style: AppStyle.bold14V2(color: appTheme.blackText),
          ),
        ],
      ),
    );
  }
}
