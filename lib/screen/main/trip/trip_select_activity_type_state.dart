
import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/screen/main/trip/trip_select_activity_type_cubit.dart';

enum TripSelectActivityTypeStatus { initial, loading, done, success, error }

class TripSelectActivityTypeState extends BaseState {
  final ActivityModel activity;
  final int dayIndex;
  final TripSelectActivityTypeStatus status;
  final List<ActivityType> activityTypes;

  TripSelectActivityTypeState({
    required this.activity, 
    this.dayIndex = 0,
    this.status = TripSelectActivityTypeStatus.initial,
    this.activityTypes = const [],
  });

  @override
  List<Object?> get props => [activity, dayIndex, status, activityTypes];

  TripSelectActivityTypeState copyWith({
    ActivityModel? activity,
    int? dayIndex,
    TripSelectActivityTypeStatus? status,
    List<ActivityType>? activityTypes,
  }) {
    return TripSelectActivityTypeState(
      activity: activity ?? this.activity,
      dayIndex: dayIndex ?? this.dayIndex,
      status: status ?? this.status,
      activityTypes: activityTypes ?? this.activityTypes,
    );
  }
}
