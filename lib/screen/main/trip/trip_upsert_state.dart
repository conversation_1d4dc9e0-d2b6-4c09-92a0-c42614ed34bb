import 'package:family_app/base/widget/cubit/base_state.dart';

enum TripUpsertStatus { initial, loading, success, error, done }

class TripUpsertState extends BaseState {
  final TripUpsertStatus status;
  final String? coverImageFilePath;
  final String? tripName;
  final DateTime? fromDate;
  final DateTime? toDate;
  final String? countryCode;
  final String? errorMessage;

  TripUpsertState({
    this.status = TripUpsertStatus.initial,
    this.coverImageFilePath,
    this.tripName,
    this.fromDate,
    this.toDate,
    this.countryCode,
    this.errorMessage,
  });

  bool get isValid => 
    tripName?.isNotEmpty == true && 
    fromDate != null && 
    toDate != null && 
    countryCode != null && countryCode!.isNotEmpty;

  @override
  List<Object?> get props => [
    status,
    coverImageFilePath,
    tripName,
    fromDate,
    toDate,
    countryCode,
    errorMessage,
  ];

  TripUpsertState copyWith({
    TripUpsertStatus? status,
    String? coverImageFilePath,
    String? tripName,
    DateTime? fromDate,
    DateTime? toDate,
    String? countryCode,
    String? errorMessage,
  }) {
    return TripUpsertState(
      status: status ?? this.status,
      coverImageFilePath: coverImageFilePath ?? this.coverImageFilePath,
      tripName: tripName ?? this.tripName,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      countryCode: countryCode ?? this.countryCode,
      errorMessage: errorMessage,
    );
  }

  TripUpsertState updateCoverImageFilePath(String? coverImageFilePath) {
    return TripUpsertState(
      status: status,
      coverImageFilePath: coverImageFilePath,
      tripName: tripName,
      fromDate: fromDate,
      toDate: toDate,
      countryCode: countryCode,
      errorMessage: errorMessage,
    );
  }
}
