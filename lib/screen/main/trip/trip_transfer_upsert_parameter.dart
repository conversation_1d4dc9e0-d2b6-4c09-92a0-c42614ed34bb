import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/transfer_model.dart';

class TripTransferUpsertParameter {
  final ActivityModel activity;
  final int dayIndex;
  final int? activityIndex; // Index of the activity in the day's activities list
  final bool addNew;
  final TransferModel? initialTransfer;

  TripTransferUpsertParameter({
    required this.activity, 
    required this.dayIndex,
    this.activityIndex,
    this.addNew = false,
    this.initialTransfer,
  });
}
