import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../data/model/hotel_booking_model.dart';
import '../../../data/model/trip_model.dart';
import 'trip_cubit.dart';
import 'trip_state.dart';

/* Trip page used to show the result of AI chatboot planning

     different from TripDetailPage which is used to show the detail of a trip FROM Activiies list
    **********
*/

@RoutePage()
class TripPage extends BaseBlocProvider<TripState, TripCubit> {
  const TripPage({required this.parameter, super.key});
  final TripParameter parameter;

  @override
  Widget buildPage() => const TripView();
  @override
  TripCubit createCubit() =>
      TripCubit(activityRepository: locator.get(), parameter: parameter);
}

class TripView extends StatefulWidget {
  const TripView({super.key});

  @override
  State<TripView> createState() => _TripViewState();
}

class _TripViewState extends BaseBlocPageState<TripView, TripState, TripCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;
  @override
  bool listenWhen(TripState previous, TripState current) {
    if (current.isSaving) {
      showLoading();
    } else if (previous.isSaving && !current.isSaving) {
      if (current.saveTripSuccess) {
        AppLogger.d('Save trip success 12');
        context.router.popUntil((route) =>
            //           (route.settings.name == ActivityHomeRoute.name) ||
            //           (route.settings.name == HomeRoute.name)||
            (route.settings.name == MainRoute.name));
      } else {
        AppLogger.d('Save trip failed');
        dismissLoading();
        showSimpleToast(LocaleKeys.save_trip_failed.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, TripCubit cubit, TripState state) {
    return CustomAppBar2(
        title: LocaleKeys.trip_screen_text.tr(),
        showBack: true,
        onBack: () => _goBack());
  }

  void _goBack() async {
    // For VTP preview, just pop back to the previous screen
    // The save functionality is available via the floating action button
    Navigator.of(context).pop();
  }

  @override
  Widget buildBody(BuildContext context, TripCubit cubit, TripState state) {
    // logd("TripView: buildBody: state loading: ${state.isLoading} heroImageUrl: ${state.heroImageUrl}");

    if (state.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.trip.hotelPreferences == null &&
        state.trip.flightPreferences == null) {
      logd("TripView: buildBody: no hotel or flight preferences");
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hero Image
          if (state.heroImageUrl != null && state.heroImageUrl!.isNotEmpty)
            _buildImage(state.heroImageUrl!)
          else
            Image.asset(Assets.images.logo.path, height: 200),

          // Trip Title and Description
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  state.trip.name,
                  style: const TextStyle(
                      fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  '${state.trip.fromDate.toString().substring(0, 10)} - ${state.trip.toDate.toString().substring(0, 10)}  (${state.trip.isDateConfirmed ? LocaleKeys.confirmed.tr() : LocaleKeys.unconfirmed.tr()})',
                ),
                const SizedBox(height: 8),
                Text(state.trip.description ?? ''),
              ],
            ),
          ),

          // Hotel and Flight Preferences
          if (state.trip.hotelPreferences != null ||
              state.trip.flightPreferences != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (state.trip.hotelPreferences != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Row(
                        children: [
                          Icon(Icons.hotel,
                              size: 20, color: appTheme.primaryColor),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              state.trip.hotelPreferences?.location ?? '',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (state.trip.flightPreferences != null)
                    Row(
                      children: [
                        Icon(Icons.flight,
                            size: 20, color: appTheme.primaryColor),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${state.trip.flightPreferences?.arrivalAirportName ?? ''} (${state.trip.flightPreferences?.arrivalAirportCode ?? ''})',
                                style: const TextStyle(fontSize: 14),
                              ),
                              if (state.trip.flightPreferences
                                      ?.preferredAirlines?.isNotEmpty ??
                                  false)
                                Text(
                                  state.trip.flightPreferences!
                                      .preferredAirlines!.first,
                                  style: const TextStyle(
                                      fontSize: 12, color: Colors.grey),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

          // Selected Hotels Section
          if (state.trip.selectedHotels != null &&
              state.trip.selectedHotels!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Selected Hotels',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  ...state.trip.selectedHotels!
                      .map((hotel) => _buildHotelCard(hotel)),
                ],
              ),
            ),

          // Itinerary
          ...List.generate(state.trip.itinerary.length, (dayIndex) {
            final itinerary = state.trip.itinerary[dayIndex];
            return ExpansionTile(
              onExpansionChanged: (expanded) {
                if (expanded) {
                  cubit.fetchDayData(dayIndex);
                }
              },
              title: Text(
                  '${LocaleKeys.day.tr()} ${dayIndex + 1}: ${_getDayTitle(itinerary)}'),
              children: [
                // Hotel Image
                (state.dayLoadedStatus[dayIndex] == true)
                    ? _buildImage(itinerary.imageUrl!, height: 150)
                    : const Center(child: CircularProgressIndicator()),

                // Timeline
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...List.generate(itinerary.activities?.length ?? 0,
                          (activityIndex) {
                        final activity = itinerary.activities![activityIndex];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                              '${activity.time} - ${activity.description}'),
                        );
                      }),
                    ],
                  ),
                ),

                // Food Carousel
                if (itinerary.food != null && itinerary.food!.isNotEmpty)
                  SizedBox(
                    height: 100,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: itinerary.food?.length ?? 0,
                      itemBuilder: (context, foodIndex) {
                        // logd("UI1: get food for index: $foodIndex");
                        // logd("UI2: get food for index: ${itinerary.food![foodIndex]}");
                        // logd("UI3: get food for index: ${itinerary.foodImageUrl}");

                        var foodImageUrl =
                            itinerary.food?.values.elementAt(foodIndex);

                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: (state.dayLoadedStatus[dayIndex] == true)
                              ? _buildImage(foodImageUrl ?? '', width: 100)
                              : const Center(
                                  child: CircularProgressIndicator()),
                        );
                      },
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
    );
  }

  /// Build a hotel card with name and map navigation button
  Widget _buildHotelCard(HotelBookingModel hotel) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // Hotel Icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: appTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.hotel,
                color: appTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),

            // Hotel Information
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    hotel.hotelName ?? 'Unknown Hotel',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    hotel.location ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${hotel.checkInDate} - ${hotel.checkOutDate}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),

            // Map Navigation Button
            IconButton(
              onPressed: () => _openHotelInMap(hotel),
              icon: Icon(
                Icons.map,
                color: appTheme.primaryColor,
                size: 24,
              ),
              tooltip: 'Open in Maps',
            ),
          ],
        ),
      ),
    );
  }

  /// Open hotel location in device's default map application
  Future<void> _openHotelInMap(HotelBookingModel hotel) async {
    try {
      final hotelName = hotel.hotelName ?? 'Hotel';
      final location = hotel.location ?? '';

      // Create search query for the hotel
      final query = '$hotelName, $location';
      final encodedQuery = Uri.encodeComponent(query);

      // Try different map URL schemes
      final urls = [
        // Apple Maps (iOS)
        'http://maps.apple.com/?q=$encodedQuery',
        // Google Maps
        'https://www.google.com/maps/search/?api=1&query=$encodedQuery',
        // Fallback web URL
        'https://maps.google.com/maps?q=$encodedQuery',
      ];

      bool launched = false;
      for (final url in urls) {
        final uri = Uri.parse(url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          launched = true;
          break;
        }
      }

      if (!launched) {
        AppLogger.w('Could not launch any map application for hotel: $query');
        showSimpleToast('Could not open maps application');
      } else {
        AppLogger.d('Opened hotel in maps: $query');
      }
    } catch (e) {
      AppLogger.e('Error opening hotel in maps: $e');
      showSimpleToast('Error opening maps application');
    }
  }

  Widget _buildImage(String url,
      {double height = 200, double width = double.infinity}) {
    if (url.isEmpty) {
      return const SizedBox(
        width: 0,
      );
    }

    return CachedNetworkImage(
      imageUrl: url,
      height: height,
      width: width,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Icon(Icons.error, color: Colors.red),
      ),
    );
  }

  String _getDayTitle(Itinerary itinerary) {
    // Implement logic to extract a concise title for the day from activities
    // For example, you could use the first activity description
    if (itinerary.activities != null && itinerary.activities!.isNotEmpty) {
      return itinerary.activities![0].description ?? "";
    } else {
      return '';
    }
  }

  @override
  Widget? buildFloatingActionButton(
      BuildContext context, TripCubit cubit, TripState state) {
    // return FloatingActionButton(
    //   onPressed: state.isSaving ? null : () => context.read<TripCubit>().saveTrip(),
    //   child: state.isSaving ? const CircularProgressIndicator() : const Icon(Icons.save),
    // );

    return Row(mainAxisAlignment: MainAxisAlignment.end, children: [
      FloatingActionButton(
        onPressed:
            state.isSaving ? null : () => context.read<TripCubit>().saveTrip(),
        child: state.isSaving
            ? const CircularProgressIndicator()
            : const Text("Save"),
      ),
      // const SizedBox(
      //   width: 20,
      // ),
      // FloatingActionButton(onPressed: () => {}, child: const Text("Modify")),
    ]);
  }
}
