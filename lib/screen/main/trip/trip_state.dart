import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/trip_model.dart';

class TripState extends BaseState {
  final Trip trip;
  final bool loading;
  final bool isSaving;
  final bool saveTripSuccess;
  String? heroImageUrl;
  Map<String, String> foodImageUrls;

  final Map<int, bool> dayLoadedStatus;

  TripState({
    required this.trip,
    this.loading = false,
    this.isSaving = false,
    this.saveTripSuccess = false,
    this.heroImageUrl,
    this.foodImageUrls = const {},
    this.dayLoadedStatus = const {},
  });

  @override
  List<Object?> get props => [trip, loading, isSaving, saveTripSuccess, heroImageUrl, foodImageUrls, dayLoadedStatus];

  TripState copyWith({
    Trip? trip,
    bool? loading,
    bool? isSaving,
    bool? saveTripSuccess,
    String? heroImageUrl,
    Map<String, String>? foodImageUrls,
    Map<int, bool>? dayLoadedStatus,
  }) {
    return TripState(
      trip: trip ?? this.trip,
      loading: loading ?? this.loading,
      isSaving: isSaving ?? this.isSaving,
      saveTripSuccess: saveTripSuccess ?? this.saveTripSuccess,
      heroImageUrl: heroImageUrl ?? this.heroImageUrl,
      foodImageUrls: foodImageUrls ?? this.foodImageUrls,
      dayLoadedStatus: dayLoadedStatus ?? this.dayLoadedStatus,
    );
  }
}
