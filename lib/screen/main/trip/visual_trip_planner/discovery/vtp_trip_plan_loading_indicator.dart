import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_cubit.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// A widget that shows a loading indicator while waiting for a trip plan
class VTPTripPlanLoadingIndicator extends StatelessWidget {
  const VTPTripPlanLoadingIndicator({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VTPDiscoveryCubit, VTPDiscoveryState>(
      buildWhen: (previous, current) =>
          previous.isTripPlanLoading != current.isTripPlanLoading ||
          previous.tripPlanErrorMessage != current.tripPlanErrorMessage ||
          previous.tripPlanStatusMessage != current.tripPlanStatusMessage,
      builder: (context, state) {
        if (!state.isTripPlanLoading && state.tripPlanErrorMessage == null) {
          // Not loading and no error, don't show anything
          return const SizedBox.shrink();
        }

        return Container(
          color: Colors.black54,
          child: Center(
            child: Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (state.isTripPlanLoading) ...[
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      if (state.tripPlanStatusMessage != null && state.tripPlanStatusMessage!.isNotEmpty) ...[
                        Text(
                          state.tripPlanStatusMessage!,
                          // 'Creating your trip plan...',
                          style: AppStyle.bold18V2(),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'This may take a moment',
                          style: AppStyle.regular14V2(),
                        ),
                      ],
                    ] else if (state.tripPlanErrorMessage != null) ...[
                      const Icon(
                        Icons.error_outline,
                        color: Colors.orange,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Unable to create trip plan',
                        style: AppStyle.bold18V2(),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        state.tripPlanErrorMessage!,
                        style: AppStyle.regular14V2(),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          // Clear the error message
                          context.read<VTPDiscoveryCubit>().resetConnectionState();
                        },
                        child: const Text('Dismiss'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
