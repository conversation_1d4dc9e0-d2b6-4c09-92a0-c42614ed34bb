import 'package:family_app/screen/main/trip/visual_trip_planner/constants/vtp_constants.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/model/trip_plan_data.dart';

/// Parameter class for the VTP Discovery Cubit
class VTPDiscoveryParameter {
  /// The token used for WebSocket authentication
  final String token;

  /// The family ID for the current user
  final String familyId;

  /// Flow type: discovery or direct
  final VTPFlowType flowType;

  // Discovery flow parameters
  /// Initial search term if any (for discovery flow)
  final String initialSearchTerm;

  /// Initial selected vibes if any (for discovery flow)
  final List<String> initialSelectedVibes;

  // Direct flow parameters
  /// Direct trip data (only used when flowType is direct)
  final DirectTripData? directTripData;

  VTPDiscoveryParameter({
    required this.token,
    required this.familyId,
    this.flowType = VTPFlowType.discovery,
    this.initialSearchTerm = '',
    this.initialSelectedVibes = const [],
    this.directTripData,
  });
}
