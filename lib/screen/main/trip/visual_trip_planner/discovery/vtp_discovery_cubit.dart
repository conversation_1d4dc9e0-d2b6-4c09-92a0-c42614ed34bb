import 'dart:async';
import 'dart:convert';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';

import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:family_app/data/usecase/flight_booking_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/AiConnectionController.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/constants/vtp_constants.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_destination_focus_screen.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_state.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/model/trip_plan_data.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/flight_offer_model.dart';
import 'package:family_app/data/model/amadeus/flight_offer_model.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:get_it/get_it.dart';

const String kVtpWebSocketUrl = 'wss://vrelay-vn1.5gencare.com/ai/ws';

/// Cubit for managing the VTP Discovery feature with WebSocket connection
class VTPDiscoveryCubit extends BaseCubit<VTPDiscoveryState> {
  /// Parameters for the VTP Discovery
  final VTPDiscoveryParameter parameter;

  /// AI Connection Controller for WebSocket communication
  late AiConnectionController _aiController;

  /// Store the last full trip JSON for patching
  Trip? _lastFullTrip;

  VTPDiscoveryCubit({
    required this.parameter,
  }) : super(VTPDiscoveryState(
          searchTerm: parameter.initialSearchTerm,
          selectedVibes: parameter.initialSelectedVibes,
        )) {
    _aiController = AiConnectionController(
      onMessage: _handleStreamData,
      onError: _handleStreamError,
      onDone: _handleStreamDone,
      onTimeout: _handleTimeout,
      timeout: const Duration(seconds: 60),
    );
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    await _connectToAI();

    // Handle different flow types
    switch (parameter.flowType) {
      case VTPFlowType.discovery:
        AppLogger.d(
            "VTP Discovery initializing with search term: ${parameter.initialSearchTerm}");

        // If initial search term and vibes are provided, perform search
        if (parameter.initialSearchTerm.isNotEmpty) {
          await searchDestinations(
            parameter.initialSearchTerm,
            parameter.initialSelectedVibes,
          );
        }
        break;

      case VTPFlowType.direct:
        AppLogger.d(
            "VTP Direct initializing with destination: ${parameter.directTripData?.destination}");

        // For direct flow, we don't need to perform an initial search
        // The user will trigger the trip planning when they're ready
        break;
    }
  }

  Future<void> _connectToAI() async {
    AppLogger.d("Initializing VTP Discovery WebSocket channel");
    final token = parameter.token;

    try {
      final url = '$kVtpWebSocketUrl?authorization=$token';
      await _aiController.connect(url);
      emit(state.copyWith(connState: VTPConnState.success, loading: false));
    } catch (e) {
      AppLogger.e("WebSocket connection error: $e");
      emit(state.copyWith(connState: VTPConnState.none, loading: false));
      _handleStreamError(e);
    }
  }

  @override
  Future<void> close() async {
    AppLogger.d("Closing VTP Discovery Cubit");
    _aiController.close();
    super.close();
  }

  /// Handle timeout events
  void _handleTimeout() {
    _handleStreamError('Connection timeout');
    emit(state.copyWith(
      connState: VTPConnState.timeout,
      loading: false,
      isWaitingForResponse: false,
      isTripPlanLoading: false, // Also clear trip plan loading state on timeout
      tripPlanErrorMessage:
          state.isTripPlanLoading ? 'Trip plan request timed out' : null,
    ));
  }

  /// Handle incoming data from the WebSocket
  void _handleStreamData(dynamic data) {
    AppLogger.d("Received data from WebSocket: $data");

    try {
      final response = jsonDecode(data) as Map<String, dynamic>;

      if (response.isEmpty) {
        _handleStreamError('Empty response received');
        return;
      }

      // Handle AI chat response
      if (response['command'] == WebSocketCommands.aiChatRespond) {
        AppLogger.d("Processing AI chat response: ${response['command']}");
        _handleAIChatResponse(response);
      } else {
        AppLogger.d("Unknown command received: ${response['command']}");
        // Log the full response for debugging
        AppLogger.d("Full response: $response");
      }
    } catch (e, stackTrace) {
      AppLogger.e("Error parsing WebSocket data: $e");
      AppLogger.e("Stack trace: $stackTrace");
      AppLogger.e("Raw data received: $data");
      _handleStreamError('Error parsing response: $e');
    }
  }

  /// Handle AI chat response from the server
  void _handleAIChatResponse(Map<String, dynamic> response) {
    try {
      final String message = response['message'] ?? '';

      // Try to determine which prompt was used
      String prompt = response['prompt'] ?? '';

      // If prompt is not directly in the response, check the original_message
      if (prompt.isEmpty) {
        final originalMessage =
            response['original_message'] as Map<String, dynamic>?;
        if (originalMessage != null && originalMessage.containsKey('prompt')) {
          prompt = originalMessage['prompt'] as String;
          AppLogger.d("Found prompt in original_message: $prompt");
        }
      }

      AppLogger.d("Handling AI chat response with prompt: $prompt");

      if (message.isEmpty) {
        emit(state.copyWith(
          destinationOptions: [],
          connState: VTPConnState.success,
          loading: false,
          isWaitingForResponse: false,
        ));
        return;
      }

      // Try to parse the message as JSON
      try {
        // Clean the message if it's wrapped in markdown code block format
        String cleanedMessage = message;

        logd("message length: ${message.length}");

        // Check if the message starts with markdown code block syntax
        if (message.trim().startsWith('```')) {
          // Extract the JSON content from the markdown code block
          final RegExp codeBlockRegex =
              RegExp(r'```(?:json)?\n([\s\S]*?)\n```');
          final Match? match = codeBlockRegex.firstMatch(message);

          if (match != null && match.groupCount >= 1) {
            cleanedMessage = match.group(1) ?? message;
            AppLogger.d(
                "Extracted JSON from markdown code block: $cleanedMessage");
          } else {
            // If regex doesn't match but still starts with ```, try a simpler approach
            cleanedMessage =
                message.replaceFirst(RegExp(r'```(?:json)?\n'), '');
            final endIndex = cleanedMessage.lastIndexOf('\n```');
            if (endIndex != -1) {
              cleanedMessage = cleanedMessage.substring(0, endIndex);
            }
            AppLogger.d(
                "Extracted JSON using simple approach: $cleanedMessage");
          }
        }

        // Parse the JSON data
        final dynamic jsonData = jsonDecode(cleanedMessage);

        // Handle different types of responses based on the prompt
        if (prompt == AIPrompts.vtpPlan) {
          AppLogger.d("Detected full trip plan response (vtp_plan)");
          // This is a full trip plan response from madlibs screen
          _handleFullTripPlanResponse(jsonData);
        } else if (prompt == AIPrompts.vtpRefine) {
          AppLogger.d("Detected trip plan refinement response (refine)");
          _handleRefinedTripPlanResponse(jsonData);
        } else {
          AppLogger.d(
              "Detected destination discovery response (default or vtp_discover)");
          // Default case: handle destination discovery response
          _handleDestinationDiscoveryResponse(jsonData);
        }
      } catch (e, stackTrace) {
        AppLogger.e("Error parsing message as JSON: $e");
        AppLogger.e("Stack trace: $stackTrace");
        AppLogger.e("Original message: $message");

        // If the message is not valid JSON, emit an error state
        emit(state.copyWith(
          destinationOptions: [],
          connState: VTPConnState.error,
          loading: false,
          isWaitingForResponse: false,
          errorMessage: 'Invalid response format: $e',
        ));
      }
    } catch (e) {
      _handleStreamError('Error parsing response: $e');
    }
  }

  /// Handle destination discovery response (list of destination options)
  void _handleDestinationDiscoveryResponse(dynamic jsonData) {
    try {
      // The message should be a JSON array of destination objects
      final List<dynamic> destinationsJson = jsonData as List<dynamic>;

      if (destinationsJson.isEmpty) {
        // Empty JSON array is an error case
        emit(state.copyWith(
          destinationOptions: [],
          connState: VTPConnState.error,
          loading: false,
          isWaitingForResponse: false,
          errorMessage:
              'No destinations found. Please refine your search terms.',
        ));
        return;
      }

      // Convert each JSON object to a DestinationOption
      final List<DestinationOption> destinations = destinationsJson.map((json) {
        // Extract the fields from the JSON object
        final String city = json['city'] ?? '';
        final String country = json['country'] ?? '';
        final String subtitle = json['subtitle'] ?? '';
        final List<String> touristActivities =
            (json['tourist_activities'] as List<dynamic>?)?.cast<String>() ??
                [];

        // Create a DestinationOption with the extracted fields
        return DestinationOption(
          name: '$city, $country',
          description: subtitle,
          iconType:
              _getIconTypeForDestination(city, country, touristActivities),
          touristActivities: touristActivities,
        );
      }).toList();

      emit(state.copyWith(
        destinationOptions: destinations,
        connState: VTPConnState.success,
        loading: false,
        isWaitingForResponse: false,
      ));
    } catch (e, stackTrace) {
      AppLogger.e("Error handling destination discovery response: $e");
      AppLogger.e("Stack trace: $stackTrace");
      _handleStreamError('Error parsing destinations: $e');
    }
  }

  /// Handle full trip plan response (Trip model)
  void _handleFullTripPlanResponse(dynamic jsonData) async {
    try {
      // Log the full trip plan data for now
      AppLogger.d("Received full trip plan: $jsonData");

      // Try to parse the data into a Trip object
      if (jsonData is Map<String, dynamic>) {
        try {
          // Check if we need to extract the trip data from a wrapper object
          Map<String, dynamic> tripData = jsonData;

          // Some responses might have the trip data nested inside another object
          if (jsonData.containsKey('ai_trip')) {
            tripData = jsonData['ai_trip'] as Map<String, dynamic>;
            AppLogger.d("Found nested trip data");
          }

          // Try to parse the Trip object
          final Trip trip = Trip.fromJson(tripData);

          // Log successful parsing and trip details
          AppLogger.d("Successfully parsed Trip: ${trip.name}\n"
              "Trip details: ${trip.city}, ${trip.country} \n Trip dates: ${trip.fromDate} to ${trip.toDate} \n"
              "Trip has ${trip.itinerary.length} days in the itinerary\n"
              " Flight pref: ${trip.flightPreferences}");

          // Enhanced: Search for hotels based on trip preferences
          Trip updatedTrip = trip;
          if (trip.hotelPreferences != null) {
            // Emit status message before hotel search
            emit(state.copyWith(
              tripPlanStatusMessage: 'Looking for Hotel...',
            ));
            AppLogger.d("Trip has hotel preferences, searching for hotels...");
            updatedTrip = await _searchAndAddHotelToTrip(trip);
          } else {
            AppLogger.d("No hotel preferences found in trip");
          }

          ////Enhance: search for A flight base on trip flight prefreence
          if (trip.flightPreferences != null) {
            // Emit status message before flight search
            emit(state.copyWith(
              tripPlanStatusMessage: 'Looking for Flights ...',
            ));
            updatedTrip = await _searchAndAddFlightToTrip(trip);
          } else {
            logd("No flight preference found in trip ");
          }

          // 2nd round: Send the updated trip to AI for refinement
          emit(state.copyWith(
            tripPlanStatusMessage: 'Refining the trip plan ...',
            isWaitingForResponse: true,
            isTripPlanLoading: true,
          ));



          final prettyString = const JsonEncoder.withIndent('  ').convert(updatedTrip.toJson());
          logd("Before refining :\n$prettyString");

          _lastFullTrip = updatedTrip; 


          //TODO check if the tripPlanDebugEnabled, if true continue, else skip the refining flow 
          //by calling  _handleRefinedTripPlanResponse() with an empty json data to trigger the exception flow. 
          final localStorage = GetIt.I<LocalStorage>();
          final debugEnabled = await localStorage.getTripPlanDebugEnabled();
          if (!debugEnabled) {
            _handleRefinedTripPlanResponse({});
            return;
          }


          // Convert updatedTrip to JSON
          final String tripJson = jsonEncode(updatedTrip.toJson());

          // Build the message for AI refinement
          final jsonMessage = jsonEncode({
            "command": WebSocketCommands.aiChat,
            "message": 'Can you refine the following plan? $tripJson',
            "auto_create": AutoCreateValues.falseValue,
            "prompt": AIPrompts.vtpRefine
          });

          AppLogger.d("Sending trip for refinement: $jsonMessage");
          _aiController.sendMessage(jsonMessage);
          // Do not emit success here; wait for the vtp_edit response
        } catch (e, stackTrace) {
          AppLogger.e("Error parsing Trip object: $e");
          AppLogger.e("Stack trace: $stackTrace");

          // Update state to indicate trip plan loading failed
          emit(state.copyWith(
            connState: VTPConnState.error,
            loading: false,
            isWaitingForResponse: false,
            isTripPlanLoading:
                false, // Trip plan loading is complete (with error)
            tripPlanErrorMessage:
                'Error parsing trip plan: $e', // Set error message
          ));

          _handleStreamError('Error parsing Trip object: $e');
        }
      } else {
        AppLogger.e(
            "Expected a JSON object for Trip, got ${jsonData.runtimeType}");

        // Update state to indicate trip plan loading failed
        emit(state.copyWith(
          connState: VTPConnState.error,
          loading: false,
          isWaitingForResponse: false,
          isTripPlanLoading:
              false, // Trip plan loading is complete (with error)
          tripPlanErrorMessage:
              'Invalid trip plan data format', // Set error message
        ));

        _handleStreamError('Invalid Trip data format');
      }
    } catch (e) {
      // Update state to indicate trip plan loading failed
      emit(state.copyWith(
        connState: VTPConnState.error,
        loading: false,
        isWaitingForResponse: false,
        isTripPlanLoading: false, // Trip plan loading is complete (with error)
        tripPlanErrorMessage:
            'Error processing trip plan: $e', // Set error message
      ));

      _handleStreamError('Error processing trip plan: $e');
    }
  }

  /// Handle refined trip plan response (2nd round from AI)
  void _handleRefinedTripPlanResponse(dynamic jsonData) {
    try {

      final prettyString = const JsonEncoder.withIndent('  ').convert(jsonData);
      AppLogger.d("Received patch: $prettyString");

      if (jsonData is Map<String, dynamic> && jsonData.containsKey('ai_trip')) {
        final aiTrip = jsonData['ai_trip'];
        final aiTripMap = {'ai_trip': aiTrip};

        // Check for JSON Patch format (patch field)
        final suggestion = ChatSuggestionModel.fromJson(
          {'message': jsonEncode(aiTripMap)},
            purposeKey: AIPurposeKey.generalPurpose //to parse for ai_trip
        );
        final aiTripData = suggestion.aiTrip;
        if (aiTripData != null) {
          final normalizedTripData = Trip.normalizeDataForTrip(aiTripData);
          final Trip trip = Trip.fromJson(normalizedTripData);
          AppLogger.d("Successfully parsed refined Trip (patched):  [1m${trip.name}");
          emit(state.copyWith(
            connState: VTPConnState.success,
            loading: false,
            isWaitingForResponse: false,
            isTripPlanLoading: false,
            tripPlanErrorMessage: null,
            aiTrip: trip,
            tripPlanStatusMessage: null,
          ));
        } else {
          AppLogger.e("Patch applied but no aiTrip data returned");
          throw Exception('Patch applied but no aiTrip data returned');
        }
      } else {
        AppLogger.e("Invalid Json : $jsonData ");
        throw Exception('Invalid Json in response or no Patch in Json');
      }
    } catch (e, stackTrace) {
      AppLogger.e("Error processing refined trip plan: $e \n $stackTrace");
      if (_lastFullTrip != null) {
        
        emit(state.copyWith(
          connState: VTPConnState.success,
          loading: false,
          isWaitingForResponse: false,
          isTripPlanLoading: false,
          tripPlanErrorMessage: null,
          aiTrip: _lastFullTrip,
          tripPlanStatusMessage: null,
        ));
      } else {
        emit(state.copyWith(
          connState: VTPConnState.error,
          loading: false,
          isWaitingForResponse: false,
          isTripPlanLoading: false,
          tripPlanErrorMessage: 'Patch applied but no aiTrip data returned',
        ));
      }


    }
  }

  /// Helper method to determine the icon type based on the destination and activities
  String _getIconTypeForDestination(
      String city, String country, List<String> activities) {
    // Check activities for keywords to determine the icon type
    final String activitiesText = activities.join(' ').toLowerCase();

    if (activitiesText.contains('beach') ||
        activitiesText.contains('ocean') ||
        activitiesText.contains('sea') ||
        activitiesText.contains('swim')) {
      return 'beach';
    } else if (activitiesText.contains('mountain') ||
        activitiesText.contains('hiking') ||
        activitiesText.contains('trek')) {
      return 'mountain';
    } else if (activitiesText.contains('temple') ||
        activitiesText.contains('shrine') ||
        activitiesText.contains('worship')) {
      return 'temple';
    } else if (activitiesText.contains('food') ||
        activitiesText.contains('restaurant') ||
        activitiesText.contains('cuisine') ||
        activitiesText.contains('dining')) {
      return 'food';
    } else if (activitiesText.contains('nature') ||
        activitiesText.contains('park') ||
        activitiesText.contains('garden')) {
      return 'nature';
    } else if (activitiesText.contains('museum') ||
        activitiesText.contains('gallery') ||
        activitiesText.contains('exhibition')) {
      return 'museum';
    }

    // Default to city icon if no specific activities match
    return 'city';
  }

  /// Handle WebSocket stream errors
  void _handleStreamError(dynamic error) {
    AppLogger.e("WebSocket error: $error");

    emit(state.copyWith(
      connState: VTPConnState.error,
      loading: false,
      isWaitingForResponse: false,
      errorMessage: 'Error: $error',
    ));
  }

  /// Handle WebSocket stream completion
  void _handleStreamDone() {
    AppLogger.d("WebSocket connection closed");

    emit(state.copyWith(
      connState: VTPConnState.done,
      loading: false,
      isWaitingForResponse: false,
    ));
  }

  /// Search for destinations with the given search term and vibes
  Future<void> searchDestinations(
      String searchTerm, List<String> selectedVibes) async {
    // Update state with new search parameters
    emit(state.copyWith(
      searchTerm: searchTerm,
      selectedVibes: selectedVibes,
      isWaitingForResponse: true,
      loading: true,
    ));

    // Create the search data as a JSON string
    final searchData =
        jsonEncode({"searchTerm": searchTerm, "selectedVibes": selectedVibes});

    // Create and send the message in the format required by the AI service
    final jsonMessage = jsonEncode({
      "command": WebSocketCommands.aiChat,
      "message": searchData,
      "auto_create": AutoCreateValues.falseValue,
      "prompt": AIPrompts.vtpDiscover
    });

    AppLogger.d("Sending search message: $jsonMessage");
    _aiController.sendMessage(jsonMessage);
  }

  /// Plan a trip with direct information
  Future<void> planDirectTrip(DirectTripData directData) async {
    // Update state to indicate waiting for response
    emit(state.copyWith(
      isWaitingForResponse: true,
      isTripPlanLoading: true,
      tripPlanErrorMessage: null,
    ));

    // Create the trip plan data
    final tripPlanData = TripPlanData(
      tripIntent: TripIntentValues.direct,
      data: directData.toJson(),
    );

    // Convert the trip plan data to a JSON string
    final tripPlanJson = jsonEncode(tripPlanData.toJson());

    // Create the WebSocket message in the format required by the AI service
    final jsonMessage = jsonEncode({
      "command": WebSocketCommands.aiChat,
      "message": tripPlanJson,
      "auto_create": AutoCreateValues.falseValue,
      "prompt": AIPrompts.vtpPlan
    });

    AppLogger.d("Sending direct trip plan message: $jsonMessage");

    // Send the message
    _aiController.sendMessage(jsonMessage);
  }

  /// Reset the connection state and clear any error messages
  void resetConnectionState() {
    AppLogger.d("Resetting connection state and clearing error messages");
    emit(state.copyWith(
      connState: VTPConnState.none,
      errorMessage: null,
      loading: false,
      isWaitingForResponse: false,
      isTripPlanLoading: false,
      tripPlanErrorMessage: null,
    ));
  }

  /// Reset the trip plan in the state
  void resetTripPlan() {
    AppLogger.d("Resetting trip plan");
    emit(state.copyWith(
      aiTrip: null,
    ));
  }

  /// Send a custom WebSocket message
  /// This can be used by other components to send messages through the same WebSocket connection
  void sendWebSocketMessage(String jsonMessage) {
    AppLogger.d("Sending custom WebSocket message: $jsonMessage");

    // Check if this is a trip plan request
    bool isTripPlanRequest = false;
    try {
      final Map<String, dynamic> message = jsonDecode(jsonMessage);
      if (message.containsKey('prompt') &&
          message['prompt'] == AIPrompts.vtpPlan) {
        isTripPlanRequest = true;
        AppLogger.d("Detected trip plan request");
      }
    } catch (e) {
      // If we can't parse the message, assume it's not a trip plan request
      AppLogger.e("Error parsing WebSocket message: $e");
    }

    // Update state to indicate waiting for response
    emit(state.copyWith(
      isWaitingForResponse: true,
      isTripPlanLoading: isTripPlanRequest ? true : state.isTripPlanLoading,
      tripPlanStatusMessage: isTripPlanRequest ? 'Creating your trip plan...' : '',
      tripPlanErrorMessage:
          isTripPlanRequest ? null : state.tripPlanErrorMessage,
    ));

    // Send the message
    _aiController.sendMessage(jsonMessage);
  }

  /// Search for hotels based on trip preferences and locations, supporting multiple hotels for multi-city trips
  Future<Trip> _searchAndAddHotelToTrip(Trip trip) async {
    try {
      final hotelPreferences = trip.hotelPreferences!;

      // Extract check-in and check-out dates from preferences or trip dates
      DateTime checkInDate = hotelPreferences.checkInDate ?? trip.fromDate;
      DateTime checkOutDate = hotelPreferences.checkOutDate ?? trip.toDate;

      AppLogger.d("Searching hotels for multi-location trip");
      AppLogger.d("Base check-in: $checkInDate, Base check-out: $checkOutDate");

      // Get the hotel booking usecase
      final HotelBookingUseCase hotelUseCase =
          locator.get<HotelBookingUseCase>();

      // Collect all unique locations with their IATA city codes from the trip
      Map<String, String> locationCodes = _extractLocationCodesFromTrip(trip);
      AppLogger.d(
          "Found ${locationCodes.length} unique locations with codes: $locationCodes");

      if (locationCodes.isEmpty) {
        AppLogger.w("No valid city codes found for hotel search");
        return trip; // Return original trip if no valid city codes
      }

      List<HotelBookingModel> selectedHotels = [];
      List<String> failedLocations = [];
      List<String> noHotelsLocations = [];

      // Search hotels for each location using city codes
      int locationIndex = 0;
      for (MapEntry<String, String> locationEntry in locationCodes.entries) {
        String cityName = locationEntry.key;
        String cityCode = locationEntry.value;
        AppLogger.d(
            "Searching hotels for location: $cityName (code: $cityCode)");

        // Create location-specific preferences using city code for API call
        HotelPreferences locationPreferences = HotelPreferences(
          location: cityCode, // Use IATA city code for API call
          checkInDate: hotelPreferences.checkInDate,
          checkOutDate: hotelPreferences.checkOutDate,
          numberOfGuests: hotelPreferences.numberOfGuests,
          roomType: hotelPreferences.roomType,
          starRating: hotelPreferences.starRating,
          amenities: hotelPreferences.amenities,
          budget: hotelPreferences.budget,
        );

        // Calculate dates for this location (for multi-city trips, we might need different date ranges)
        HotelStayPeriod locationStayPeriod = _calculateLocationDateRange(
            trip, cityName, locationIndex, locationCodes.length);

        try {
          // Search for hotels using the new method with city code
          List<HotelModel> hotels =
              await hotelUseCase.searchHotelsByPreferences(
            locationPreferences,
            locationStayPeriod.checkIn,
            locationStayPeriod.checkOut,
          );

          AppLogger.d(
              "Found ${hotels.length} hotels for $cityName (code: $cityCode)");

          if (hotels.isNotEmpty) {
            // TODO: Add placeholder for filtering function that can be called later
            List<HotelModel> filteredHotels =
                _filterHotelsForTrip(hotels, locationPreferences);

            // Keep the first item in the list
            HotelModel selectedHotel =
                filteredHotels.isNotEmpty ? filteredHotels.first : hotels.first;

            AppLogger.d("Selected hotel for $cityName: ${selectedHotel.name}");

            // Create hotel booking for this location using existing HotelBookingModel
            HotelBookingModel hotelBooking = HotelBookingModel(
              hotelId: selectedHotel.id,
              hotelName: selectedHotel.name,
              imageUrl: selectedHotel.imageURL,
              location: cityName, // Use city name for display
              provider: "Auto-selected for trip planning",
              checkInDate: locationStayPeriod.checkIn
                  .toIso8601String()
                  .split('T')[0], // YYYY-MM-DD format
              checkOutDate: locationStayPeriod.checkOut
                  .toIso8601String()
                  .split('T')[0], // YYYY-MM-DD format
              bookingResults: [
                {
                  "itineraryDayIndexes":
                      _getItineraryDaysForLocation(trip, cityName),
                  "notes": "Auto-selected based on trip preferences",
                  "selectionTimestamp": DateTime.now().toIso8601String(),
                  "cityCode": cityCode, // Store the city code used for search
                }
              ],
            );

            selectedHotels.add(hotelBooking);
          } else {
            // No hotels found for this location
            AppLogger.w(
                "No hotels found for location: $cityName (code: $cityCode)");
            noHotelsLocations.add(cityName);

            // Create a placeholder booking to indicate the issue
            HotelBookingModel placeholderBooking =
                _createPlaceholderHotelBooking(cityName, locationStayPeriod,
                    trip, "No hotels available for this location");
            selectedHotels.add(placeholderBooking);
          }
        } catch (e) {
          // API call failed for this location
          AppLogger.e(
              "Hotel search API failed for location $cityName (code: $cityCode): $e");
          failedLocations.add(cityName);

          // Create a placeholder booking to indicate the failure
          HotelBookingModel placeholderBooking = _createPlaceholderHotelBooking(
              cityName,
              locationStayPeriod,
              trip,
              "Hotel search failed: ${e.toString()}");
          selectedHotels.add(placeholderBooking);
        }

        locationIndex++; // Increment for next location
      }

      // Log summary of hotel search results
      AppLogger.d("Hotel search completed:");
      AppLogger.d("- Total locations: ${locationCodes.length}");
      AppLogger.d(
          "- Successful hotels: ${selectedHotels.length - failedLocations.length - noHotelsLocations.length}");
      AppLogger.d(
          "- Failed API calls: ${failedLocations.length} (${failedLocations.join(', ')})");
      AppLogger.d(
          "- No hotels available: ${noHotelsLocations.length} (${noHotelsLocations.join(', ')})");

      // Create a new Trip object with the selected hotels
      return Trip(
        name: trip.name,
        isDateConfirmed: trip.isDateConfirmed,
        fromDate: trip.fromDate,
        toDate: trip.toDate,
        country: trip.country,
        city: trip.city,
        color: trip.color,
        description: trip.description,
        itinerary: trip.itinerary,
        familyId: trip.familyId,
        uuid: trip.uuid,
        hotelPreferences: trip.hotelPreferences,
        flightPreferences: trip.flightPreferences,
        selectedHotels: selectedHotels.isNotEmpty ? selectedHotels : null,
        cityCode: trip.cityCode,
        additionalCities: trip.additionalCities,
        additionalCityCode: trip.additionalCityCode,
      );
    } catch (e) {
      AppLogger.e("Error searching for hotels: $e");
      // Return the original trip if hotel search fails
      return trip;
    }
  }

 

  //Best to move to AI for better thinking 
  // 
  Future<dynamic> _searchAndAddFlightToTrip(Trip trip) async {
    final flightPreference = trip.flightPreferences;
    if (flightPreference == null) {
      AppLogger.d("No flight preference found in trip");
      return trip;
    }

    try {
      final FlightBookingUseCase flightUseCase = locator.get<FlightBookingUseCase>();
      final String origin = flightPreference.departureAirportCode ?? '';
      final String destination = flightPreference.arrivalAirportCode ?? '';
      final DateTime? departureDate = flightPreference.departureDate;
      final DateTime? returnDate = flightPreference.returnDate;
      final int adults = flightPreference.numberOfPassengers ?? 1;
      final List<String>? airlineCodes = flightPreference.preferredAirlines;

      if (departureDate == null) {
        AppLogger.e("No departure date in flight preferences");
        return trip;
      }

      AppLogger.d(
          "Searching flights: $origin -> $destination, depart: $departureDate, return: $returnDate, adults: $adults, airlines: $airlineCodes");

      final List<FlightOfferModel> flightOffers = await flightUseCase.searchFlights(
        origin,
        destination,
        departureDate,
        adults,
        airlineCodesP: airlineCodes,
        returnDate: returnDate,
        sortBy: 'duration', ascending: false
      );

      AppLogger.d("Found ${flightOffers.length} flight offers");

      // for (var offer in flightOffers) {
      //   final AmaFlightOfferModel amaFlight = offer as AmaFlightOfferModel;
      //   AppLogger.d("Flight Offer: "
      //       "Duration: ${amaFlight.itineraries[0].duration}");
      // }
      

      FlightOfferModel? selectedFlight;
      if (flightOffers.isNotEmpty) {
        selectedFlight = flightOffers.last; 
      }

      if (selectedFlight != null && selectedFlight is AmaFlightOfferModel) {
        final AmaFlightOfferModel amaFlight = selectedFlight;
        
        // Insert/replace in itinerary: only update the first day and last day
        List<Itinerary> updatedItinerary = List.from(trip.itinerary);
        // Outbound flight activities (first itinerary in AmaFlightOfferModel)
        List<Activity> outboundActivities = [];
        // Return flight activities (second itinerary in AmaFlightOfferModel, if exists)
        List<Activity> returnActivities = [];

        //OUTBOUND FLIGHT
        if (amaFlight.itineraries.isNotEmpty) {
          for (final segment in amaFlight.itineraries[0].segments) {
            String flightNumber = '${segment.carrierCode ?? ''} ${segment.number ?? ''}';
            if (segment.operating.containsKey('carrierCode') &&
                segment.operating['carrierCode'] != segment.carrierCode) {
              //This segment is operated by a different airline - the number may not be correct,
              flightNumber = '${segment.operating['carrierCode']}';
            }


            final activity = Activity(
              time: segment.departure?.at.substring(11, 16) ?? '',
              venue: segment.arrival?.iataCode,
              type: 'T',
              mode: 'flight',
              origin: segment.departure?.iataCode,
              destination: segment.arrival?.iataCode,
              duration: _parseDurationMinutes(segment.duration),
              depatureTimeAndDate: segment.departure?.at,
              arrivalTimeAndDate: segment.arrival?.at,
              transportCode: flightNumber, 

            );
            /*  outbound segment: 'VN 306 SGN → NRT'
               depature at: 2025-12-04T00:20:00 - arr at: 2025-12-04T07:45:00*/
            logd(
                " outbound segment: '${segment.carrierCode ?? ''} ${segment.number ?? ''} ${segment.departure?.iataCode ?? ''} → ${segment.arrival?.iataCode ?? ''}' \n"
                " depature at: ${segment.departure?.at} - arr at: ${segment.arrival?.at}\n"
                " duration: ${segment.duration}");

            outboundActivities.add(activity);
          }
          AppLogger.d('Built outbound flight activities: count=${outboundActivities.length}');
        }

        // RETURN FLIGHT ---- 
        if (amaFlight.itineraries.length > 1) {
          for (final segment in amaFlight.itineraries[1].segments) {
            String flightNumber = '${segment.carrierCode ?? ''} ${segment.number ?? ''}';
            if (segment.operating.containsKey('carrierCode') &&
                segment.operating['carrierCode'] != segment.carrierCode) {
              //This segment is operated by a different airline - the number may not be correct,
              flightNumber = '${segment.operating['carrierCode']}';
            }
            final activity = Activity(
              time: segment.departure?.at.substring(11, 16) ?? '', // Should we do the same? 
              venue: segment.arrival?.iataCode,
              type: 'T',
              mode: 'flight',
              origin: segment.departure?.iataCode,
              destination: segment.arrival?.iataCode,
              duration: _parseDurationMinutes(segment.duration),
              depatureTimeAndDate: segment.departure?.at,
              arrivalTimeAndDate: segment.arrival?.at,
              transportCode: flightNumber, 
            );

            logd(
                "REturn segment: '${segment.carrierCode ?? ''} ${segment.number ?? ''} ${segment.departure?.iataCode ?? ''} → ${segment.arrival?.iataCode ?? ''}' \n"
                " depature at: ${segment.departure?.at} - arr at: ${segment.arrival?.at} \n"
                " duration: ${segment.duration}");
            returnActivities.add(activity);
          }
          AppLogger.d('Built return flight activities: count=${returnActivities.length}');
        }


        // FIRST DAY: PRE-PEND the OUTBOUND FLIGHT into first day.
        if (updatedItinerary.isNotEmpty) {
          AppLogger.d('Updating first day of itinerary with outbound flight activities...');
          final dayItinerary = updatedItinerary[0];
          List<Activity> dayActivities = List.from(dayItinerary.activities ?? []);
          int firstTIdx = dayActivities.indexWhere((a) => a.type == 'T');
          if (firstTIdx != -1 && dayActivities[firstTIdx].mode == 'flight') {
            dayActivities.removeAt(firstTIdx);
            dayActivities.insertAll(firstTIdx, outboundActivities);
            AppLogger.d('Replaced first T/flight activity with outbound flight activities.');
          } else {
            dayActivities = [...outboundActivities, ...dayActivities];
            AppLogger.d('Prepended outbound flight activities to first day.');
          }

          //TODO: adjust the subsequence Transport activity to match the arrival time here 
          // Find the arrival time of the last outbound flight segment (use arrival.at)
          String? arrivalTime;
          if (amaFlight.itineraries.isNotEmpty && amaFlight.itineraries[0].segments.isNotEmpty) {
            final lastSegment = amaFlight.itineraries[0].segments.last;
            if (lastSegment.arrival?.at != null) {
              arrivalTime = lastSegment.arrival!.at;
            }
          }
          AppLogger.d('Flight arrival time (ISO): $arrivalTime');
          // Use simplified function to remove the next T (non-flight) activity
          _removeImmediateNextTransportActivity(dayActivities, outboundActivities.last);
          // Keep the original adjustment logic in a separate function for reuse
          // _adjustNextTransportActivityTime(dayActivities, arrivalTime);


          updatedItinerary[0] = Itinerary(
            accommodation: dayItinerary.accommodation,
            activities: dayActivities,
            food: dayItinerary.food,
            imageUrl: dayItinerary.imageUrl,
            documents: dayItinerary.documents,
          );
          AppLogger.d('First day of itinerary updated.');
        }
        // LAST DAY: append the return flight into the last day 
        if (updatedItinerary.isNotEmpty && returnActivities.isNotEmpty) {
          AppLogger.d('Updating last day of itinerary with return flight activities...');
          final lastIdx = updatedItinerary.length - 1;
          final lastDayItinerary = updatedItinerary[lastIdx];
          List<Activity> lastDayActivities = List.from(lastDayItinerary.activities ?? []);
          int lastTIdx = lastDayActivities.lastIndexWhere((a) => a.type == 'T');
          if (lastTIdx != -1 && lastDayActivities[lastTIdx].mode == 'flight') {
            lastDayActivities.removeAt(lastTIdx);
            lastDayActivities.insertAll(lastTIdx, returnActivities);
            AppLogger.d('Replaced last T/flight activity with return flight activities.');
          } else {
            lastDayActivities = [...lastDayActivities, ...returnActivities];
            AppLogger.d('Appended return flight activities to last day.');
          }

          //TODO: adjust the other previous Transport activity to match the depature time here 
          // Find the departure time of the first return flight segment (use departure.at)
          String? departureTime;
          if (amaFlight.itineraries.length > 1 && amaFlight.itineraries[1].segments.isNotEmpty) {
            final firstSegment = amaFlight.itineraries[1].segments.first;
            if (firstSegment.departure?.at != null) {
              departureTime = firstSegment.departure!.at;
            }
          }
          AppLogger.d('Return flight departure time (ISO): $departureTime');
          // Use simplified function to remove the previous T (non-flight) activity
          _removeImmediatePreviousTransportActivity(lastDayActivities, returnActivities.first);
          // Keep the original adjustment logic in a separate function for reuse
          // _adjustPreviousTransportActivityTime(lastDayActivities, departureTime);

          updatedItinerary[lastIdx] = Itinerary(
            accommodation: lastDayItinerary.accommodation,
            activities: lastDayActivities,
            food: lastDayItinerary.food,
            imageUrl: lastDayItinerary.imageUrl,
            documents: lastDayItinerary.documents,
          );
          AppLogger.d('Last day of itinerary updated.');
        }

        return Trip(
          name: trip.name,
          isDateConfirmed: trip.isDateConfirmed,
          fromDate: trip.fromDate,
          toDate: trip.toDate,
          country: trip.country,
          city: trip.city,
          color: trip.color,
          description: trip.description,
          itinerary: updatedItinerary,
          familyId: trip.familyId,
          uuid: trip.uuid,
          hotelPreferences: trip.hotelPreferences,
          flightPreferences: trip.flightPreferences,
          selectedHotels: trip.selectedHotels,
          cityCode: trip.cityCode,
          additionalCities: trip.additionalCities,
          additionalCityCode: trip.additionalCityCode,
          gpsCoord: trip.gpsCoord,
        );
      }
      return trip;
    } catch (e, stackTrace) {
      AppLogger.e("Error searching for flights: $e");
      AppLogger.e("Stack trace: $stackTrace");
      return trip;
    }
  }

  int _parseDurationMinutes(String? duration) {
    if (duration == null) return 0;
    // Example: 'PT2H30M'
    final regex = RegExp(r'PT(?:(\d+)H)?(?:(\d+)M)?');
    final match = regex.firstMatch(duration);
    if (match == null) return 0;
    final hours = int.tryParse(match.group(1) ?? '0') ?? 0;
    final minutes = int.tryParse(match.group(2) ?? '0') ?? 0;
    return hours * 60 + minutes;
  }

  DateTime _extractDateFromTime(String time, DateTime baseDate) {
    // Use baseDate for now; can be improved to parse from segment.at
    return baseDate;
  }

  int _findItineraryIndexByDate(Trip trip, DateTime date) {
    for (int i = 0; i < trip.itinerary.length; i++) {
      // For now, match by index; can be improved to match by actual date
      return i;
    }
    return -1;
  }

  /// Create a placeholder hotel booking when hotel search fails or returns no results
  HotelBookingModel _createPlaceholderHotelBooking(
    String location,
    HotelStayPeriod stayPeriod,
    Trip trip,
    String errorMessage,
  ) {
    return HotelBookingModel(
      hotelId: "placeholder_${location.toLowerCase().replaceAll(' ', '_')}",
      hotelName: "Hotel search unavailable",
      imageUrl: null,
      location: location,
      provider: "System placeholder",
      checkInDate: stayPeriod.checkIn.toIso8601String().split('T')[0],
      checkOutDate: stayPeriod.checkOut.toIso8601String().split('T')[0],
      bookingResults: [
        {
          "itineraryDayIndexes": _getItineraryDaysForLocation(trip, location),
          "notes": errorMessage,
          "status": "failed",
          "isPlaceholder": true,
          "failureTimestamp": DateTime.now().toIso8601String(),
        }
      ],
    );
  }

  /// Placeholder method for filtering hotels for the trip
  /// This can be enhanced later to support more sophisticated filtering
  List<HotelModel> _filterHotelsForTrip(
    List<HotelModel> hotels,
    HotelPreferences preferences,
  ) {
    // For now, just return the original list
    // Future enhancements can include filtering by:
    // - Star rating
    // - Amenities
    // - Budget range
    // - Room type
    // - Distance from city center
    // - User reviews/ratings
    AppLogger.d("Applying trip-specific hotel filters (placeholder)");
    return hotels;
  }

  /// Extract all unique locations from the trip and map them to their IATA city codes
  /// Returns a map of city name to city code for hotel search API
  Map<String, String> _extractLocationCodesFromTrip(Trip trip) {
    Map<String, String> locationCodes = {};

    // Add main city with its code
    if (trip.city != null &&
        trip.city!.isNotEmpty &&
        trip.cityCode != null &&
        trip.cityCode!.isNotEmpty) {
      locationCodes[trip.city!] = trip.cityCode!;
    }

    // Add additional cities with their codes
    if (trip.additionalCities != null && trip.additionalCityCode != null) {
      for (int i = 0;
          i < trip.additionalCities!.length &&
              i < trip.additionalCityCode!.length;
          i++) {
        String cityName = trip.additionalCities![i];
        String cityCode = trip.additionalCityCode![i];
        if (cityName.isNotEmpty && cityCode.isNotEmpty) {
          locationCodes[cityName] = cityCode;
        }
      }
    }

    AppLogger.d("Extracted location codes: $locationCodes");
    return locationCodes;
  }

  /// Extract all unique locations from the trip (main city, additional cities, and activity cities)
  /// This method is kept for backward compatibility but should use _extractLocationCodesFromTrip for hotel search
  List<String> _extractLocationsFromTrip(Trip trip) {
    Set<String> locations = {};

    // Add main city
    if (trip.city != null && trip.city!.isNotEmpty) {
      locations.add(trip.city!);
    }

    // Add additional cities
    if (trip.additionalCities != null) {
      locations.addAll(trip.additionalCities!.where((city) => city.isNotEmpty));
    }

    // If no locations found, use hotel preferences location as fallback
    if (locations.isEmpty && trip.hotelPreferences?.location != null) {
      locations.add(trip.hotelPreferences!.location!);
    }

    return locations.toList();
  }

  /// Get a map of locations to their corresponding itinerary day indexes
  Map<String, List<int>> _getLocationDaysFromItinerary(Trip trip) {
    Map<String, List<int>> locationDays = {};

    for (int dayIndex = 0; dayIndex < trip.itinerary.length; dayIndex++) {
      var itinerary = trip.itinerary[dayIndex];

      // Check if any activity in this day has a city specified
      if (itinerary.activities != null) {
        Set<String> citiesInThisDay = {};

        for (var activity in itinerary.activities!) {
          if (activity.city != null && activity.city!.isNotEmpty) {
            citiesInThisDay.add(activity.city!);
          }
        }

        // Add this day index to all cities found in this day
        for (String city in citiesInThisDay) {
          if (!locationDays.containsKey(city)) {
            locationDays[city] = [];
          }
          locationDays[city]!.add(dayIndex);
        }
      }
    }

    // If no cities found in activities, assign all days to main city and additional cities
    if (locationDays.isEmpty) {
      AppLogger.d(
          "No cities found in activities, using main city and additional cities");

      List<String> allLocations = [];
      if (trip.city != null && trip.city!.isNotEmpty) {
        allLocations.add(trip.city!);
      }
      if (trip.additionalCities != null) {
        allLocations
            .addAll(trip.additionalCities!.where((city) => city.isNotEmpty));
      }

      if (allLocations.isNotEmpty) {
        // Distribute days equally among locations
        int totalDays = trip.itinerary.length;
        int daysPerLocation = (totalDays / allLocations.length).ceil();

        for (int i = 0; i < allLocations.length; i++) {
          String location = allLocations[i];
          List<int> daysForLocation = [];

          int startDay = i * daysPerLocation;
          int endDay = (i == allLocations.length - 1)
              ? totalDays - 1 // Last location gets remaining days
              : (startDay + daysPerLocation - 1);

          for (int day = startDay; day <= endDay && day < totalDays; day++) {
            daysForLocation.add(day);
          }

          locationDays[location] = daysForLocation;
        }
      }
    }

    AppLogger.d("Location days mapping: $locationDays");
    return locationDays;
  }

  /// Calculate hotel stay period for a specific location based on itinerary days
  HotelStayPeriod _calculateLocationDateRange(
      Trip trip, String location, int locationIndex, int totalLocations) {
    // Count actual days for each location from the itinerary
    Map<String, List<int>> locationDays = _getLocationDaysFromItinerary(trip);

    List<int> daysForLocation = locationDays[location] ?? [];

    if (daysForLocation.isEmpty) {
      AppLogger.w(
          "No itinerary days found for location: $location, using fallback logic");
      // Fallback to equal division if no specific days found
      Duration tripDuration = trip.toDate.difference(trip.fromDate);
      int daysPerLocation = (tripDuration.inDays / totalLocations).ceil();

      DateTime checkIn =
          trip.fromDate.add(Duration(days: locationIndex * daysPerLocation));
      DateTime checkOut = locationIndex == totalLocations - 1
          ? trip.toDate
          : checkIn.add(Duration(days: daysPerLocation));

      if (checkOut.isAfter(trip.toDate)) {
        checkOut = trip.toDate;
      }

      return HotelStayPeriod(checkIn: checkIn, checkOut: checkOut);
    }

    // Calculate check-in and check-out dates based on actual itinerary days
    int firstDay =
        daysForLocation.reduce((a, b) => a < b ? a : b); // Min day index
    int lastDay =
        daysForLocation.reduce((a, b) => a > b ? a : b); // Max day index

    DateTime checkIn = trip.fromDate.add(Duration(days: firstDay));
    DateTime checkOut = trip.fromDate
        .add(Duration(days: lastDay + 1)); // +1 because check-out is next day

    // Ensure check-out doesn't exceed trip end date
    if (checkOut.isAfter(trip.toDate)) {
      checkOut = trip.toDate;
    }

    AppLogger.d(
        "Location $location: Days $daysForLocation, Check-in: $checkIn, Check-out: $checkOut");

    return HotelStayPeriod(checkIn: checkIn, checkOut: checkOut);
  }

  /// Get itinerary day indexes that correspond to a specific location
  List<int> _getItineraryDaysForLocation(Trip trip, String location) {
    List<int> dayIndexes = [];

    for (int i = 0; i < trip.itinerary.length; i++) {
      var itinerary = trip.itinerary[i];

      // Check if any activity in this day is in the specified location
      if (itinerary.activities != null) {
        bool hasLocationActivity = itinerary.activities!.any((activity) =>
            activity.city != null &&
            activity.city!.toLowerCase() == location.toLowerCase());

        if (hasLocationActivity) {
          dayIndexes.add(i);
        }
      }
    }

    // If no specific days found, return empty list (hotel applies to whole trip)
    return dayIndexes;
  }

  void clearAiTrip() {
    emit(state.copyWith(aiTrip: null));
  }

  AiConnectionController get aiController => _aiController;

  /// Public method to reconnect the WebSocket if needed
  Future<void> reconnect() async {
    await _connectToAI();
  }

  /// Original adjustment logic for the next transport activity after outbound flight (preserved for reuse)
  void _adjustNextTransportActivityTime(List<Activity> activities, String? arrivalTime) {
    if (arrivalTime != null && activities.isNotEmpty) {
      int idx = activities.indexWhere((a) => a.type == 'T' && a.mode != 'flight');
      if (idx != -1) {
        DateTime arrDT = DateTime.parse(arrivalTime);
        DateTime newTime = arrDT.add(const Duration(minutes: 60));
        String adjustedTime = _formatTimeHHmm(newTime);
        AppLogger.d('Adjusted next transfer activity time: $adjustedTime');
        activities[idx].time = _formatTimeHHmm(newTime); //historical purpose
        activities[idx].depatureTimeAndDate = newTime.toIso8601String().split('.').first;
      }
    }
  }

  /// Original adjustment logic for the previous transport activity before return flight (preserved for reuse)
  void _adjustPreviousTransportActivityTime(List<Activity> activities, String? departureTime) {
    if (departureTime != null && activities.isNotEmpty) {
      int idx = activities.lastIndexWhere((a) => a.type == 'T' && a.mode != 'flight');
      if (idx != -1) {
        DateTime depDT = DateTime.parse(departureTime);
        DateTime newArrivalTime = depDT.subtract(const Duration(minutes: 60));
        String arrivalTimeStr = newArrivalTime.toIso8601String().split('.').first;
        DateTime newDepartureTime = newArrivalTime.subtract(Duration(minutes: activities[idx].duration ?? 0));
        String adjustedTime = _formatTimeHHmm(newDepartureTime);
        AppLogger.d('Adjusted previous transfer activity time: $adjustedTime, arrivalTime: $arrivalTimeStr');
        activities[idx].time = adjustedTime;
        activities[idx].arrivalTimeAndDate = arrivalTimeStr;
      }
    }
  }

  /// Simplified: Remove the immediate next 'T' (non-flight) activity after the given activity
  void _removeImmediateNextTransportActivity(List<Activity> activities, Activity afterActivity) {
    int idx = activities.indexOf(afterActivity);
    if (idx != -1 && idx + 1 < activities.length) {
      final nextActivity = activities[idx + 1];
      if (nextActivity.type == 'T' && nextActivity.mode != 'flight') {
        activities.removeAt(idx + 1);
        AppLogger.d('Removed immediate next T (non-flight) activity after outbound flight.');
      }
    }
  }

  /// Simplified: Remove the immediate previous 'T' (non-flight) activity before the given activity
  void _removeImmediatePreviousTransportActivity(List<Activity> activities, Activity beforeActivity) {
    int idx = activities.indexOf(beforeActivity);
    if (idx > 0) {
      final prevActivity = activities[idx - 1];
      if (prevActivity.type == 'T' && prevActivity.mode != 'flight') {
        activities.removeAt(idx - 1);
        AppLogger.d('Removed immediate previous T (non-flight) activity before return flight.');
      }
    }
  }
}

/// Helper class to represent a hotel stay period with check-in and check-out dates
class HotelStayPeriod {
  final DateTime checkIn;
  final DateTime checkOut;

  HotelStayPeriod({required this.checkIn, required this.checkOut});

  /// Get the duration of the hotel stay in days
  int get durationInDays => checkOut.difference(checkIn).inDays;

  /// Check if the stay period is valid (check-out after check-in)
  bool get isValid => checkOut.isAfter(checkIn);

  @override
  String toString() {
    return 'HotelStayPeriod{checkIn: $checkIn, checkOut: $checkOut, duration: ${durationInDays} days}';
  }
}

// Helper to parse activity time string (HH:mm, HH.mm, etc.) to DateTime
DateTime _parseActivityTime(String time, DateTime dateTime) {
  if (time.isEmpty || time == 'AM') {
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
  } else if (time == 'PM') {
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 16, 0);
  } else if (time.contains(' - ')) {
    final times = time.split(' - ');
    if (times.length == 2) {
      final startTime = times[0].trim().split(':');
      if (startTime.length == 2) {
        final hour = int.tryParse(startTime[0]) ?? 10;
        final minute = int.tryParse(startTime[1]) ?? 0;
        return DateTime(dateTime.year, dateTime.month, dateTime.day, hour, minute);
      }
    }
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
  } else if (RegExp(r'^[0-9]{1,2}\\.[0-9]{2}?$').hasMatch(time.trim())) {
    // Matches 'HH.mm' or 'H.mm' (e.g., '10.00', '8.30', '15.45')
    final parts = time.trim().split('.');
    if (parts.length == 2) {
      final hour = int.tryParse(parts[0]) ?? 10;
      final minute = int.tryParse(parts[1]) ?? 0;
      return DateTime(dateTime.year, dateTime.month, dateTime.day, hour, minute);
    }
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
  } else if (RegExp(r'^[0-9]{1,2}:[0-9]{2}?$').hasMatch(time.trim())) {
    // Matches 'HH:mm' or 'H:mm' (e.g., '18:00', '8:30', '15:45')
    final parts = time.trim().split(':');
    if (parts.length == 2) {
      final hour = int.tryParse(parts[0]) ?? 10;
      final minute = int.tryParse(parts[1]) ?? 0;
      return DateTime(dateTime.year, dateTime.month, dateTime.day, hour, minute);
    }
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
  }
  return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
}

// Helper to format DateTime to HH:mm string
String _formatTimeHHmm(DateTime dt) {
  return dt.hour.toString().padLeft(2, '0') + ':' + dt.minute.toString().padLeft(2, '0');
}
