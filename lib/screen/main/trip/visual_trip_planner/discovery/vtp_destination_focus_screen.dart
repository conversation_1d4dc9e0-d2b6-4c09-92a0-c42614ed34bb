import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/main.dart';
import 'package:auto_route/auto_route.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_cubit.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_state.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_logistics_madlibs_screen.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_trip_plan_loading_indicator.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Model class for destination options
class DestinationOption {
  final String name;
  final String description;
  final String iconType;
  final List<String> touristActivities;

  DestinationOption({
    required this.name,
    required this.description,
    required this.iconType,
    this.touristActivities = const [],
  });

  /// Factory method to create a DestinationOption from JSON
  factory DestinationOption.fromJson(Map<String, dynamic> json) {
    return DestinationOption(
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      iconType: json['iconType'] ?? 'location_city',
      touristActivities:
          (json['tourist_activities'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  /// Get the appropriate icon based on the iconType
  IconData getIcon() {
    switch (iconType) {
      case 'beach':
        return Icons.beach_access;
      case 'mountain':
        return Icons.landscape;
      case 'city':
        return Icons.location_city;
      case 'temple':
        return Icons.temple_buddhist;
      case 'food':
        return Icons.restaurant;
      case 'nature':
        return Icons.nature;
      case 'museum':
        return Icons.museum;
      case 'park':
        return Icons.park;
      default:
        return Icons.place;
    }
  }

  /// Check if two DestinationOption objects are equal
  /// This compares all properties to ensure uniqueness
  bool isEqualTo(DestinationOption other) {
    return name == other.name &&
        description == other.description &&
        iconType == other.iconType &&
        _listEquals(touristActivities, other.touristActivities);
  }

  /// Helper method to compare two lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'DestinationOption(name: $name, description: $description)';
  }
}

class VTPDestinationFocusScreen extends StatefulWidget {
  const VTPDestinationFocusScreen({
    required this.initialData,
    required this.onComplete,
    super.key,
  });

  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onComplete;

  @override
  State<VTPDestinationFocusScreen> createState() =>
      _VTPDestinationFocusScreenState();
}

class _VTPDestinationFocusScreenState extends State<VTPDestinationFocusScreen> {
  late String _searchTerm;
  late List<String> _selectedVibes;
  DestinationOption? _selectedDestination;
  VTPDiscoveryCubit? _discoveryCubit;
  bool _isInitializing = true;
  final LocalStorage _localStorage = GetIt.instance.get<LocalStorage>();
  // Add this line:
  final AccountService accountService = locator.get();

  @override
  void initState() {
    super.initState();
    _searchTerm = widget.initialData['searchTerm'] ?? '';
    _selectedVibes =
        List<String>.from(widget.initialData['selectedVibes'] ?? []);

    // Initialize the discovery cubit
    _initializeDiscoveryCubit();
  }

  /// Initialize the discovery cubit with the token from localStorage
  Future<void> _initializeDiscoveryCubit() async {
    setState(() {
      _isInitializing = true;
    });

    final token = await _localStorage.accessToken() ?? '';

    _discoveryCubit = VTPDiscoveryCubit(
      parameter: VTPDiscoveryParameter(
        token: token,
        familyId: '', // Family ID is not needed for this implementation
        initialSearchTerm: _searchTerm,
        initialSelectedVibes: _selectedVibes,
      ),
    );

    // Initialize the cubit
    await _discoveryCubit!.onInit();

    if (mounted) {
      setState(() {
        _isInitializing = false;
      });
    }
  }

  @override
  void dispose() {
    _discoveryCubit?.close();
    super.dispose();
  }

  void _handleDestinationSelection(DestinationOption destination) {
    // Only update if the selection has changed
    // Using the isEqualTo method for proper object comparison
    if (_selectedDestination == null ||
        !_selectedDestination!.isEqualTo(destination)) {
      setState(() {
        _selectedDestination = destination;
      });

      // Log the selection for debugging
      AppLogger.d(
          "Selected destination: ${destination.name} (${destination.description})");
    }
  }

  void _handleConfirm() {
    if (_selectedDestination == null || _discoveryCubit == null) return;

    // We already have the selected destination object
    final selectedOption = _selectedDestination!;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => VTPLogisticsMadLibsSheet(
        initialData: {
          ...widget.initialData, // Merge all previous initialData
          'destination': selectedOption.name,
          'selectedVibes': _selectedVibes,
          'searchTerm': _searchTerm,
          'touristActivities': selectedOption.touristActivities,
        },
        discoveryCubit:
            _discoveryCubit, // Pass the discovery cubit for WebSocket communication
        onComplete: (data) {
          widget.onComplete({
            ...data,
            'destination': selectedOption.name,
            'selectedVibes': _selectedVibes,
            'searchTerm': _searchTerm,
            'touristActivities': selectedOption.touristActivities,
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while initializing
    if (_isInitializing || _discoveryCubit == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Loading destinations...',
                style: AppStyle.regular16V2(),
              ),
            ],
          ),
        ),
      );
    }

    return BlocProvider<VTPDiscoveryCubit>.value(
      value: _discoveryCubit!,
      child: Stack(
        children: [
          BlocConsumer<VTPDiscoveryCubit, VTPDiscoveryState>(
            listenWhen: (previous, current) {
              final shouldListen = //current.aiTrip != null;
                  previous.aiTrip == null && current.aiTrip != null;
              AppLogger.d(
                  "BlocConsumer listenWhen: previous.aiTrip=${previous.aiTrip != null ? 'not null' : 'null'}, current.aiTrip=${current.aiTrip != null ? 'not null' : 'null'}, shouldListen=$shouldListen");
              return shouldListen;
            },
            listener: (context, state) {
              AppLogger.d(
                  "BlocConsumer listener triggered with aiTrip: ${state.aiTrip != null ? 'not null' : 'null'}");
              // If we have a trip plan, navigate to the trip preview screen
              if (state.aiTrip != null) {
                _showTripPreview(context, state);
              }
            },
            builder: (context, state) {
              // Don't show any error dialog, the UI will handle all states
              // The empty state and error state will be displayed in the UI

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocaleKeys.trip_planner_destination_title.tr(),
                      style: AppStyle.bold24V2(),
                    ),
                    const SizedBox(height: 24),

                    // Search Inspiration Card
                    _buildSearchInspirationCard(),
                    const SizedBox(height: 24),

                    // Destination Options
                    Text(
                      LocaleKeys.trip_planner_destination_options.tr(),
                      style: AppStyle.bold18V2(),
                    ),
                    const SizedBox(height: 16),

                    // Destination options based on state
                    Builder(
                      builder: (context) {
                        if (state.loading) {
                          return _buildLoadingDestinationOptions();
                        } else if (state.connState == VTPConnState.error) {
                          return _buildErrorDestinationOptions(
                              state.errorMessage ?? 'Unknown error');
                        } else if (state.destinationOptions.isEmpty) {
                          return _buildEmptyDestinationOptions();
                        } else {
                          return Column(
                            children: state.destinationOptions
                                .map(
                                    (option) => _buildDestinationOption(option))
                                .toList(),
                          );
                        }
                      },
                    ),

                    const SizedBox(height: 32),

                    // Confirm Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _selectedDestination != null
                            ? _handleConfirm
                            : null,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: const Color(
                              0xFF4E46B4), // Purple color for consistency
                          foregroundColor: Colors.white,
                          elevation:
                              4, // Increased elevation for better visibility
                          disabledBackgroundColor: const Color(0xFF4E46B4)
                              .withAlpha(
                                  128), // Semi-transparent purple when disabled (alpha 128 = 50%)
                          disabledForegroundColor: Colors.white.withAlpha(
                              178), // Semi-transparent white text when disabled (alpha 178 = 70%)
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.trip_planner_destination_confirm.tr(),
                              style: AppStyle.bold18V2(
                                  color: Colors.white), // Increased font size
                            ),
                            const SizedBox(width: 8),
                            const Icon(Icons.arrow_forward,
                                color: Colors.white), // Added icon
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          // Add the loading indicator overlay
          const VTPTripPlanLoadingIndicator(),
        ],
      ),
    );
  }

  Widget _buildSearchInspirationCard() {
    // This is the main container for the search inspiration card
    // You can easily switch between different styles by changing which method is called

    // UNCOMMENT ONE OF THESE STYLES AND COMMENT OUT THE OTHERS

    // Style 1: Gradient card with white text (original style)
    return _buildSearchInspirationCardStyle1();

    // Style 2: Modern card with decorative elements
    //return _buildSearchInspirationCardStyle2();

    // Style 3: Minimalist card with clean layout
    //return _buildSearchInspirationCardStyle3();
  }

  // Style 3: Minimalist card with clean layout
  // ignore: unused_element
  Widget _buildSearchInspirationCardStyle3() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey[200]!),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search term display
            Text(
              "Your Search",
              style: AppStyle.bold14V2(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.search, size: 20, color: Colors.grey),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _searchTerm,
                      style: AppStyle.bold16V2(),
                    ),
                  ),
                ],
              ),
            ),

            // Vibes section
            if (_selectedVibes.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                LocaleKeys.trip_planner_destination_vibes.tr(),
                style: AppStyle.bold14V2(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedVibes.map((vibe) {
                  return Chip(
                    label: Text(vibe),
                    backgroundColor: appTheme.backgroundSelago,
                    labelStyle:
                        AppStyle.regular12V2(color: appTheme.primaryColorV2),
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Style 2: Modern card with image background and more visual elements
  // ignore: unused_element
  Widget _buildSearchInspirationCardStyle2() {
    return Card(
      elevation: 4,
      clipBehavior: Clip.antiAlias, // Ensures the image doesn't overflow
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // Background with semi-transparent overlay
          Container(
            height: _selectedVibes.isEmpty ? 150 : 220,
            decoration: BoxDecoration(
              color: appTheme.primaryColorV2,
            ),
          ),

          // Decorative elements
          Positioned(
            right: -20,
            top: -20,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withAlpha(30),
              ),
            ),
          ),
          Positioned(
            left: -30,
            bottom: -30,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withAlpha(20),
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Icon and title in a row with more emphasis
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.travel_explore,
                        color: appTheme.primaryColorV2,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Exploring",
                            style: AppStyle.regular14V2(
                                color: Colors.white.withAlpha(200)),
                          ),
                          Text(
                            _searchTerm,
                            style: AppStyle.bold24V2(color: Colors.white),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Vibes section with a different style
                if (_selectedVibes.isNotEmpty) ...[
                  const SizedBox(height: 20),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(30),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.local_activity_outlined,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              "Trip Vibes",
                              style: AppStyle.bold14V2(color: Colors.white),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _selectedVibes.map((vibe) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                vibe,
                                style: AppStyle.regular12V2(
                                    color: appTheme.primaryColorV2),
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Style 1: Gradient card with white text
  Widget _buildSearchInspirationCardStyle1() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              appTheme.primaryColorV2.withAlpha(204),
              appTheme.primaryColorV2,
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Term Header
            _buildSearchHeader(),
            const SizedBox(height: 12),

            // Search Term Display
            _buildSearchTermDisplay(),

            // Selected Vibes
            if (_selectedVibes.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildVibesSection(),
            ],
          ],
        ),
      ),
    );
  }

  // Header with search icon and title
  Widget _buildSearchHeader() {
    return Row(
      children: [
        const Icon(Icons.search, color: Colors.white, size: 20),
        const SizedBox(width: 8),
        Text(
          "Search Inspiration",
          style: AppStyle.bold16V2(color: Colors.white),
        ),
      ],
    );
  }

  // Container displaying the search term
  Widget _buildSearchTermDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(51),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              _searchTerm,
              style: AppStyle.bold18V2(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  // Section displaying selected vibes as chips
  Widget _buildVibesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.trip_planner_destination_vibes.tr(),
          style: AppStyle.regular14V2(color: Colors.white.withAlpha(230)),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _selectedVibes.map((vibe) => _buildVibeChip(vibe)).toList(),
        ),
      ],
    );
  }

  // Individual vibe chip
  Widget _buildVibeChip(String vibe) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(77),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        vibe,
        style: AppStyle.regular14V2(color: Colors.white),
      ),
    );
  }

  /// Widget to display when destinations are loading
  Widget _buildLoadingDestinationOptions() {
    return Column(
      children: List.generate(
        3,
        (index) => Card(
          margin: const EdgeInsets.only(bottom: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 120,
                        height: 16,
                        color: Colors.grey[200],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        height: 12,
                        color: Colors.grey[200],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Error dialog removed as per requirement

  /// Widget to display when there's an error loading destinations
  Widget _buildErrorDestinationOptions(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.info_outline, // Changed from error_outline to info_outline
            color: Colors
                .grey, // Changed from red to grey for less alarming appearance
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No destinations available', // Changed text to be less alarming
            style: AppStyle.bold16V2(),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search term or selecting different vibes', // Generic message instead of error
            style: AppStyle.regular14V2(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          // "Try Again" button removed as requested
        ],
      ),
    );
  }

  /// Widget to display when no destinations are found
  Widget _buildEmptyDestinationOptions() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.info_outline, // Changed to match error state
            color: Colors.grey,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No destinations available', // Changed to match error state
            style: AppStyle.bold16V2(),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search term or selecting different vibes', // Updated text
            style: AppStyle.regular14V2(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationOption(DestinationOption option) {
    // Check if this specific destination is selected using proper object comparison
    final isSelected =
        _selectedDestination != null && _selectedDestination!.isEqualTo(option);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 2 : 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? appTheme.primaryColorV2 : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          // Set only this destination as selected
          _handleDestinationSelection(option);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? appTheme.primaryColorV2.withAlpha(26)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  option.getIcon(),
                  color:
                      isSelected ? appTheme.primaryColorV2 : Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      option.name,
                      style: AppStyle.bold16V2(
                        color:
                            isSelected ? appTheme.primaryColorV2 : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      option.description,
                      style: AppStyle.regular14V2(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: appTheme.primaryColorV2,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Navigate to the trip preview screen
  void _showTripPreview(BuildContext context, VTPDiscoveryState state) {
 

    Trip trip = state.aiTrip!;

    AppLogger.d("Navigating to trip preview screen: \n"
        "Trip flight pref: ${trip.flightPreferences}");

    // Reset the aiTrip in the state to prevent multiple navigations

    _discoveryCubit?.resetTripPlan();

    // Convert Trip to ActivityModel for TripDetailParameter
    final activityModel = ActivityModel(
      uuid: trip.uuid ?? '',
      name: trip.name,
      description: trip.description,
      isDateConfirmed: trip.isDateConfirmed ? 'true' : 'false',
      country: trip.country,
      city: trip.city,
      fromDate: trip.fromDate.toIso8601String(),
      toDate: trip.toDate.toIso8601String(),
      color: trip.color,
      itinerary: trip.itinerary,
      familyId: accountService.familyId,
      activityType: 'trip',
      hotelPreferences: trip.hotelPreferences,
      flightPreferences: trip.flightPreferences,
      cityCode: trip.cityCode,
      additionalCities: trip.additionalCities,
      additionalCityCode: trip.additionalCityCode,
      gpsCoord: (trip.gpsCoord != null && trip.gpsCoord!.length == 2)
          ? LatLng(trip.gpsCoord![0], trip.gpsCoord![1])
          : null,
      imagePath: trip.tripImageUrl,
      hotelBookings: trip.selectedHotels,
    );

    logd("don't attach aiConntroller ");

    final parameter = TripDetailParameter(
      activityId: activityModel.uuid,
      activity: activityModel,
      // aiConnectionController: _discoveryCubit?.aiController,
    );

    // Use push to add trip detail screen to stack - user can pop() back to VTP
    context.router.push(TripDetailRoute(parameter: parameter));
    //XXX: press back will not be allowed.
  }
}
