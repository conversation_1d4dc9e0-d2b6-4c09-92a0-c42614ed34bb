import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_state.dart';
import 'package:family_app/utils/log/app_logger.dart';

class VisualTripPlannerCubit extends BaseCubit<VisualTripPlannerState> {
  final VisualTripPlannerParameter parameter;
  final AccountService accountService = locator.get();

  VisualTripPlannerCubit({
    required this.parameter,
  }) : super(VisualTripPlannerState());

  @override
  Future<void> onInit() async {
    super.onInit();
    try {
      emit(state.copyWith(isLoading: true));
      // Initialize with data from parameter
      if (parameter.initialData != null) {
        // Extract and enhance aiTripIntent data
        final enhancedData = _extractAiTripIntentData(parameter.initialData!);
        emit(state.copyWith(
          initialData: enhancedData,
        ));
      }
      emit(state.copyWith(isLoading: false));
    } catch (e, stackTrace) {
      AppLogger.e('Error initializing VTP cubit: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to initialize trip planner',
      ));
    }
  }

  /* {
flutter: │   "trip_intent": "Discovery",
flutter: │   "travelers": {
flutter: │     "adults": 4,
flutter: │     "children": 2
flutter: │   },
flutter: │   "origin": "Hong Kong",
flutter: │   "destinations": ["Osaka", "Kobe"],
flutter: │   "start_date": "2025-08-12T00:00:00.000Z",
flutter: │   "end_date": "2025-08-19T00:00:00.000Z",
flutter: │   "flight_pref": ["CX"],
flutter: │   "interests": ["hotel", "restaurants", "sightseeing"]
flutter: │ }*/

  /// Extract and process aiTripIntent data to enhance the initial data
  Map<String, dynamic> _extractAiTripIntentData(
      Map<String, dynamic> initialData) {
    final enhancedData = Map<String, dynamic>.from(initialData);

    // Extract fields from the new AI response structure
    final tripIntent = initialData['trip_intent'] as String?;
    final travelers = initialData['travelers'] as Map<String, dynamic>?;
    final origin = initialData['origin'] as String?;
    final destinations = initialData['destinations'] as List<dynamic>?;
    final startDate = initialData['start_date'] as String?;
    final endDate = initialData['end_date'] as String?;
    final flightPref = initialData['flight_pref'] as List<dynamic>?;
    final interests = initialData['interests'] as List<dynamic>?;

    AppLogger.d('Extracting aiTripIntent data:');
    AppLogger.d('  - trip_intent: $tripIntent');
    AppLogger.d('  - travelers: $travelers');
    AppLogger.d('  - origin: $origin');
    AppLogger.d('  - destinations: $destinations');
    AppLogger.d('  - start_date: $startDate');
    AppLogger.d('  - end_date: $endDate');
    AppLogger.d('  - flight_pref: $flightPref');
    AppLogger.d('  - interests: $interests');

    // Add extracted data to enhanced data for use in flows
    if (tripIntent != null) {
      enhancedData['extracted_trip_intent'] = tripIntent;
    }
    if (travelers != null) {
      enhancedData['extracted_travelers'] = travelers;
      enhancedData['num_adults'] = travelers['adults'] ?? 0;
      enhancedData['num_children'] = travelers['children'] ?? 0;
      enhancedData['total_travelers'] =
          (travelers['adults'] ?? 0) + (travelers['children'] ?? 0);
    }
    if (origin != null) {
      enhancedData['extracted_origin'] = origin;
    }
    if (destinations != null && destinations.isNotEmpty) {
      enhancedData['extracted_destinations'] = destinations;
      enhancedData['main_destination'] = destinations.first;
    }
    if (startDate != null) {
      enhancedData['extracted_start_date'] = startDate;
      final parsedStart = _parseTime(startDate);
      if (parsedStart != null) {
        enhancedData['parsed_start_date'] = parsedStart;
      }
    }
    if (endDate != null) {
      enhancedData['extracted_end_date'] = endDate;
      final parsedEnd = _parseTime(endDate);
      if (parsedEnd != null) {
        enhancedData['parsed_end_date'] = parsedEnd;
      }
    }
    if (startDate != null && endDate != null) {
      final parsedStart = _parseTime(startDate);
      final parsedEnd = _parseTime(endDate);
      if (parsedStart != null && parsedEnd != null) {
        final numDays = parsedEnd.difference(parsedStart).inDays;
        enhancedData['trip_num_days'] = numDays;
      }
    }
    if (flightPref != null) {
      enhancedData['extracted_flight_pref'] = flightPref;
    }
    if (interests != null) {
      enhancedData['extracted_interests'] = interests;
    }

    return enhancedData;
  }

  /// Parse duration string (e.g., "5 days", "3 nights") to extract number
  Map<String, dynamic> _parseDuration(String duration) {
    final result = <String, dynamic>{};

    // Extract numbers from duration string
    final regex = RegExp(r'(\d+)\s*(day|night|week)s?', caseSensitive: false);
    final match = regex.firstMatch(duration.toLowerCase());

    if (match != null) {
      final number = int.tryParse(match.group(1) ?? '');
      final unit = match.group(2)?.toLowerCase();

      if (number != null && unit != null) {
        result['number'] = number;
        result['unit'] = unit;

        // Convert to days for consistency
        int days = number;
        if (unit == 'week') {
          days = number * 7;
        } else if (unit == 'night') {
          days = number + 1; // nights + 1 = days
        }
        result['days'] = days;
      }
    }

    AppLogger.d('Parsed duration "$duration" to: $result');
    return result;
  }

  /// Parse time string (ISO format) to DateTime
  DateTime? _parseTime(String time) {
    try {
      final dateTime = DateTime.parse(time);
      AppLogger.d('Parsed time "$time" to: $dateTime');
      return dateTime;
    } catch (e) {
      AppLogger.e('Failed to parse time "$time": $e');
      return null;
    }
  }

  void handleDiscoveryFlowComplete(Map<String, dynamic> data) {
    try {
      emit(state.copyWith(isLoading: true));

      // Handle the next screen based on the data
      if (data['nextScreen'] == 'destination_focus') {
        // Navigate to destination focus screen
        final context = navigatorKey.currentContext;
        if (context != null) {
          context.router.push(
            VisualTripPlannerRoute(
              parameter: VisualTripPlannerParameter(
                initialData: data,
              ),
            ),
          );
        }
      } else if (data['nextScreen'] == 'logistics') {
        // Navigate to logistics screen
        final context = navigatorKey.currentContext;
        if (context != null) {
          context.router.push(
            VisualTripPlannerRoute(
              parameter: VisualTripPlannerParameter(
                initialData: data,
              ),
            ),
          );
        }
      } else if (data['nextScreen'] == 'complete') {
        // Create trip plan with all collected data
        _createTripPlan(data);
      }

      emit(state.copyWith(isLoading: false));
    } catch (e, stackTrace) {
      AppLogger.e('Error handling discovery flow completion: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to process trip preferences',
      ));
    }
  }

  void handleDirectFlowComplete(Map<String, dynamic> data) {
    try {
      emit(state.copyWith(isLoading: true));

      // Create trip plan with the confirmed details
      _createTripPlan(data);

      emit(state.copyWith(isLoading: false));
    } catch (e, stackTrace) {
      AppLogger.e('Error handling direct flow completion: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to create trip plan',
      ));
    }
  }

  Future<void> _createTripPlan(Map<String, dynamic> data) async {
    try {
      // TODO: Implement trip plan creation logic
      AppLogger.d('Creating trip plan with data: $data');

      final cityName = data['destination'];

      // Create a Trip object from the data
      final trip = Trip(
          name: data['destination'] ?? 'New Trip',
          isDateConfirmed: true,
          fromDate: DateTime.parse(data['startDate']),
          toDate: DateTime.parse(data['endDate']),
          country: data['country'],
          city: data['destination'],
          color: null,
          description: data['description'],
          // includedEvents: [],
          itinerary: [],
          familyId: accountService.familyId,
          uuid: null,
          cityCode: data['city_code']);

      // Navigate to trip plan details or loading screen
      final context = navigatorKey.currentContext;
      if (context != null) {
        context.router.push(
          TripRoute(
            parameter: TripParameter(trip: trip),
          ),
        );
      }
    } catch (e, stackTrace) {
      AppLogger.e('Error creating trip plan: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to create trip plan',
      ));
    }
  }

  void addTripPlan(dynamic plan) {
    final updatedPlans = List<dynamic>.from(state.tripPlans)..add(plan);
    emit(state.copyWith(tripPlans: updatedPlans));
  }

  void removeTripPlan(int index) {
    final updatedPlans = List<dynamic>.from(state.tripPlans)..removeAt(index);
    emit(state.copyWith(tripPlans: updatedPlans));
  }

  void updateTripPlan(int index, dynamic plan) {
    final updatedPlans = List<dynamic>.from(state.tripPlans);
    updatedPlans[index] = plan;
    emit(state.copyWith(tripPlans: updatedPlans));
  }
}
