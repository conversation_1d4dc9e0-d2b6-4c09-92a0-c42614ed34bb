import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_cubit.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_state.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_entry_discovery_screen.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_destination_focus_screen.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_logistics_madlibs_screen.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/direct/vtp_entry_direct_screen.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:flutter/material.dart';

@RoutePage()
class VisualTripPlannerPage
    extends BaseBlocProvider<VisualTripPlannerState, VisualTripPlannerCubit> {
  const VisualTripPlannerPage({required this.parameter, super.key});

  final VisualTripPlannerParameter parameter;

  @override
  Widget buildPage() => VisualTripPlannerView(parameter: parameter);

  @override
  VisualTripPlannerCubit createCubit() => VisualTripPlannerCubit(
        parameter: parameter,
      );
}

class VisualTripPlannerView extends StatefulWidget {
  const VisualTripPlannerView({required this.parameter, super.key});

  final VisualTripPlannerParameter parameter;

  @override
  State<VisualTripPlannerView> createState() => _VisualTripPlannerViewState();
}

class _VisualTripPlannerViewState extends BaseBlocPageState<
    VisualTripPlannerView, VisualTripPlannerState, VisualTripPlannerCubit> {
  @override
  String get title => LocaleKeys.trip_planner_title.tr();

  @override
  PreferredSizeWidget buildAppBar(BuildContext context,
      VisualTripPlannerCubit cubit, VisualTripPlannerState state) {
    return CustomAppBar2(
      title: title,
      showBack: true,
    );
  }

  @override
  Widget buildBody(BuildContext context, VisualTripPlannerCubit cubit,
      VisualTripPlannerState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final initialData = state.initialData;
    if (initialData != null) {
      final tripIntent = initialData['trip_intent'] as String?;
      final nextScreen = initialData['nextScreen'] as String?;

      if (tripIntent == 'Discovery') {
        if (nextScreen == 'destination_focus') {
          return VTPDestinationFocusScreen(
            initialData: initialData,
            onComplete: (data) {
              cubit.handleDiscoveryFlowComplete(data);
            },
          );
        } else {
          return VTPEntryDiscoveryScreen(
            initialData: initialData,
            onComplete: (data) {
              cubit.handleDiscoveryFlowComplete(data);
            },
          );
        }
      } else if (tripIntent == 'Direct') {
        return VTPEntryDirectScreen(
          initialData: initialData,
          onComplete: (data) {
            cubit.handleDirectFlowComplete(data);
          },
        );
      }
    }

    // Default fallback
    return Center(
      child: Text(
        LocaleKeys.trip_planner_error.tr(),
        style: AppStyle.regular16V2(),
      ),
    );
  }
}
