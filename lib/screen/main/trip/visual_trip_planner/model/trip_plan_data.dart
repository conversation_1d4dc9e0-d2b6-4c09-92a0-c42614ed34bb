import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'trip_plan_data.g.dart';

@JsonSerializable()
class TripPlanData {
  final String tripIntent;
  final Map<String, dynamic> data;

  TripPlanData({
    required this.tripIntent,
    required this.data,
  });

  factory TripPlanData.fromJson(Map<String, dynamic> json) =>
      _$TripPlanDataFromJson(json);
  Map<String, dynamic> toJson() => _$TripPlanDataToJson(this);

  String toJsonString() => jsonEncode(toJson());
}

// Discovery path data model
@JsonSerializable()
class DiscoveryTripData {
  final String? searchTerm;
  final List<String> selectedVibes;
  final String? destination;
  final String? origin;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? travelParty;
  final String? budget;
  final String? accommodationType;
  final String? transportationType;
  // final String? activityStyle;
  final List<String>? activityIdeas;
  final int totalTravellers; // Int as String
  final int numAdults;
  final int numChildren;
  final List<String>? airlinePref; // airline prefrences airline codes.

  DiscoveryTripData(
      {this.searchTerm,
      this.selectedVibes = const [],
      this.destination,
      this.startDate,
      this.endDate,
      this.travelParty,
      this.budget,
      this.accommodationType,
      this.transportationType,
      this.activityIdeas,
      this.totalTravellers = 0,
      this.numAdults = 0,
      this.numChildren = 0,
      this.origin,
      this.airlinePref});

  factory DiscoveryTripData.fromJson(Map<String, dynamic> json) =>
      _$DiscoveryTripDataFromJson(json);
  Map<String, dynamic> toJson() => _$DiscoveryTripDataToJson(this);
}

// Direct path data model
@JsonSerializable()
class DirectTripData {
  final String destination;
  final DateTime startDate;
  final DateTime endDate;
  final String travelParty;
  final String purpose;
  final String relativeArea;
  final bool needsAccommodation;
  final bool needsFlights;

  DirectTripData({
    required this.destination,
    required this.startDate,
    required this.endDate,
    required this.travelParty,
    required this.purpose,
    required this.relativeArea,
    required this.needsAccommodation,
    required this.needsFlights,
  });

  factory DirectTripData.fromJson(Map<String, dynamic> json) =>
      _$DirectTripDataFromJson(json);
  Map<String, dynamic> toJson() => _$DirectTripDataToJson(this);
}
