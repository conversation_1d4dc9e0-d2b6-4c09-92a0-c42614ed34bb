import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/constants/vtp_constants.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_cubit.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_state.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_trip_plan_loading_indicator.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/model/trip_plan_data.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class VTPEntryDirectScreen extends StatefulWidget {
  const VTPEntryDirectScreen({
    required this.initialData,
    required this.onComplete,
    super.key,
  });

  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onComplete;

  @override
  State<VTPEntryDirectScreen> createState() => _VTPEntryDirectScreenState();
}

class _VTPEntryDirectScreenState extends State<VTPEntryDirectScreen> {
  late String _destination;
  late DateTime _startDate;
  late DateTime _endDate;
  late String _travelParty;
  late String _purpose;
  String? _relativeArea;
  late bool _needAccommodation;
  late bool _needFlights;
  final _formKey = GlobalKey<FormState>();

  // Discovery cubit for WebSocket communication
  VTPDiscoveryCubit? _discoveryCubit;
  bool _isInitializing = true;
  final LocalStorage _localStorage = GetIt.instance.get<LocalStorage>();

  @override
  void initState() {
    super.initState();
    _initializeFromAiTripIntent();

    // Initialize the discovery cubit
    _initializeDiscoveryCubit();
  }

  /// Initialize form fields from aiTripIntent data
  void _initializeFromAiTripIntent() {
    // Initialize with data from aiTripIntent or defaults
    _destination = widget.initialData['extracted_destination'] ??
        widget.initialData['destination'] ??
        '';

    // Use parsed start date from aiTripIntent if available
    final parsedStartDate =
        widget.initialData['parsed_start_date'] as DateTime?;
    if (parsedStartDate != null) {
      _startDate = parsedStartDate;
      AppLogger.d('Using start date from aiTripIntent: $_startDate');
    } else if (widget.initialData['startDate'] != null) {
      _startDate = DateTime.parse(widget.initialData['startDate']);
    } else {
      _startDate = DateTime.now();
    }

    // Use parsed duration from aiTripIntent if available
    final parsedDuration =
        widget.initialData['parsed_duration'] as Map<String, dynamic>?;
    if (parsedDuration != null && parsedDuration['days'] != null) {
      final days = parsedDuration['days'] as int;
      _endDate = _startDate.add(Duration(days: days));
      AppLogger.d(
          'Using duration from aiTripIntent: $days days, end date: $_endDate');
    } else if (widget.initialData['endDate'] != null) {
      _endDate = DateTime.parse(widget.initialData['endDate']);
    } else {
      _endDate = _startDate.add(const Duration(days: 7));
    }

    _travelParty = widget.initialData['travelParty'] ?? 'Just Me';
    _purpose = widget.initialData['purpose'] ?? 'General Visit';
    _relativeArea = widget.initialData['relativeArea'];
    _needAccommodation = widget.initialData['needAccommodation'] ?? true;
    _needFlights = widget.initialData['needFlights'] ?? true;

    AppLogger.d('Initialized Direct flow with aiTripIntent data:');
    AppLogger.d('  - destination: $_destination');
    AppLogger.d('  - startDate: $_startDate');
    AppLogger.d('  - endDate: $_endDate');
    AppLogger.d('  - duration: ${_endDate.difference(_startDate).inDays} days');
  }

  /// Initialize the discovery cubit with the token from localStorage
  Future<void> _initializeDiscoveryCubit() async {
    setState(() {
      _isInitializing = true;
    });

    final token = await _localStorage.accessToken() ?? '';

    // Create DirectTripData from the current form values
    final directData = DirectTripData(
      destination: _destination,
      startDate: _startDate,
      endDate: _endDate,
      travelParty: _travelParty,
      purpose: _purpose,
      relativeArea: _relativeArea ?? '',
      needsAccommodation: _needAccommodation,
      needsFlights: _needFlights,
    );

    _discoveryCubit = VTPDiscoveryCubit(
      parameter: VTPDiscoveryParameter(
        token: token,
        familyId: '', // Family ID is not needed for this implementation
        flowType: VTPFlowType.direct, // Specify that this is the direct flow
        directTripData: directData,
      ),
    );

    // Initialize the cubit
    await _discoveryCubit!.onInit();

    if (mounted) {
      setState(() {
        _isInitializing = false;
      });
    }
  }

  @override
  void dispose() {
    _discoveryCubit?.close();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  void _showTravelPartySelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Just Me'),
            onTap: () {
              setState(() => _travelParty = 'Just Me');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Couple'),
            onTap: () {
              setState(() => _travelParty = 'Couple');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Family'),
            onTap: () {
              setState(() => _travelParty = 'Family');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Group'),
            onTap: () {
              setState(() => _travelParty = 'Group');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showPurposeSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Visiting Family/Friends'),
            onTap: () {
              setState(() => _purpose = 'Visiting Family/Friends');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Business'),
            onTap: () {
              setState(() => _purpose = 'Business');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Event'),
            onTap: () {
              setState(() => _purpose = 'Event');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Tourism'),
            onTap: () {
              setState(() => _purpose = 'Tourism');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Other'),
            onTap: () {
              setState(() => _purpose = 'Other');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _handleGetMyPlan() {
    if (_formKey.currentState?.validate() ?? false) {
      _formKey.currentState?.save();

      final directData = DirectTripData(
        destination: _destination,
        startDate: _startDate,
        endDate: _endDate,
        travelParty: _travelParty,
        purpose: _purpose,
        relativeArea: _relativeArea ?? '',
        needsAccommodation: _needAccommodation,
        needsFlights: _needFlights,
      );

      // Send to AI chat using the VTP discovery cubit
      if (_discoveryCubit != null) {
        // Use the planDirectTrip method directly
        _discoveryCubit!.planDirectTrip(directData);

        // DO NOT close the bottom sheet - stay on this screen to show the loading indicator
        // The VTP loading indicator overlay will be shown automatically since we're updating
        // the isTripPlanLoading state in the cubit
        // The trip preview will be shown when the AI response is received
      } else {
        AppLogger.e("VTP Discovery Cubit is null, cannot send trip plan to AI");
        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error: Unable to connect to AI service'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Navigate to the trip preview screen
  void _showTripPreview(BuildContext context, Trip trip) {
    AppLogger.d("Navigating to trip preview screen from direct flow");

    // Reset the aiTrip in the state to prevent multiple navigations
    _discoveryCubit?.resetTripPlan();

    // Create a TripParameter with the trip
    final parameter = TripParameter(trip: trip);

    // Use push to add trip screen to stack - user can pop() back to VTP
    context.router.push(TripRoute(parameter: parameter));
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while initializing
    if (_isInitializing || _discoveryCubit == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Initializing trip planner...',
                style: AppStyle.regular16V2(),
              ),
            ],
          ),
        ),
      );
    }

    return BlocProvider<VTPDiscoveryCubit>.value(
      value: _discoveryCubit!,
      child: Stack(
        children: [
          BlocConsumer<VTPDiscoveryCubit, VTPDiscoveryState>(
            listenWhen: (previous, current) =>
                previous.aiTrip == null && current.aiTrip != null,
            listener: (context, state) {
              // If we have a trip plan, navigate to the trip preview screen
              if (state.aiTrip != null) {
                _showTripPreview(context, state.aiTrip!);
              }
            },
            builder: (context, state) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.trip_planner_direct_title.tr(),
                        style: AppStyle.bold24V2(),
                      ),
                      const SizedBox(height: 24),

                      // Destination
                      _buildEditableField(
                        title: LocaleKeys.trip_planner_direct_destination.tr(),
                        value: _destination,
                        onEdit: () {
                          // TODO: Implement destination editing
                        },
                      ),

                      // Dates
                      _buildEditableField(
                        title: LocaleKeys.trip_planner_direct_dates.tr(),
                        value:
                            '${DateFormat('MMM d').format(_startDate)} - ${DateFormat('MMM d, y').format(_endDate)}',
                        onEdit: () => _selectDate(context, true),
                      ),

                      // Travel Party
                      _buildEditableField(
                        title: LocaleKeys.trip_planner_direct_travelers.tr(),
                        value: _travelParty,
                        onEdit: _showTravelPartySelector,
                      ),

                      // Purpose
                      _buildEditableField(
                        title: LocaleKeys.trip_planner_direct_purpose.tr(),
                        value: _purpose,
                        onEdit: _showPurposeSelector,
                      ),

                      // Relative Area (only shown if purpose is Visiting Family/Friends)
                      if (_purpose == 'Visiting Family/Friends')
                        Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                LocaleKeys.trip_planner_direct_relative_area
                                    .tr(),
                                style: AppStyle.regular14V2(
                                    color: Colors.grey[600]),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                decoration: InputDecoration(
                                  hintText: LocaleKeys
                                      .trip_planner_direct_relative_area_hint
                                      .tr(),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                onChanged: (value) => _relativeArea = value,
                              ),
                            ],
                          ),
                        ),

                      const SizedBox(height: 24),

                      // Accommodation and Flights
                      Row(
                        children: [
                          Expanded(
                            child: _buildToggleField(
                              title: LocaleKeys
                                  .trip_planner_direct_accommodation
                                  .tr(),
                              value: _needAccommodation,
                              onChanged: (value) =>
                                  setState(() => _needAccommodation = value),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildToggleField(
                              title:
                                  LocaleKeys.trip_planner_direct_flights.tr(),
                              value: _needFlights,
                              onChanged: (value) =>
                                  setState(() => _needFlights = value),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 32),

                      // Get My Plan Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _handleGetMyPlan,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: const Color(
                                0xFF4E46B4), // Purple color for consistency
                            foregroundColor: Colors.white,
                            elevation:
                                4, // Increased elevation for better visibility
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                LocaleKeys.trip_planner_get_my_plan.tr(),
                                style: AppStyle.bold18V2(
                                    color: Colors.white), // Larger text
                              ),
                              const SizedBox(width: 8),
                              const Icon(Icons.arrow_forward,
                                  color: Colors.white), // Added icon
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          // Add the loading indicator overlay for trip plan loading
          const VTPTripPlanLoadingIndicator(),
        ],
      ),
    );
  }

  Widget _buildEditableField({
    required String title,
    required String value,
    required VoidCallback onEdit,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppStyle.regular14V2(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: onEdit,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: AppStyle.regular16V2(),
                    ),
                  ),
                  const Icon(Icons.edit, size: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleField({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppStyle.regular14V2(color: Colors.grey[600]),
        ),
        const SizedBox(height: 8),
        SwitchListTile(
          value: value,
          onChanged: onChanged,
          contentPadding: EdgeInsets.zero,
          title: Text(
            value ? LocaleKeys.yes.tr() : LocaleKeys.no.tr(),
            style: AppStyle.regular16V2(),
          ),
        ),
      ],
    );
  }
}
