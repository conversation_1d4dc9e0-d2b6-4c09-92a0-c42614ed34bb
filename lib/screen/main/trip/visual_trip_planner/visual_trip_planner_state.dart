import 'package:family_app/base/widget/cubit/base_state.dart';

class VisualTripPlannerState extends BaseState {
  final bool isLoading;
  final String? error;
  final List<dynamic> tripPlans;
  final Map<String, dynamic>? initialData;

  VisualTripPlannerState({
    this.isLoading = false,
    this.error,
    this.tripPlans = const [],
    this.initialData,
  });

  VisualTripPlannerState copyWith({
    bool? isLoading,
    String? error,
    List<dynamic>? tripPlans,
    Map<String, dynamic>? initialData,
  }) {
    return VisualTripPlannerState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      tripPlans: tripPlans ?? this.tripPlans,
      initialData: initialData ?? this.initialData,
    );
  }

  @override
  List<Object?> get props => [isLoading, error, tripPlans, initialData];
}
