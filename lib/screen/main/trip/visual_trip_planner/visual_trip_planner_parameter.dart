import 'package:family_app/screen/main/chat/chat_cubit.dart';

class VisualTripPlannerParameter {
  final String? tripId;
  final Map<String, dynamic>? initialData;

  const VisualTripPlannerParameter({
    this.tripId,
    this.initialData,
  });

  VisualTripPlannerParameter copyWith({
    String? tripId,
    Map<String, dynamic>? initialData,
    ChatCubit? chatCubit,
  }) {
    return VisualTripPlannerParameter(
      tripId: tripId ?? this.tripId,
      initialData: initialData ?? this.initialData,
    );
  }

  List<Object?> get props => [tripId, initialData];
}
