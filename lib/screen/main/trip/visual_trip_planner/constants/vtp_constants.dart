/// Enum for VTP flow types
enum VTPFlowType {
  discovery,
  direct,
}

/// Constants for WebSocket commands
class WebSocketCommands {
  static const String aiChat = 'ai_chat';
  static const String aiChatRespond = 'ai_chat_repond';
}

/// Constants for AI prompts
class AIPrompts {
  static const String vtpDiscover = 'vtp_discover';
  static const String vtpPlan = 'vtp_plan';
  static const String vtpEdit = 'vtp_edit';
  static const String vtpRefine = 'vtp_refine'; // Added for 2nd round refinement
}

/// Constants for auto-create values
class AutoCreateValues {
  static const String falseValue = 'false';
}

/// Constants for trip intent values
class TripIntentValues {
  static const String discovery = 'Discovery';
  static const String direct = 'Direct';
}
