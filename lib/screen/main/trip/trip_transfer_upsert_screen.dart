import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx_io.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/image_cache_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/file_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_cubit.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/custom_easy_image_viewer/easy_image_viewer.dart';
import 'package:family_app/widget/pdf_viewer_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class TripTransferUpsertPage extends BaseBlocProvider<TripTransferUpsertState, TripTransferUpsertCubit> {
  const TripTransferUpsertPage({required this.parameter, super.key});
  final TripTransferUpsertParameter parameter;

  @override
  Widget buildPage() => TripTransferUpsertView(parameter: parameter);

  @override
  TripTransferUpsertCubit createCubit() =>
      TripTransferUpsertCubit(activityRepository: locator.get(), parameter: parameter);
}

class TripTransferUpsertView extends StatefulWidget {
  const TripTransferUpsertView({super.key, required this.parameter});
  final TripTransferUpsertParameter parameter;

  @override
  State<TripTransferUpsertView> createState() => _TripTransferUpsertViewState();
}

class _TripTransferUpsertViewState
    extends BaseBlocPageState<TripTransferUpsertView, TripTransferUpsertState, TripTransferUpsertCubit> {

  @override
  bool? get isBottomSafeArea => false;

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool listenWhen(TripTransferUpsertState previous, TripTransferUpsertState current) {
    if (current.status == TripTransferUpsertStatus.loading) {
      showLoading();
    } else if (current.status == TripTransferUpsertStatus.success) {
      dismissLoading();
      AppLogger.d('Trip transfer upsert success: ${current.transferResult}');
      context.router.maybePop(current.transferResult);
    } else if (current.status == TripTransferUpsertStatus.error) {
      dismissLoading();
      // Optionally show error feedback
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return CustomAppBar2(
      title: widget.parameter.addNew ? 'Add Transfer'.tr() : 'Edit Transfer'.tr(),
      showBack: true,
      actions: [
        GestureDetector(
          onTap: () {
            AppLogger.d('Save activity pressed');
            cubit.saveActivity();
          },
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: appTheme.backgroundV2,
            padding: padding(all: 7),
            child: Assets.icons.icActionCheck.svg(),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    if (state.status == TripTransferUpsertStatus.loading) {
      return const Center(child: CircularProgressIndicator());
    }
    // Find the index of the selected transport for initialIndex
    int initialTabIndex = 0;
    if (state.selectedTransport != null) {
      final idx = state.transports.indexWhere((t) => t.type == state.selectedTransport!.type);
      if (idx != -1) {
        initialTabIndex = idx;
      }
    }
    return DefaultTabController(
      length: state.transports.length, // Number of tabs
      initialIndex: initialTabIndex,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 50,
            child: _buildTabHeader(context, cubit, state),
          ),
          Expanded(
            child: _buildContent(context, cubit, state),
          ),
        ],
      ),
    );
  }

  Widget _buildTabHeader(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return TabBar(
      onTap: (value) {
        final transport = state.transports[value];
        // AppLogger.d('Transport selected: ${transport.name}');
        cubit.updateSelectedTransport(transport);
      },
      padding: const EdgeInsets.all(8),
      isScrollable: true, // Make tabs scrollable if needed
      labelColor: Colors.white, // Active tab text color
      unselectedLabelColor: Colors.grey, // Inactive tab text color
      indicator: BoxDecoration(
        // Customize the indicator
        color: const Color(0xFF4E46B4), // Indicator background color
        borderRadius: BorderRadius.circular(20), // Rounded corners
      ),
      splashBorderRadius: BorderRadius.circular(20), // Rounded corners for pressed effect
      labelPadding: const EdgeInsets.symmetric(horizontal: 16), // Adjust padding
      tabs: state.transports.map((transport) {
        return _buildTabHeaderItemView(context, cubit, state, transport);
      }).toList(),
      // Ensure minimum height
      dividerColor: Colors.transparent,
      indicatorSize: TabBarIndicatorSize.tab,
      indicatorPadding: EdgeInsets.zero,
      indicatorWeight: 2.0,
      tabAlignment: TabAlignment.start,
    );
  }

  Widget _buildTabHeaderItemView(
      BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state, Transport transport) {
    // AppLogger.d('Transport: $transport , selectedTransport: ${state.selectedTransport}');
    final isSelected = transport.type == state.selectedTransport?.type;
    return Tab(
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              transport.icon,
              color: isSelected ? Colors.white : const Color(0xFF595D62), // Set color to white if selected
            ), // Display transport icon
            const SizedBox(width: 8), // Add space between icon and text
            Text(transport.name,
                style: TextStyle(color: isSelected ? Colors.white : const Color(0xFF595D62))), // Display transport name
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return SingleChildScrollView(
        child: Card(
            margin: const EdgeInsets.only(left: 8, right: 8, top: 0, bottom: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.0),
            ),
            color: Colors.white,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTitle(context, cubit, state),
                  _buildTicketNumber(context, cubit, state),
                  const SizedBox(height: 16),
                  _buildDepartureArrival(context, cubit, state, true),
                  const SizedBox(height: 16),
                  _buildDepartureArrival(context, cubit, state, false),
                  _buildDivider(),
                  _buildTicketPriceQuantityView(context, cubit, state),
                  const SizedBox(height: 8),
                  _buildNote(context, cubit, state),
                  const SizedBox(height: 8),
                  _buildFileSection(cubit, state),
                ],
              ),
            )));
  }

  Widget _buildDivider() {
    return const Divider(
      color: Colors.grey,
      thickness: 0.5,
      height: 32,
    );
  }

  Widget _buildTitle(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return _buildText(state.selectedTransport?.title ?? '');
  }

  Widget _buildText(String text) {
    return Padding(
      padding: EdgeInsets.zero,
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTicketNumber(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(state.selectedTransport?.ticketNoTitle ?? ''),
          TextFormField(
            initialValue: state.ticketNo,
            onChanged: (value) {
              cubit.updateTicketNo(value);
            },
            decoration: InputDecoration(
              hintText: 'Enter the ticket number',
              border: OutlineInputBorder(
                borderSide: BorderSide(color: state.isTicketNoValid ? appTheme.borderGreyColor : Colors.red),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: state.isTicketNoValid ? appTheme.borderGreyColor : Colors.red),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartureArrival(
      BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state, bool isDeparture) {
    return Container(
      // Add elevation for shadow effect
      decoration: BoxDecoration(
        border: Border.all(color: appTheme.borderGreyColor),
        borderRadius: BorderRadius.circular(10), // Rounded corners
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0), // Padding inside the card
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isDeparture
                  ? (state.selectedTransport?.fromLocationTitle ?? '')
                  : (state.selectedTransport?.toLocationTitle ?? ''),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
            ),
            const SizedBox(height: 4),
            _buildAirport(context, cubit, state, isDeparture),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildFlightDate(context, cubit, state, isDeparture),
                const SizedBox(width: 8),
                _buildFlightTime(context, cubit, state, isDeparture),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAirport(
      BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state, bool isDeparture) {
    final location = isDeparture ? (state.departureLocation ?? '') : (state.arrivalLocation ?? '');
    final isValid = isDeparture ? state.isDepartureLocationValid : state.isArrivalLocationValid;
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(color: isValid ? appTheme.borderGreyColor : Colors.red),
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              initialValue: location,
              onChanged: (value) {
                if (isDeparture) {
                  cubit.updateDepartureLocation(value);
                } else {
                  cubit.updateArrivalLocation(value);
                }
              },
              decoration: const InputDecoration(
                hintText: 'Enter the location',
                border: InputBorder.none,
              ),
            ),
          ),
          const SizedBox(width: 8),
          SvgPicture.asset(Assets.icons.icMap.path),
        ],
      ),
    );
  }

  Widget _buildFlightDate(
      BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state, bool isDeparture) {
    final dateTime = isDeparture ? state.departureDate : state.arrivalDate;
    final isValid = isDeparture ? state.isDepartureDateValid : state.isArrivalDateValid;
    return Expanded(
      flex: 60,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isDeparture ? (state.selectedTransport?.fromDateTitle ?? '') : (state.selectedTransport?.toDateTitle ?? ''),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
          ),
          const SizedBox(height: 4),
          InkWell(
            onTap: () => cubit.selectDate(context, isDeparture),
            child: Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: isValid ? appTheme.borderGreyColor : Colors.red),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    dateTime != null ? DateFormat('dd/MM/yyyy').format(dateTime) : 'Select Date',
                  ),
                  const SizedBox(width: 4),
                  SvgPicture.asset(Assets.icons.icCalendar.path),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlightTime(
      BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state, bool isDeparture) {
    final time = isDeparture ? state.departureTime : state.arrivalTime;
    final isValid = isDeparture ? state.isDepartureTimeValid : state.isArrivalTimeValid;
    return Expanded(
      flex: 40,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Time',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
          ),
          const SizedBox(height: 4),
          InkWell(
              onTap: () => cubit.selectTime(context, isDeparture),
              child: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  border: Border.all(color: isValid ? appTheme.borderGreyColor : Colors.red),
                  borderRadius: BorderRadius.circular(5.0),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(time != null ? _formatTimeOfDay(time) : 'Select Time'),
                    const SizedBox(width: 4),
                    SvgPicture.asset(Assets.icons.icClock.path),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hours = time.hour.toString().padLeft(2, '0'); // Ensures two digits
    final minutes = time.minute.toString().padLeft(2, '0');
    return "$hours:$minutes";
  }

  Widget _buildTicketPriceQuantityView(
      BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return state.selectedTransport?.type != TransportType.drive
        ? Padding(
            padding: EdgeInsets.zero,
            child: Row(
              children: [
                _buildTicketPrice(context, cubit, state),
                const SizedBox(width: 16),
                _buildQuantity(context, cubit, state),
              ],
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildTicketPrice(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return Expanded(
      flex: 70,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Ticket price',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
          ),
          const SizedBox(height: 8),
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              decoration: BoxDecoration(
                border: Border.all(color: appTheme.borderGreyColor),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: state.price.toString(),
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        final price = double.tryParse(value) ?? state.price;
                        cubit.updatePrice(price);
                      },
                      decoration: const InputDecoration(
                        border: InputBorder.none, // Remove underline
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  DropdownButton<String>(
                    value: state.selectedCurrency,
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        cubit.updateSelectedCurrency(newValue);
                      }
                    },
                    items: state.currencies.map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(
                          value,
                          style: TextStyle(color: Color(0xFF4E46B4)),
                        ),
                      );
                    }).toList(),
                    underline: const SizedBox.shrink(), // Remove underline
                    icon: SvgPicture.asset(
                      Assets.icons.icDropdown.path,
                      color: const Color(0xFF4E46B4),
                    ),
                  ),
                ],
              )),
        ],
      ),
    );
  }

  Widget _buildQuantity(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    return Expanded(
      flex: 30,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quantity',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
          ),
          const SizedBox(height: 8),
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              decoration: BoxDecoration(
                border: Border.all(color: appTheme.borderGreyColor),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: TextFormField(
                initialValue: state.quantity.toString(),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final quantity = int.tryParse(value) ?? state.quantity;
                  cubit.updateQuantity(quantity);
                },
                decoration: const InputDecoration(
                  border: InputBorder.none, // Remove underline
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildNote(BuildContext context, TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    // return _buildTextField('Note', 'Enter the note', state.note);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Note'),
          TextFormField(
            initialValue: state.note,
            decoration: InputDecoration(
              hintText: 'Enter the note',
              border: OutlineInputBorder(
                borderSide: BorderSide(color: appTheme.borderGreyColor), // Set grey border
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: appTheme.borderGreyColor), // Set grey border
              ),
            ),
            onChanged: (value) {
              cubit.updateNote(value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFileSection( TripTransferUpsertCubit cubit, TripTransferUpsertState state)  {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Documents',
          style: AppStyle.medium16().copyWith(color: appTheme.blackText),
        ),
        const SizedBox(height: 12),
        _buildDocumentPicker(state, cubit),
      ],
    );
  }

  Widget _buildDocumentPicker(TripTransferUpsertState state, TripTransferUpsertCubit cubit) {
    return Wrap(
      spacing: 8.0, // Space between items
      runSpacing: 8.0, // Space between rows
      children: [
        for (int i = 0; i < state.files.length; i++)
          _buildFileModelItem(cubit, state.files[i], i),
        _buildAddFileButton(cubit),
      ],
    );
  }

  Widget _buildFileModelItem(TripTransferUpsertCubit cubit, FileModel file, int index) {
   
    final fileName = file.fileName;
    final extension = path.extension(fileName).toLowerCase();
    IconData iconData;
    Color iconColor = appTheme.primaryColor;
    if (['.pdf'].contains(extension)) {
      iconData = Icons.picture_as_pdf;
      iconColor = Colors.red;
    } else if (['.doc', '.docx'].contains(extension)) {
      iconData = Icons.description;
      iconColor = Colors.blue;
    } else if (['.xls', '.xlsx'].contains(extension)) {
      iconData = Icons.table_chart;
      iconColor = Colors.green;
    } else if (['.jpg', '.jpeg', '.png'].contains(extension)) {
      iconData = Icons.image;
      iconColor = Colors.purple;
    } else {
      iconData = Icons.insert_drive_file;
    }
    return Stack(
      children: [
        GestureDetector(
          onTap: () => {_onFileTap(file)},
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: appTheme.background,
              borderRadius: BorderRadius.circular(10.0),
              border: Border.all(color: appTheme.borderColor),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  iconData,
                  color: iconColor,
                  size: 32,
                ),
                const SizedBox(height: 4),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Text(
                    fileName,
                    style: AppStyle.regular10(color: appTheme.fadeTextColor),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => cubit.removeFile(index, context),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _onFileTap(FileModel file) async {
      final fileName = file.fileName;
    final extension = path.extension(fileName).toLowerCase();
    final isImage = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(extension);

    // For images, show image viewer
    if (isImage) {
      await _showImageViewer(file);
      return;
    }
      
    // For documents, open in external viewer
    await _openDocument(file);
  }

  Future<void> _showImageViewer(FileModel file) async {
    try {
      String imageUrl;

      if (file.fileType == 'local') {
        // Local file - use file path directly
        imageUrl = file.fileUrl;
      } else {
        // Remote file - get URL from cache service
        final url = await imageCacheService.getImageUrl(file.fileUuid!);
        if (url == null) {
          showSimpleToast('Failed to load image');
          return;
        }
        imageUrl = url;
      }

      // Show image viewer using the existing pattern from your codebase
      showImageViewer(
        context,
        imageUrl.startsWith('http') ? NetworkImage(imageUrl) : FileImage(File(imageUrl)) as ImageProvider,
        doubleTapZoomable: true,
        swipeDismissible: true,
      );
    } catch (e) {
      AppLogger.e('Error showing image viewer: $e');
      showSimpleToast('Failed to open image');
    }
  }

  Future<void> _openDocument(FileModel file) async {
    try {
      String documentUrl;

      if (file.fileType == 'local') {
        documentUrl = file.fileUrl;
      } else {
        final url = await imageCacheService.getImageUrl(file.fileUuid!);
        if (url == null) {
          showSimpleToast('Failed to load document');
          return;
        }
        documentUrl = url;
      }

      final extension = path.extension(file.fileName).toLowerCase();
      if (extension == '.pdf') {
        if (!mounted) return;
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => PdfViewerScreen(pdfUrl: documentUrl),
          ),
        );
        return;
      }

      // For other documents, open in external viewer
      final uri = Uri.parse(documentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        showSimpleToast('Unable to open document');
      }
    } catch (e) {
      AppLogger.e('Error opening document: $e');
      showSimpleToast('Failed to open document');
    }
  }

  Widget _buildAddFileButton( TripTransferUpsertCubit cubit) {
    return InkWell(
      onTap: () =>  cubit.pickFileOrImage(context),
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: appTheme.background,
          borderRadius: BorderRadius.circular(10.0),
          border: Border.all(
            color: appTheme.borderColor,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              color: appTheme.fadeTextColor,
              size: 32,
            ),
            const SizedBox(height: 4),
            Text(
              'Add File',
              style: AppStyle.regular10(color: appTheme.fadeTextColor),
            ),
          ],
        ),
      ),
    );
  }

  void submitTransfer(TripTransferUpsertCubit cubit, TripTransferUpsertState state) {
    // AppLogger.d('Submit transfer');
    // AppLogger.d('Selected transport: ${state.selectedTransport}');
    // AppLogger.d('Ticket no: ${state.ticketNo}');
    // AppLogger.d('Departure location: ${state.departureLocation}');
    // AppLogger.d('Departure date: ${state.departureDate}');
    // AppLogger.d('Departure time: ${state.departureTime}');
    // AppLogger.d('Arrival location: ${state.arrivalLocation}');
    // AppLogger.d('Arrival date: ${state.arrivalDate}');
    // AppLogger.d('Arrival time: ${state.arrivalTime}');
    // AppLogger.d('Selected currency: ${state.selectedCurrency}');
    // AppLogger.d('Quantity: ${state.quantity}');
    // AppLogger.d('Price: ${state.price}');
    // AppLogger.d('Note: ${state.note}');
    // AppLogger.d('Uploaded files: ${state.uploadedFiles}');
  }
}
