import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/trip/place/place_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:family_app/utils/dialog.dart';

import '../../../widget/button_icon.dart';
import 'trip_detail_cubit.dart';
import 'trip_detail_state.dart';
import 'package:family_app/screen/main/chat/chat_screen.dart';
import 'package:family_app/data/model/document_model.dart';

@RoutePage()
class TripDetailPage
    extends BaseBlocProvider<TripDetailState, TripDetailCubit> {
  const TripDetailPage({required this.parameter, super.key});

  final TripDetailParameter parameter;

  @override
  Widget buildPage() => const TripDetailView();

  @override
  TripDetailCubit createCubit() =>
      TripDetailCubit(activityRepository: locator.get(), parameter: parameter);
}

class TripDetailView extends StatefulWidget {
  const TripDetailView({super.key});

  @override
  State<TripDetailView> createState() => _TripDetailViewState();
}

class _TripDetailViewState extends BaseBlocPageState<TripDetailView,
    TripDetailState, TripDetailCubit> {
  late TripDetailCubit _cubit;

  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();
    isTopSafeArea = true;
    _scrollController.addListener(() {
      final isNowScrolled = _scrollController.offset > kToolbarHeight;
      if (_isScrolled != isNowScrolled) {
        setState(() {
          _isScrolled = isNowScrolled;
        });
      }
    });
  }

  @override
  bool listenWhen(TripDetailState previous, TripDetailState current) {
    if (current.isSaving && !previous.isSaving) {
      showLoading();
    } else if (previous.isSaving && !current.isSaving) {
      dismissLoading();
      if (current.saveTripSuccess) {
        AppLogger.d('Save trip success 11');
        // context.pushRoute(const ActivityHomeRoute());
      } else {
        AppLogger.d('Save trip failed');
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(
      BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    return Container();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget buildBody(
      BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    if (state.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Prevent TabController error if days is empty
    if (state.days.isEmpty) {
      return const Center(child: Text('No days available'));
    }
    final isEditMode = state.editMode;
    _cubit = cubit;
    final titleController =
        TextEditingController(text: state.activity?.name ?? '');
    if (isEditMode) {
      titleController.selection = TextSelection.fromPosition(
        TextPosition(offset: titleController.text.length),
      );
    }
    final fromDate = state.activity?.fromDate?.toDateTime();
    final toDate = state.activity?.toDate?.toDateTime();

    return DefaultTabController(
      length: state.days.length,
      initialIndex: state.selectedIndex,
      child: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            leading: CircleItem(
                onTap: () => onBack(),
                backgroundColor: appTheme.backgroundV2,
                child: ButtonIcon(Assets.icons.icArrowLeft.path, () {
                  onBack();
                }, bg: appTheme.backgroundV2)),
            title: _isScrolled
                ? Row(
                    children: [
                      Expanded(
                        child: Text(
                          state.activity?.name ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: AppStyle.bold16(),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          if (state.activity?.attachments !=
                              null) //Has attachment
                          {
                            final attachment = state.activity?.attachments!;
                            final tripNote =
                                DocumentModel.fromJson(attachment![0]);

                            cubit.onEditDocument(context, tripNote,
                                isRootDocument: true);
                          } else {
                            //Add new note
                            cubit.onEditDocument(context, null,
                                isRootDocument: true);
                          }
                        },
                        child: Assets.icons.file.svg(
                          width: 34,
                          height: 34,
                          colorFilter: ColorFilter.mode(
                              appTheme.grayV2, BlendMode.srcIn),
                        ),
                      ),
                    ],
                  )
                : null,
            expandedHeight: 250.h2,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.white,
            flexibleSpace: FlexibleSpaceBar(
                background: Column(
              children: [
                SizedBox(
                  height: 250.h2,
                  child: Stack(
                    children: [
                      Container(
                        height: 250.h2,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: (state.activity?.imagePath != null &&
                                    state.activity?.imagePath?.isNotEmpty ==
                                        true)
                                ? CachedNetworkImageProvider(
                                    state.activity?.imagePath ?? '')
                                : Image.asset(Assets
                                        .images.birthdayRegistryCover.path)
                                    .image,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      // Top navigation bar
                      Align(
                        alignment: Alignment(0, 0.9),
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          padding: paddingV2(horizontal: 26, vertical: 18),
                          decoration: BoxDecoration(
                            color: appTheme.backgroundWhite,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    appTheme.blackColor.withValues(alpha: 0.1),
                                blurRadius: 20,
                                offset: const Offset(0, -4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Trip title
                                  Expanded(
                                    child: isEditMode
                                        ? Container(
                                            decoration: BoxDecoration(
                                              color: Colors.grey
                                                  .withValues(alpha: 0.1),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              border: Border.all(
                                                  color:
                                                      appTheme.primaryColorV2,
                                                  width: 1),
                                            ),
                                            child: TextField(
                                              controller: titleController,
                                              style: AppStyle.bold24V2(),
                                              decoration: const InputDecoration(
                                                border: InputBorder.none,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 12,
                                                        vertical: 4),
                                              ),
                                              onChanged: (value) {
                                                cubit.updateTripTitle(value);
                                              },
                                            ),
                                          )
                                        : Text(
                                            state.activity?.name ?? '',
                                            style: AppStyle.bold24V2(),
                                            maxLines: 3,
                                          ),
                                  ),
                                  const SizedBox(width: 8),
                                  GestureDetector(
                                    onTap: () {
                                      if (state.activity?.attachments !=
                                          null) //Has attachment
                                      {
                                        final attachment =
                                            state.activity?.attachments!;
                                        final tripNote = DocumentModel.fromJson(
                                            attachment![0]);

                                        cubit.onEditDocument(context, tripNote,
                                            isRootDocument: true);
                                      } else {
                                        //Add new note
                                        cubit.onEditDocument(context, null,
                                            isRootDocument: true);
                                      }
                                    },
                                    child: Assets.icons.file.svg(
                                      width: 34,
                                      height: 34,
                                      colorFilter: ColorFilter.mode(
                                          appTheme.grayV2, BlendMode.srcIn),
                                    ),
                                  ),
                                  // Settings icon
                                  GestureDetector(
                                    onTap: () async {
                                      if (state.editMode) {
                                        await cubit.updateTripNameAndDates(
                                          state.activity?.name ?? '',
                                          state.activity?.fromDate
                                              ?.toDateTime(),
                                          state.activity?.toDate?.toDateTime(),
                                        );
                                      }
                                      cubit.toggleEditMode();
                                    },
                                    child: state.editMode
                                        ? Assets.icons.icActionCheck.svg(
                                            width: 30,
                                            height: 30,
                                            colorFilter: ColorFilter.mode(
                                                appTheme.grayV2,
                                                BlendMode.srcIn),
                                          )
                                        : Assets.icons.iconEdit.svg(
                                            width: 30,
                                            height: 30,
                                            colorFilter: ColorFilter.mode(
                                                appTheme.grayV2,
                                                BlendMode.srcIn)),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 8),
                              // Trip dates and cancel button inline
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: isEditMode
                                        ? GestureDetector(
                                            onTap: () async {
                                              final picked = await showDateRangePicker(
                                                context: context,
                                                firstDate: DateTime(2000),
                                                lastDate: DateTime(2100),
                                                initialDateRange:
                                                    fromDate != null && toDate != null
                                                        ? DateTimeRange(
                                                            start: fromDate,
                                                            end: toDate)
                                                        : null,
                                              );
                                              if (picked != null) {
                                                cubit.updateTripDates(
                                                    picked.start, picked.end);
                                              }
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(
                                                  horizontal: 12, vertical: 4),
                                              decoration: BoxDecoration(
                                                color: Colors.grey.withValues(alpha: 0.1),
                                                borderRadius: BorderRadius.circular(8),
                                                border: Border.all(
                                                    color: appTheme.primaryColorV2,
                                                    width: 1),
                                              ),
                                              child: Text(
                                                "${_formatDate(state.activity?.fromDate)} - ${_formatDate(state.activity?.toDate)}",
                                                style: AppStyle.medium14V2(),
                                              ),
                                            ),
                                          )
                                        : Text(
                                            "${_formatDate(state.activity?.fromDate)} - ${_formatDate(state.activity?.toDate)}",
                                            style: AppStyle.medium14V2(),
                                          ),
                                  ),
                                  if (state.deleteMode)
                                    Padding(
                                      padding: const EdgeInsets.only(left: 8.0),
                                      child: ElevatedButton(
                                        onPressed: () => cubit.disableDeleteMode(),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: appTheme.blackColor.withValues(alpha: 0.56),
                                          elevation: 0,
                                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                                          minimumSize: const Size(0, 28),
                                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                        ),
                                        child: Text(LocaleKeys.cancel.tr(),
                                            style: TextStyle(
                                                color: appTheme.whiteText,
                                                fontSize: 10.w,
                                                fontWeight: FontWeight.w600)),
                                      ),
                                    ),
                                ],
                              ),
                              const SizedBox(height: 12),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                // White content container with dynamic height
              ],
            )),
          ),
          SliverToBoxAdapter(child: Container()),
          SliverPersistentHeader(
            pinned: true,
            delegate: _TabBarDelegate(
              TabBar(
                onTap: (value) => cubit.setSelectedIndex(value),
                isScrollable: true,
                tabs: state.days
                    .map((day) => Tab(
                          height: 32,
                          child: Padding(
                            padding: paddingV2(horizontal: 2),
                            child: Text(day),
                          ),
                        ))
                    .toList(),
                tabAlignment: TabAlignment.start,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerHeight: 0,
                labelColor: appTheme.primaryColorV2,
                unselectedLabelColor: appTheme.grayV2,
                labelStyle: AppStyle.regular14V2(),
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(999),
                  border: Border.all(
                      color: appTheme.primaryColorV2.withValues(alpha: 0.12)),
                  color: appTheme.primaryColorV2.withValues(alpha: 0.12),
                ),
                splashFactory: NoSplash.splashFactory,
                overlayColor: WidgetStateProperty.all(Colors.transparent),
              ),
            ),
          ),
        ],
        body: TabBarView(
          children: List.generate(state.daysDetail.length, (index) {
            final itinerary = state.activity?.itinerary != null &&
                    index < state.activity!.itinerary!.length
                ? state.activity!.itinerary![index]
                : Itinerary(); // fallback
            return _buildActivities(context, cubit, state, itinerary, index);
          }),
        ),
      ),
    );
  }

  Widget _buildActivities(BuildContext context, TripDetailCubit cubit,
      TripDetailState state, Itinerary itinerary, int dayIndex) {
    final timelineList = cubit.getTimelineList(state.activity!, dayIndex);

    final count = timelineList.length;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(dayIndex < state.daysDetail.length
            ? "${state.daysDetail[dayIndex]}"
            : "Day ${dayIndex + 1}"),
        Expanded(
            child: ListView.builder(
          padding: paddingV2(vertical: 8),
          itemCount: count + 1, // Add 1 for the "Add new item"
          itemBuilder: (context, index) {
            if (index == count) {
              return _buildAddNewItemTile(
                  context, cubit, state, itinerary, dayIndex);
            }

            final timelineItem = timelineList[index];
            Widget child;
            if (timelineItem.data is TransferModel) {
              child = _buildTransferTimelineTile(
                  context, cubit, state, timelineItem, index, count);
            } else if (timelineItem.data is HotelBookingModel) {
              child = _buildHotelTile(context, cubit, state,
                  timelineItem.data as HotelBookingModel, index, count);
            } else if (timelineItem.data is DocumentModel) {
              return _buildDocumentTile(context, cubit, state,
                  timelineItem.data as DocumentModel, index, timelineItem);
            } else {
              child = _buildPlaceTile(context, cubit, state,
                  timelineItem.data as Activity, index, count);
            }
            // Wrap with GestureDetector for long press to enable delete mode
            return GestureDetector(
              onLongPress: () {
                if (!state.deleteMode) cubit.enableDeleteMode();
              },
              child: Stack(
                children: [
                  child,
                  if (state.deleteMode)
                    Positioned(
                      bottom: 10,
                      right: 5,
                      child: GestureDetector(
                        onTap: () async {
                          final shouldDelete = await showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(LocaleKeys.delete_activity.tr()),
                              content: Text(LocaleKeys.confirm_content_delete_activity.tr()),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(false),
                                  child: Text(LocaleKeys.cancel.tr(), style: TextStyle(color: appTheme.grayV2)),
                                ),
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(true),
                                  child: Text(LocaleKeys.delete.tr(), style: TextStyle(color: Colors.red)),
                                ),
                              ],
                            ),
                          );
                          if (shouldDelete == true) {
                            cubit.deleteTimelineItem(dayIndex, index);
                          }
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: appTheme.redColor.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: appTheme.redColor,
                              width: 1,
                            ),
                          ),
                          padding: const EdgeInsets.all(6),
                          child: SvgPicture.asset(
                            Assets.icons.iconDelete.path,
                            width: 17,
                            height: 17,
                            colorFilter: ColorFilter.mode(
                                appTheme.redColor, BlendMode.srcIn
                            )
                          )
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ))
      ],
    );
  }

  Widget _buildHotelTile(BuildContext context, TripDetailCubit cubit,
      TripDetailState state, HotelBookingModel hotel, int index, int length) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: appTheme.backgroundWhite,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Assets.icons.icHotel.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding:
            EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: _buildHotelItemView(context, cubit, state, hotel),
      ),
    );
  }

  Widget _buildTransferTimelineTile(
      BuildContext context,
      TripDetailCubit cubit,
      TripDetailState state,
      ActivityTimelineItem timelineItem,
      int index,
      int length) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: appTheme.backgroundWhite,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Assets.icons.icTransfer.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding:
            EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: _buildTransferItemView(context, cubit, state, timelineItem),
      ),
    );
  }

  Widget _buildAddNewItemTile(BuildContext context, TripDetailCubit cubit,
      TripDetailState state, Itinerary itinerary, int dayIndex) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: appTheme.backgroundWhite,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Assets.icons.icTimelineAdd.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding:
            EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: SizedBox(
          height: 40,
          child: Align(
            alignment: Alignment.centerLeft,
            child: TextButton(
              onPressed: () {
                cubit.onAddNewItemPressed(context, dayIndex);
              },
              child: Text(
                'Add new item',
                style: AppStyle.bold14(color: appTheme.primaryColorV2),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransferItemView(BuildContext context, TripDetailCubit cubit,
      TripDetailState state, ActivityTimelineItem timelineItem) {
    final transfer = timelineItem.data as TransferModel;

    // Helper: get +1 if arrival is next day
    String getPlusOne() {
      if (transfer.fromTime != null &&
          transfer.toTime != null &&
          transfer.fromTime!.isNotEmpty &&
          transfer.toTime!.isNotEmpty) {
        // Parse fromTime and toTime as DateTime directly
        final from = DateTime.parse(transfer.fromTime!);
        final to = DateTime.parse(transfer.toTime!);
        final fromDate = DateTime(from.year, from.month, from.day);
        final toDate = DateTime(to.year, to.month, to.day);
        final dayDiff = toDate.difference(fromDate).inDays;
        if (dayDiff > 0) {
          return '+$dayDiff';
        }
      }
      return '';
    }

    Widget buildTopRow() {
      return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            transfer.getFromDate(),
            style: AppStyle.regular12V2(color: appTheme.grayV2),
          ),
          if (transfer.transportCode != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: Text('|',
                  style: AppStyle.regular12V2(color: appTheme.grayV2)),
            ),
          Text(
            transfer.transportCode ?? '',
            style: AppStyle.regular12V2(color: appTheme.grayV2),
          ),
        ],
      );
    }

    Widget buildAirportRow() {
      double getFontSize(String? text) {
        if (text == null) return 14;
        if (text.length > 20) return 12;
        if (text.length > 35) return 10;
        return 14;
      }

      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Departure airport
          SizedBox(
            width: 90,
            child: Text(
              transfer.fromLocation ?? '',
              style:
                  AppStyle.medium14V2(color: appTheme.primaryColorV2).copyWith(
                fontSize: getFontSize(transfer.fromLocation),
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              softWrap: true,
            ),
          ),
          // Timeline
          Expanded(
            child: Center(
              child: Text(
                transfer.getDuration(),
                style: AppStyle.regular14V2(color: appTheme.grayV2),
              ),
            ),
          ),
          // Arrival airport
          SizedBox(
            width: 90,
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                transfer.toLocation ?? '',
                style: AppStyle.medium14V2(color: appTheme.primaryColorV2)
                    .copyWith(
                  fontSize: getFontSize(transfer.toLocation),
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                softWrap: true,
                textAlign: TextAlign.right,
              ),
            ),
          ),
        ],
      );
    }

    Widget buildTimeRow() {
      String getTransferIconPath(String? type) {
        switch (type?.toLowerCase()) {
          case 'flight':
            return Assets.icons.icFlight.path;
          case 'train':
            return Assets.icons.icTrain.path;
          case 'drive':
            return Assets.icons.icDrive.path;
          case 'bus':
            return Assets.icons.icBus.path;
          case 'car':
            return Assets.icons.icCar.path;
          case 'ferry':
            return Assets.icons.icFerry.path;
          default:
            return Assets.icons.icBus.path;
        }
      }

      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Departure time
          Text(
            transfer.getFromTime(),
            style: AppStyle.bold20(),
          ),
          // Duration
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 14.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.circle, size: 10, color: appTheme.grayV2),
                  Expanded(
                    child: DottedLine(
                      direction: Axis.horizontal,
                      lineThickness: 1.0,
                      dashLength: 3.0,
                      dashColor: appTheme.grayV2,
                      dashGapLength: 4.0,
                      dashRadius: 0.0,
                    ),
                  ),
                  if (transfer.type != null && transfer.type!.isNotEmpty) ...{
                    const SizedBox(width: 2),
                    SvgPicture.asset(
                      getTransferIconPath(transfer.type),
                      width: 20,
                      height: 20,
                      colorFilter:
                          ColorFilter.mode(appTheme.grayV2, BlendMode.srcIn),
                    ),
                    const SizedBox(width: 2),
                  },
                  Expanded(
                    child: DottedLine(
                      direction: Axis.horizontal,
                      lineThickness: 1.0,
                      dashLength: 3.0,
                      dashColor: appTheme.grayV2,
                      dashGapLength: 4.0,
                      dashRadius: 0.0,
                    ),
                  ),
                  Icon(Icons.circle, size: 10, color: appTheme.grayV2),
                ],
              ),
            ),
          ),
          // Arrival time (+1)
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                transfer.getToTime(),
                style: AppStyle.bold20(),
              ),
              if (getPlusOne().isNotEmpty) ...[
                const SizedBox(width: 2),
                Text(
                  getPlusOne(),
                  style: AppStyle.regular12(color: appTheme.grayColor),
                ),
              ]
            ],
          ),
        ],
      );
    }

    return GestureDetector(
      onTap: () {
        if (timelineItem.activityIndex != null) {
          cubit.onEditTransfer(context, transfer, timelineItem.activityIndex!);
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: appTheme.backgroundWhite,
          borderRadius: BorderRadius.circular(18.0),
          boxShadow: [
            BoxShadow(
              color: appTheme.blackColor.withOpacity(0.06),
              blurRadius: 12,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if ((transfer.getFromDate().isNotEmpty) ||
                (transfer.ticketNo != null &&
                    transfer.ticketNo!.isNotEmpty)) ...[
              buildTopRow(),
            ],
            const SizedBox(height: 4),
            buildAirportRow(),
            buildTimeRow(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceTile(BuildContext context, TripDetailCubit cubit,
      TripDetailState state, Activity activity, int index, int length) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: appTheme.backgroundWhite,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Assets.icons.icPosition.svg(width: 32.w, height: 32.w),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding:
            EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: SizedBox(
          height: 150.w,
          child: _buildPlaceItemView(context, cubit, state, activity, index),
        ),
      ),
    );
  }

  Widget _buildHotelItemView(BuildContext context, TripDetailCubit cubit,
      TripDetailState state, HotelBookingModel hotel) {
    return InkWell(
      onTap: () {
        // Tap on hotel shows hotel details/booking info
        cubit.onBookHotel(context, hotel);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: appTheme.borderColor),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                width: 100,
                height: 90,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(hotel.imageUrl ?? ''),
                    fit: BoxFit.cover,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      hotel.hotelName ?? '',
                      style: AppStyle.bold12(),
                      maxLines: 2,
                      overflow: TextOverflow.visible,
                    ),
                    if (hotel.location != 'null, null')
                      _buildLocationView(context, hotel.location!),
                  ],
                ),
              ),
              // Map Button
              CircleItem(
                onTap: () => _openLocationInMap(context,
                    name: hotel.hotelName, location: hotel.location),
                padding: padding(all: 10),
                backgroundColor:
                    appTheme.primaryColorV2.withValues(alpha: 0.12),
                child: Transform.rotate(
                  angle: pi,
                  child: Assets.icons.icMap.svg(
                    width: 18.w,
                    height: 18.w,
                    colorFilter: ColorFilter.mode(
                        appTheme.primaryColorV2, BlendMode.srcIn),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Open a location in device's default map application (for hotel or activity)
  Future<void> _openLocationInMap(BuildContext context,
      {String? name, String? location}) async {
    try {
      final displayName = name ?? 'Location';
      final loc = location ?? '';
      final query = (displayName.isNotEmpty && loc.isNotEmpty)
          ? '$displayName, $loc'
          : (displayName.isNotEmpty ? displayName : loc);
      final encodedQuery = Uri.encodeComponent(query);
      final urls = [
        'https://www.google.com/maps/search/?api=1&query=$encodedQuery',
        'https://maps.google.com/maps?q=$encodedQuery',
      ];
      bool launched = false;
      for (final url in urls) {
        final uri = Uri.parse(url);
        if (await canLaunch(uri.toString())) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          launched = true;
          break;
        }
      }
      if (!launched) {
        print('Could not launch any map application for: $query');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open maps application')),
        );
      } else {
        print('Opened location in maps: $query');
      }
    } catch (e) {
      print('Error opening location in maps: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error opening maps application')),
      );
    }
  }

  Widget _buildPlaceItemView(
    BuildContext context,
    TripDetailCubit cubit,
    TripDetailState state,
    Activity activity,
    int activityIndex,
  ) {
    final imageUrl = activity.activityImage;
    return GestureDetector(
      onTap: () {
        final itinerary = Itinerary(activities: [activity]);
        // View mode: go to map selection
        InitialLocation? initialLocation;
        if (activity.latitude != null && activity.longitude != null) {
          initialLocation = InitialLocation(
            name: activity.city,
            latLng: LatLng(activity.latitude!, activity.longitude!),
          );
        } else {
          initialLocation = InitialLocation(
            name: activity.city,
            latLng: state.activity?.gpsCoord,
          );
        }
        context
            .pushRoute(PlaceUpsertRoute(
          parameter: PlaceUpsertParameter(
            tripId: state.activityId,
            dayIndex: state.selectedIndex,
            activityIndex: activityIndex,
            place: itinerary,
            initialLocation: initialLocation,
          ),
        ))
            .then((value) {
          if (value == true) {
            cubit.fetchTripDetail();
          }
        });

        // if (state.editMode) {
        //   // Edit mode: go to PlaceUpsertRoute
        //   InitialLocation? initialLocation;
        //   if (activity.latitude != null && activity.longitude != null) {
        //     initialLocation = InitialLocation(
        //       name: activity.city,
        //       latLng: LatLng(activity.latitude!, activity.longitude!),
        //     );
        //   } else {
        //     initialLocation = InitialLocation(
        //       name: activity.city,
        //       latLng: state.activity?.gpsCoord,
        //     );
        //   }
        //   context
        //       .pushRoute(PlaceUpsertRoute(
        //     parameter: PlaceUpsertParameter(
        //       tripId: state.activityId,
        //       dayIndex: state.selectedIndex,
        //       activityIndex: activityIndex,
        //       place: itinerary,
        //       initialLocation: initialLocation,
        //     ),
        //   ))
        //       .then((value) {
        //     if (value == true) {
        //       cubit.fetchTripDetail();
        //     }
        //   });
        // } else
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: appTheme.backgroundWhite,
          borderRadius: BorderRadius.circular(16.w),
          border: Border.all(color: appTheme.borderColorV2, width: 1.0),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                width: 96.w,
                height: 74.w,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: imageUrl?.isEmpty == false
                        ? CachedNetworkImageProvider(imageUrl!)
                        : Image.asset(Assets.images.birthdayRegistryCover.path)
                            .image,
                    fit: BoxFit.cover,
                  ),
                  borderRadius: BorderRadius.circular(8.w),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      activity.description ?? '',
                      style: AppStyle.bold12(),
                      maxLines: 3,
                      overflow: TextOverflow.visible,
                    ),
                    _buildLocationView(context, activity.venue ?? ''),
                    _buildTimeView(context, cubit, state, activity),
                  ],
                ),
              ),
              // Map Button for activity
              if ((activity.venue != null && activity.venue!.isNotEmpty) ||
                  (activity.city != null && activity.city!.isNotEmpty))
                CircleItem(
                  onTap: () => _openLocationInMap(
                    context,
                    name: activity.venue,
                    location: activity.city,
                  ),
                  padding: padding(all: 10),
                  backgroundColor:
                      appTheme.primaryColorV2.withValues(alpha: 0.12),
                  child: Transform.rotate(
                    angle: pi,
                    child: Assets.icons.icMap.svg(
                      width: 18.w,
                      height: 18.w,
                      colorFilter: ColorFilter.mode(
                          appTheme.primaryColorV2, BlendMode.srcIn),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentTile(
      BuildContext context,
      TripDetailCubit cubit,
      TripDetailState state,
      DocumentModel document,
      int index,
      ActivityTimelineItem timelineItem) {
    return TimelineTile(
      alignment: TimelineAlign.start,
      indicatorStyle: IndicatorStyle(
        width: 32.w,
        height: 32.w,
        color: appTheme.backgroundWhite,
        indicator: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Icon(Icons.insert_drive_file,
              color: appTheme.primaryColorV2, size: 28),
        ),
      ),
      beforeLineStyle: LineStyle(
        color: appTheme.borderColorV2,
        thickness: 2,
      ),
      lineXY: 0.09,
      endChild: Padding(
        padding:
            EdgeInsets.only(left: 16.w, right: 8.w, top: 8.w, bottom: 16.w),
        child: _buildDocumentItemView(
            context, cubit, document, state, timelineItem),
      ),
    );
  }

  Widget _buildDocumentItemView(
      BuildContext context,
      TripDetailCubit cubit,
      DocumentModel document,
      TripDetailState state,
      ActivityTimelineItem timelineItem) {
    // Build comma-separated file names
    final fileNames = document.files.map((f) => f.fileName).join(', ');
    return InkWell(
      onTap: () {
        cubit.onEditDocument(context, document);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: appTheme.borderColor),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    document.title,
                    style: AppStyle.bold14(),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (document.description.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        document.description,
                        style: AppStyle.regular12(color: appTheme.grayColor),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      fileNames,
                      style: AppStyle.regular10(color: appTheme.fadeTextColor),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.chevron_right, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationView(BuildContext context, String location) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minTileHeight: 0,
      minVerticalPadding: 4,
      minLeadingWidth: 0,
      horizontalTitleGap: 8,
      leading: Assets.icons.icLocation.svg(width: 16, height: 16),
      title: Text(
        location,
        maxLines: 2,
        style: AppStyle.regular12(),
      ),
    );
  }

  Widget _buildTimeView(BuildContext context, TripDetailCubit cubit,
      TripDetailState state, Activity activity) {
    // Parse time range from activity.time
    String startTime = '10:00';
    String endTime = '12:00';

    if (activity.time.isNotEmpty) {
      // print('Activity time: ${activity.time}');
      // print('Activity duration: ${activity.duration}');
      if (activity.time == 'AM') {
        startTime = '10:00';
        endTime = '12:00';
      } else if (activity.time == 'PM') {
        startTime = '16:00';
        endTime = '18:00';
      } else if (activity.time.contains(' - ')) {
        // Handle time range format: "09:30 - 11:30" or "09.30 - 11.30"
        final times = activity.time.split(' - ');
        if (times.length == 2) {
          startTime = times[0].trim().replaceAll('.', ':');
          endTime = times[1].trim().replaceAll('.', ':');
        }
      } else if ((activity.time.contains('.') || activity.time.contains(':')) &&
          activity.duration != null) {
        // Only handle as a single time if not a range
        startTime = activity.time;
        if (activity.time.contains('.')) {
          startTime = activity.time.replaceAll('.', ':');
        }

        final durationInMinutes =
            activity.duration!; // Assuming duration is in minutes

        // Calculate end time by adding duration to start time
        //start time is in HH:mm format , duration is in minutes , convert duration to delta
        // Example: if startTime is "10:30" and duration is 180, endTime will be "13:30"
        final endTimeDate = DateTime.parse('2025-01-01 $startTime')
            .add(Duration(minutes: durationInMinutes));

        // create dateformater to format endTimeDate to HH:mm
        final endTimeFormatter = DateFormat('HH:mm');
        endTime = endTimeFormatter.format(endTimeDate);
      }
    }

    return Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.start.tr(),
            style: AppStyle.regular12(),
          ),
          Text(
            startTime,
            style: AppStyle.bold12(),
          ),
        ],
      ),
      const SizedBox(width: 16),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.end.tr(),
            style: AppStyle.regular12(),
          ),
          Text(
            endTime,
            style: AppStyle.bold12(),
          ),
        ],
      ),
    ]);
  }

//TODO: trip detail screen has 2 purposes
//  1- to show the trip that's already saved in server - with uuid
//  2- to show the preview of the trip that's not Saved yet - without uuid
//     2.1 - need to have an 'customization mode' to modify small item in itinerray xxx

  @override
  Widget? buildFloatingActionButton(
      BuildContext context, TripDetailCubit cubit, TripDetailState state) {
    if (cubit.isPreviewMode == true) {
      // Preview mode: show 'Personalize your trip' button
      return ElevatedButton(
        onPressed: () async {
          _goToCustomizeTrip(state, cubit);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: appTheme.primaryColorV2,
          // Background color (purple)
          foregroundColor: appTheme.whiteText,
          // Text and icon color
          textStyle: const TextStyle(
            fontSize: 16, // Adjust the font size as needed
            fontWeight: FontWeight.w500, // Medium fontWeig
          ),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 3,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Assets.icons.icLightningBolt.svg(
              height: 20,
              width: 20,
            ),
            const SizedBox(width: 8),
            const Text('Personalize your trip'),
          ],
        ),
      );
    } else {
      // normal mode - show ASK AI button
      return ElevatedButton(
        onPressed: () {
          AppLogger.d("Ask AI button pressed!");
          _goToEditTrip(state, cubit);
          // context.pushRoute(TripUpsertRoute(/* pass trip details if editing */));
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: appTheme.primaryColorV2,
          // Background color (purple)
          foregroundColor: appTheme.whiteText,
          // Text and icon color
          textStyle: const TextStyle(
            fontSize: 16, // Adjust the font size as needed
            fontWeight: FontWeight.w500, // Medium fontWeig
          ),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          // Adjust padding as needed
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20), // Rounded corners
          ),
          elevation: 3, // Add a subtle elevation (shadow)
        ),
        child: Row(
          mainAxisSize:
              MainAxisSize.min, // Ensure the Row takes only the space it needs
          children: [
            Assets.icons.icLightningBolt.svg(
              height: 20, // Adjust icon size
              width: 20, // Ensure the icon is white
            ),
            const SizedBox(width: 8), // Spacing between icon and text
            const Text('Ask AI'),
          ],
        ),
      );
    }
  }

  @override
  void onBack() async {
    if (_cubit.isPreviewMode == true) {
      // Show confirmation dialog
      final shouldSave = await DialogUtils.showConfirmDialog(
        context,
        title: 'Save trip?',
        content: 'Do you want to save this trip?',
        confirmText: 'Yes',
        cancelText: 'No',
      );
      if (shouldSave == true) {
        await _cubit.savePreviewTrip();
        if (context.mounted) {
          if (locator.isRegistered<HomeCubit>()) {
            final homeCubit = locator.get<HomeCubit>();
            homeCubit.onRefresh();
          }
          context.router
              .popUntil((route) => (route.settings.name == MainRoute.name));
        }
      } else {
        context.router
            .popUntil((route) => (route.settings.name == MainRoute.name));
      }
    } else {
      if (_cubit.activityIsModified == true) {
        // Show confirmation dialog
        final shouldSave = await DialogUtils.showConfirmDialog(
          context,
          title: 'Save trip?',
          content: 'Do you want to save this trip?',
          confirmText: 'Yes',
          cancelText: 'No',
        );
        if (shouldSave == true) {
          await _cubit.saveExistingTrip();
          if (context.mounted) {
            context.maybePop();
          }
        } else {
          context.maybePop();
        }
      } else {
        context.maybePop();
      }
    }
  }

  Future<void> _goToCustomizeTrip(
      TripDetailState state, TripDetailCubit cubit) async {
    try {
      final token = await locator.get<LocalStorage>().accessToken();
      if (token == null || token.isEmpty) {
        await locator.get<LocalStorage>().clear();
        if (context.mounted) {
          context.replaceRoute(const AuthRoute());
        }
        return;
      }

      // Use cubit to build geoCoding string
      final geoCoding = cubit.buildDateRangeString() + cubit.buildGeoCoding();

      // TODO: Review this part later - connection is already open from VTPDiscoveryCubit
      // This saves initialization time with AI when coming from VTP flow
      final customizedTrip = await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: false,
        // Prevent tap outside to dismiss
        enableDrag: false,
        builder: (context) => FractionallySizedBox(
          heightFactor: 0.8, // 80% of the screen height
          child: ChatPage(
            parameter: ChatParameter(
              chatContext: ChatContext.getCustomizeTripContext(token,
                  prePromptContext: geoCoding),
              customData: state.activity!,
              // aiConnectionController:cubit.aiController, // Pass the AI connection controller
            ),
          ),
        ),
      );
      // After bottom sheet closes, reload the preview trip info
      if (customizedTrip != null) {
        // TODO: customizedTrip is type Trip , need to convert to ActivityModel

        logd("got Customizedtrip: ");
        final convertedTrip = customizedTrip.toActivityModel();
        cubit.updatePreviewTrip(convertedTrip);
      } else {
        loge("Error , null customized trip");
      }
    } catch (e, st) {
      AppLogger.e('Error opening personalize chat: $e\n$st');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  Future<void> _goToEditTrip(
      TripDetailState state, TripDetailCubit cubit) async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      // Use cubit to build geoCoding string
      final geoCoding = cubit.buildDateRangeString() + cubit.buildGeoCoding();

      // This saves initialization time with AI when coming from VTP flow
      final editedTrip = await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: false,
        // Prevent tap outside to dismiss
        enableDrag: false,
        builder: (context) => FractionallySizedBox(
          heightFactor: 0.8, // 80% of the screen height
          child: ChatPage(
            parameter: ChatParameter(
              chatContext: ChatContext.getEditTripContext(
                  token, state.activityId,
                  geoCoding: geoCoding),
              customData: state.activity!,//needed to show some diaglog 
              aiConnectionController:
                  cubit.aiController, // Pass the AI connection controller
            ),
          ),
        ),
      );

      if (editedTrip != null) {
        // customizedTrip is type Trip , need to convert to ActivityModel

        logd("got edited trip: ");
        final convertedTrip = editedTrip.toActivityModel();
        cubit.updatePreviewTrip(convertedTrip);
      } else {
        loge("Error , null edit trip ");
      }
      // context.pushRoute(ChatRoute(
      //     parameter: ChatParameter(
      //         chatContext:
      //             ChatContext.getEditTripContext(token, state.activityId))));
    }
  }

  String _formatDate(String? date) {
    if (date == null || date.isEmpty) return '';
    try {
      return date.toDateTime().MMM_d_yyyy;
    } catch (_) {
      return date;
    }
  }
}

class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _TabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: appTheme.backgroundWhite,
      alignment: Alignment.centerLeft,
      child: Material(
        color: Colors.transparent,
        child: tabBar,
      ),
    );
  }

  @override
  bool shouldRebuild(_TabBarDelegate oldDelegate) =>
      oldDelegate.tabBar != tabBar;
}
