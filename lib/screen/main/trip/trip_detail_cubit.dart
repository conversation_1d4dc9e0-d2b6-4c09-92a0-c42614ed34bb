import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';

import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/AiConnectionController.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'trip_detail_state.dart';
import 'trip_select_activity_type_parameter.dart';
import 'trip_select_activity_type_screen.dart';
import 'package:family_app/data/model/document_model.dart';
import 'package:family_app/screen/main/trip/trip_transfer_upsert_parameter.dart';
import 'package:family_app/screen/main/trip/document/document_upsert_parameter.dart';
import 'dart:convert';

class TripDetailCubit extends BaseCubit<TripDetailState> {
  final IActivityRepository activityRepository;
  final TripDetailParameter parameter;

  bool isPreviewMode;

  //set to true when the activity is modified but NOT saved.
  // used when exiting this page, will ask the user once.
  bool activityIsModified = false;

  /// AI Connection Controller for WebSocket communication
  /// This can be passed from VTPDiscoveryCubit (case 1) or created new (case 2)
  late AiConnectionController _aiController;

  TripDetailCubit(
      {required this.activityRepository,
      required this.parameter,
      this.isPreviewMode = false})
      : super(TripDetailState(
            activityId: parameter.activityId, activity: parameter.activity)) {
    // Initialize AI Connection Controller
    // Case 1: Use the connection passed in the parameter (from VTPDiscoveryCubit)
    // Case 2: If not available, create a new one (for server trips)
    if (parameter.aiConnectionController != null) {
      _aiController = parameter.aiConnectionController!;
      AppLogger.d(
          "Using existing AI connection controller from VTPDiscoveryCubit");
    } else {
      // Create a new AI connection controller for server trips
      // TODO: Review this part later - connection is already open, need to handle properly
      _aiController = AiConnectionController(
        onMessage: _handleStreamData,
        onError: _handleStreamError,
        onDone: _handleStreamDone,
        onTimeout: _handleTimeout,
        timeout: const Duration(seconds: 60),
      );
      AppLogger.d("Created new AI connection controller for server trip");
    }
  }

  @override
  void onInit() {
    super.onInit();
    locator.registerSingleton(this);
    if (parameter.activityId.isEmpty) {
      // Use the passed activity directly
      isPreviewMode = true;
      _processActivityData(parameter.activity!);
    } else {
      isPreviewMode = false;
      _fetchTripDetails();
    }
  }

  @override
  Future<void> close() {
    locator.unregister<TripDetailCubit>();
    // Close the AI connection controller if it was created by this cubit
    // (not if it was passed from VTPDiscoveryCubit)
    if (parameter.aiConnectionController == null) {
      logd("close ai_connnection ");
      _aiController.close();
    }
    return super.close();
  }

  /// Sets the selected day index for the trip.
  void setSelectedIndex(int index) {
    if (index != state.selectedIndex) {
      emit(state.copyWith(selectedIndex: index));
    }
  }

  /// Fetches trip details and updates state.
  Future<void> _fetchTripDetails() async {
    if (state.loading) return;
    emit(state.copyWith(loading: true));
    try {

      // logd("Fetching TEST activity :XXXX");
      // final String test_uuid = "abd6e601-6e3e-4354-a6c2-d44516e66635";
      // final activity = await activityRepository.getActivityById(test_uuid);

      final activity = await activityRepository.getActivityById(parameter.activity?.uuid ?? parameter.activityId);
      await _processActivityData(activity);
    } catch (e) {
      AppLogger.e('Error fetching trip details: $e');
      emit(state.copyWith(loading: false));
    }
  }

  /// Processes activity data and updates state accordingly.
  Future<void> _processActivityData(ActivityModel activity) async {
    try {
      List<String> days = _generateDaysList(activity);
      final daysDetail = _generateDaysDetailList(activity, days.length);
      final updatedActivity = await _fetchTripImagesIfNeeded(activity);
      logd("Trip Hotel number of days: ${daysDetail.length}");

      // //NOT a good way to handle error in trip generation, should be done in vtp_plan
      // if (days.length > daysDetail.length) {
      //   loge(
      //       "ERROR: Potential trip generation error (From AI), itinerary has more days than the Trip duration ($days / ${daysDetail.length}), prioritize trip duration for now");
      //   // Trim the itinerary to match daysDetail length
      //   days = days.sublist(0, daysDetail.length);
      // }

      //Log itinerary details
      if (updatedActivity.itinerary == null ||
          updatedActivity.itinerary!.isEmpty) {
        logd("Trip Itinerary is empty or null");
      } else {
        logd(
            "Trip Itinerary: ${updatedActivity.itinerary!.map((e) => e.toString()).join(', ')}");
      }
      emit(state.copyWith(
        loading: false,
        activity: updatedActivity,
        heroImageUrl: updatedActivity.imagePath,
        days: days,
        daysDetail: daysDetail,
      ));
    } catch (e) {
      AppLogger.e('Error processing activity data: $e');
      emit(state.copyWith(loading: false));
    }
  }

  /// Generates a list of day labels from the itinerary.
  List<String> _generateDaysList(ActivityModel activity) {
    return activity.itinerary
            ?.asMap()
            .entries
            .map((entry) => 'Day ${entry.key + 1}')
            .toList() ??
        [];
  }

/* consider both from - to & number of days in Itinerary , as the actual day may vary a bit due to long flight*/
  List<String> _generateDaysDetailList(ActivityModel activity, int numDayFromItinerary) {
    final fromDate = activity.fromDate?.toDateTime();
    final toDate = activity.toDate?.toDateTime();

    if (fromDate == null || toDate == null) return [];

    final days = <String>[];
    DateTime current = fromDate;

    // while (!current.isAfter(toDate)) {
    while (numDayFromItinerary-- >= 0) {
      days.add(_formatDateFromDateTime(current));
      current = current.add(const Duration(days: 1));
    }

    return days;
  }

  String _formatDateFromDateTime(DateTime date) {
    return DateFormat('EEE MMM dd').format(date);
  }

  /// Fetches trip images if not already present, returns a new ActivityModel.
  Future<ActivityModel> _fetchTripImagesIfNeeded(ActivityModel activity) async {
    if (activity.imagePath != null) return activity;
    String? imagePath;
    try {
      // Use location bias from GPS coordinates for better search results
      LatLng? activityLocation = activity.gpsCoord;

      if (activity.city != null) {
        // Enhanced provider with location bias for cost optimization
        imagePath = await provider.fetchImageUrl(activity.city!,
            location: activityLocation);
      } else if (activity.country != null) {
        imagePath = await provider.fetchImageUrl(activity.country!,
            location: activityLocation);
      }
    } catch (e) {
      AppLogger.e('Error fetching trip images: $e');
    }
    return activity.copyWith(imagePath: imagePath);
  }

  /// Fetches trip detail and updates state.
  Future<void> fetchTripDetail() async {
    if (state.loading) return;
    emit(state.copyWith(loading: true));
    try {
      final result = await activityRepository.getActivityById(state.activityId);
      final updatedResult = await _fetchTripImagesIfNeeded(result);
      final dayItinerary = _generateDaysList(updatedResult);
      emit(state.copyWith(
        loading: false,
        activity: updatedResult,
        heroImageUrl: updatedResult.imagePath,
        days: dayItinerary,
        daysDetail: _generateDaysDetailList(updatedResult, dayItinerary.length),
      ));
    } catch (e) {
      AppLogger.e("Error fetching trip: $e");
      emit(state.copyWith(loading: false));
    }
  }

  /// Returns the timeline list for a given day index.
  List<ActivityTimelineItem> getTimelineList(
      ActivityModel activityModel, int dayIndex) {
    if (dayIndex < 0 || dayIndex >= (activityModel.itinerary?.length ?? 0)) {
      return [];
    }
    final List<ActivityTimelineItem> timelineItems = [];
    final itinerary = activityModel.itinerary![dayIndex];
    final dateTime =
        activityModel.fromDate?.toLocalDT.add(Duration(days: dayIndex));
    if (dateTime == null) return [];
    _addDocumentsToTimeline(timelineItems, itinerary, dateTime);
    _addActivitiesToTimeline(timelineItems, itinerary, dateTime);
    _addHotelBookingsToTimeline(timelineItems, activityModel, dateTime);
    return timelineItems;
  }

  void _addActivitiesToTimeline(List<ActivityTimelineItem> timelineItems,
      Itinerary itinerary, DateTime dateTime) {
    itinerary.activities?.asMap().forEach((activityIndex, activity) {
      // Use depatureTimeAndDate and arrivalTimeAndDate if available
      DateTime? depatureDT = activity.depatureTimeAndDate != null && activity.depatureTimeAndDate!.isNotEmpty
          ? activity.depatureTimeAndDate!.toDateTime()
          : null;
      DateTime? arrivalDT = activity.arrivalTimeAndDate != null && activity.arrivalTimeAndDate!.isNotEmpty
          ? activity.arrivalTimeAndDate!.toDateTime()
          : null;

      // logd("depature Time: ${depatureDT} , arr: ${arrivalDT}");

      if (activity.type != 'T') {
        // For non-transfer activities, use depatureTimeAndDate if available, else fallback
        DateTime timelineDateTime = depatureDT ?? _parseActivityTime(activity.time, dateTime);
        timelineItems.add(ActivityTimelineItem(dateTime: timelineDateTime, data: activity));
      } else {
        // For transfer activities, prefer depatureTimeAndDate/arrivalTimeAndDate
        if (activity.transfer != null && activity.transfer!.fromTime != null) {
          timelineItems.add(ActivityTimelineItem(
            dateTime: activity.transfer!.fromTime!.toDateTime(),
            data: activity.transfer,
            activityIndex: activityIndex,
          ));
        } else {
          // Use depatureTimeAndDate for fromTime, arrivalTimeAndDate for toTime if available
          DateTime fromTime = depatureDT ?? _parseActivityTime(activity.time, dateTime);
          DateTime? toTime;
          if (arrivalDT != null) {
            toTime = arrivalDT;
          } else if (activity.duration != null && activity.duration! > 0) {
            toTime = fromTime.add(Duration(minutes: activity.duration!));
          }
          // logd("Activity time: ${activity.time}, fromTime: $fromTime \n"
          // "toTime: $toTime");
          final aTransferItem = TransferModel(
            type: activity.mode,
            fromLocation: activity.origin,
            toLocation: activity.destination,
            fromTime: fromTime.toIso8601String(),
            toTime: toTime!.toIso8601String(),
            ticketNo: null,
            currency: null,
            quantity: null,
            price: null,
            note: activity.description,
            attachments: null,
            duration: activity.duration,
            transportCode: activity.transportCode,
          );
          timelineItems.add(ActivityTimelineItem(
            dateTime: fromTime,
            data: aTransferItem,
            activityIndex: activityIndex,
          ));
        }
      }
    });
  }

  DateTime _parseActivityTime(String time, DateTime dateTime) {
    if (time.isEmpty || time == 'AM') {
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
    } else if (time == 'PM') {
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 16, 0);
    } else if (time.contains(' - ')) {
      final times = time.split(' - ');
      if (times.length == 2) {
        final startTime = times[0].trim().split(':');
        if (startTime.length == 2) {
          final hour = int.tryParse(startTime[0]) ?? 10;
          final minute = int.tryParse(startTime[1]) ?? 0;
          return DateTime(
              dateTime.year, dateTime.month, dateTime.day, hour, minute);
        }
      }
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
    } else if (RegExp(r'^[0-9]{1,2}\.[0-9]{2}?$').hasMatch(time.trim())) {
      // Matches 'HH.mm' or 'H.mm' (e.g., '10.00', '8.30', '15.45')
      final parts = time.trim().split('.');
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]) ?? 10;
        final minute = int.tryParse(parts[1]) ?? 0;
        return DateTime(dateTime.year, dateTime.month, dateTime.day, hour, minute);
      }
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
    } else if (RegExp(r'^[0-9]{1,2}:[0-9]{2}?$').hasMatch(time.trim())) {
      // Matches 'HH:mm' or 'H:mm' (e.g., '18:00', '8:30', '15:45')
      final parts = time.trim().split(':');
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]) ?? 10;
        final minute = int.tryParse(parts[1]) ?? 0;
        return DateTime(dateTime.year, dateTime.month, dateTime.day, hour, minute);
      }
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
    }
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
  }

  void _addHotelBookingsToTimeline(List<ActivityTimelineItem> timelineItems,
      ActivityModel activityModel, DateTime dateTime) {
    activityModel.hotelBookings?.forEach((booking) {
      if (booking.checkInDate != null && booking.checkOutDate != null) {
        var checkInMilis =
            booking.checkInDate!.toLocalDT.millisecondsSinceEpoch;
        var checkOutMilis =
            booking.checkOutDate!.toLocalDT.millisecondsSinceEpoch;
        var dayTripMilis = dateTime.millisecondsSinceEpoch;
        if (dayTripMilis >= checkInMilis && dayTripMilis < checkOutMilis) {
          timelineItems.add(ActivityTimelineItem(
              dateTime: booking.checkInDate!.toLocalDT, data: booking));
        }
      }
    });
  }

  void _addDocumentsToTimeline(List<ActivityTimelineItem> timelineItems,
      Itinerary itinerary, DateTime dateTime) {
    if (itinerary.documents != null) {
      for (final doc in itinerary.documents!) {
        timelineItems.add(ActivityTimelineItem(dateTime: dateTime, data: doc));
      }
    }
  }

  /// Handles the add new item action from the UI.
  void onAddNewItemPressed(BuildContext context, int dayIndex) {
    if (state.activity == null) return;

    BottomSheetUtils.showHeightReturnValue(context,
            height: 0.3,
            child: TripSelectActivityTypeBts(
                parameter:
                    TripSelectActivityTypeParameter(state.activity!, dayIndex)))
        .then((value) {
      AppLogger.d(
          'Add new item pressed return value: ${value.runtimeType} / $value');

      if (value != null && value is HotelBookingModel) {
        //udpate trip detail with this new Booking info
        final HotelBookingModel hotelBooking = value;
        final List<HotelBookingModel> currentBookings =
            List.from(state.activity?.hotelBookings ?? []);
        // Add as new booking
        currentBookings.add(hotelBooking);

        final updatedActivity =
            state.activity!.copyWith(hotelBookings: currentBookings);
        emit(state.copyWith(
          activity: updatedActivity,
          loading: false,
        ));

        ///call trip update
        if (isPreviewMode) {
          //do nothing
        } else {
          // Save trip to Server
          saveExistingTrip();
        }
      } else if (value != null && value is TransferModel) {
        // Add new transfer activity to the correct day
        final List<Itinerary> itineraries = List.from(state.activity?.itinerary ?? []);
        if (dayIndex >= itineraries.length) return;
        final Itinerary dayItinerary = itineraries[dayIndex];
        final List<Activity> activities = List.from(dayItinerary.activities ?? []);
        final newTransferActivity = Activity(
          time: '',
          description: '',
          venue: '',
          type: 'T',
          transfer: value,
        );
        activities.add(newTransferActivity);
        dayItinerary.activities = activities;
        itineraries[dayIndex] = dayItinerary;

        final updatedActivity = state.activity!.copyWith(itinerary: itineraries);
        emit(state.copyWith(
          activity: updatedActivity,
          loading: false,
        ));

        ///call trip update
        if (isPreviewMode) {
          //do nothing
        } else {
          // Save trip to Server
          saveExistingTrip();
        }
      } else if (value != null && value is DocumentModel) {
        // Add or update document in the correct day
        final List<Itinerary> itineraries = List.from(state.activity?.itinerary ?? []);
        if (dayIndex >= itineraries.length) return;
        final Itinerary dayItinerary = itineraries[dayIndex];
        final List<DocumentModel> docs = List.from(dayItinerary.documents ?? []);
        final int idx = docs.indexWhere((d) => d.title == value.title);
        if (idx != -1) {
          docs[idx] = value;
        } else {
          docs.add(value);
        }
        dayItinerary.documents = docs;
        itineraries[dayIndex] = dayItinerary;
        final updatedActivity = state.activity!.copyWith(itinerary: itineraries);
        emit(state.copyWith(
          activity: updatedActivity,
          loading: false,
        ));
        if (!isPreviewMode) {
          saveExistingTrip();
        }
      }

      // Sort the itinerary by time after any modifications
      if (state.activity != null) {
        final bool wasSorted = _sortItineraryByTime(state.activity!);

        // Only save to server if sorting was actually performed
        if (wasSorted && !isPreviewMode) {
          saveExistingTrip();
        }
      }
    });
  }

  /// Navigates to the hotel booking detail pag�e.
  Future<void> onBookHotel(
      BuildContext context, HotelBookingModel? hotel) async {
    if (state.activity == null) return;

    logd(" update a hotel booking");

    // Create HotelModel from HotelBookingModel using the toHotelModel() method
    final HotelModel? hotelModel = hotel?.toHotelModel();

    final dynamic result = await context.pushRoute(HotelDetailRoute(
      parameter: HotelDetailParameter(
        hotelId: hotel?.hotelId ?? '',
        hotel: hotelModel,
        activity: state.activity!,
        location: hotel?.location,
        dayIndex: state.selectedIndex,
        addNew: false,
      ),
    ));

    // logd("Hotel detail route result: $result, type: ${result.runtimeType}");

    if (result != null && result is HotelBookingModel) {
      final HotelBookingModel hotelBooking = result;
      final List<HotelBookingModel> currentBookings =
          List.from(state.activity?.hotelBookings ?? []);
      if (hotel != null) {
        // Update existing booking
        int updateIndex = currentBookings.indexWhere((b) => b == hotel);
        if (updateIndex != -1) {
          logd("update booking at :$updateIndex");
          currentBookings[updateIndex] = hotelBooking;
        } else {
          logd("add new booking");
          currentBookings.add(hotelBooking);
        }
      } else {
        // Add as new booking
        currentBookings.add(hotelBooking);
      }
      final updatedActivity =
          state.activity!.copyWith(hotelBookings: currentBookings);
      emit(state.copyWith(
        activity: updatedActivity,
        loading: false,
      ));
      //API
      saveExistingTrip();
    } else {
      logd("No hotel selected, cancel flow ");
    }
  }

  Future<void> onEditTransfer(BuildContext context, TransferModel transfer, int activityIndex) async {
    if (state.activity == null) return;

    final List<Itinerary> itineraries = List.from(state.activity?.itinerary ?? []);
    final int dayIdx = state.selectedIndex;
    if (dayIdx < itineraries.length) {
      final Itinerary dayItinerary = itineraries[dayIdx];
      final List<Activity> activities = List.from(dayItinerary.activities ?? []);
      bool isDummyTransfer = false;
      if (activityIndex < activities.length) {
        final Activity oldActivity = activities[activityIndex];
        isDummyTransfer = oldActivity.transfer == null;
      } else {
        isDummyTransfer = true;
      }

      final result = await context.pushRoute(TripTransferUpsertRoute(
        parameter: TripTransferUpsertParameter(
          activity: state.activity!,
          dayIndex: state.selectedIndex,
          activityIndex: activityIndex,
          addNew: false,
          initialTransfer: isDummyTransfer ? transfer : null,
        ),
      ));

      if (result is TransferModel) {
        if (isDummyTransfer) {
          final newTransferActivity = Activity(
            time: '',
            description: '',
            venue: '',
            type: 'T',
            transfer: result,
          );
          activities.add(newTransferActivity);
        } else {
          // Update the existing activity's transfer field
          final Activity oldActivity = activities[activityIndex];
          final Activity updatedActivity = Activity(
            time: oldActivity.time,
            description: oldActivity.description,
            venue: oldActivity.venue,
            type: oldActivity.type,
            uuid: oldActivity.uuid,
            activityImage: oldActivity.activityImage,
            city: oldActivity.city,
            gpsCoord: oldActivity.gpsCoord,
            category: oldActivity.category,
            latitude: oldActivity.latitude,
            longitude: oldActivity.longitude,
            duration: oldActivity.duration,
            transfer: result,
            origin: oldActivity.origin,
            destination: oldActivity.destination,
            mode: oldActivity.mode,
          );
          activities[activityIndex] = updatedActivity;
        }
        dayItinerary.activities = activities;
        itineraries[dayIdx] = dayItinerary;
        final updatedActivityModel = state.activity!.copyWith(itinerary: itineraries);
        emit(state.copyWith(activity: updatedActivityModel, loading: false));

        if (!isPreviewMode) {
          saveExistingTrip();
        }
      }
    }
  }

  /// Handles editing and saving a document, either at the root ActivityModel level or within a specific Itinerary.
  Future<void> onEditDocument(BuildContext context, DocumentModel? document,
      {int? docIndex, bool isRootDocument = false}) async {

    final result = await context.pushRoute(DocumentUpsertRoute(
      document: document,
      parameter: DocumentUpsertParameter(
        activity: state.activity!,
        dayIndex: isRootDocument ? null : state.selectedIndex,
        addNew: false,
        isRootDocument: isRootDocument,
      ),
    ));

    if (result is DocumentModel) {
      if (isRootDocument) {
        // Update the root-level document field in ActivityModel

        ///Phung put document in list of attachments too.. for saving to API
        final updatedActivity = state.activity!.copyWith(document: result, attachments: [result]);

        

        print("isRootDocument updatedActivity: ${result.title}");
        emit(state.copyWith(activity: updatedActivity, loading: false));
        if (!isPreviewMode) {
          saveExistingTrip();
        }
      } else {
        // Update the document in the correct day's itinerary
        final List<Itinerary> itineraries = List.from(state.activity?.itinerary ?? []);
        final int dayIdx = state.selectedIndex;
        if (dayIdx < itineraries.length) {
          final Itinerary dayItinerary = itineraries[dayIdx];
          final List<DocumentModel> docs = List.from(dayItinerary.documents ?? []);
          int idx = docIndex ?? docs.indexWhere((d) => d.title == document?.title);
          if (idx != -1) {
            docs[idx] = result;
          } else {
            docs.add(result);
          }
          dayItinerary.documents = docs;
          itineraries[dayIdx] = dayItinerary;
          final updatedActivityModel = state.activity!.copyWith(itinerary: itineraries);
          emit(state.copyWith(activity: updatedActivityModel, loading: false));
          if (!isPreviewMode) {
            saveExistingTrip();
          }
        }
      }
    }
  }

  /// Toggles edit mode for the trip detail screen.
  void toggleEditMode() {
    emit(state.copyWith(editMode: !state.editMode));
  }

  /// Updates the trip name and dates, saving to the repository.
  Future<void> updateTripNameAndDates(
      String name, DateTime? fromDate, DateTime? toDate) async {
    if (state.activity == null) return;
    emit(state.copyWith(isSaving: true));
    try {
      final updatedActivity = state.activity!.copyWith(
        name: name,
        fromDate: fromDate?.toIso8601String(),
        toDate: toDate?.toIso8601String(),
      );
      await activityRepository.updateActivity(
          updatedActivity.uuid, updatedActivity.toCreateActivityParameter());
      emit(state.copyWith(
        activity: updatedActivity,
        isSaving: false,
        saveTripSuccess: true,
      ));
    } catch (e) {
      AppLogger.e('Error updating trip name and dates: $e');
      emit(state.copyWith(isSaving: false, saveTripSuccess: false));
    }
  }

  /// Updates the trip title only in the state.
  void updateTripTitle(String name) {
    if (state.activity == null) return;
    final updatedActivity = state.activity!.copyWith(name: name);
    emit(state.copyWith(activity: updatedActivity));
  }

  /// Updates the trip dates only in the state.
  void updateTripDates(DateTime fromDate, DateTime toDate) {
    if (state.activity == null) return;
    final updatedActivity = state.activity!.copyWith(
      fromDate: fromDate.toIso8601String(),
      toDate: toDate.toIso8601String(),
    );
    emit(state.copyWith(activity: updatedActivity));
  }

//Not much different from preview trip but separate for easy understanding.
  Future<void> saveExistingTrip() async {
    if (state.activity == null) return;
    emit(state.copyWith(isSaving: true));

    try {
      final param = state.activity!.toCreateActivityParameter();

      logd(" Saving existing trip: Total Itinerary days: ${param.itinerary.length}");

      // print("Saving existing trip with Param: ${param.toJson()}");

      // final prettyString = const JsonEncoder.withIndent('  ').convert(param.toJson());
      // logd("saving with Param:\n$prettyString");
      
      final result =
          await activityRepository.updateActivity(state.activity!.uuid, param);
      emit(state.copyWith(
        activity: result,
        isSaving: false,
        saveTripSuccess: true,
      ));
    } catch (e) {
      AppLogger.e('Error saving preview trip: $e');
      emit(state.copyWith(isSaving: false, saveTripSuccess: false));
    }
  }

  /// FOR PREVIEW TRIP

  void updatePreviewTrip(ActivityModel? activity) {
    if (activity != null) {
      activityIsModified = true;


      final isSorted = _sortItineraryByTime(activity);

      logd("Sorting ? $isSorted");

      _processActivityData(activity);
    }
  }

  /// Saves the current trip in preview/customization mode.
  Future<void> savePreviewTrip() async {
    if (state.activity == null) return;
    emit(state.copyWith(isSaving: true));
    try {
      final param = state.activity!.toCreateActivityParameter();
     
      final prettyString = const JsonEncoder.withIndent('  ').convert(param.toJson());
      logd("Save PREIVEW TRIP Param:\n$prettyString");
       

      final result = await activityRepository.createActivity(param);
      emit(state.copyWith(
        activity: result,
        isSaving: false,
        saveTripSuccess: true,
      ));
    } catch (e, stacktrace) {
      AppLogger.e('Error saving preview trip: $e \n $stacktrace ');
      emit(state.copyWith(isSaving: false, saveTripSuccess: false));
    }
  }

  /// Getter for the AI connection controller
  /// This can be used by the UI to pass to ChatParameter
  AiConnectionController get aiController => _aiController;

  /// Handle incoming data from the WebSocket
  void _handleStreamData(dynamic data) {
    AppLogger.d("TripDetailCubit received WebSocket data: $data");
    // TODO: Implement if needed for trip detail functionality
  }

  /// Handle WebSocket stream errors
  void _handleStreamError(dynamic error) {
    AppLogger.e("TripDetailCubit WebSocket error: $error");
    // TODO: Implement if needed for trip detail functionality
  }

  /// Handle WebSocket stream completion
  void _handleStreamDone() {
    AppLogger.d("TripDetailCubit WebSocket connection closed");
    // TODO: Implement if needed for trip detail functionality
  }

  /// Handle timeout events
  void _handleTimeout() {
    AppLogger.e("TripDetailCubit WebSocket connection timeout");
    // TODO: Implement if needed for trip detail functionality
  }

  /// Builds a geoCoding string from the current activity (country, main city, additional cities)
  String buildGeoCoding() {
    final activity = state.activity;
    if (activity == null) return '';
    final Set<String> cities = {};
    if (activity.city != null && activity.city!.isNotEmpty) {
      cities.add(activity.city!);
    }
    if (activity.additionalCities != null) {
      for (final city in activity.additionalCities!) {
        if (city.isNotEmpty) cities.add(city);
      }
    }
    final country = activity.country ?? '';
    return [if (country.isNotEmpty) country, ...cities].join(', ');
  }

  /// Builds a date range string from the current activity (fromDate - toDate) in 'dd/MM/yyyy - dd/MM/yyyy' format
  String buildDateRangeString() {
    final activity = state.activity;
    if (activity == null) return '';
    final from = activity.fromDate;
    final to = activity.toDate;
    if (from == null || from.isEmpty || to == null || to.isEmpty) return '';
    final fromStr = from.ddMMyy;
    final toStr = to.ddMMyy;
    return ' $fromStr - $toStr ';
  }

  /// Sorts activities within each day's itinerary by time
  /// Returns true if any sorting was performed, false otherwise
  bool _sortItineraryByTime(ActivityModel activity) {
    if (activity.itinerary == null || activity.itinerary!.isEmpty) return false;

    final List<Itinerary> sortedItineraries = [];
    bool hasChanges = false;

    for (int dayIndex = 0; dayIndex < activity.itinerary!.length; dayIndex++) {
      final Itinerary dayItinerary = activity.itinerary![dayIndex];
      if (dayItinerary.activities == null || dayItinerary.activities!.isEmpty) {
        sortedItineraries.add(dayItinerary);
        continue;
      }

      // Get the date for this day
      final dateTime = activity.fromDate?.toDateTime().add(Duration(days: dayIndex));
      if (dateTime == null) {
        sortedItineraries.add(dayItinerary);
        continue;
      }

      // Create a list of activities with their parsed times for sorting
      final List<MapEntry<Activity, DateTime>> activitiesWithTime = [];

      for (final activity in dayItinerary.activities!) {
        DateTime parsedTime;

        if (activity.type == 'T' && activity.transfer != null && activity.transfer!.fromTime != null) {
          // For transfer activities with fromTime, use the transfer's fromTime
          parsedTime = activity.transfer!.fromTime!.toDateTime();
        } else {
          // For regular activities or transfers without fromTime, parse the activity time
          parsedTime = _parseActivityTime(activity.time, dateTime);
        }

        activitiesWithTime.add(MapEntry(activity, parsedTime));
      }

      // Sort activities by time
      activitiesWithTime.sort((a, b) => a.value.compareTo(b.value));

      // Create new itinerary with sorted activities
      final sortedActivities = activitiesWithTime.map((entry) => entry.key).toList();

      // Check if the order has changed
      final originalActivities = dayItinerary.activities!;
      bool dayHasChanges = false;
      for (int i = 0; i < sortedActivities.length; i++) {
        if (i >= originalActivities.length || sortedActivities[i] != originalActivities[i]) {
          dayHasChanges = true;
          break;
        }
      }

      if (dayHasChanges) {
        hasChanges = true;
      }

      final sortedDayItinerary = Itinerary(
        accommodation: dayItinerary.accommodation,
        activities: sortedActivities,
        food: dayItinerary.food,
        imageUrl: dayItinerary.imageUrl,
      );
      sortedItineraries.add(sortedDayItinerary);
    }

    // Update the activity with sorted itineraries
    final updatedActivity = activity.copyWith(itinerary: sortedItineraries);
    emit(state.copyWith(activity: updatedActivity));

    return hasChanges;
  }

  /// Enables delete mode for timeline items
  void enableDeleteMode() {
    emit(state.copyWith(deleteMode: true));
  }

  /// Disables delete mode for timeline items
  void disableDeleteMode() {
    emit(state.copyWith(deleteMode: false));
  }

  /// Deletes a timeline item by day and timeline index
  void deleteTimelineItem(int dayIndex, int timelineIndex) {
    if (state.activity == null) return;
    final activity = state.activity!;
    final timelineList = getTimelineList(activity, dayIndex);
    if (timelineIndex < 0 || timelineIndex >= timelineList.length) return;
    final item = timelineList[timelineIndex];

    bool updated = false;
    final itineraries = List<Itinerary>.from(activity.itinerary ?? []);
    final itinerary = itineraries[dayIndex];
    List<HotelBookingModel> hotelBookings = List<HotelBookingModel>.from(activity.hotelBookings ?? []);

    // Remove from activities
    if (item.data is Activity) {
      final activities = List<Activity>.from(itinerary.activities ?? []);
      activities.remove(item.data);
      itinerary.activities = activities;
      updated = true;
    } else if (item.data is TransferModel) {
      // Remove the activity that wraps this transfer
      final activities = List<Activity>.from(itinerary.activities ?? []);
      final transfer = item.data as TransferModel?;
      int removedCount = 0;
      if (transfer != null) {
        // Try to remove by transfer reference
        final before = activities.length;
        activities.removeWhere((a) => a.transfer == transfer);
        removedCount = before - activities.length;
      }
      bool removed = removedCount > 0;
      if (!removed && item.activityIndex != null) {
        // Remove by index if transfer not found (dummy transfer)
        final idx = item.activityIndex!;
        if (idx >= 0 && idx < activities.length && activities[idx].type == 'T') {
          activities.removeAt(idx);
          removed = true;
        }
      }
      itinerary.activities = activities;
      updated = removed;
    } else if (item.data is HotelBookingModel) {
      // Remove from hotelBookings (root level)
      // final hotelBookings =
      //     List<HotelBookingModel>.from(activity.hotelBookings ?? []);
      final hotelIdToDelete = (item.data as HotelBookingModel).hotelId;
      print("[DEBUG] Hotel bookings before: " +
          hotelBookings.map((h) => h.hotelId).toList().toString());
      print("[DEBUG] Trying to remove hotelId: $hotelIdToDelete");
      final before = hotelBookings.length;
      hotelBookings.removeWhere((h) => h.hotelId == hotelIdToDelete);
      final removedCount = before - hotelBookings.length;
      print("[DEBUG] Removed count: $removedCount");
      print("[DEBUG] Hotel bookings after: " + hotelBookings.map((h) => h.hotelId).toList().toString());
      
      updated = true;
    } else if (item.data is DocumentModel) {
      // Remove from documents in itinerary
      final docs = List<DocumentModel>.from(itinerary.documents ?? []);
      docs.remove(item.data);
      itinerary.documents = docs;
      updated = true;
    }
    if (updated) {
      itineraries[dayIndex] = itinerary;
      final updatedActivity = activity.copyWith(
        itinerary: itineraries,
        hotelBookings: hotelBookings,
      );
      print("[DEBUG] Updated activity: ${updatedActivity.hotelBookings?.map((h) => h.hotelId).toList()}");
      emit(state.copyWith(activity: updatedActivity, loading: false));
      if (!isPreviewMode) {
        saveExistingTrip();
      }
    }
  }

}
