import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/screen/main/trip/hotel/widgets/booking_info_textfield.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:flutter/material.dart';

import 'flight_booking_cubit.dart';
import 'flight_booking_parameter.dart';
import 'flight_booking_state.dart';

@RoutePage()
class FlightBookingPage extends BaseBlocProvider<FlightBookingState, FlightBookingCubit> {
  const FlightBookingPage({required this.parameter, super.key});

  final FlightBookingParameter parameter;

  @override
  Widget buildPage() => const FlightBookingView();

  @override
  FlightBookingCubit createCubit() => FlightBookingCubit(
        parameter: parameter,
        useCase: locator.get(),
      );
}

class FlightBookingView extends StatefulWidget {
  const FlightBookingView({super.key});

  @override
  State<FlightBookingView> createState() => _FlightBookingViewState();
}

class _FlightBookingViewState extends BaseBlocPageState<FlightBookingView, FlightBookingState, FlightBookingCubit> {
  @override
  Widget buildAppBar(BuildContext context, FlightBookingCubit cubit, FlightBookingState state) {
    return CustomAppBar2(
      title: LocaleKeys.booking_info.tr(),
      showBack: true,
      actions: [],
    );
  }

  @override
  bool listenWhen(FlightBookingState previous, FlightBookingState current) {
    if(current.status == FlightBookingStatus.error && current.errorMessage != null) {
      showErrorToast(current.errorMessage!);
    }else if (current.status == FlightBookingStatus.success) {
      int count = 0;
      Navigator.of(context).popUntil((route) {
        return count++ == 3;
      });
    }
    return super.listenWhen(previous, current);
  }



  @override
  Widget buildBody(BuildContext context, FlightBookingCubit cubit, FlightBookingState state) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Contact", style: AppStyle.bold14V2()),
                const SizedBox(height: 10),
                BookingInfoTextField(
                  label: LocaleKeys.first_name.tr(),
                  onChanged: (v) {},
                  text: "",
                  validator: (value) {
                    return null;
                  },
                ),
                BookingInfoTextField(
                  label: LocaleKeys.last_name.tr(),
                  onChanged: (v) {},
                  text: "",
                  validator: (value) {
                    return null;
                  },
                )

              ],
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.only(left: 16, right: 16, bottom: MediaQuery.of(context).padding.bottom + 4),
          child: PrimaryButtonV2(
            text: LocaleKeys.book.tr(),
            isLoading:  state.status == FlightBookingStatus.loading,
            onTap: () {
              cubit.bookFlight(context);
            },
          ),
        ),

      ],
    );
  }
}
