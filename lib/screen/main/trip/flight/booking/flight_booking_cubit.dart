import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/usecase/flight_booking_usecase.dart';
import 'package:family_app/data/usecase/model/booking_flight_param.dart';
import 'package:family_app/utils/loading.dart';
import 'package:flutter/material.dart';

import 'flight_booking_parameter.dart';
import 'flight_booking_state.dart';

class FlightBookingCubit extends BaseCubit<FlightBookingState> {
  final FlightBookingParameter parameter;
  final FlightBookingUseCase useCase;

  FlightBookingCubit({
    required this.parameter,
    required this.useCase,
  }) : super(FlightBookingState());

  @override
  void onInit() {
    List<BookingFlightPassenger> passengers = [];
    for (var i = 0; i < parameter.adults; i++) {
      passengers.add(BookingFlightPassenger(
        firstName: 'Passenger',
        lastName: "Bot",
        gender: i % 2 == 0 ? "MALE" : "FEMALE",
        dateOfBirth: "2000-01-01",
        emailAddress: "example$<EMAIL>",
        phoneNumber: "1234567$i",
        phoneCountryCallingCode: "82",
        id: "${i + 1}",
      ));
    }
    emit(state.copyWith(
      passengers: passengers,
      status: FlightBookingStatus.loaded,
    ));
    super.onInit();
  }

  Future<void> bookFlight(BuildContext context) async {
    emit(state.copyWith(status: FlightBookingStatus.loading));
    showLoading();
    try {
      final bookingParam = BookingFlightParam(
        flightOffers: [parameter.offer],
        travelers: state.passengers,
      );
      await useCase.bookFlight(bookingParam);
      dismissLoading();
      emit(state.copyWith(status: FlightBookingStatus.success));
    } catch (e) {
      dismissLoading();
      emit(state.copyWith(status: FlightBookingStatus.error, errorMessage: e.toString()));
    }
  }
}
