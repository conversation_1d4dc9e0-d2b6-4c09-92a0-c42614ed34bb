import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/usecase/model/booking_flight_param.dart';

enum FlightBookingStatus {
  initial,
  loading,
  loaded,
  success,
  error,
}
class FlightBookingState extends BaseState {
  final FlightBookingStatus status;
  final String? errorMessage;
  final List<BookingFlightPassenger> passengers;

  FlightBookingState({
    this.status = FlightBookingStatus.initial,
    this.passengers = const [],
    this.errorMessage,
  });

  FlightBookingState copyWith({
    FlightBookingStatus? status,
    String? errorMessage,
    List<BookingFlightPassenger>? passengers,
  }) {
    return FlightBookingState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      passengers: passengers ?? this.passengers,
    );
  }

  @override
  List<Object?> get props => [status, errorMessage, passengers];
}