class FlightOfferParameter {
  final String originLocationCode;
  final String destinationLocationCode;
  final DateTime departureDate;
  final int adults;
  final int children;
  final List<String>? prefAirlines; 

  FlightOfferParameter({
    required this.originLocationCode,
    required this.destinationLocationCode,
    required this.departureDate,
    required this.adults,
    this.children = 0,
    this.prefAirlines, 
  });
}
