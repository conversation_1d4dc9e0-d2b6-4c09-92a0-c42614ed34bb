import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/amadeus/flight_offer_model.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/trip/flight/flight_offer_cubit.dart';
import 'package:family_app/screen/main/trip/flight/flight_offer_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:flutter/material.dart';

import 'flight_offer_parameter.dart';

@RoutePage()
class FlightOfferPage extends BaseBlocProvider<FlightOfferState, FlightOfferCubit> {
  const FlightOfferPage({required this.parameter, super.key});

  final FlightOfferParameter parameter;

  @override
  Widget buildPage() => const FlightOfferView();

  @override
  FlightOfferCubit createCubit() => FlightOfferCubit(
        useCase: locator.get(),
        parameter: parameter,
      );
}

class FlightOfferView extends StatefulWidget {
  const FlightOfferView({super.key});

  @override
  State<FlightOfferView> createState() => _FlightOfferViewState();
}

class _FlightOfferViewState extends BaseBlocPageState<FlightOfferView, FlightOfferState, FlightOfferCubit> {
  @override
  Widget buildAppBar(BuildContext context, FlightOfferCubit cubit, FlightOfferState state) {
    return CustomAppBar2(
      title: LocaleKeys.select_new_hotel.tr(),
      showBack: true,
      actions: [],
    );
  }

  @override
  Widget buildBody(BuildContext context, FlightOfferCubit cubit, FlightOfferState state) {
    return ListView.builder(
        itemBuilder: (context, index) {
          final flightOffer = state.flightOffers[index];
          if (flightOffer is AmaFlightOfferModel) {
            return InkWell(onTap: () {
              cubit.onBookFlight(context, flightOffer);
            }, child: AMAFlightOfferItem(flightOffer: flightOffer));
          } else {
            return const SizedBox.shrink();
          }
        },
        itemCount: state.flightOffers.length);
  }
}

class AMAFlightOfferItem extends StatelessWidget {
  final AmaFlightOfferModel flightOffer;

  const AMAFlightOfferItem({required this.flightOffer, super.key});

  @override
  Widget build(BuildContext context) {
    var airline = flightOffer.airlines.map((e) => e.businessName).join(', ');
    return Container(
      decoration: BoxDecoration(
        color: appTheme.whiteText,
      ),
      margin: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            airline.isNotEmpty ? airline : flightOffer.validatingAirlineCodes.join(', '),
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Text("Route", style: AppStyle.bold14V2(color: appTheme.grayA8Color)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Center(
                  child: Text(
                    "Departure",
                    style: AppStyle.bold14V2(color: appTheme.blackText),
                  ),
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    "Arrival",
                    style: AppStyle.bold14V2(color: appTheme.blackText),
                  ),
                ),
              ),
            ],
          ),
          ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: flightOffer.itineraries.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                var itinerary = flightOffer.itineraries[index];
                return AmaFlightOfferItineraryItemWidget(itinerary: itinerary, index: index);
              }),
          Text(
            '${flightOffer.price.currency} ${flightOffer.currency}',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

class AmaFlightOfferItineraryItemWidget extends StatelessWidget {
  final AmaFlightOfferItinerary itinerary;
  final int index;

  const AmaFlightOfferItineraryItemWidget({required this.itinerary, required this.index, super.key});

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      Expanded(
        child: Column(
          children: [
            Text(
              itinerary.segments.first.departure?.iataCode ?? "",
              style: AppStyle.bold14V2(color: appTheme.blackText),
            ),
            Text(
              itinerary.segments.first.departure?.at.toLocalDT.yyyy_MM_dd ?? "",
              style: AppStyle.regular14V2(color: appTheme.blackText),
            ),
            Text(
              itinerary.segments.first.departure?.at.toLocalDT.HH_mm ?? "",
              style: AppStyle.regular14V2(color: appTheme.blackText),
            ),
          ],
        ),
      ),
      Icon(
        Icons.arrow_forward,
        color: appTheme.grayA8Color,
      ),
      Expanded(
        child: Column(
          children: [
            Text(
              itinerary.segments.last.arrival?.iataCode ?? "",
              style: AppStyle.bold14V2(color: appTheme.blackText),
            ),
            Text(
              itinerary.segments.first.arrival?.at.toLocalDT.yyyy_MM_dd ?? "",
              style: AppStyle.regular14V2(color: appTheme.blackText),
            ),
            Text(
              itinerary.segments.first.arrival?.at.toLocalDT.HH_mm ?? "",
              style: AppStyle.regular14V2(color: appTheme.blackText),
            ),
          ],
        ),
      ),
    ]);
  }
}
