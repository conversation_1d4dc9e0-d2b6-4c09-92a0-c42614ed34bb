import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/model/amadeus/flight_offer_model.dart';
import 'package:family_app/data/usecase/flight_booking_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/flight/booking/flight_booking_parameter.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';

import 'flight_offer_parameter.dart';
import 'flight_offer_state.dart';

class FlightOfferCubit extends BaseCubit<FlightOfferState> {
  FlightBookingUseCase useCase;
  FlightOfferParameter parameter;

  FlightOfferCubit({
    required this.useCase,
    required this.parameter,
  }) : super(FlightOfferState());

  @override
  void onInit() {
    _loadFlightOffers();
    super.onInit();
  }

  _loadFlightOffers() async {
    emit(state.copyWith(status: FlightOfferStatus.loading));

    final originAirport = parameter.originLocationCode;
    final destinationAirport = parameter.destinationLocationCode;

    logd(" Searching flight from: $originAirport, to $destinationAirport, depart: ${parameter.departureDate}");
    


    try {
      showLoading();
      final flightOffers = await useCase.searchFlights(
          originAirport, destinationAirport, parameter.departureDate,
        parameter.adults,
          airlineCodesP: parameter.prefAirlines
        // returnDate: DateTime(2025, 06, 08),
      );
      dismissLoading();
      emit(state.copyWith(status: FlightOfferStatus.loaded, flightOffers: flightOffers));
    } catch (e) {
      dismissLoading();
      rethrow;
      // emit(state.copyWith(status: FlightOfferStatus.error, errorMessage: e.toString()));
    }
  }

  @override
  Future<void> close() {
    return super.close();
  }

  void onBookFlight(BuildContext context, AmaFlightOfferModel flightOffer) {
    context
        .pushRoute(FlightBookingRoute(parameter: FlightBookingParameter(offer: flightOffer, adults: parameter.adults)));
  }
}
