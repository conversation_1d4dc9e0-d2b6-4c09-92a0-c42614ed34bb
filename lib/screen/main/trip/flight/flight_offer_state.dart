import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/flight_offer_model.dart';

enum FlightOfferStatus { initial, loading, loaded, error, selected }

class FlightOfferState extends BaseState {
  FlightOfferState({
    this.status = FlightOfferStatus.initial,
    this.flightOffers = const [],
    this.errorMessage,
  });

  final FlightOfferStatus status;
  final List<FlightOfferModel> flightOffers;
  final String? errorMessage;

  FlightOfferState copyWith({
    FlightOfferStatus? status,
    List<FlightOfferModel>? flightOffers,
    String? errorMessage,
  }) {
    return FlightOfferState(
      status: status ?? this.status,
      flightOffers: flightOffers ?? this.flightOffers,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, flightOffers, errorMessage];
}
