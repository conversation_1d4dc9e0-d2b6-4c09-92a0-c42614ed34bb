import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';

enum BirthdayRegistryUpsertStatus { initial, loading, success, error, done }
class BirthdayRegistryUpsertState extends BaseState {
  final BirthdayRegistryUpsertStatus status;
  final String? coverImageFilePath;
  final String? caption;
  final Account? selectedMember;
  final DateTime? dateTime;
  final List<Account> members;
  final bool isCaptionValid;
  final bool isMemberValid;
  final bool isDateTimeValid;

  BirthdayRegistryUpsertState({
    this.status = BirthdayRegistryUpsertStatus.initial,
    this.coverImageFilePath,
    this.caption,
    this.selectedMember,
    this.dateTime,
    this.members = const [],
    this.isCaptionValid = true,
    this.isMemberValid = true,
    this.isDateTimeValid = true,
  });

  @override
  List<Object?> get props => [
        status,
        coverImageFilePath,
        caption,
        selectedMember,
        dateTime,
        members,
        isCaptionValid,
        isMemberValid,
        isDateTimeValid
      ];

  BirthdayRegistryUpsertState copyWith({
    BirthdayRegistryUpsertStatus? status,
    String? coverImageFilePath,
    String? caption,
    Account? selectedMember,
    DateTime? dateTime,
    List<Account>? members,
    bool? isCaptionValid,
    bool? isMemberValid,
    bool? isDateTimeValid,
  }) {
    return BirthdayRegistryUpsertState(
      status: status ?? this.status,
      coverImageFilePath: coverImageFilePath ?? this.coverImageFilePath,
      caption: caption ?? this.caption,
      selectedMember: selectedMember ?? this.selectedMember,
      dateTime: dateTime ?? this.dateTime,
      members: members ?? this.members,
      isCaptionValid: isCaptionValid ?? this.isCaptionValid,
      isMemberValid: isMemberValid ?? this.isMemberValid,
      isDateTimeValid: isDateTimeValid ?? this.isDateTimeValid,
    );
  }

  BirthdayRegistryUpsertState updateCaption(String? caption, bool captionValid) {
    return BirthdayRegistryUpsertState(
      status: status,
      coverImageFilePath: coverImageFilePath,
      caption: caption,
      selectedMember: selectedMember,
      dateTime: dateTime,
      members: members,
      isCaptionValid: captionValid,
      isMemberValid: isMemberValid,
      isDateTimeValid: isDateTimeValid,
    );
  }

  BirthdayRegistryUpsertState updateCoverImageFilePath(String? coverImageFilePath) {
    return BirthdayRegistryUpsertState(
      status: status,
      coverImageFilePath: coverImageFilePath,
      caption: caption,
      selectedMember: selectedMember,
      dateTime: dateTime,
      members: members,
      isCaptionValid: isCaptionValid,
      isMemberValid: isMemberValid,
      isDateTimeValid: isDateTimeValid,
    );
  }
}
