import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/trip_model.dart';

enum BirthdayRegistryDetailStatus {
  initial,
  loading,
  success,
  error,
  done
}
class BirthdayRegistryDetailState extends BaseState {
  final BirthdayRegistryDetailStatus status;
  final String activityId;
  final ActivityModel? activity;
  final bool loading;
  final bool isSaving;
  final int selectedIndex;
  String? heroImageUrl;
  Map<String, String> foodImageUrls;
  List<String> tabs;
  List<Activity> scheduleActivities;
  List<Item>? wishlists;

  BirthdayRegistryDetailState({
    this.status = BirthdayRegistryDetailStatus.initial,
    required this.activityId,
    this.activity,
    this.loading = false,
    this.isSaving = false,
    this.selectedIndex = 0,
    this.heroImageUrl,
    this.foodImageUrls = const {},
    this.tabs = const [],
    this.scheduleActivities = const [],
    this.wishlists = const [],
  });

  @override
  List<Object?> get props =>
      [status, activityId, activity, loading, isSaving, selectedIndex, heroImageUrl, foodImageUrls, tabs, scheduleActivities, wishlists];

  BirthdayRegistryDetailState copyWith({
    BirthdayRegistryDetailStatus? status,
    String? activityId,
    ActivityModel? activity,
    bool? loading,
    bool? isSaving,
    int? selectedIndex,
    String? heroImageUrl,
    Map<String, String>? foodImageUrls,
    List<String>? tabs,
    List<Activity>? scheduleActivities,
    List<Item>? wishlists,
  }) {
    return BirthdayRegistryDetailState(
      status: status ?? this.status,
      activityId: activityId ?? this.activityId,
      activity: activity ?? this.activity,
      loading: loading ?? this.loading,
      isSaving: isSaving ?? this.isSaving,
      selectedIndex: selectedIndex ?? this.selectedIndex,
      heroImageUrl: heroImageUrl ?? this.heroImageUrl,
      foodImageUrls: foodImageUrls ?? this.foodImageUrls,
      tabs: tabs ?? this.tabs,
      scheduleActivities: scheduleActivities ?? this.scheduleActivities,
      wishlists: wishlists ?? this.wishlists,
    );
  }
}
