import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';

enum BirthdayRegistryWishlistsUpsertStatus {
  initial,
  loading,
  success,
  error,
  done
}

class BirthdayRegistryWishlistsUpsertState extends BaseState {
  final BirthdayRegistryWishlistsUpsertStatus status;
  final ActivityModel activity;
  final String? name;

  BirthdayRegistryWishlistsUpsertState({
    required this.activity,
    this.status = BirthdayRegistryWishlistsUpsertStatus.initial,
    this.name,
  });

  @override
  List<Object?> get props => [status, activity, name];

  BirthdayRegistryWishlistsUpsertState copyWith({
    BirthdayRegistryWishlistsUpsertStatus? status,
    ActivityModel? activity,
    String? name,
  }) {
    return BirthdayRegistryWishlistsUpsertState(
      status: status ?? this.status,
      activity: activity ?? this.activity,
      name: name ?? this.name,
    );
  }
}
