import 'dart:convert';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/usecase/model/activity_item_parameter.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_detail_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';

import 'birthday_registry_detail_state.dart';

class BirthdayRegistryDetailCubit
    extends BaseCubit<BirthdayRegistryDetailState> {
  final IActivityRepository activityRepository;
  final BirthdayRegistryDetailParameter parameter;

  BirthdayRegistryDetailCubit(
      {required this.activityRepository, required this.parameter})
      : super(BirthdayRegistryDetailState(
            activityId: parameter.activityId, activity: parameter.activity));

  @override
  void onInit() {
    super.onInit();
    _initData();
    AppLogger.d("\Birthday registry detail activityId= ${state.activityId}");
  }

  void setSelectedIndex(int index) {
    emit(state.copyWith(selectedIndex: index));
  }

  Future<void> _initData() async {
    emit(state.copyWith(loading: true));
    final tabs = [
      // 'Schedule',
      'Wish lists',
    ];
    final List<Activity> scheduleActivities = [
      Activity(
        description: 'Ares Restaurant',
        time: 'AM',
        venue: 'Mount Ba Na',
      ),
      Activity(
        description: 'Pool Party',
        time: 'PM',
        venue: '7 District, HCMC',
      ),
    ];
    try {
      // final activity = await activityRepository.getActivityById(parameter.activityId);
      await fetchWishlists();
      emit(state.copyWith(loading: false, tabs: tabs, scheduleActivities: scheduleActivities));
    } catch (error) {
      AppLogger.e('Init data error: $error');
    }
  }

  Future<void> fetchWishlists() async {
    try {
      final ActivityItemParameter params =
          ActivityItemParameter(listId: state.activityId);
      final result = await activityRepository.getAllItemInActivity(params);
      emit(state.copyWith(wishlists: result));
    } catch (e) {
      AppLogger.e("Error deleting trip: $e");
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  Future<void> upsertActivityItemStatus(Item wishlistItem, bool? newValue) async {
    emit(state.copyWith(status: BirthdayRegistryDetailStatus.loading));
    try {
      final UpsertItemParam params =
          UpsertItemParam(status: newValue != null && newValue ? 1 : 0);
      await activityRepository.updateItemInActivity(wishlistItem.uuid!, params);
      await fetchWishlists();
      emit(state.copyWith(status: BirthdayRegistryDetailStatus.success));
    } catch (e) {
      AppLogger.e('Error update activity item status: $e');
      emit(state.copyWith(status: BirthdayRegistryDetailStatus.error));
    } finally {
      emit(state.copyWith(status: BirthdayRegistryDetailStatus.done));
    }
  }

  Future<void> deleteActivityItem(Item wishlistItem)
  async {
    emit(state.copyWith(status: BirthdayRegistryDetailStatus.loading));
    try {
      final result = await activityRepository.deleteItemInActivity(wishlistItem.uuid!);
      if (result) await fetchWishlists();
      emit(state.copyWith(status: BirthdayRegistryDetailStatus.success));
    } catch (e) {
      AppLogger.e('Error delete activity item: $e');
      emit(state.copyWith(status: BirthdayRegistryDetailStatus.error));
    } finally {
      emit(state.copyWith(status: BirthdayRegistryDetailStatus.done));
    }
  }
}
