import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_wishlists_upsert_cubit.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_wishlists_upsert_parameter.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_wishlists_upsert_state.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';

class BirthdayRegistryWishlistsUpsertBts extends BaseBlocProvider<
    BirthdayRegistryWishlistsUpsertState,
    BirthdayRegistryWishlistsUpsertCubit> {
  final BirthdayRegistryWishlistsUpsertParameter parameter;

  const BirthdayRegistryWishlistsUpsertBts(
      {required this.parameter, super.key});

  static Future<bool?> show(BuildContext context, ActivityModel activity,
      {Item? wishlistsItem}) async {
    return BottomSheetUtils.showHeightReturnBool(context,
        height: 0.6,
        child: BirthdayRegistryWishlistsUpsertBts(
            parameter: BirthdayRegistryWishlistsUpsertParameter(activity, wishlistsItem)));
  }

  @override
  Widget buildPage() => const _BirthdayRegistryWishlistsUpsertScreen();

  @override
  BirthdayRegistryWishlistsUpsertCubit createCubit() =>
      BirthdayRegistryWishlistsUpsertCubit(
          parameter: parameter,
          activityRepository: locator.get(),
          familyRepository: locator.get());
}

class _BirthdayRegistryWishlistsUpsertScreen extends StatefulWidget {
  const _BirthdayRegistryWishlistsUpsertScreen({super.key});

  @override
  State<_BirthdayRegistryWishlistsUpsertScreen> createState() =>
      _BirthdayRegistryWishlistsUpsertScreenState();
}

class _BirthdayRegistryWishlistsUpsertScreenState extends BaseBlocPageState<
    _BirthdayRegistryWishlistsUpsertScreen,
    BirthdayRegistryWishlistsUpsertState,
    BirthdayRegistryWishlistsUpsertCubit> {
  @override
  bool listenWhen(BirthdayRegistryWishlistsUpsertState previous,
      BirthdayRegistryWishlistsUpsertState current) {
    switch(current.status) {
      case BirthdayRegistryWishlistsUpsertStatus.loading:
        showLoading();
        break;
      case BirthdayRegistryWishlistsUpsertStatus.success:
        dismissLoading();
        Navigator.of(context).pop(true);
        break;
      default:
        dismissLoading();
        break;
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(
      BuildContext context,
      BirthdayRegistryWishlistsUpsertCubit cubit,
      BirthdayRegistryWishlistsUpsertState state) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(
      BuildContext context,
      BirthdayRegistryWishlistsUpsertCubit cubit,
      BirthdayRegistryWishlistsUpsertState state) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildHeader(context, cubit, state),
          Container(height: 1, color: Theme.of(context).dividerColor),
          _buildTextBox(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildHeader(
      BuildContext context,
      BirthdayRegistryWishlistsUpsertCubit cubit,
      BirthdayRegistryWishlistsUpsertState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text(
              'Cancel',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const Expanded(
            child: Center(
              child: Text('New item',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
          ),
          _buildSubmitButton(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildSubmitButton(
      BuildContext context,
      BirthdayRegistryWishlistsUpsertCubit cubit,
      BirthdayRegistryWishlistsUpsertState state) {
    const text = 'Save';
    return TextButton(
      onPressed: () async {
        AppLogger.d('Call upsert!');
        cubit.upsertActivityItem(state.activity, state.name);
      },
      child: state.name == null || state.name!.isEmpty
          ? Text(
              text,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: appTheme.greyColor,
              ),
            )
          : const Text(
              text,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }

  Widget _buildTextBox(
      BuildContext context,
      BirthdayRegistryWishlistsUpsertCubit cubit,
      BirthdayRegistryWishlistsUpsertState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextFormField(
        initialValue: state.name,
        maxLines: null,
        decoration: const InputDecoration(
          hintText: 'Type your wishlist item here...',
          border: InputBorder.none, // Hide the border
        ),
        onChanged: (value) {
          cubit.updateName(value);
        },
      ),
    );
  }
}
