import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_wishlists_upsert_parameter.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_wishlists_upsert_state.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';

class BirthdayRegistryWishlistsUpsertCubit
    extends BaseCubit<BirthdayRegistryWishlistsUpsertState> {
  final BirthdayRegistryWishlistsUpsertParameter parameter;
  final IFamilyRepository familyRepository;
  final IActivityRepository activityRepository;
  final AccountService accountService = locator.get();

  BirthdayRegistryWishlistsUpsertCubit({
    required this.parameter,
    required this.familyRepository,
    required this.activityRepository,
  }) : super(BirthdayRegistryWishlistsUpsertState(activity: parameter.activity));

  @override
  void onInit() {
    super.onInit();

    _initData();
  }

  void _initData() {
    emit(state.copyWith(name: parameter.wishlistItem?.name!));
  }

  void updateName(String name) {
    emit(state.copyWith(name: name));
  }

  Future<void> upsertActivityItem(ActivityModel activity, String? name) async {
    emit(state.copyWith(status: BirthdayRegistryWishlistsUpsertStatus.loading));
    try {
      final UpsertItemParam params = UpsertItemParam(
        listUuid: activity.uuid,
        familyUuid: accountService.familyId,
        name: name,
      );
      await activityRepository.createItemInActivity(params);
      emit(state.copyWith(status: BirthdayRegistryWishlistsUpsertStatus.success));
    } catch (e) {
      AppLogger.e('Error saving activity item: $e');
      emit(state.copyWith(status: BirthdayRegistryWishlistsUpsertStatus.error));
    } finally {
      emit(state.copyWith(status: BirthdayRegistryWishlistsUpsertStatus.done));
    }
  }
}
