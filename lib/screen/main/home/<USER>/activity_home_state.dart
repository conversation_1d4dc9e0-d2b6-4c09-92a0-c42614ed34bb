import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';


enum ActivityHomeStatus { initial, loading, success, error, done }

class ActivityHomeState extends BaseState {
  final ActivityHomeStatus status;
  bool isBannerVisible;
  final List<ActivityModel> activityList;

  ActivityHomeState({
    this.status = ActivityHomeStatus.initial,
    this.isBannerVisible = true,
    this.activityList = const [],
  });

  @override
  List<Object?> get props => [status, isBannerVisible, activityList];

  ActivityHomeState copyWith({
    ActivityHomeStatus? status,
    bool? isBannerVisible,
    List<ActivityModel>? activityList,
  }) {
    return ActivityHomeState(
      status: status ?? this.status,
      isBannerVisible: isBannerVisible ?? this.isBannerVisible,
      activityList: activityList ?? this.activityList,
    );
  }
}
