import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/dotted_border/dotted_border.dart';
import 'package:family_app/widget/list_vertical_item.dart';
import 'package:flutter/material.dart';

class HomeCard<T> extends StatelessWidget {
  HomeCard({
    super.key,
    this.title = '',
    this.itemBuilder,
    this.backgroundColor,
    this.isEmpty = false,
    this.subTitle,
    this.addText = '',
    this.emptyTitle = '',
    this.items = const [],
    this.listItemPadding,
    this.lineItem = 1,
  });

  final String title;
  final bool isEmpty;
  final Widget Function(int index, T item)? itemBuilder;
  final int lineItem;
  final List<T> items;
  final Color? backgroundColor;
  final String emptyTitle;
  final String addText;
  final Widget? subTitle;
  final EdgeInsets? listItemPadding;

  final ValueNotifier<bool> isExpandedNotifier = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: appTheme.whiteText,
        boxShadow: [
          const BoxShadow(color: Color(0x15150000), offset: Offset(0, 2), blurRadius: 4.0),
        ],
      ),
      child: Container(
        width: double.infinity,
        padding: isEmpty ? padding(vertical: 8, horizontal: 12) : padding(),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isEmpty ? appTheme.whiteText : backgroundColor?.withOpacity(.05),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isEmpty) ...[
              Text(emptyTitle, style: AppStyle.normal14()),
              const SizedBox(height: 13),
              DottedBorder(
                radius: const Radius.circular(8),
                borderType: BorderType.RRect,
                color: appTheme.primaryColor,
                padding: padding(top: 11, bottom: 14),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add, color: appTheme.primaryColor, size: 16),
                    const SizedBox(width: 10.67),
                    Text(addText, style: AppStyle.normal14(color: appTheme.primaryColor)),
                  ],
                ),
              ),
            ] else ...[
              Container(
                width: double.infinity,
                padding: padding(top: 3, bottom: 6, horizontal: 12),
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: backgroundColor),
                child: Row(
                  children: [
                    Expanded(child: Text(title, style: AppStyle.regular14(color: appTheme.whiteText))),
                    subTitle ?? const SizedBox()
                  ],
                ),
              ),
              if (items.isNotEmpty)
                ValueListenableBuilder<bool>(
                    valueListenable: isExpandedNotifier,
                    builder: (context, isExpanded, child) {
                      final displayedItems = isExpanded || items.length <= 5 ? items : items.take(5).toList();

                      return Column(
                        children: [
                          ListVerticalItem<T>(
                            viewPadding: listItemPadding ?? padding(top: 8, bottom: 9, horizontal: 12),
                            physics: const NeverScrollableScrollPhysics(),
                            lineItemCount: lineItem,
                            items: displayedItems,
                            paddingBetweenLine: 13,
                            itemBuilder: (index, item) =>
                                itemBuilder?.call(index, displayedItems[index]) ?? const SizedBox(),
                          ),
                          if (items.length > 5)
                            Padding(
                              padding: padding(bottom: 12),
                              child: GestureDetector(
                                onTap: () {
                                  isExpandedNotifier.value = !isExpandedNotifier.value;
                                },
                                child: Text(
                                  isExpandedNotifier.value ? LocaleKeys.show_less_text.tr() : LocaleKeys.more_text.tr(),
                                  style: AppStyle.regular14(color: appTheme.primaryColor),
                                ),
                              ),
                            ),
                        ],
                      );
                    })
              else
                Padding(
                  padding: padding(top: 8, bottom: 14, horizontal: 12),
                  child: Text(emptyTitle, style: AppStyle.regular12(color: appTheme.hintColor)),
                )
            ]
          ],
        ),
      ),
    );
  }
}
