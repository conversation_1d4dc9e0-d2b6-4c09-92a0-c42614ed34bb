import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/home/<USER>/activity_select_type_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';

class ActivitySelectTypeCubit extends BaseCubit<ActivitySelectTypeState> {
  final IActivityRepository activityRepository;

  ActivitySelectTypeCubit({required this.activityRepository}) : super(ActivitySelectTypeState());

  final AccountService accountService = locator.get();

  @override
  Future<void> close() {
    locator.unregister<ActivitySelectTypeCubit>();
    return super.close();
  }

  @override
  void onInit() {
    locator.registerSingleton(this);
    super.onInit();
  }

  void updateActivityType(ActivityType type) {
    emit(state.copyWith(activityType: type));
  }
}
