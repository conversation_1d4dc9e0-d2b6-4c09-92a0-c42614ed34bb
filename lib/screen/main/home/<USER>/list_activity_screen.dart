import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/list_activity_cubit.dart';
import 'package:family_app/screen/main/home/<USER>/list_activity_state.dart';
import 'package:family_app/screen/main/home/<USER>/home_card.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/round_item_view.dart';
import 'package:flutter/material.dart';

@RoutePage()
class ListActivityPage extends BaseBlocProvider<ListActivityState, ListActivityCubit> {
  const ListActivityPage({super.key});

  @override
  Widget buildPage() => const ListActivityView();

  @override
  ListActivityCubit createCubit() => ListActivityCubit(
        activityRepository: locator.get(),
        mainCubit: locator.get(),
      );
}

class ListActivityView extends StatefulWidget {
  const ListActivityView({super.key});

  @override
  State<ListActivityView> createState() => _ListActivityViewState();
}

class _ListActivityViewState extends BaseBlocPageState<ListActivityView, ListActivityState, ListActivityCubit> {
  @override
  bool? get isBottomSafeArea => false;

  @override
  Widget buildAppBar(BuildContext context, ListActivityCubit cubit, ListActivityState state) {
    return AppBarCustom(
      title: LocaleKeys.activity_text.tr(),
      actions: [
        GestureDetector(
          onTap: () async {
            if (isViewer) {
              showSimpleToast(LocaleKeys.viewer_not_permission_activity.tr());
              return;
            }
            final result = await context.router.push(UpsertActivityRoute());
            if (result is ActivityModel) {
              cubit.updateActivity(result);
            }
          },
          behavior: HitTestBehavior.opaque,
          child: RoundItemView(child: Assets.images.add.image(width: 16, height: 16)),
        )
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, ListActivityCubit cubit, ListActivityState state) {
    return state.activityList.isNotEmpty || state.endActivityList.isNotEmpty
        ? _buildActivityView(cubit, state)
        : _buildEmptyView();
  }

  Widget _buildActivityView(ListActivityCubit cubit, ListActivityState state) {
    return RefreshIndicator(
      onRefresh: cubit.fetchActivity,
      child: ListView(
        padding: padding(all: 16),
        children: [
          ...state.activityList.map((e) {
            return Column(
              children: [
                _buildModeView(
                  highlightText: e.type?.name ?? '',
                  roundColor: e.color.toColor,
                  title: 'In my trip : ${e.name}',
                  content: e.description ?? '',
                ),
                SizedBox(height: 12.h),
                _buildActivityItem(e),
              ],
            );
          }),
          if (state.endActivityList.isNotEmpty)
            Padding(
              padding: padding(top: 20, bottom: 15),
              child: Row(
                children: [
                  Flexible(
                    child: Container(
                      height: 1.h,
                      width: double.infinity,
                      color: appTheme.grayF6Color,
                    ),
                  ),
                  Padding(
                    padding: padding(horizontal: 12),
                    child: Text(
                      LocaleKeys.ended_activities_text.tr(),
                      style: AppStyle.regular12(color: appTheme.fadeTextColor),
                    ),
                  ),
                  Flexible(
                    child: Container(
                      height: 1.h,
                      width: double.infinity,
                      color: appTheme.grayF6Color,
                    ),
                  ),
                ],
              ),
            ),
          ...state.endActivityList.map(_buildActivityItem)
        ],
      ),
    );
  }

  Widget _buildEmptyView() {
    return Padding(
      padding: padding(horizontal: 16, vertical: 15),
      child: Column(
        children: [
          SizedBox(
              width: double.infinity,
              height: 146,
              child: Stack(children: [
                Positioned(
                  top: 0,
                  right: 15,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Assets.images.tapHere.image(width: 72, height: 61),
                      const SizedBox(height: 7),
                      Text(LocaleKeys.tap_here_to_create_text.tr(), style: AppStyle.regular14())
                    ],
                  ),
                )
              ])),
          Assets.images.emptyActivity.image(width: 198, height: 156),
          const SizedBox(height: 18),
          Text(LocaleKeys.no_activity_yet_text.tr(), style: AppStyle.regular14(color: appTheme.fadeTextColor)),
        ],
      ),
    );
  }

  Widget _buildActivityItem(ActivityModel model) {
    Color backgroundColor = model.toDate?.toLocalDT.isBeforeToday ?? false ? appTheme.grayA8Color : model.color.toColor;
    return Padding(
      padding: padding(bottom: 12),
      child: GestureDetector(
        onTap: () => context.pushRoute(DetailActivityRoute(parameter: DetailActivityParameter(activity: model))),
        child: HomeCard(title: model.name ?? '', backgroundColor: backgroundColor, emptyTitle: model.description ?? ''),
      ),
    );
  }

  Widget _buildModeView({
    Color? roundColor,
    String highlightText = '',
    String title = '',
    String content = '',
  }) {
    return RoundItemView(
      viewPadding: padding(top: 8, bottom: 9, horizontal: 12),
      child: Row(
        children: [
          RoundItemView(viewSize: 40, text: highlightText, backgroundColor: roundColor),
          const SizedBox(width: 12),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: AppStyle.normal14()),
              const SizedBox(height: 5),
              Text(content, style: AppStyle.regular12(color: appTheme.fadeTextColor))
            ],
          ))
        ],
      ),
    );
  }
}
