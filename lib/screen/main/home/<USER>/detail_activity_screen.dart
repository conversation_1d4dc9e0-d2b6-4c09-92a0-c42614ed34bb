import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_state.dart';
import 'package:family_app/screen/main/home/<USER>/upsert_activity_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/line_item_selection_view.dart';
import 'package:family_app/widget/member_horizontal_view.dart';
import 'package:family_app/widget/round_item_view.dart';
import 'package:flutter/material.dart';

import 'detail_activity_cubit.dart';

@RoutePage()
class DetailActivityPage extends BaseBlocProvider<DetailActivityState, DetailActivityCubit> {
  const DetailActivityPage({required this.parameter, super.key});

  final DetailActivityParameter parameter;

  @override
  Widget buildPage() => const DetailActivityView();

  @override
  DetailActivityCubit createCubit() => DetailActivityCubit(
        parameter: parameter,
        activityRepository: locator.get(),
        eventRepository: locator.get(),
      );
}

class DetailActivityView extends StatefulWidget {
  const DetailActivityView({super.key});

  @override
  State<DetailActivityView> createState() => _DetailActivityViewState();
}

class _DetailActivityViewState extends BaseBlocPageState<DetailActivityView, DetailActivityState, DetailActivityCubit> {
  @override
  Color get backgroundColor => appTheme.background;

  @override
  String get title => LocaleKeys.activity.tr();

  @override
  bool listenWhen(DetailActivityState previous, DetailActivityState current) {
    if (previous.state != current.state) {
      if (current.state == DetailState.delete) {
        showSimpleToast(LocaleKeys.delete_activity_success.tr());
        context.maybePop();
      } else if (current.state == DetailState.notFound) {
        showSimpleToast(LocaleKeys.no_activity_yet_text.tr());
        context.maybePop();
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  List<Widget>? appBarActions(DetailActivityCubit cubit, DetailActivityState state) {
    if (isViewer) return [];
    return [
      GestureDetector(
        onTap: () {
          DialogUtils.showDeleteDialog(
            context,
            title: LocaleKeys.delete_activity.tr(),
            content: LocaleKeys.activity_content_delete.tr(),
            confirmText: LocaleKeys.confirm.tr(),
            onConfirm: () async {
              await cubit.onDeleteActivity();
              context.back();
            },
          );
        },
        child: RoundItemView(
          child: ImageAssetCustom(
            imagePath: Assets.images.trash.path,
            size: 18,
            color: appTheme.redColor,
          ),
        ),
      ),
      const SizedBox(width: 12),
      GestureDetector(
        onTap: () async {
          final result = await context
              .pushRoute(UpsertActivityRoute(parameter: UpsertActivityParameter(activityModels: state.activity)));
          if (result != null && result is ActivityModel) {
            cubit.updateActivity();
          }
        },
        child: RoundItemView(child: ImageAssetCustom(imagePath: Assets.images.iconEdit.path, size: 16)),
      ),
    ];
  }

  @override
  Widget buildBody(BuildContext context, DetailActivityCubit cubit, DetailActivityState state) {
    return Column(
      children: [
        ColoredBox(
          color: state.activity?.color?.toColor ?? appTheme.redColor,
          child: Stack(
            children: [
              Padding(
                padding: padding(vertical: 14, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(state.activity?.name ?? '', style: AppStyle.medium20(color: appTheme.whiteText)),
                    SizedBox(height: 4.h),
                    Text(state.activity?.description ?? '', style: AppStyle.regular14(color: appTheme.whiteText)),
                    SizedBox(height: 19.h),
                    Row(
                      children: [
                        ImageAssetCustom(
                          imagePath: Assets.images.calendar.path,
                          color: appTheme.whiteText,
                          width: 16.w,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          state.activity?.fromDate?.MMMDDyyyyTime ?? '',
                          style: AppStyle.regular14(color: appTheme.whiteText),
                        ),
                        Text(' ~ ', style: AppStyle.regular14(color: appTheme.whiteText)),
                        Text(
                          state.activity?.toDate?.MMMDDyyyyTime ?? '',
                          style: AppStyle.regular14(color: appTheme.whiteText),
                        ),
                        const Spacer(),
                        ImageAssetCustom(imagePath: Assets.images.trip.path, color: appTheme.whiteText, width: 16.w),
                        SizedBox(width: 8.w),
                        Text(state.activity?.type?.name ?? '', style: AppStyle.regular14(color: appTheme.whiteText)),
                        SizedBox(width: 32.w),
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                right: -45,
                bottom: -108,
                child: Assets.images.circleBackground.image(width: 222, height: 183, fit: BoxFit.cover),
              ),
            ],
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: padding(all: 13),
          color: appTheme.whiteText,
          child: Row(
            children: [
              ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w),
              SizedBox(width: 10.w),
              Expanded(
                child: MemberHorizontalView(
                  createBy: state.activity?.createdBy ?? '',
                  accounts: state.activity?.includedMembers ?? <Account>[],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12.h),
        LineItemSelectionView(
          imagePath: Assets.images.calendarEvent.path,
          title: LocaleKeys.includingEvents.tr(),
          paddingHorizontal: 16,
          showArrowDown: false,
          onTap: () => BottomSheetUtils.showListEvent(context,
              date: state.activity?.fromDate?.toLocalDT ?? DateTime.now(),
              to: state.activity?.toDate?.toLocalDT,
              listEvent: state.activity?.includedEvents ?? []),
          content: (state.activity != null && state.activity!.includedEvents != null)
              ? state.activity!.includedEvents!.length.toString()
              : '0',
        ),
        SizedBox(height: 12.h),
        LineItemSelectionView(
          imagePath: Assets.images.list.path,
          title: LocaleKeys.includingLists.tr(),
          paddingHorizontal: 16,
          content: (state.activity != null && state.activity!.includedLists != null)
              ? state.activity!.includedLists!.length.toString()
              : LocaleKeys.none_text.tr(),
          showArrowDown: false,
        ),
        if (state.logs.isNotEmpty) ...[
          SizedBox(height: 12.h),
          Container(
            padding: padding(horizontal: 16, top: 10, bottom: 18),
            width: double.infinity,
            color: appTheme.whiteText,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(LocaleKeys.log_of_list.tr(), style: AppStyle.regular14()),
                ...state.logs.map((e) {
                  final day = e.createdAt?.toLocalDT.shortDay ?? '';
                  return Text(
                    '$day ${e.actionByLog?.fullName ?? ''} ${e.actionName ?? ''}',
                    style: AppStyle.regular12(color: appTheme.fadeTextColor),
                  );
                }).toList()
              ],
            ),
          ),
        ],
      ],
    );
  }
}
