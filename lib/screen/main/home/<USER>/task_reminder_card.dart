import 'package:auto_route/auto_route.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/daily_tasks/widget/task_card.dart';
import 'package:family_app/utils/assets/shadow_util.dart';
import 'package:flutter/material.dart';
import 'package:family_app/screen/main/check_list/utils/utils.dart';

class TaskReminderCard extends StatelessWidget {
  final HomeState state;
  final HomeCubit cubit;

  const TaskReminderCard({
    Key? key,
    required this.state,
    required this.cubit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final userName = cubit.accountService.account?.firstName ?? 'User';

    final checklistsWithDueTodayTasks = ChecklistUtils.getChecklistsWithTasksDueToday(state.listItems, DateTime.now());
    final dueTodayTaskCount = checklistsWithDueTodayTasks.values.fold<int>(0, (sum, tasks) => sum + tasks.length);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context, userName, dueTodayTaskCount),
        if (checklistsWithDueTodayTasks.isNotEmpty) _buildTaskCarousel(context, checklistsWithDueTodayTasks),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, String userName, int taskCount) {
    return Padding(
      padding: paddingV2(left: 16, right: 16, top: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.good_morning.tr(namedArgs: {'field': userName}),
            style: AppStyle.regular16V2(color: appTheme.grayV2),
          ),
          const SizedBox(height: 4),
          RichText(
            text: TextSpan(
              style: AppStyle.bold28V2(height: 1.2),
              children: [
                const TextSpan(text: 'You have '),
                WidgetSpan(
                  alignment: PlaceholderAlignment.baseline,
                  baseline: TextBaseline.alphabetic,
                  child: _buildGradientText('$taskCount'),
                ),
                const TextSpan(text: ' tasks in this family today!'),
              ],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () async {
                  context.router.push(const DailyTasksRoute());
                },
                child: Text(
                  LocaleKeys.view_all.tr(),
                  style: AppStyle.bold14V2(color: appTheme.grayV2),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGradientText(String text) {
    return ShaderMask(
      shaderCallback: (bounds) => LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          appTheme.secondaryColorV2,
          appTheme.primaryColorV2,
        ],
        stops: [0.0, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
      blendMode: BlendMode.srcIn,
      child: Text(
        text,
        style: AppStyle.bold28V2(height: 1.2, fontWeight: FontWeight.w900),
      ),
    );
  }

  Widget _buildAddTaskButton(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: () => cubit.onCreateListItem(context, false, null),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.15,
            decoration: BoxDecoration(
              color: appTheme.primaryColorV2.withAlpha(31),
              borderRadius: BorderRadius.circular(16),
              boxShadow: ShadowUtil.backgroundShadow,
            ),
            child: Center(
              child: Icon(
                Icons.add,
                color: appTheme.primaryColorV2,
                size: 32,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTaskCarousel(
    BuildContext context,
    Map<ListItem, List<Item>> checklistMap,
  ) {
    final checklists = checklistMap.keys.toList();
    const maxTasksToShow = 2;
    const maxLinesTaskName = 2;
    const double defaultAspectRatio = 1.35;
    const double maxAspectRatio = 1.25;

    double calculateAspectRatio(Map<ListItem, List<Item>> checklistMap) {
      if (checklistMap.isEmpty) return defaultAspectRatio;
      final maxTasks = checklistMap.values.map((tasks) => tasks.length).fold(0, (a, b) => a > b ? a : b);
      return maxTasks > maxTasksToShow ? maxAspectRatio : defaultAspectRatio;
    }

    final aspectRatio = calculateAspectRatio(checklistMap);

    return CarouselSlider.builder(
      itemCount: checklists.length + 1,
      itemBuilder: (context, index, realIndex) {
        final isLast = index == checklists.length;
        final isFirst = index == 0;
        if (isLast) {
          // Add Task button
          return Padding(
            padding: const EdgeInsets.only(left: 6, right: 0, top: 16, bottom: 20),
            child: _buildAddTaskButton(context),
          );
        }
        final list = checklists[index];
        final dueTodayTasks = checklistMap[list]!;

        return Padding(
          padding: EdgeInsets.only(left: isFirst ? 16 : 6, right: isLast ? 0 : 6, top: 16, bottom: 20),
          child: ConstrainedBox(
            constraints: const BoxConstraints(minWidth: 224),
            child: TaskCard(
              checklist: list,
              tasks: dueTodayTasks,
              onTap: () => cubit.onTapCheckListItem(context, list),
              iconSize: 16,
              enableSpacer: true,
              maxTasksToShow: maxTasksToShow,
              maxLinesTaskName: maxLinesTaskName,
            ),
          ),
        );
      },
      options: CarouselOptions(
        aspectRatio: aspectRatio,
        viewportFraction: 0.625,
        enableInfiniteScroll: false,
        autoPlay: false,
        enlargeCenterPage: true,
        enlargeFactor: 0,
        initialPage: 0,
        padEnds: false,
        onPageChanged: (index, reason) {
          cubit.todayIndex.value = index;
        },
      ),
    );
  }
}
