import 'package:family_app/base/widget/cubit/base_state.dart';

enum ActivityType { unknown, trip, birthdayRegistry }
class ActivitySelectTypeState extends BaseState {
  bool loading = false;
  final ActivityType activityType;

  ActivitySelectTypeState({
    this.loading = false,
    this.activityType = ActivityType.unknown,
  });

  @override
  List<Object?> get props => [loading, activityType];

  ActivitySelectTypeState copyWith({
    bool? loading,
    ActivityType? activityType,
  }) {
    return ActivitySelectTypeState(
      loading: loading ?? this.loading,
      activityType: activityType ?? this.activityType,
    );
  }
}
