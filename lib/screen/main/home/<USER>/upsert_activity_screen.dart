import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/home/<USER>/upsert_activity_parameter.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/bottom_sheet/date_picker_bottom_sheet.dart';
import 'package:family_app/widget/bottom_sheet/select_event_bottom_sheet.dart';
import 'package:family_app/widget/bottom_sheet/select_list_bts.dart';
import 'package:family_app/widget/bottom_sheet/select_member_bts.dart';
import 'package:family_app/widget/bottom_sheet/view_remove_event_bts.dart';
import 'package:family_app/widget/line_item_selection_view.dart';
import 'package:family_app/widget/list_color_selection.dart';
import 'package:family_app/widget/round_item_view.dart';
import 'package:family_app/widget/textfield/row_text_field_view.dart';
import 'package:flutter/material.dart';

import 'upsert_activity_cubit.dart';
import 'upsert_activity_state.dart';

@RoutePage()
class UpsertActivityPage extends BaseBlocProvider<UpsertActivityState, UpsertActivityCubit> {
  const UpsertActivityPage({super.key, this.parameter});

  final UpsertActivityParameter? parameter;

  @override
  Widget buildPage() => UpsertActivityView(parameter: parameter);

  @override
  UpsertActivityCubit createCubit() => UpsertActivityCubit(
        activityUsecase: locator.get(),
        accountService: locator.get(),
        parameter: parameter,
        mainCubit: locator.get(),
      );
}

class UpsertActivityView extends StatefulWidget {
  const UpsertActivityView({super.key, this.parameter});

  final UpsertActivityParameter? parameter;

  @override
  State<UpsertActivityView> createState() => _UpsertActivityViewState();
}

class _UpsertActivityViewState extends BaseBlocPageState<UpsertActivityView, UpsertActivityState, UpsertActivityCubit> {
  final MainCubit mainCubit = locator.get();

  @override
  String get title => LocaleKeys.activity.tr();

  @override
  bool listenWhen(UpsertActivityState previous, UpsertActivityState current) {
    if (current.activity != null && previous.activity == null) {
      mainCubit.upsertActivity(current.activity);
      context.maybePop(current.activity);
    }

    return super.listenWhen(previous, current);
  }

  @override
  List<Widget>? appBarActions(UpsertActivityCubit cubit, UpsertActivityState state) {
    return [
      GestureDetector(
        onTap: cubit.createActivity,
        child: RoundItemView(text: LocaleKeys.save.tr(), textColor: appTheme.primaryColor),
      )
    ];
  }

  @override
  Widget buildBody(BuildContext context, UpsertActivityCubit cubit, UpsertActivityState state) {
    // final isEditActivity = widget.parameter?.activityModels?.uuid?.isNotEmpty == true;
    return ListView(
      padding: padding(),
      children: [
        _buildSelectionListType(cubit, state),
        SizedBox(height: 12.h),
        RowTextFieldView(
          handler: cubit.title,
          bottomSizedBox: 0,
          formHandler: cubit.formHandler,
        ),
        RowTextFieldView(
          handler: cubit.description,
          topPadding: 17,
          bottomSizedBox: 28,
          formHandler: cubit.formHandler,
        ),
        ColoredBox(
          color: appTheme.whiteText,
          child: Padding(
            padding: padding(horizontal: 16, bottom: 20),
            child: ListColorSelection(selectedColor: state.selectedColor, onSelectColor: cubit.updateSelectedColor),
          ),
        ),
        _buildDivider(),
        LineItemSelectionView(
          imagePath: Assets.images.person.path,
          title: LocaleKeys.member.tr(),
          paddingHorizontal: 16,
          content: state.memberList.isNotEmpty
              ? state.memberList.length == accountService.memberInFamily.length
                  ? LocaleKeys.everyone.tr()
                  : '${state.memberList.length}'
              : '0',
          onTap: () => SelectMemberBts.show(
            context,
            selectedMembers: state.memberList.map((member) => member.familyMemberUuid ?? '').toList(),
            onSelected: cubit.updateSelectedMember,
          ),
        ),
        _buildDivider(),
        LineItemSelectionView(
          imagePath: Assets.images.calendarEvent.path,
          title: LocaleKeys.includingEvents.tr(),
          paddingHorizontal: 16,
          content: state.eventList.isNotEmpty ? '${state.eventList.length}' : LocaleKeys.none.tr(),
          onTap: () {
            if (state.eventList.isNotEmpty) {
              ViewRemoveEventBts.show(
                context,
                selectList: state.eventList,
                updateEvents: cubit.updateSelectedEvent,
              );
            } else {
              SelectEventBottomSheet.show(
                context,
                selectList: state.eventList,
                onSelected: cubit.updateSelectedEvent,
              );
            }
          },
        ),
        _buildDivider(),
        LineItemSelectionView(
          imagePath: Assets.images.plan.path,
          title: LocaleKeys.includingLists.tr(),
          paddingHorizontal: 16,
          content: state.listItems.isNotEmpty ? '${state.listItems.length}' : LocaleKeys.none.tr(),
          onTap: () => SelectListBts.show(
            context,
            selectList: state.listItems,
            onSelected: cubit.updateListItem,
          ),
        ),
        _buildDivider(),
        ColoredBox(
          color: appTheme.whiteText,
          child: Row(
            children: [
              Expanded(
                child: LineItemSelectionView(
                  imagePath: Assets.icons.calendarOff.path,
                  onTap: () => DatePickerBottomSheet.show(context,
                      initialDate: state.startTime,
                      title: LocaleKeys.start_date.tr(),
                      onSelected: cubit.updateStartDate),
                  title: state.startTime != null ? state.startTime?.timeMMMddyyyy ?? '' : LocaleKeys.start_date.tr(),
                  colorText: state.startTime != null ? appTheme.blackText : null,
                  paddingHorizontal: 16,
                  showArrowDown: false,
                ),
              ),
              const SizedBox(width: 7),
              Text(LocaleKeys.to.tr(), style: AppStyle.medium14(color: appTheme.fadeTextColor)),
              const SizedBox(width: 17),
              Expanded(
                child: LineItemSelectionView(
                  title: state.endTime != null ? state.endTime?.timeMMMddyyyy ?? '' : LocaleKeys.end_date.tr(),
                  paddingHorizontal: 16,
                  showArrowDown: false,
                  onTap: () => DatePickerBottomSheet.show(context,
                      initialDate: state.endTime, title: LocaleKeys.end_date.tr(), onSelected: cubit.updateEndDate),
                  colorText: state.endTime != null ? appTheme.blackText : null,
                ),
              ),
            ],
          ),
        ),
        _buildDivider(),
        // _buildAddItem(cubit, state),
      ],
    );
  }

  Widget _buildSelectionListType(UpsertActivityCubit cubit, UpsertActivityState state) {
    return ColoredBox(
      color: appTheme.whiteText,
      child: DropdownButton<ListType>(
        value: state.type,
        underline: const SizedBox(),
        elevation: 3,
        isExpanded: true,
        icon: Icon(Icons.keyboard_arrow_down, color: appTheme.blackColor),
        alignment: Alignment.bottomCenter,
        dropdownColor: appTheme.whiteText,
        borderRadius: BorderRadius.circular(12),
        style: AppStyle.regular14(),
        padding: padding(horizontal: 16, bottom: 4),
        items: [
          DropdownMenuItem(
            onTap: () => cubit.onUpdateListType(ListType.Trip),
            value: ListType.Trip,
            child: Text(LocaleKeys.trip_text.tr(), style: AppStyle.regular14()),
          ),
        ],
        onChanged: (value) {},
      ),
    );
  }

  Widget _buildDivider() => const SizedBox(height: 12);
}
