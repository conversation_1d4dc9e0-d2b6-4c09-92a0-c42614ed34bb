// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:flutter/material.dart';

class UpsertActivityState extends BaseState {
  final DateTime? startTime;
  final DateTime? endTime;
  final Color? selectedColor;
  final List<Account> memberList;
  final List<EventModels> eventList;
  final List<ListItem> listItems;
  final ActivityModel? activity;
  final ListType type;
  final List<CheckListItem> items;
  final int currentIndexEdit;

  UpsertActivityState({
    this.startTime,
    this.endTime,
    this.selectedColor,
    this.memberList = const [],
    this.eventList = const [],
    this.listItems = const [],
    this.activity,
    this.type = ListType.Trip,
    this.items = const [],
    this.currentIndexEdit = -1,
  });

  @override
  List<Object?> get props => [
        startTime,
        endTime,
        selectedColor,
        memberList,
        eventList,
        activity,
        listItems,
        type,
        items,
        currentIndexEdit,
      ];

  UpsertActivityState copyWith({
    DateTime? startTime,
    DateTime? endTime,
    Color? selectedColor,
    List<Account>? memberList,
    List<EventModels>? eventList,
    List<ListItem>? listItems,
    ActivityModel? activity,
    ListType? type,
    List<CheckListItem>? items,
    int? currentIndexEdit,
  }) {
    return UpsertActivityState(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      selectedColor: selectedColor ?? this.selectedColor,
      memberList: memberList ?? this.memberList,
      eventList: eventList ?? this.eventList,
      listItems: listItems ?? this.listItems,
      activity: activity ?? this.activity,
      type: type ?? this.type,
      items: items ?? this.items,
      currentIndexEdit: currentIndexEdit ?? this.currentIndexEdit,
    );
  }
}
