import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/screen/main/home/<USER>/list_activity_state.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/loading.dart';

class ListActivityCubit extends BaseCubit<ListActivityState> {
  final MainCubit mainCubit;
  final IActivityRepository activityRepository;

  ListActivityCubit({required this.activityRepository, required this.mainCubit}) : super(ListActivityState());

  final AccountService accountService = locator.get();

  @override
  void onInit() {
    super.onInit();
    fetchActivity();
  }

  Future<void> fetchActivity() async {
    try {
      showLoading();
      final result = <ActivityModel>[...mainCubit.state.activityList];
      // final result = await activityRepository.getAllActivities(accountService.familyId);

      List<ActivityModel> activeActivities = [];
      List<ActivityModel> endedActivities = [];

      for (var e in result) {
        // final from = e.fromDate?.toLocalDT ?? DateTime.now();
        final to = e.toDate?.toLocalDT ?? DateTime.now();
        if ((to.isAfterToday || to.isToday)) {
          activeActivities.add(e);
        } else {
          endedActivities.add(e);
        }
      }
      emit(state.copyWith(activityList: activeActivities, endActivityList: endedActivities));
    } catch (e) {
      print(e);
    } finally {
      dismissLoading();
    }
  }

  void updateActivity(ActivityModel? activity) {
    if (activity == null) return;
    List<ActivityModel> activeActivities = [
      activity,
      ...state.activityList,
    ];

    emit(state.copyWith(activityList: activeActivities));
  }
}
