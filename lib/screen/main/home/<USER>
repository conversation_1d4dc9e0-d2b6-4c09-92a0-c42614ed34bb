import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dartx/dartx_io.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/provider/loading_provider.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/assets/shadow_util.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:family_app/config/service/notification/notification_service.dart';

import '../../../widget/image/circle_avatar_custom.dart';
import 'widgets/task_reminder_card.dart';

@RoutePage()
class HomePage extends BaseBlocProvider<HomeState, HomeCubit> {
  const HomePage({super.key});

  @override
  Widget buildPage() => const HomeView();

  @override
  HomeCubit createCubit() => HomeCubit(
        categoryRepository: locator.get(),
        accountService: locator.get(),
        activityRepository: locator.get(),
        socketService: locator.get(),
        notificationService: locator.get(),
        familyRepository: locator.get(),
        signOutUsecase: locator.get(),
        eventRepository: locator.get(),
        threadRepository: locator.get(),
        eventService: locator.get(),
        homeRepository: HomeRepository(
          categoryRepository: locator.get(),
          familyRepository: locator.get(),
          activityRepository: locator.get(),
          listRepository: locator.get(),
          threadRepository: locator.get(),
          eventRepository: locator.get(),
          eventService: locator.get(),
        ),
      );
}

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState
    extends BaseBlocPageStateV2<HomeView, HomeState, HomeCubit> {
  static bool _hasLoadedOnce = false;

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  // UI Constants
  static const double _cardBorderRadius = 16.0;
  static const double _memorySectionHeight = 200.0;
  static const double _activityImageHeight = 160.0;
  static const double _sectionSpacing = 8.0;
  static const double _emptyStateHeight = 120.0;
  static const int _maxChecklistItemsToShow = 3;

  @override
  void initState() {
    super.initState();
    if (!_hasLoadedOnce) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final loadingProvider = context.read<LoadingProvider>();
        loadingProvider.show();
        try {
          final notificationService = locator.get<NotificationService>();
          await notificationService.handleDeferredNotifications();
          await context.read<HomeCubit>().onRefresh();
        } finally {
          loadingProvider.hide();
          _hasLoadedOnce = true;
        }
      });
    }
  }

  @override
  bool listenWhen(HomeState previous, HomeState current) {
    if (previous.status != current.status) {
      if (current.status == HomeStatus.logoutSuccess) {
        context.router.replaceAll([const SignInRoute()]);
        showSimpleToast(LocaleKeys.logout_successful_text.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, HomeCubit cubit, HomeState state) {
    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: () async {
            await cubit.onRefresh();
          },
          child: ListView(
            padding:
                EdgeInsets.only(top: context.top, bottom: context.bottom),
            children: [
              _buildProfileSection(context, cubit, state),
              _buildTaskSection(context, cubit, state),
              _buildMemorySection(context, cubit, state),
              _buildChecklistSection(context, cubit, state),
              _buildActivityAndMessageSection(context, cubit, state),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfileSection(
      BuildContext context, HomeCubit cubit, HomeState state) {
    return Column(
      children: [
        _buildProfileRow(cubit),
        SizedBox(height: _sectionSpacing.h2),
      ],
    );
  }

  Widget _buildTaskSection(BuildContext context, HomeCubit cubit, HomeState state) {
    return _buildTaskReminderCard(state, cubit);
  }

  Widget _buildMemorySection(
      BuildContext context, HomeCubit cubit, HomeState state) {
    return Padding(
      padding: paddingV2(horizontal: 16, top: 16, bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(LocaleKeys.memories.tr()),
          SizedBox(height: _sectionSpacing.h2),
          _buildMemoryCard(cubit, state),
        ],
      ),
    );
  }

  // ==================== CHECKLIST SECTION ====================

  /// Builds the complete checklist section including header and cards.
  /// Displays up to 3 checklist items directly, or shows all items in a scrollable list if more than 3.
  Widget _buildChecklistSection(
      BuildContext context, HomeCubit cubit, HomeState state) {
    return Container(
      // Add margin to prevent shadow clipping at the parent level
      margin: paddingV2(horizontal: 16, vertical: 14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(LocaleKeys.checklists.tr()),
          SizedBox(height: _sectionSpacing.h2),
          Container(
            clipBehavior: Clip.none,
            child: _buildChecklistCards(state),
          ),
        ],
      ),
    );
  }

  /// Creates a consistent section header with standard styling
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: AppStyle.medium17V2(),
    );
  }

  // ==================== ACTIVITY SECTION ====================

  Widget _buildActivityAndMessageSection(
      BuildContext context, HomeCubit cubit, HomeState state) {
    return Padding(
      padding: paddingV2(horizontal: 16, vertical: 14),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(LocaleKeys.upcoming_trip.tr()),
          SizedBox(height: _sectionSpacing.h2),
          _buildActivityCard(state),
        ],
      ),
    );
  }

  Widget _buildProfileRow(HomeCubit cubit) {
    final familyName = cubit.accountService.account?.activeFamilyName ?? '';
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () async {
              context.pushRoute(const FamilyListRoute());
            },
            borderRadius: BorderRadius.circular(16),
            child: cubit.accountService.account?.photoUrl?.isEmpty == true
                ? Container(
              padding: paddingV2(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      image: DecorationImage(
                        image: AssetImage(Assets.images.avatarFamily.path),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      familyName,
                      style: AppStyle.bold18(color: appTheme.blackText),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.chevron_right,
                    size: 24,
                    color: appTheme.blackText,
                  ),
                ],
              ),
            )
                : Container(
              padding: paddingV2(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    child:  CircleAvatarCustom(
                      borderWidth: 1.w,
                      size: 50.w,
                      imageUrl: cubit.accountService.account?.photoUrl ?? '',
                      borderColor: appTheme.borderColorV2,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      familyName,
                      style: AppStyle.bold18(color: appTheme.blackText),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.chevron_right,
                    size: 24,
                    color: appTheme.blackText,
                  ),
                ],
              ),
            )
          ),
        ),
        const SizedBox(width: 8),
        _buildChatButton(),
      ],
    );
  }

  //Bloc builder is used in the main buildBody already, now just get the params and use them
  Widget _buildTaskReminderCard(HomeState state, HomeCubit cubit) {
    return TaskReminderCard(state: state, cubit: cubit);
  }

  Widget _buildMemoryCard(HomeCubit cubit, HomeState state) {
    return InkWell(
      onTap: () async {
        await context.pushRoute(const MemoriesRoute());
      },
      child: Container(
        height: _memorySectionHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          boxShadow: ShadowUtil.cardShadow,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10.0),
          child: _buildMemoryImage(context, cubit, state),
        ),
      ),
    );
  }

  Widget _buildMemoryImage(
      BuildContext context, HomeCubit cubit, HomeState state) {
    if (state.latestMemory?.uuid == null) {
      return _buildEmptyMemory();
    }

    final allPhotoUrls = <String>[];
    for (final memory in state.allMemories ?? <dynamic>[]) {
      for (final file in memory.files ?? <dynamic>[]) {
        if (file.fileUrl != null) {
          allPhotoUrls.add(file.fileUrl!);
        }
      }
    }

    if (allPhotoUrls.isEmpty) return _buildEmptyMemory();

    return CarouselSlider.builder(
      itemCount: allPhotoUrls.length,
      itemBuilder: (context, index, realIndex) {
        return CachedNetworkImage(
          imageUrl: allPhotoUrls[index],
          height: _memorySectionHeight,
          width: double.infinity,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            height: _memorySectionHeight,
            width: double.infinity,
            color: appTheme.grayV2.withOpacity(0.3),
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: (context, url, error) => Container(
            height: _memorySectionHeight,
            width: double.infinity,
            color: appTheme.grayV2.withOpacity(0.3),
            child: const Icon(Icons.error, color: Colors.red),
          ),
        );
      },
      options: CarouselOptions(
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        height: _memorySectionHeight,
        enlargeCenterPage: false,
        viewportFraction: 1.0,
      ),
    );
  }

  Widget _buildEmptyMemory() {
    return Container(
      height: _memorySectionHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_cardBorderRadius),
        color: appTheme.whiteText,
      ),
      child: Center(
        child: Text(
          "Create your first memory now!",
          style: AppStyle.bold16(color: appTheme.blackText),
        ),
      ),
    );
  }

  Widget _buildActivityCard(HomeState state) {
    final latestActivity = state.activityList.firstOrNull;

    return InkWell(
      onTap: () => context.pushRoute(const ActivityHomeRoute()),
      borderRadius: BorderRadius.circular(_cardBorderRadius),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_cardBorderRadius),
          boxShadow: ShadowUtil.cardShadow,
        ),
        child: Column(
          children: [
            _buildActivityImage(latestActivity),
            _buildUpcomingTripContent(latestActivity),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityImage(ActivityModel? latestActivity) {
    return SizedBox(
      height: _activityImageHeight,
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(_cardBorderRadius),
          topRight: Radius.circular(_cardBorderRadius),
        ),
        child: CachedNetworkImage(
          imageUrl: latestActivity?.imagePath ?? '',
          width: double.infinity,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: appTheme.grayV2.withOpacity(0.3),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF87CEEB), // Sky blue
                  Color(0xFF4682B4), // Steel blue
                ],
              ),
            ),
            child: const Center(
              child: Icon(
                Icons.landscape,
                size: 60,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUpcomingTripContent(ActivityModel? latestActivity) {
    return Container(
      width: double.infinity,
      padding: paddingV2(all: 16),
      decoration: BoxDecoration(
        color: appTheme.whiteText,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(_cardBorderRadius),
          bottomRight: Radius.circular(_cardBorderRadius),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              latestActivity?.name ?? LocaleKeys.no_activity.tr(),
              style: AppStyle.medium20(color: appTheme.blackText),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(height: 3.h2),
          if (latestActivity != null && latestActivity.fromDate != null && latestActivity.toDate != null)
            Text(
              formatActivityDate(latestActivity.fromDate!, latestActivity.toDate!),
              style: AppStyle.regular12(color: appTheme.grayV2),
            ),
        ],
      ),
    );
  }

  String formatActivityDate(String fromDate, String toDate) {
    final startDate = fromDate.toDateTime().MMM_d_yyyy;
    final endDate = toDate.toDateTime().MMM_d_yyyy;
    
    return startDate == endDate ? startDate : '$startDate - $endDate';
  }

  Widget _buildChatButton() {
    return IconButton(
      onPressed: () => context.pushRoute(const ThreadRoute()),
      icon: SvgPicture.asset(
        Assets.icons.icChat.path,
        width: 24,
        height: 24,
      ),
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(),
    );
  }

  // ==================== CHECKLIST CARDS ====================

  /// Builds the checklist cards container with empty state or scrollable list of items
  Widget _buildChecklistCards(HomeState state) {
    if (state.listItems.isEmpty) {
      return _buildEmptyChecklistState();
    }

    // If 3 or fewer items, show them directly without scrolling
    if (state.listItems.length <= _maxChecklistItemsToShow) {
      return Column(
        children: _buildChecklistItems(state.listItems),
      );
    }

    // If more than 3 items, create a scrollable list with gradient overlay
    return SizedBox(
      height: 240,
      child: Stack(
        children: [
          // Direct ListView without any padding wrapper
          ListView.builder(
            padding: EdgeInsets.zero,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: state.listItems.length,
            itemBuilder: (context, index) {
              final listItem = state.listItems[index];
              return Container(
                margin: EdgeInsets.only(
                  bottom: index < state.listItems.length - 1 ? 8 : 4,
                ),
                child: _buildChecklistItemCard(listItem),
              );
            },
          ),
          // Gradient overlay for scrollable indication
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: IgnorePointer(
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      appTheme.backgroundV2.withOpacity(0),
                      appTheme.backgroundV2.withOpacity(0.9),
                    ],
                    stops: const [0.0, 1.0],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Creates an empty state widget for when no checklists exist
  Widget _buildEmptyChecklistState() {
    return Container(
      height: _emptyStateHeight,
      padding: paddingV2(horizontal: 16, vertical: 14),
      decoration: _buildCardDecoration(),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.checklist_outlined,
              size: 32,
              color: appTheme.grayV2,
            ),
            SizedBox(height: 8.h2),
            Text(
              "No checklists yet",
              style: AppStyle.regular14(color: appTheme.grayV2),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds individual checklist item widgets
  List<Widget> _buildChecklistItems(List<dynamic> listItems) {
    return listItems.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      return Padding(
        padding: EdgeInsets.only(
          bottom: index < listItems.length - 1 ? 8 : 0, // No padding for last item
        ),
        child: _buildChecklistItemCard(item),
      );
    }).toList();
  }

  Widget _buildChecklistItemCard(dynamic listItem) {
    final itemCount = listItem.items?.length ?? 0;
    final checklistName = listItem.name ?? LocaleKeys.no_description.tr();

    return Material(
      elevation: 2,
      borderRadius: BorderRadius.circular(_cardBorderRadius),
      shadowColor: Colors.black.withOpacity(0.1),
      child: InkWell(
        onTap: _navigateToChecklistPage,
        borderRadius: BorderRadius.circular(_cardBorderRadius),
        child: Container(
          padding: paddingV2(horizontal: 24, vertical: 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(_cardBorderRadius),
            color: appTheme.whiteText,
          ),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                margin: const EdgeInsets.only(right: 5),
                child: _getIconForListType(_getListTypeFromItem(listItem)),
              ),
              Expanded(
                child: Text(
                  checklistName,
                  style: AppStyle.regular16V2(color: appTheme.blackText),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '$itemCount ${LocaleKeys.items.tr()}',
                style: AppStyle.regular14V2(color: appTheme.grayV2),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // ==================== HELPER METHODS ====================

  ListType _getListTypeFromItem(dynamic listItem) {
    final categoryId = listItem?.categoryId as String?;
    return switch (categoryId) {
      'shopping' => ListType.Shopping,
      'todo' => ListType.Todo,
      'trip' => ListType.Trip,
      _ => ListType.Other,
    };
  }

  Widget _getIconForListType(ListType listType) {
    final emoji = switch (listType) {
      ListType.Shopping => '🛒',
      ListType.Todo => '✅',
      ListType.Trip => '✈️',
      ListType.Other => '🫧',
    };

    return Text(emoji, style: const TextStyle(fontSize: 16));
  }

  /// Creates a consistent card decoration with optional border
  BoxDecoration _buildCardDecoration({Border? border}) {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(_cardBorderRadius),
      color: appTheme.whiteText,
      boxShadow: ShadowUtil.itemCardShadow,
      border: border,
    );
  }

  void _navigateToChecklistPage() => context.pushRoute(const CheckListRoute());
}
