import 'dart:async';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/category/icategory_repository.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HomeRepository {
  final ICategoryRepository categoryRepository;
  final IActivityRepository activityRepository;
  final IFamilyRepository familyRepository;
  final IListRepository listRepository;
  final IEventRepository eventRepository;
  final IThreadRepository threadRepository;
  final EventService eventService;

  HomeRepository({
    required this.categoryRepository,
    required this.activityRepository,
    required this.familyRepository,
    required this.listRepository,
    required this.eventRepository,
    required this.threadRepository,
    required this.eventService,
  });

  Future<HomeDataResult> fetchHomeData(String familyId) async {
    try {
      // Parallel API calls for better performance
      final results = await Future.wait([
        _fetchLists(familyId),
        _fetchActivities(familyId),
        _fetchMemories(familyId),
        _fetchEvents(familyId),
        _fetchMessages(familyId),
        _fetchTasksDueToday(familyId),
      ], eagerError: false);

      return HomeDataResult(
        lists: results[0] as List<ListItem>,
        activities: results[1] as List<ActivityModel>,
        memories: results[2] as MemoryResult,
        messages: results[4] as List<ThreadFamily>,
        tasksDueToday: results[5] as List<Item>,
        error: null,
      );
    } catch (e) {
      AppLogger.e('Error fetching home data: $e');
      return HomeDataResult.error(e.toString());
    }
  }

  Future<List<ListItem>> _fetchLists(String familyId) async {
    try {
      AppLogger.d('HomeRepository: Fetching lists for family $familyId');
      final lists = await listRepository.getListByFamilyId(familyId);
      AppLogger.d('HomeRepository: Received ${lists.length} lists');
      // for (final list in lists) {
      //   AppLogger.d(
      //       '  List: ${list.name} (${list.uuid}) - Category: ${list.categoryId}');
      // }
      return lists;
    } catch (e) {
      AppLogger.e('Error fetching lists: $e');
      return <ListItem>[];
    }
  }

  Future<List<ActivityModel>> _fetchActivities(String familyId) async {
    try {
      AppLogger.d('HomeRepository: Fetching activities for family $familyId');
      final activities =
          await activityRepository.getFamilyActivities(familyId, limit: 10);
      AppLogger.d(
          'HomeRepository: Fetched ${activities.length} raw activities');

      // Process activities with images using enhanced multi-API provider
      for (var activity in activities) {
        if (activity.imagePath == null) {
          if (activity.activityType == 'birthday_registry') {
            activity.imagePath = activity.getImagePath();
          } else {
            try {
              // Create location bias from GPS coordinates if available
              LatLng? activityLocation = activity.gpsCoord;

              if (activity.city != null) {
                // Use enhanced provider with location bias for better results
                activity.imagePath = await provider
                    .fetchImageUrl(activity.city!, location: activityLocation);
              } else if (activity.country != null) {
                activity.imagePath = await provider.fetchImageUrl(
                    activity.country!,
                    location: activityLocation);
              }
            } catch (e) {
              AppLogger.e(
                  'Error fetching image for activity ${activity.name}: $e');
            }
          }
        }

        // Calculate trip duration
        if (activity.fromDate != null && activity.toDate != null) {
          activity.trip_duration = activity.toDate!.toLocalDT
                  .difference(activity.fromDate!.toLocalDT)
                  .inDays +
              1;
        }
      }

      AppLogger.d('HomeRepository: Processed ${activities.length} activities');
      return activities;
    } catch (e, stackTrace) {
      AppLogger.e('Error fetching activities: $e');
      AppLogger.e('Stack trace: $stackTrace');
      return <ActivityModel>[];
    }
  }

  Future<MemoryResult> _fetchMemories(String familyId) async {
    try {
      final memoryList =
          await familyRepository.getMemoryListByFamily(familyId, type: 'list');

      // Sort by date descending
      memoryList.sort((a, b) {
        final dateA = _parseDateTime(a.updatedAt);
        final dateB = _parseDateTime(b.updatedAt);
        return dateB.compareTo(dateA);
      });

      final latestMemory = memoryList.firstOrNull;
      String? latestMemoryUserName;

      if (latestMemory?.userId != null) {
        final user = await familyRepository.getUserInfo(latestMemory!.userId!);
        latestMemoryUserName = user.fullName;
      }

      return MemoryResult(
        latest: latestMemory,
        latestUserName: latestMemoryUserName,
        all: memoryList,
      );
    } catch (e) {
      AppLogger.e('Error fetching memories: $e');
      return MemoryResult(
        latest: const MemoryModel(),
        latestUserName: null,
        all: [],
      );
    }
  }

  Future<void> _fetchEvents(String familyId) async {
    try {
      await eventService.fetchEventByFamilyId(familyId);
    } catch (e) {
      AppLogger.e('Error fetching events: $e');
    }
  }

  Future<List<ThreadFamily>> _fetchMessages(String familyId) async {
    try {
      return await threadRepository.getAllThread(familyId);
    } catch (e) {
      AppLogger.e('Error fetching messages: $e');
      return <ThreadFamily>[];
    }
  }

  Future<List<Item>> _fetchTasksDueToday(String familyId) async {
    try {
      AppLogger.d(
          'HomeRepository: Fetching tasks due today for family $familyId');
      final tasks = await familyRepository.getTaskDueToday(familyId);
      AppLogger.d('HomeRepository: Received ${tasks.length} tasks due today');
      return tasks;
    } catch (e) {
      AppLogger.e('Error fetching tasks due today: $e');
      return <Item>[];
    }
  }

  /// Helper method to parse date strings safely
  DateTime _parseDateTime(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return DateTime(0); // Return epoch time for null/empty dates
    }

    try {
      // Try parsing ISO 8601 format first
      return DateTime.parse(dateString);
    } catch (e) {
      try {
        // Try parsing with milliseconds since epoch
        final timestamp = int.tryParse(dateString);
        if (timestamp != null) {
          return DateTime.fromMillisecondsSinceEpoch(timestamp);
        }
      } catch (e) {
        AppLogger.e('Error parsing date string: $dateString, error: $e');
      }
      return DateTime(0); // Fallback to epoch time
    }
  }
}

class HomeDataResult {
  final List<ListItem> lists;
  final List<ActivityModel> activities;
  final MemoryResult memories;
  final List<ThreadFamily> messages;
  final List<Item> tasksDueToday;
  final String? error;

  HomeDataResult({
    required this.lists,
    required this.activities,
    required this.memories,
    required this.messages,
    required this.tasksDueToday,
    this.error,
  });

  HomeDataResult.error(String errorMessage)
      : lists = [],
        activities = [],
        memories = MemoryResult(latest: null, latestUserName: null, all: []),
        messages = [],
        tasksDueToday = [],
        error = errorMessage;

  bool get hasError => error != null;
}

class MemoryResult {
  final MemoryModel? latest;
  final String? latestUserName;
  final List<MemoryModel> all;

  MemoryResult({
    required this.latest,
    required this.latestUserName,
    required this.all,
  });
}

extension ListExtensions on List<ThreadFamily> {
  int get totalUnreadMessages {
    return fold<int>(0, (total, thread) => total + (thread.unread ?? 0));
  }
}
