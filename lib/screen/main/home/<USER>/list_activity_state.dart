import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';

class ListActivityState extends BaseState {
  final List<ActivityModel> activityList;
  final List<ActivityModel> endActivityList;

  ListActivityState({
    this.activityList = const [],
    this.endActivityList = const [],
  });

  @override
  List<Object?> get props => [activityList, endActivityList];

  ListActivityState copyWith({
    List<ActivityModel>? activityList,
    List<ActivityModel>? endActivityList,
  }) {
    return ListActivityState(
      activityList: activityList ?? this.activityList,
      endActivityList: endActivityList ?? this.endActivityList,
    );
  }
}
