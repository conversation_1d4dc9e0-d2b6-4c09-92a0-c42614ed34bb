import 'package:flutter/material.dart';

const double kDraggableFabRightOffset = 16.0;
const double kDraggableFabBottomOffset = 130.0;

class DraggableFab extends StatefulWidget {
  final Widget child;
  const DraggableFab({Key? key, required this.child}) : super(key: key);

  @override
  State<DraggableFab> createState() => _DraggableFabState();
}

class _DraggableFabState extends State<DraggableFab> {
  Offset? _fabPosition;
  static const double _fabSize = 72.0;

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    if (_fabPosition == null) {
      // Default: bottom right
      return Positioned(
        right: kDraggableFabRightOffset,
        bottom: kDraggableFabBottomOffset,
        child: GestureDetector(
          onPanStart: (details) {
            // Calculate initial position from right/bottom
            setState(() {
              _fabPosition = Offset(
                screenSize.width - kDraggableFabRightOffset - _fabSize,
                screenSize.height - kDraggableFabBottomOffset - _fabSize,
              );
            });
          },
          onPanUpdate: (details) {
            if (_fabPosition != null) {
              setState(() {
                double newDx = _fabPosition!.dx + details.delta.dx;
                double newDy = _fabPosition!.dy + details.delta.dy;
                newDx = newDx.clamp(0.0, screenSize.width - _fabSize);
                newDy = newDy.clamp(0.0, screenSize.height - _fabSize);
                _fabPosition = Offset(newDx, newDy);
              });
            }
          },
          child: widget.child,
        ),
      );
    } else {
      // After drag: use left/top
      return Positioned(
        left: _fabPosition!.dx,
        top: _fabPosition!.dy,
        child: GestureDetector(
          onPanUpdate: (details) {
            setState(() {
              double newDx = _fabPosition!.dx + details.delta.dx;
              double newDy = _fabPosition!.dy + details.delta.dy;
              newDx = newDx.clamp(0.0, screenSize.width - _fabSize);
              newDy = newDy.clamp(0.0, screenSize.height - _fabSize);
              _fabPosition = Offset(newDx, newDy);
            });
          },
          child: widget.child,
        ),
      );
    }
  }
}
