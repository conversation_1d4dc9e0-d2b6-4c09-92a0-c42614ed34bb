import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/list_log_item.dart';

enum DetailState { none, notFound, delete }

class DetailActivityState extends BaseState {
  final ActivityModel? activity;
  final List<ListLog> logs;
  final DetailState state;
  // final List<CheckListItem> items;

  DetailActivityState({
    this.activity,
    this.logs = const [],
    this.state = DetailState.none,
    // this.items = const [],
  });

  @override
  List<Object?> get props => [
        activity,
        logs,
        state,
        // items,
      ];

  DetailActivityState copyWith({
    ActivityModel? activity,
    List<ListLog>? logs,
    DetailState? state,
    // List<CheckListItem>? items,
  }) {
    return DetailActivityState(
      activity: activity ?? this.activity,
      logs: logs ?? this.logs,
      state: state ?? this.state,
      // items: items ?? this.items,
    );
  }
}
