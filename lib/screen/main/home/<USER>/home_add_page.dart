import 'package:auto_route/auto_route.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/screen/main/memories/memory_upsert_screen.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:flutter/material.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/main.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:family_app/extension.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/base/widget/cubit/base_state.dart';

@RoutePage()
class HomeAddPage extends StatelessWidget {
  const HomeAddPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<HomeAddCubit>(
      create: (_) => HomeAddCubit(),
      child: const HomeAddView(),
    );
  }
}

class HomeAddView extends StatefulWidget {
  const HomeAddView({super.key});

  @override
  State<HomeAddView> createState() => _HomeAddViewState();
}

class HomeAddState extends BaseState {
  HomeAddState();
  @override
  List<Object?> get props => [];
}

class HomeAddCubit extends BaseCubit<HomeAddState> {
  HomeAddCubit() : super(HomeAddState());
}

class _HomeAddViewState extends BaseBlocPageState<HomeAddView, HomeAddState, HomeAddCubit> {
  @override
  String get title => 'Add';

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get showBack => false;

  @override
  Widget buildBody(BuildContext context, HomeAddCubit cubit, HomeAddState state) {
    return Padding(
      padding: padding(horizontal: 16.0, top: 8.0),
      child: ListView(
        padding: const EdgeInsets.symmetric(vertical: 16),
        children: [
          _AddCard(
            icon: SvgPicture.asset(Assets.icons.iconAddTodo.path, width: 24, height: 24),
            title: 'To-do',
            subtitle: 'Quick tasks you want to get done',
            onTap: () async {
              final result = await context.router.push(
                UpsertListItemRoute(parameter: UpsertListItemParameter(type: ListType.Todo)),
              );
              if (result != null) {
                context.router.navigate(const CheckListRoute());
              }
            },
          ),
          const SizedBox(height: 16),
          _AddCard(
            icon: SvgPicture.asset(Assets.icons.iconAddChecklist.path, width: 24, height: 24),
            title: 'Checklist',
            subtitle: 'A list of to-dos',
            onTap: () async {
              final result = await context.router.push(
                UpsertListItemRoute(parameter: UpsertListItemParameter(type: ListType.Todo)),
              );
              if (result != null) {
                context.router.navigate(const CheckListRoute());
              }
            },
          ),
          const SizedBox(height: 16),
          _AddCard(
            icon: SvgPicture.asset(Assets.icons.iconAddEvent.path, width: 24, height: 24),
            title: 'Event',
            subtitle: 'Happening at a specific time and date',
            onTap: () async {
              final result = await context.router.push(
                UpsertEventRoute(upsertEventParameter: UpsertEventParameter()),
              );
              if (result != null) {
                context.router.navigate(const CalendarRoute());
              }
            },
          ),
          const SizedBox(height: 16),
          _AddCard(
            icon: SvgPicture.asset(Assets.icons.iconAddActivity.path, width: 24, height: 24),
            title: 'Activity Plan',
            subtitle: 'Templates for trips, parties and more',
            onTap: () async {
              final result = await context.router.push(const ActivitySelectTypeRoute());
              if (result != null) {
                context.router.navigate(const ActivityHomeRoute());
              }
            },
          ),
          const SizedBox(height: 16),
          _AddCard(
            icon: SvgPicture.asset(Assets.icons.iconAddMemory.path, width: 24, height: 24),
            title: 'Memory',
            subtitle: 'Templates for trips, parties and more',
            onTap: () async {
              final result = await MemoryUpsertBts.show(context);
              if (result != null) {
                context.router.navigate(const MemoriesRoute());
              }
            },
          ),
          const SizedBox(height: 16),
          FutureBuilder<String?>(
            future: locator.get<LocalStorage>().accessToken(),
            builder: (context, snapshot) {
              return _AddCard(
                icon: Image.asset('assets/images/AI_LINKA.png', width: 40, height: 40),
                title: 'Not sure?',
                subtitle: 'Ask our AI assistant',
                onTap: snapshot.hasData && snapshot.data != null
                    ? () => context.router.push(
                          ChatRoute(
                            parameter: ChatParameter(
                              chatContext: ChatContext.getGeneralChatContext(snapshot.data!),
                            ),
                          ),
                        )
                    : null,
              );
            },
          ),
        ],
      ),
    );
  }
}

class _AddCard extends StatelessWidget {
  final Widget icon;
  final String title;
  final String subtitle;
  final VoidCallback? onTap;

  const _AddCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          shadows: [
            const BoxShadow(
              color: Color(0x19000000),
              blurRadius: 12,
              offset: Offset(0, 4),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          children: [
            SizedBox(
              width: 60,
              height: 60,
              child: Center(child: icon),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Color(0xFF595D62),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
