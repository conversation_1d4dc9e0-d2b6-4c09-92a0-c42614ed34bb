import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/usecase/activity_usecase.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/upsert_activity_parameter.dart';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/util.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

import 'upsert_activity_state.dart';

class UpsertActivityCubit extends BaseCubit<UpsertActivityState> {
  final ActivityUsecase activityUsecase;
  final AccountService accountService;
  final UpsertActivityParameter? parameter;
  final MainCubit mainCubit;

  UpsertActivityCubit({
    required this.activityUsecase,
    required this.accountService,
    this.parameter,
    required this.mainCubit,
  }) : super(UpsertActivityState());

  late final TextFieldHandler title;
  late final TextFieldHandler description;
  late final FormTextFieldHandler formHandler;

  final TextEditingController textEditingController = TextEditingController();
  final textNode = FocusNode();

  @override
  void onInit() {
    final activity = parameter?.activityModels;
    final data = parameter?.message?.data;
    super.onInit();
    title = TextFieldHandler(
      field: 'title',
      hintText: LocaleKeys.activity_title_text.tr(),
      isRequired: true,
      initializeText: data?.name ?? activity?.name ?? '',
      isFieldValid: (value) => Utils.isNull(value),
    );
    description = TextFieldHandler(
      field: 'description',
      hintText: LocaleKeys.activity_description_text.tr(),
      isRequired: true,
      initializeText: data?.description ?? activity?.description ?? '',
      isFieldValid: (value) => Utils.isNull(value),
    );
    formHandler = FormTextFieldHandler(handlers: [title, description], validateForm: (map) async {});
    emit(state.copyWith(
      selectedColor: activity?.color.toColor,
      memberList: activity?.includedMembers ?? accountService.memberInFamily.value,
      eventList: activity?.includedEvents,
      listItems: activity?.includedLists,
      startTime: (data?.fromDate ?? activity?.fromDate)?.toLocalDT,
      endTime: (data?.toDate ?? activity?.toDate)?.toLocalDT,
    ));
  }

  void updateSelectedColor(Color color) {
    emit(state.copyWith(selectedColor: color));
  }

  void updateSelectedMember(List<Account> selectedMember) async {
    emit(state.copyWith(memberList: selectedMember));
  }

  void updateSelectedEvent(List<EventModels> selectedEvent) async {
    emit(state.copyWith(eventList: selectedEvent));
  }

  void updateStartDate(DateTime newDateTime) {
    if (state.endTime != null && state.endTime!.isBefore(newDateTime)) {
      return showSimpleToast(LocaleKeys.end_time_has_been_reset_because_it_cannot_be_earlier_than_start_time_text.tr());
    }

    emit(state.copyWith(startTime: newDateTime));
  }

  void updateListItem(List<ListItem> listItems) {
    emit(state.copyWith(listItems: listItems));
  }

  void updateEndDate(DateTime newDateTime) {
    if (state.startTime != null && newDateTime.isBefore(state.startTime!)) {
      return showSimpleToast(LocaleKeys.end_time_cannot_be_earlier_than_start_time_text.tr());
    }

    emit(state.copyWith(endTime: newDateTime));
  }

  void onUpdateListType(ListType type) {
    emit(state.copyWith(type: type));
  }

  void createActivity() async {
    if (title.text.isEmpty) {
      return showSimpleToast(LocaleKeys.please_enter_an_activity_title_text.tr());
    } else if (description.text.isEmpty) {
      return showSimpleToast(LocaleKeys.please_enter_an_activity_description_text.tr());
    } else if (state.selectedColor == null) {
      return showSimpleToast(LocaleKeys.please_select_a_color_text.tr());
    }
    //  else if (state.memberList.isEmpty) {
    //   return showSimpleToast(LocaleKeys.please_select_a_member_text.tr());
    // } else if (state.eventList.isEmpty) {
    //   return showSimpleToast(LocaleKeys.please_select_an_event_text.tr());
    // }
    else if (state.startTime == null) {
      return showSimpleToast(LocaleKeys.please_select_a_start_time_text.tr());
    } else if (state.endTime == null) {
      return showSimpleToast(LocaleKeys.please_select_an_end_time_text.tr());
    }

    showLoading();

    try {
      final result = await activityUsecase.call(
        CreateActivityParameter(
            uuid: parameter?.activityModels?.uuid,
            name: title.text.trim(),
            fromDate: state.startTime?.toUtc().toIso8601String() ?? '',
            toDate: state.endTime?.toUtc().toIso8601String() ?? '',
            color: state.selectedColor?.text ?? '',
            caption: '',
            description: description.text.trim(),
            includedMembers: state.memberList.map((member) => member.familyMemberUuid ?? '').toList(),
            includedEvents: state.eventList.map((event) => event.uuid ?? '').toList(),
            includedLists: state.listItems.map((list) => list.uuid ?? '').toList(),
            familyId: accountService.familyId,
            activityType: state.type.name,
            oldEventIds: parameter?.activityModels?.includedEvents?.map((e) => e.uuid ?? '').toList() ?? <String>[]),
      );
      mainCubit.updateListActivity(result);
      emit(state.copyWith(activity: result));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    } finally {
      dismissLoading();
    }
  }

  void onAddNewCheckItem() {
    final items = [...state.items];
    if (textNode.hasFocus && state.currentIndexEdit < items.length) {
      if (textEditingController.text.isEmpty) return;
      items[state.currentIndexEdit].name = textEditingController.text;
      textNode.unfocus();
    }
    items.add(CheckListItem());
    emit(state.copyWith(items: items, currentIndexEdit: items.length - 1));
    textEditingController.text = '';
    textNode.requestFocus();
  }

  void onUpdateStatusCheckItem(int index) {
    final items = [...state.items];
    items[index].isDone = !(items[index].isDone ?? false);
    emit(state.copyWith(items: items));
  }

  void onRemoveItem(int index) {
    final items = [...state.items];
    items.removeAt(index);
    emit(state.copyWith(items: items));
  }

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }
}
