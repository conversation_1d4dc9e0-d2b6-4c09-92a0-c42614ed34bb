import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/home/<USER>/activity_home_state.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:family_app/screen/main/home/<USER>';

class ActivityHomeCubit extends BaseCubit<ActivityHomeState> {
  final IActivityRepository activityRepository;
  final HomeCubit homeCubit;

  ActivityHomeCubit({
    required this.activityRepository,
    required this.homeCubit,
  }) : super(ActivityHomeState());

  final AccountService accountService = locator.get();

  @override
  Future<void> close() {
    locator.unregister<ActivityHomeCubit>();
    return super.close();
  }

  @override
  void onInit() {
    locator.registerSingleton(this);
    super.onInit();

    fetchActivity();
  }

  Future<void> fetchActivity() async {
    logd("reloading eventsss >>>> ");
    emit(state.copyWith(status: ActivityHomeStatus.loading));
    try {
      // Fetch activities and process them inline (same logic as home repository)
 
      final activities = await activityRepository
          .getFamilyActivities(accountService.familyId, limit: 10);

      // Process activities with images - same logic as home repository

      for (var activity in activities) {
        if (activity.imagePath == null) {
          if (activity.activityType == 'birthday_registry') {
            activity.imagePath = activity.getImagePath();
          } else {
            try {
              // Use location bias from GPS coordinates for better results
              LatLng? activityLocation = activity.gpsCoord;
              
              if (activity.city != null) {
                // Enhanced provider with location bias for cost optimization
                activity.imagePath = await provider.fetchImageUrl(activity.city!, location: activityLocation);
              } else if (activity.country != null) {
                activity.imagePath = await provider.fetchImageUrl(activity.country!, location: activityLocation);
 
              }
            } catch (e) {
              AppLogger.e(
                  'Error fetching image for activity ${activity.name}: $e');
            }
          }
        }

        // Calculate trip duration
        if (activity.fromDate != null && activity.toDate != null) {
          activity.trip_duration = activity.toDate!.toLocalDT
                  .difference(activity.fromDate!.toLocalDT)
                  .inDays +
              1;
        }
      }

      emit(state.copyWith(
          activityList: activities, status: ActivityHomeStatus.success));
    } catch (e) {
      emit(state.copyWith(status: ActivityHomeStatus.error));
      print(e);
    } finally {
      emit(state.copyWith(status: ActivityHomeStatus.done));
    }
  }

  void updateActivity(ActivityModel? activity) {
    if (activity == null) return;
    List<ActivityModel> activeActivities = [
      activity,
      ...state.activityList,
    ];

    emit(state.copyWith(activityList: activeActivities));
  }

  Future<List<ActivityModel>> getActivityList(
      {int page = 1, int limit = LIMIT}) async {
    try {
      final result = await activityRepository
          .getFamilyActivities(accountService.familyId, limit: 100);
      return result;
    } catch (e) {
      print('getActivityList error: $e');
      return [];
    }
  }

  void removeActivity(ActivityModel activity) async {
    emit(state.copyWith(status: ActivityHomeStatus.loading));
    try {
      final result = await activityRepository.deleteActivity(activity.uuid);
      if (result) {
        await fetchActivity();
        homeCubit.onRefresh();
      }
      emit(state.copyWith(status: ActivityHomeStatus.success));
    } catch (e) {
      emit(state.copyWith(status: ActivityHomeStatus.error));
      print('getActivityList error: $e');
    } finally {
      emit(state.copyWith(status: ActivityHomeStatus.done));
    }
  }
}
