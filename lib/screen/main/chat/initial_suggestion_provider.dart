import 'package:family_app/screen/main/chat/chat_context.dart';

/// Provides initial suggestions based on chat context and purpose
class InitialSuggestionProvider {
  /// Get initial suggestions based on the chat context purpose
  static List<String> getSuggestions(AIPurposeKey purposeKey,
      {dynamic customData}) {
    switch (purposeKey) {
      case AIPurposeKey.editATrip:
        return _getEditTripSuggestions();

      case AIPurposeKey.customizeTrip:
        return _getCustomizeTripSuggestions();

      case AIPurposeKey.planATrip:
        return _getPlanTripSuggestions();

      case AIPurposeKey.planAnEvent:
        return _getPlanEventSuggestions();

      case AIPurposeKey.editAnEvent:
        return _getEditEventSuggestions();

      case AIPurposeKey.createAList:
        return _getCreateListSuggestions();

      case AIPurposeKey.editAList:
        return _getEditListSuggestions();

      case AIPurposeKey.generalPurpose:
        return _getGeneralSuggestions();
    }
  }

  /// Suggestions for editing an existing trip
  static List<String> _getEditTripSuggestions() {
    return [
      'Looking for restaurants?',
      'Looking for a particular point of interest?',
      'Want to buy some souvenirs?',
      'Modify your plan',
      'Add more activities',
      'Change accommodation',
      'Update travel dates',
      'Find local attractions'
    ];
  }

  /// Suggestions for customizing a trip
  static List<String> _getCustomizeTripSuggestions() {
    return [
      'Add more days to the trip',
      'Change hotel preferences',
      'Include family-friendly activities',
      'Add cultural experiences',
      'Find budget-friendly options',
      'Include outdoor activities',
      'Add shopping destinations',
      'Find local cuisine spots'
    ];
  }

  /// Suggestions for planning a new trip
  static List<String> _getPlanTripSuggestions() {
    return [
      'Plan a family vacation',
      'Weekend getaway ideas',
      'Budget-friendly destinations',
      'Adventure travel options',
      'Cultural city tours',
      'Beach destinations',
      'Mountain retreats',
      'Historical sites to visit'
    ];
  }

  /// Suggestions for planning an event
  static List<String> _getPlanEventSuggestions() {
    return [
      'Birthday party ideas',
      'Anniversary celebration',
      'Family gathering',
      'Holiday celebration',
      'Graduation party',
      'Wedding planning',
      'Baby shower ideas',
      'Reunion planning'
    ];
  }

  /// Suggestions for editing an event
  static List<String> _getEditEventSuggestions() {
    return [
      'Change event date',
      'Update guest list',
      'Modify venue',
      'Add activities',
      'Update catering',
      'Change decorations',
      'Adjust timing',
      'Add entertainment'
    ];
  }

  /// Suggestions for creating a list
  static List<String> _getCreateListSuggestions() {
    return [
      'Travel packing list',
      'Shopping list',
      'To-do list',
      'Gift ideas list',
      'Restaurant wishlist',
      'Book reading list',
      'Movie watchlist',
      'Bucket list items'
    ];
  }

  /// Suggestions for editing a list
  static List<String> _getEditListSuggestions() {
    return [
      'Add new items',
      'Remove completed items',
      'Reorder priorities',
      'Update descriptions',
      'Mark as completed',
      'Add due dates',
      'Categorize items',
      'Share with family'
    ];
  }

  /// General purpose suggestions
  static List<String> _getGeneralSuggestions() {
    return [
      'Plan a trip',
      'Create an event',
      'Make a list',
      'Find recommendations',
      'Get travel advice',
      'Discover new places',
      'Plan activities',
      'Organize family time'
    ];
  }

  /// Get contextual suggestions based on additional data
  /// This can be extended to provide more specific suggestions based on
  /// trip data, location, time of year, etc.
  static List<String> getContextualSuggestions(AIPurposeKey purposeKey,
      {dynamic customData, String? location, DateTime? date}) {
    List<String> baseSuggestions =
        getSuggestions(purposeKey, customData: customData);

    // Add contextual suggestions based on location or other factors
    if (location != null && location.isNotEmpty) {
      baseSuggestions = _addLocationBasedSuggestions(baseSuggestions, location);
    }

    if (date != null) {
      baseSuggestions = _addTimeBasedSuggestions(baseSuggestions, date);
    }

    return baseSuggestions;
  }

  /// Add location-based suggestions
  static List<String> _addLocationBasedSuggestions(
      List<String> suggestions, String location) {
    // This could be enhanced to provide location-specific suggestions
    // For now, just return the original suggestions
    return suggestions;
  }

  /// Add time-based suggestions (seasonal, time of day, etc.)
  static List<String> _addTimeBasedSuggestions(
      List<String> suggestions, DateTime date) {
    // This could be enhanced to provide time-specific suggestions
    // For now, just return the original suggestions
    return suggestions;
  }
}
