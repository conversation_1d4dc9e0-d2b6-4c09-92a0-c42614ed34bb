import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/screen/main/chat/voice_recording_cubit.dart';
import 'package:family_app/screen/main/chat/voice_recording_parameter.dart';
import 'package:family_app/screen/main/chat/voice_recording_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:flutter/material.dart';

class VoiceRecordingBts extends BaseBlocProvider<VoiceRecordingState, VoiceRecordingCubit> {
  const VoiceRecordingBts({required this.parameter, super.key});
  final VoiceRecordingParameter parameter;

  @override
  Widget buildPage() => const VoiceRecordingScreen();

  @override
  VoiceRecordingCubit createCubit() => VoiceRecordingCubit(
        parameter: parameter,
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
      );
}

class VoiceRecordingScreen extends StatefulWidget {
  const VoiceRecordingScreen({super.key});

  @override
  State<VoiceRecordingScreen> createState() => _VoiceRecordingScreenState();
}

class _VoiceRecordingScreenState
    extends BaseBlocPageState<VoiceRecordingScreen, VoiceRecordingState, VoiceRecordingCubit> {
  @override
  bool listenWhen(VoiceRecordingState previous, VoiceRecordingState current) {
    if (current.status == VoiceRecordingStatus.error) {
      showSimpleToast(current.errorMessage ?? 'Failed to record audio');
      Navigator.of(context).pop();
    } else if (current.status == VoiceRecordingStatus.success) {
      Navigator.of(context).pop(current.filePath);
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(BuildContext context, VoiceRecordingCubit cubit, VoiceRecordingState state) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(BuildContext context, VoiceRecordingCubit cubit, VoiceRecordingState state) {
    return SingleChildScrollView(
      // Added for scrollability
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildHeader(context, cubit, state),
          _buildDivider(context),
          _buildRecordingView(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, VoiceRecordingCubit cubit, VoiceRecordingState state) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              'Cancel',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          // Expanded(
          //   child: Center(
          //     child: Text('Voice recording', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 1,
      color: Theme.of(context).dividerColor,
    );
  }

  Widget _buildRecordingView(BuildContext context, VoiceRecordingCubit cubit, VoiceRecordingState state) {
    final isRecording = state.status == VoiceRecordingStatus.recording;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Important for BottomSheet
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                isRecording ? 'Listening' : 'Tap to Talk',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              InkWell(
                borderRadius: BorderRadius.circular(50), // Adjusted to wrap CircleAvatar
                onTap: () {
                  if (isRecording) {
                    cubit.stopRecording(); // Close BottomSheet after recording
                  } else {
                    cubit.startRecording();
                  }
                },
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    /// Mic Icon
                    CircleAvatar(
                      backgroundColor: isRecording ? Colors.red : Colors.blue,
                      radius: 50,
                      child: Icon(
                        isRecording ? Icons.mic_off : Icons.mic,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
