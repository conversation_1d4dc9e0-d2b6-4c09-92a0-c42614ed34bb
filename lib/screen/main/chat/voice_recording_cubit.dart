import 'dart:async';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/screen/main/chat/voice_recording_parameter.dart';
import 'package:family_app/screen/main/chat/voice_recording_state.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';

class VoiceRecordingCubit extends BaseCubit<VoiceRecordingState> {
  final VoiceRecordingParameter parameter;
  final IFamilyRepository familyRepository;
  final IUploadRepository uploadRepository;
  final IActivityRepository activityRepository;

  VoiceRecordingCubit(
      {required this.parameter,
      required this.familyRepository,
      required this.uploadRepository,
      required this.activityRepository})
      : super(VoiceRecordingState());

  final AudioRecorder _audioRecorder = AudioRecorder();
  Timer? _timer;

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();
  }

  @override
  Future<void> close() {
    AppLogger.d('VoiceRecordingCubit closed');
    locator.unregister<VoiceRecordingCubit>();
    _audioRecorder.cancel();
    _audioRecorder.dispose();
    _timer?.cancel();
    return super.close();
  }

  Future<void> startRecording() async {
    try {
      if (await _audioRecorder.hasPermission()) {
        final dir = await getTemporaryDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final fileName = 'voice_recording_$timestamp.wav';
        final filePath = '${dir.path}/$fileName';
        await _audioRecorder.start(
          const RecordConfig(
            encoder: AudioEncoder.wav,
            sampleRate: 44100,
          ),
          path: filePath,
        );

        // Update progress every second
        _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          emit(state.copyWith(duration: state.duration + const Duration(seconds: 1)));
        });

        emit(state.copyWith(status: VoiceRecordingStatus.recording));
      } else {
        emit(state.copyWith(status: VoiceRecordingStatus.error, errorMessage: 'Permission denied'));
      }
    } catch (e) {
      AppLogger.e('Error starting recording: $e');
      emit(state.copyWith(status: VoiceRecordingStatus.error, errorMessage: 'Failed to record audio'));
    }
  }

  Future<void> stopRecording() async {
    try {
      _timer?.cancel();
      final path = await _audioRecorder.stop();
      AppLogger.d('Recording saved at: $path');
      emit(state.copyWith(status: VoiceRecordingStatus.success, filePath: path));
    } catch (e) {
      AppLogger.e('Error stopping recording: $e');
      emit(state.copyWith(status: VoiceRecordingStatus.error, errorMessage: 'Failed to save audio'));
    }
  }
}
