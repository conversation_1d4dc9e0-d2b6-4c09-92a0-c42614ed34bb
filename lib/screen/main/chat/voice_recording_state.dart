import 'package:family_app/base/widget/cubit/base_state.dart';

enum VoiceRecordingStatus { initial, recording, success, error }

class VoiceRecordingState extends BaseState {
  final VoiceRecordingStatus status;
  final Duration duration;
  final String? filePath;
  final String? errorMessage;

  VoiceRecordingState({
    this.status = VoiceRecordingStatus.initial,
    this.duration = Duration.zero,
    this.filePath,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [status, duration, filePath, errorMessage];

  VoiceRecordingState copyWith({
    VoiceRecordingStatus? status,
    Duration? duration,
    String? filePath,
    String? errorMessage,
  }) {
    return VoiceRecordingState(
      status: status ?? this.status,
      duration: duration ?? this.duration,
      filePath: filePath ?? this.filePath,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
