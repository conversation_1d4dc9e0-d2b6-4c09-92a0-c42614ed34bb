import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/AiConnectionController.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class ChatParameter {
  final ChatContext chatContext;
  final dynamic
      customData; // Accepts any data for context, e.g., ActivityModel JSON
  final AiConnectionController? aiConnectionController;

  ChatParameter({
    required this.chatContext,
    this.customData,
    this.aiConnectionController,
  });
}
