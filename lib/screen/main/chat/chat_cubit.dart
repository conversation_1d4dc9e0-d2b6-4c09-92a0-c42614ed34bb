import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/ai_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/chat/chat_state.dart';
import 'package:family_app/screen/main/chat/chat_speech_controller.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/speech/speech_controller.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:intl/intl.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/data/usecase/event_usecase.dart';
import 'package:family_app/data/model/chat_suggestion_model.dart';
import 'package:family_app/main.dart';
import 'package:auto_route/auto_route.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/AiConnectionController.dart';

const String kAiWebSocketUrl = 'wss://vrelay-vn1.5gencare.com/ai/ws';

class ChatCubit extends BaseCubit<ChatState> {
  final IActivityRepository activityRepository;
  final ChatParameter parameter;
  late AiConnectionController _aiController;

  // Local copy of trip being edited for consecutive patches
  ActivityModel? _editingTrip;

  ActivityModel? get editingTrip => _editingTrip;

  final EventUsecase eventUsecase = locator.get();
  final AccountService accountService = locator.get();
  final HomeCubit homeCubit;

  // Speech controller
  late ChatSpeechController _speechController;

  // TTS
  FlutterTts flutterTts = FlutterTts();
  bool isFlutterTtsSpeaking = false;

  ChatCubit({
    required this.activityRepository,
    required this.parameter,
    required this.homeCubit,
  }) : super(ChatState()) {
    _initializeSpeechController();
    if (parameter.aiConnectionController != null) {
      // Use the provided controller and set callbacks
      _aiController = parameter.aiConnectionController!;
      _aiController.setCallbacks(
        onMessage: _handleStreamData,
        onError: _handleStreamError,
        onDone: _handleStreamDone,
        onTimeout: _handleTimeout,
      );
    } else {
      // Create a new controller
      _aiController = AiConnectionController(
        onMessage: _handleStreamData,
        onError: _handleStreamError,
        onDone: _handleStreamDone,
        onTimeout: _handleTimeout,
        timeout: const Duration(seconds: 60),
      );
    }
  }

  void _initializeSpeechController() {
    _speechController = ChatSpeechController(
      heuristicType: HeuristicType.adaptivePace,
      onSpeechFinalized: _onSpeechFinalized,
      onRecordingStateChanged: _onRecordingStateChanged,
      onError: _onSpeechError,
    );
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    AppLogger.d("chat parameter uuid: ${parameter.chatContext.tripId}, ");


if (parameter.chatContext.purposeKey == AIPurposeKey.editATrip) {
      //hardcode for now:
      List<String> list = [
        'Looking for restaurants? ',
        'Looking for a particular point of interest?',
        'Want to buy some souvenirs ?',
        'Modify your plan'
      ];
    }


    await _connectToAI();

    await _initTTS();
    await _speechController.initialize();
  }

  Future<void> _connectToAI() async {
    logd("Connect to AI if needed");

    //We can be here because of many reasons not just Trip planning (or edit/customize)

    final token = parameter.chatContext.token;
    try {
      Map<String, String> initMsg = await parameter.chatContext.getInitMessage();

      if (!_aiController.isConnected) {
        //Since we passed the aicontroller from outside, ai has knowledge of the trip , no need to send it again
        if (parameter.chatContext.purposeKey == AIPurposeKey.customizeTrip) {
          ActivityModel currentTripData = parameter.customData as ActivityModel;
          AppLogger.d("Open new connection: customizing trip data: $currentTripData");
          initMsg['message'] = initMsg['message']! + jsonEncode(currentTripData.toJson());
        }

        AppLogger.d("Ai connection is not avail, connect it ");
        final url = '$kAiWebSocketUrl?authorization=$token';
        await _aiController.connect(url, initMessage: initMsg);
      } else {

        //There are other usecases, please check the purposeKey 

        if (parameter.chatContext.purposeKey == AIPurposeKey.customizeTrip) {
          ActivityModel currentTripData = parameter.customData as ActivityModel;
          AppLogger.d("customizing trip data: $currentTripData");
          initMsg['message'] = initMsg['message']! + jsonEncode(currentTripData.toJson());

          sendMessage(initMsg['message']!, null, null, autoSpeak: false, addToChatLog: false);
        } else {
          AppLogger.d("connection is ready , General use ");
          sendMessage("hi", null, null, autoSpeak: false, addToChatLog: false);
        }
      }

      emit(state.copyWith(connState: ConnState.success, loading: false));
    } catch (e) {
      AppLogger.e("WebSocket connection error: $e");
      emit(state.copyWith(connState: ConnState.none, loading: false));
      _handleStreamError(e);
    }
  }

  @override
  Future<void> close() async {
    AppLogger.d("closing websocket channel and stopping TTS/STT");

    // Only close the AI connection controller if it was created inside this cubit
    // (not if it was passed from outside like from VTPDiscoveryCubit)
    if (parameter.aiConnectionController == null) {
      AppLogger.d("Closing AI connection controller created by this cubit");
      _aiController.close();
    } else {
      AppLogger.d(
          "Not closing AI connection controller - it was passed from outside");
    }

    await stopTTSAndSTT();
    _speechController.dispose();
    super.close();
  }

  // Speech controller callbacks
  void _onSpeechFinalized(String text) async {
    await sendSpeechMessage(text);
  }

  void _onRecordingStateChanged(bool isRecording) {
    emit(state.copyWith(isRecording: isRecording));
  }

  void _onSpeechError(String error) {
    emit(state.copyWith(
      isRecording: false,
      errorMessage: error,
    ));
  }

  Future<void> _initTTS() async {
    await flutterTts.setLanguage("en-US");
    await flutterTts.setSpeechRate(0.5);
    await flutterTts.awaitSpeakCompletion(true);
    await flutterTts.setVolume(1.0);
    await flutterTts.setPitch(1.5);

    var voices = await flutterTts.getVoices;
    if (voices == null) {
      AppLogger.d("No voices available");
      return;
    }
    if (Platform.isIOS) {
      await flutterTts.setIosAudioCategory(
          IosTextToSpeechAudioCategory.playback,
          [IosTextToSpeechAudioCategoryOptions.defaultToSpeaker]);

      // filter the voices en-US
      await flutterTts.setVoice({
        "identifier": "com.apple.ttsbundle.siri_Nicky_en-US_compact",
        "locale": "en-US",
        "name": "Nicky",
        "quality": "default",
        "gender": "female"
      });

      // Uncomment to test TTS on iOS
      // await flutterTts.speak("Hello, I am ready to assist you!");
    }
    if (Platform.isAndroid) {
      //female voice
      await flutterTts.setVoice({
        'name': 'en-us-x-sfg#female_1-local', // Change to the voice you found
        'locale': 'en-US'
      });
    }
    log("TTS Initialized! $voices");
    flutterTts.setStartHandler(() {
      isFlutterTtsSpeaking = true;
      logd("TTS: Speaking started");
      _speechController.onTTSStarted();
    });
    flutterTts.setCompletionHandler(() {
      isFlutterTtsSpeaking = false;
      logd("TTS: Speaking completed");
      _speechController.onTTSCompleted();
    });
  }

  void _handleTimeout() async {
    _handleStreamError('Connection timeout');
    if (_speechController.isListening) {
      await stopTTSAndSTT();
      emit(state.clearRecordingInfo().copyWith(
          connState: ConnState.timeout,
          loading: false,
          isWaitingForResponse: false));
    } else {
      emit(state.copyWith(
          connState: ConnState.timeout,
          loading: false,
          messages: [],
          isWaitingForResponse: false));
    }
  }

  void _handleStreamData(dynamic data) async {
    final response = jsonDecode(data) as Map<String, dynamic>;

    logd("_handleStreamData state: ${state.loading} , response: $response");
    final List<Map<String, dynamic>> allMessages = List.from(state.messages);

    Trip? aiTrip;
    FlEvent? aiEvent;
    FlList? aiList;
    Map<String, dynamic>? aiTripIntent;
    dynamic newMessage;
    ConnState connState = ConnState.none;
    if (response['command'] == "ai_chat_repond") {
      //dont add the empty message to the list
      if (response['message'] != "") {
        connState = ConnState.success;
        // Initialize editing trip if needed
        if (parameter.chatContext.purposeKey == AIPurposeKey.editATrip) {
          await _initializeEditingTrip();
        } else if (parameter.chatContext.purposeKey ==
            AIPurposeKey.customizeTrip) {
          await _initializeCustomizingTrip();
        }

        // Use purpose-aware parsing for different chat contexts
        final suggestionModel = ChatSuggestionModel.fromJson(response,
            purposeKey: parameter.chatContext.purposeKey,
            currentTrip: _editingTrip);

        // Process AI response using simplified logic
        final responseData = await _processAIResponse(suggestionModel);
        aiTrip = responseData.aiTrip;

        aiEvent = responseData.aiEvent;
        aiList = responseData.aiList;
        aiTripIntent = responseData.aiTripIntent;
        newMessage = responseData.message;

        // Update editing trip with modifications if this is an edit trip response
        if ((parameter.chatContext.purposeKey == AIPurposeKey.editATrip ||
                parameter.chatContext.purposeKey ==
                    AIPurposeKey.customizeTrip) &&
            aiTrip != null) {
          logd(
              "STore the editted trip ${suggestionModel.aiTrip!.runtimeType} ");
          try {
            _editingTrip = aiTrip.toActivityModel();
          } catch (e, stacktrace) {
            loge("got exception: $e \n $stacktrace");
          }
        }

        // Handle editing completion
        if (suggestionModel.editingComplete == true) {
          await _finalizeEditedTrip();
          _resetEditingState();
        }
      } // if response message != null
    } else {
      newMessage = {
        "type": "unknown",
        "message": 'Unknown response: $response',
        "timestamp": DateFormat('HH:mm').format(DateTime.now())
      };
      connState = ConnState.success;
    }

    var latestMess;
    if (allMessages.isNotEmpty) {
      latestMess = allMessages.last;
    }
    // log("latestMess: $latestMess");
    log("newMessage: $newMessage");
    if (latestMess != null &&
        (latestMess['message_type'] == MessageType.audio ||
            latestMess['auto_speak'] == true) &&
        newMessage['type'] == 'response') {
      // remove the last message
      var messageString = newMessage['message'];
      flutterTts.speak(messageString);
    }

    allMessages.add(newMessage);

    emit(state.copyWith(
        messages: allMessages,
        connState: connState,
        loading: false,
        isWaitingForResponse: false,
        aiTrip: aiTrip,
        aiEvent: aiEvent,
        aiList: aiList,
        aiTripIntent: aiTripIntent));
  }

  /// Process AI response based on the parsed suggestion model
  Future<_ResponseData> _processAIResponse(
      ChatSuggestionModel suggestionModel) async {
    Trip? aiTrip;
    FlEvent? aiEvent;
    FlList? aiList;
    Map<String, dynamic>? aiTripIntent;
    Map<String, dynamic> newMessage = {};

    // Extract data from suggestion model
    dynamic trip = suggestionModel.aiTrip;
    dynamic jsonAiEvent = suggestionModel.aiEvent;
    dynamic jsonAiList = suggestionModel.aiList;
    dynamic jsonAiTripIntent = suggestionModel.aiTripIntent;
    dynamic jsonRecommendations = suggestionModel.recommendations; 

    try {
      if (trip != null) {
         
        // Handle trip response
        // Normalize data for Trip compatibility (convert String bool to actual bool)
        final normalizedTripData = Trip.normalizeDataForTrip(trip);
        aiTrip = Trip.fromJson(normalizedTripData);
        aiTrip.familyId = accountService.familyId;
        AppLogger.d(
            "chatContext purposeKey: ${parameter.chatContext.purposeKey}, tripId: ${parameter.chatContext.tripId}");

        // For edit trip, preserve the existing trip ID
        if (parameter.chatContext.purposeKey == AIPurposeKey.editATrip) {
          aiTrip.uuid = parameter.chatContext.tripId!;
        }

        newMessage = {
          "trip_available": true,
          "type": "response",
          "message": suggestionModel.message,
          "timestamp": DateFormat('HH:mm').format(DateTime.now()),
          "suggestions": suggestionModel.suggestions
        };
      } else if (jsonAiEvent != null) {
        // Handle event response
        aiEvent = FlEvent.fromJson(jsonAiEvent);
        logd("Creating event: $aiEvent");

        // Create the new event in user calendar
        var newEvent = await createEvent(aiEvent);

        newMessage = {
          "event_available": true,
          "type": "response",
          "message": suggestionModel.message,
          "timestamp": DateFormat('HH:mm').format(DateTime.now()),
          "suggestions": suggestionModel.suggestions,
          "newEvent": newEvent
        };
      } else if (jsonAiList != null) {
        // Handle list response
        aiList = FlList.fromJson(jsonAiList);
        logd("Creating list: $aiList");

        newMessage = {
          "list_available": true,
          "type": "response",
          "message": suggestionModel.message,
          "timestamp": DateFormat('HH:mm').format(DateTime.now()),
          "suggestions": suggestionModel.suggestions
        };
      } else if (jsonAiTripIntent != null) {
        // Handle trip intent - navigate to VTP
        logd("Trip intent detected: $jsonAiTripIntent");
        aiTripIntent = jsonAiTripIntent;

        newMessage = {
          "trip_intent_available": true,
          "type": "response",
          "message": suggestionModel.message,
          "timestamp": DateFormat('HH:mm').format(DateTime.now()),
          "suggestions": suggestionModel.suggestions
        };
      } else if (jsonRecommendations != null) {
        //Handle "recommendations" from Trip edit
        newMessage = {
          "type": "response",
          "message": suggestionModel.message,
          "timestamp": DateFormat('HH:mm').format(DateTime.now()),
          "recommendations": suggestionModel.recommendations
        }; 
      } else {
        logd("No specific AI response data found, handling general response");
        // Handle general response
        newMessage = {
          "type": "response",
          "message": suggestionModel.message,
          "timestamp": DateFormat('HH:mm').format(DateTime.now()),
          "suggestions": suggestionModel.suggestions
        };
      }
    } catch (e, stacktrace) {
      logd("error: $e \n $stacktrace");
    }

    return _ResponseData(
      aiTrip: aiTrip,
      aiEvent: aiEvent,
      aiList: aiList,
      aiTripIntent: aiTripIntent,
      message: newMessage,
    );
  }

  /// Initialize editing trip once when edit session starts
  Future<void> _initializeEditingTrip() async {
    if (_editingTrip == null && parameter.chatContext.tripId != null) {
      try {
        // Get the current trip from repository
        // final currentTrip = await activityRepository
        //     .getActivityById(parameter.chatContext.tripId!);
        // _editingTrip = currentTrip;

        _editingTrip = parameter.customData! as ActivityModel;

        AppLogger.d("Initialized editing trip: ${_editingTrip!.name}");
      } catch (e) {
        AppLogger.e("Failed to initialize editing trip: $e");
      }
    }
  }

  Future<void> _initializeCustomizingTrip() async {
    if (_editingTrip == null) {
      try {
        _editingTrip = parameter.customData! as ActivityModel;

        AppLogger.d("Initialized Customizing trip: ${_editingTrip!.name}");
      } catch (e) {
        AppLogger.e("Failed to customizing editing trip: $e");
      }
    }
  }

  /// Finalize the edited trip when editing is complete
  Future<void> _finalizeEditedTrip() async {
    if (_editingTrip != null) {
      if (parameter.chatContext.purposeKey == AIPurposeKey.customizeTrip) {
        logd("customizng trip flow.. do nothing here");
      } else {
        try {
          // Save the edited trip to repository
          await activityRepository.updateActivity(
              _editingTrip!.uuid, _editingTrip!.toCreateActivityParameter());
          AppLogger.d("Finalized edited trip: ${_editingTrip!.name}");

          // Refresh home to show updated trip
          homeCubit.onRefresh();
        } catch (e) {
          AppLogger.e("Failed to finalize edited trip: $e");
        }
      }
    }
  }

  /// Reset editing state when editing is complete or cancelled
  void _resetEditingState() {
    _editingTrip = null;
    AppLogger.d("Reset editing state");
  }

  void _handleStreamError(dynamic error) {
    AppLogger.e("error: $error");
    final newMessage = {
      "type": "error",
      "message": 'Error: $error',
      "timestamp": DateFormat('HH:mm').format(DateTime.now())
    };
    final List<Map<String, dynamic>> messages = List.from(state.messages);
    messages.add(newMessage);
    // Cancel any ongoing recording
    if (_speechController.isListening) {
      stopTTSAndSTT();
    }

    emit(state.copyWith(
        connState: ConnState.error,
        messages: messages,
        isRecording: false,
        isWaitingForResponse: false));
  }

  void _handleStreamDone() {
    AppLogger.d("WebSocket connection closed , stream DONE?");
    emit(state.copyWith(
        connState: ConnState.done,
        isWaitingForResponse: false,
        loading: false));
  }

  Future<void> pickImage() async {
    final result = await FilePicker.platform
        .pickFiles(type: FileType.image, withReadStream: true);

    if (result == null || result.files.isEmpty) {
      throw Exception('No files picked or file picker was canceled');
    }

    final file = result.files.single;
    AppLogger.d("Image selected: ${file.name}");
    AppLogger.d("Image size: ${file.size}");

    // Read file data from stream instead of using bytes
    if (file.readStream == null) {
      throw Exception('File stream is not available');
    }

    try {
      // Collect all bytes from the stream
      final List<int> bytes = [];
      await for (final chunk in file.readStream!) {
        bytes.addAll(chunk);
      }

      String base64ImageStr = base64Encode(bytes);
      String fileNameStr = file.name;
      AppLogger.d("Image processed from stream: $fileNameStr");
      emit(state.copyWith(base64Image: base64ImageStr, fileName: fileNameStr));
    } catch (e) {
      AppLogger.e("Error reading file stream: $e");
      throw Exception('Failed to read file data from stream: $e');
    }
  }

  Future<void> reconnect() async {
    try {
      emit(state.copyWith(loading: true, messages: []));
      _aiController.close();
      await _connectToAI();
    } catch (e) {
      AppLogger.e("Reconnection failed: $e");
      emit(state.copyWith(connState: ConnState.none, loading: false));
    }
  }

  void resetConnectionState() {
    emit(state.copyWith(connState: ConnState.none));
  }

  /// Clear the aiTripIntent from the state
  void clearAiTripIntent() {
    emit(state.copyWith(clearAiTripIntent: true));
    AppLogger.d("Cleared aiTripIntent from chat state");
  }

  Future<void> sendMessage(
      String message, String? base64Image, String? fileName,
      {bool? autoSpeak, bool addToChatLog = true, bool usePrePrompt = true}) async {
    final List<Map<String, dynamic>> allMessages = List.from(state.messages);
    final newMessage = {
      'message': message,
      'type': 'user',
      'message_type': MessageType.text,
      'auto_speak': autoSpeak ?? false,
      'timestamp': DateFormat('HH:mm').format(DateTime.now()),
    };
    if (addToChatLog) allMessages.add(newMessage);

    emit(state.copyWith(
        messages: allMessages,
        connState: ConnState.success,
        isWaitingForResponse: true,
        base64Image: base64Image,
        fileName: fileName));
    final jsonMessage = jsonEncode({
      "command": "ai_chat",
      "message": message,
      "auto_create": ChatContext.autoCreateFalse,
      "file": base64Image,
      "file_name": fileName,
      "prompt": parameter.chatContext.aiPrompt,
      "pre_prompt": usePrePrompt ? parameter.chatContext.aiPrePrompt ?? '' : '',
      "pre_prompt_context": parameter.chatContext.extraChatContext
    });

    if (base64Image != null) {
      AppLogger.d("Sending message with attached file name: $fileName");
    } else {
      AppLogger.d("Sending message: $jsonMessage");
    }
    _aiController.sendMessage(jsonMessage);
  }

  // Test method to trigger VisualTripPlannerRoute
  void testVisualTripPlanner() {
    // Don't close the chat channel - let it stay connected
    // The chat screen will handle reconnection when returning from VTP

    final context = navigatorKey.currentContext;
    if (context != null) {
      final sampleData = {
        'trip_intent': 'Direct', //'Discovery',
        'destination': 'New York'
      };

      context.router.push(
        VisualTripPlannerRoute(
          parameter: VisualTripPlannerParameter(
            initialData: sampleData,
          ),
        ),
      );
    }
  }

  Future<EventModels> createEvent(FlEvent aiEvent) async {
    final FlEvent event = aiEvent;
    //final eventModel = event.toEventModel();

    //*** HERE ALL TIME SHOULD BE IN UTC Already */

    DateTime startDate = event.fromDate.toDateTime();
    DateTime endDate = event.toDate.toDateTime();
    DateTime? reminderTime;

    //     if(state.isAllDay) {
    //       startDate = DateTime(startDate.year, startDate.month, startDate.day);
    //       endDate = DateTime(endDate.year, endDate.month, endDate.day);
    //     }

    //just set reminder to 30min
    var defaultReminder = locator.get<LocalStorage>().defaultReminder;
    if (defaultReminder > 0) {
      reminderTime = startDate.subtract(Duration(minutes: defaultReminder));
    }

    final result = await eventUsecase.call(
      EventParameter(
        uuid: '',
        //sure it's empty
        name: event.name.trim(),
        fromDate: startDate.toIso8601String(),
        toDate: endDate.toIso8601String(),
        color: '',
        caption: '',
        description: event.description.trim(),
        familyId: accountService.familyId,
        activityId: '',
        notificationStatus: '',
        notificationTime: reminderTime?.toIso8601String() ?? '',
        // repeatType: EventRepeatType.none,
        members: [],
      ),
    );

    logd("Event created successfully");
    logd("Event is: $result");
    homeCubit.onRefresh();
    return result;
  }

  // Add this public method for testing
  @visibleForTesting
  void handleStreamDataForTesting(dynamic data) {
    _handleStreamData(data);
  }

  // Speech-related methods
  Future<void> sendSpeechMessage(String? latestTranscribedText) async {
    final recognizedWords = latestTranscribedText;
    if (recognizedWords != null && recognizedWords.isNotEmpty) {
      AppLogger.d("STT: Sending recognized words: $recognizedWords");
      await sendMessage(recognizedWords, null, null, autoSpeak: true);
    } else {
      emit(state.copyWith(errorMessage: 'No speech detected'));
    }
  }

  Future<void> startListening() async {
    await _speechController.startListening();
  }

  Future<void> stopListening() async {
    await _speechController.stopListening();
  }

  Future<void> cancelListening() async {
    await _speechController.cancelListening();
  }

  Future<void> stopTTSAndSTT() async {
    try {
      // logd("Stopping TTS and STT");
      await _speechController.cancelListening();
      await flutterTts.stop();

      isFlutterTtsSpeaking = false;
      emit(state.clearRecordingInfo());
    } catch (e) {
      AppLogger.e('Error stopping speech recognition and TTS: $e');
      emit(state.copyWith(
          isRecording: false,
          errorMessage: 'Failed to stop speech recognition and TTS'));
    }
  }

  
  Future<void> handleRecommendationsPressed(List<SuggestionItem> items, {Function(List<String>)? onSelectDay}) async {
    // For edit trip, preserve the existing trip ID
    if (parameter.chatContext.purposeKey == AIPurposeKey.customizeTrip ||
        parameter.chatContext.purposeKey == AIPurposeKey.editATrip) {
      final activity = parameter.customData! as ActivityModel;

      final days_ = activity.getValidDaysForActivities();

      // Call the callback to show the bottom sheet in the UI
      if (onSelectDay != null) {
        onSelectDay(days_ ?? []);
      }

      // create a recommendations array with the above list, and show a bottomsheet (call in chat_screen) to allow user to select which day. then come back here with the string
    } else {
      //TEST
      final days = <String>[];
      for (int i = 2; i < 10; i++) {
        days.add('Day $i');
      }
      // Call the callback to show the bottom sheet in the UI
      if (onSelectDay != null) {
        onSelectDay(days);
      }
    }

    return;
  }

  Future<void> addRecommendationsToDay(String selectedDay, List<SuggestionItem> items) async {
    // Prepare the message to send to AI
    final recommendations = items.map((e) => e.toJson()).toList();

    // For edit trip, preserve the existing trip ID
    if (parameter.chatContext.purposeKey == AIPurposeKey.customizeTrip ||
        parameter.chatContext.purposeKey == AIPurposeKey.editATrip) {
      //Build a message to AI :
      //"Add $item.toList() to day $x of my trip."
      final message = 'Please add $recommendations to the $selectedDay of my trip and resolve any conflict yourself.';
      sendMessage(message, null, null, usePrePrompt: false, addToChatLog: false);
    } else {
      throw Exception("Not supported");
    }
  }


}

/// Helper class to organize response data from AI processing
class _ResponseData {
  final Trip? aiTrip;
  final FlEvent? aiEvent;
  final FlList? aiList;
  final Map<String, dynamic>? aiTripIntent;
  final Map<String, dynamic> message;

  _ResponseData({
    this.aiTrip,
    this.aiEvent,
    this.aiList,
    this.aiTripIntent,
    required this.message,
  });
}
