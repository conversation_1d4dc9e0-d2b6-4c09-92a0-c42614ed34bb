import 'package:flutter_timezone/flutter_timezone.dart';

enum AIPurposeKey {
  generalPurpose,
  planATrip,
  editATrip,
  planAnEvent,
  editAnEvent,
  createAList,
  editAList,
  customizeTrip,
}

final String ai_extra_message =
    ""; //"please remember to provide suggestions in JSON format.";

// a table mapping the purpose key to the corresponding message
final Map<AIPurposeKey, String> purposeKeyToMessage = {
  AIPurposeKey.generalPurpose: 'Greetings. $ai_extra_message',
  AIPurposeKey.planATrip: 'Hi, I need to plan a trip $ai_extra_message',
  AIPurposeKey.editATrip:
      'Hi, I need to modify an existing trip $ai_extra_message',
  AIPurposeKey.planAnEvent: 'Hi, I need to plan an event.',
  AIPurposeKey.editAnEvent: 'Hi, I need to modify an existing event.',
  AIPurposeKey.createAList: 'Hi, I need to create a list.',
  AIPurposeKey.editAList: 'Hi, I need to update an existing list.',
  AIPurposeKey.customizeTrip: "I want to customize a few things in this trip.",
};

class ChatContext {
  final String token;
  final AIPurposeKey purposeKey;
  final String? tripId;
  final String? eventId;
  final String? listId;
  final String? aiPrompt; // "vtp_plan", "vtp_discover",
  final String? aiPrePrompt; // "intent_router" or NULL
  final String?
      extraChatContext; // string indicating the context for the question, for eg: "pharmacy near me" is too generic, the extraChatContext will give further location like : "Ho Chi minh City" or "Ha Noi" or "District 1, Ho Chi minh City" - geocoding info

  static const String autoCreateFalse = "false";
  ChatContext(
      {required this.token,
      required this.purposeKey,
      this.tripId,
      this.eventId,
      this.listId,
      this.aiPrompt,
      this.aiPrePrompt,
      this.extraChatContext});

  //return a Map String: List<String> to represent the inital message to be sent to AI bot
  Future<Map<String, String>> getInitMessage() async {
    //get the purpose message
    String currentTimeZone = await FlutterTimezone.getLocalTimezone();
    Map<String, String> initMessage = {};

    String timeZone_msg =
        "Please the current time and location in mind. It is now: ${DateTime.now().toIso8601String()} at $currentTimeZone. ";

    //get the purpose message and add to initMessage Map
    switch (purposeKey) {
      case AIPurposeKey.planATrip:
        initMessage['command'] = "ai_init";
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.planATrip]!;
        initMessage['auto_create'] = autoCreateFalse;
        break;
      case AIPurposeKey.editATrip:
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.editATrip]!;
        initMessage['uuid'] = tripId!;
        initMessage['command'] = "ai_edit";
        initMessage['type'] = "activity";
        initMessage['auto_create'] = autoCreateFalse;

        break;
      case AIPurposeKey.planAnEvent:
        initMessage['command'] = "ai_init";
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.planAnEvent]!;
        break;
      case AIPurposeKey.editAnEvent:
        initMessage['command'] = "ai_edit";
        initMessage['type'] = "event";
        // initMessage['auto_create'] = "true";
        initMessage['uuid'] = eventId!;
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.editAnEvent]!;
        break;
      case AIPurposeKey.createAList:
        initMessage['command'] = "ai_init";
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.createAList]!;
        break;
      case AIPurposeKey.editAList:
        initMessage['command'] = "ai_edit";
        initMessage['type'] = "list";
        // initMessage['auto_create'] = "true";
        initMessage['uuid'] = listId!;
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.editAList]!;
        break;
      case AIPurposeKey.customizeTrip:
        // The context of this is special, the chat has started already, we just continue conversation here. simply ask for modifying the trip. ai prompt is vtp_plan
        initMessage['command'] = "ai_chat";
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.customizeTrip]!;
        break;
      default:
        initMessage['command'] = "ai_init";
        initMessage['message'] =
            timeZone_msg + purposeKeyToMessage[AIPurposeKey.generalPurpose]!;
    }
    //attach ai_prompt if not null
    if (aiPrompt != null) {
      initMessage['prompt'] = aiPrompt!;
    }

    return initMessage;
  }

  static ChatContext getGeneralChatContext(String userToken) {
    return ChatContext(
        token: userToken, purposeKey: AIPurposeKey.generalPurpose);
  }

  static ChatContext getEditTripContext(String token, String tripUuid,
      {String geoCoding = ''}) {
    String aiPrompt = "vtp_edit"; // need an constant list for all ai prompts.
    const String prePrompt = "intent_router";
    return ChatContext(
        token: token,
        purposeKey: AIPurposeKey.editATrip,
        tripId: tripUuid,
        aiPrompt: aiPrompt,
        aiPrePrompt: prePrompt,
        extraChatContext: geoCoding);
  }

  static ChatContext getEditEventContext(String token, String tripUuid,
      {String? aiPrompt}) {
    return ChatContext(
        token: token,
        purposeKey: AIPurposeKey.editAnEvent,
        eventId: tripUuid,
        aiPrompt: aiPrompt);
  }

  static ChatContext getCustomizeTripContext(String userToken,
      {String prePromptContext = ''}) {
    const String aiPrompt = "vtp_edit";
    const String prePrompt = "intent_router";
    return ChatContext(
        token: userToken,
        purposeKey: AIPurposeKey.customizeTrip,
        aiPrompt: aiPrompt,
        aiPrePrompt: prePrompt,
        extraChatContext: prePromptContext);
  }
}
