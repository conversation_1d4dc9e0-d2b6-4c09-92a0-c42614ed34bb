import 'dart:async';
import 'package:family_app/utils/speech/speech_controller.dart';
import 'package:family_app/utils/log/app_logger.dart';

class ChatSpeechController {
  final SpeechController _speechController;

  // TTS integration flags
  bool _isTTSSpeaking = false;
  bool _shouldRestartAfterTTS = false;

  // Callbacks
  final Function(String) onSpeechFinalized;
  final Function(bool) onRecordingStateChanged;
  final Function(String) onError;

  ChatSpeechController({
    required HeuristicType heuristicType,
    required this.onSpeechFinalized,
    required this.onRecordingStateChanged,
    required this.onError,
  }) : _speechController = SpeechController.create(type: heuristicType) {
    _setupCallbacks();
  }

  void _setupCallbacks() {
    _speechController.setCallbacks(
      onSpeechFinalized: (text) {
        //call speech finalized - this will call send text message to AI
        onSpeechFinalized(text);

        // it's not our turn to talk yet.. so just wait
        _shouldRestartAfterTTS = true;

        //set it here so that onStatus done will not restart listenning
        _isTTSSpeaking = true;
      },
      onError: onError,
      onStatus: (status) {
        // Handle status changes and restart logic

        // Handle restart logic when STT engine stops by itself
        if (status == 'done' &&
            _speechController.isListening &&
            !_isTTSSpeaking) {
          AppLogger.d(
              "ChatSpeechController: STT engine stopped, restarting listening");
          _restartListening();
        } else if (status == 'done') {
          logd(
              "Not restart listening because: status: $status, isListening: ${_speechController.isListening} isSpking:$_isTTSSpeaking");
        }
      },
      onRecordingStateChanged: (isRecording) {
        onRecordingStateChanged(isRecording);
      },
    );
  }

  /// Initialize the speech controller
  Future<void> initialize() async {
    await _speechController.initialize();
  }

  /// Start listening for speech (always in hands-free mode)
  Future<void> startListening() async {
    await _speechController.startListening(uiUpdate: true);
  }

  /// Stop listening for speech
  Future<void> stopListening() async {
    //prevent auto start after TTS
    _shouldRestartAfterTTS = false;

    // await _speechController.stopListening(); - can't call this because it will not changed isRecording
    await _speechController.cancelListening(uiUpdate: true);
  }

  /// Cancel listening for speech
  Future<void> cancelListening() async {
    await _speechController.cancelListening();
  }

  /// Called when TTS starts speaking
  void onTTSStarted() {
    _isTTSSpeaking = true;
    AppLogger.d("ChatSpeechController: TTS started, stopping STT");

    // Stop STT when TTS starts
    if (_speechController.isListening) {
      cancelListening();
    }
  }

  /// Called when TTS completes speaking
  void onTTSCompleted() {
    _isTTSSpeaking = false;
    AppLogger.d("ChatSpeechController: TTS completed");

    // Restart STT if it was requested
    if (_shouldRestartAfterTTS) {
      _shouldRestartAfterTTS = false;
      AppLogger.d("ChatSpeechController: Restarting STT after TTS completion");
      _restartListening();
    }
  }

  /// Restart listening when STT engine stops by itself
  Future<void> _restartListening() async {
    try {
      AppLogger.d("ChatSpeechController: Restarting listening");
      await _speechController.cancelListening();

      // Small delay to ensure clean restart
      //await Future.delayed(const Duration(milliseconds: 100));

      // Restart listening if TTS is not speaking
      await _speechController.startListening();
    } catch (e) {
      AppLogger.e("ChatSpeechController: Error restarting listening: $e");
      onError("Failed to restart speech recognition");
    }
  }

  /// Get current listening state
  bool get isListening => _speechController.isListening;

  /// Get current text
  String get text => _speechController.text;

  /// Get current locale ID
  String get currentLocaleId => _speechController.currentLocaleId;

  /// Get error message if any
  String? get errorMessage => _speechController.errorMessage;

  /// Dispose the controller
  void dispose() {
    _speechController.dispose();
  }
}
