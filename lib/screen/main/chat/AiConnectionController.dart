import 'dart:async';
import 'dart:convert';
import 'package:rxdart/rxdart.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

enum ConnectionState {
  disconnected,
  connecting,
  connected,
  error,
  closed,
}

/// Handles WebSocket connection and communication with the AI server.
class AiConnectionController {
  WebSocketChannel? _channel;
  Timer? _timeoutTimer;
  final Duration timeoutDuration;
  ConnectionState _connectionState = ConnectionState.disconnected;

  // Callbacks for events
  void Function(dynamic data)? onMessage;
  void Function(dynamic error)? onError;
  void Function()? onDone;
  void Function()? onTimeout;

  AiConnectionController({
    this.onMessage,
    this.onError,
    this.onDone,
    this.onTimeout,
    Duration? timeout,
  }) : timeoutDuration = timeout ?? const Duration(seconds: 60);

  ConnectionState get connectionState => _connectionState;
  bool get isConnected => _connectionState == ConnectionState.connected;

  void setCallbacks({
    void Function(dynamic data)? onMessage,
    void Function(dynamic error)? onError,
    void Function()? onDone,
    void Function()? onTimeout,
  }) {
    this.onMessage = onMessage;
    this.onError = onError;
    this.onDone = onDone;
    this.onTimeout = onTimeout;
  }

  Future<void> connect(String url, {dynamic initMessage}) async {
    if (_connectionState == ConnectionState.connected) {
      return; // Already connected
    }

    _connectionState = ConnectionState.connecting;

    try {
      _channel = WebSocketChannel.connect(Uri.parse(url));
      _channel!.stream.listen(
        (data) {
          _cancelTimeout();
          if (onMessage != null) onMessage!(data);
        },
        onError: (error) {
          _cancelTimeout();
          _connectionState = ConnectionState.error;
          if (onError != null) onError!(error);
        },
        onDone: () {
          _cancelTimeout();
          _connectionState = ConnectionState.closed;
          if (onDone != null) onDone!();
        },
      );
      await _channel!.ready;
      if (initMessage != null) {
        _channel?.sink.add(jsonEncode(initMessage));
      }
      _connectionState = ConnectionState.connected;
      _startTimeout();
    } catch (e) {
      _cancelTimeout();
      _connectionState = ConnectionState.error;
      if (onError != null) onError!(e);
    }
  }

  void close() {
    _cancelTimeout();
    _connectionState = ConnectionState.disconnected;


    _channel?.sink.close();
  }

  void sendMessage(dynamic message) {
    if (_connectionState != ConnectionState.connected) {
      throw Exception('Cannot send message: not connected');
    }
    _channel?.sink.add(message);
    _startTimeout();
  }

  void _startTimeout() {
    _cancelTimeout();
    _timeoutTimer = Timer(timeoutDuration, () {
      _connectionState = ConnectionState.error;
      if (onTimeout != null) onTimeout!();
    });
  }

  void _cancelTimeout() {
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
  }

  // Add more methods as needed for reconnection, state, etc.
}
