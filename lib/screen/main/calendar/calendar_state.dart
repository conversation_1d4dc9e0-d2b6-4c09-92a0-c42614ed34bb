import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/calendar.dart';
import 'package:family_app/data/model/event.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class CalendarState extends BaseState {
  final FLCalendarView calendarViewFL;
  final DateTime currentMonth;
  final DateTime? currentDate;
  final List<DateTime> currentDates;
  final List<EventModels> models;
  final Map<String, String>? holidays;
  final bool isLoadingHolidays;
  final bool weekStartForMonday;

  CalendarState({
    this.calendarViewFL = FLCalendarView.agenda,
    required this.currentMonth,
    this.currentDate,
    this.currentDates = const [],
    this.models = const [],
    this.holidays,
    this.isLoadingHolidays = false,
    this.weekStartForMonday = false,
  });

  @override
  List<Object?> get props => [
        calendarViewFL,
        currentMonth,
        models,
        currentDates,
        currentDate,
        holidays,
        isLoadingHolidays,
        weekStartForMonday,
      ];

  CalendarState copyWith({
    FLCalendarView? calendarViewFL,
    DateTime? currentMonth,
    DateTime? currentDate,
    List<EventModels>? models,
    List<DateTime>? currentDates,
    Map<String, String>? holidays,
    bool? isLoadingHolidays,
    bool? weekStartForMonday,
  }) {
    return CalendarState(
      calendarViewFL: calendarViewFL ?? this.calendarViewFL,
      currentMonth: currentMonth ?? this.currentMonth,
      models: models ?? this.models,
      currentDate: currentDate ?? this.currentDate,
      currentDates: currentDates ?? this.currentDates,
      holidays: holidays ?? this.holidays,
      isLoadingHolidays: isLoadingHolidays ?? this.isLoadingHolidays,
      weekStartForMonday: weekStartForMonday ?? this.weekStartForMonday,
    );
  }

  CalendarView get calendarViewType {
    switch (calendarViewFL) {
      case FLCalendarView.month:
        return CalendarView.month;
      case FLCalendarView.week:
        return CalendarView.week;
      case FLCalendarView.day:
        return CalendarView.day;
      case FLCalendarView.agenda:
        return CalendarView.month;
    }
  }

  get firstDayOfWeek => weekStartForMonday ? 1 : 7;

  bool isHoliday(DateTime date) {
    final key = "${date.month}-${date.day}";
    return holidays?.containsKey(key) ?? false;
  }
}
