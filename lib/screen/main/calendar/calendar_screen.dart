import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/calendar.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:family_app/screen/main/calendar/calendar_state.dart';
import 'package:family_app/screen/main/calendar/overlay/calendar_switch_overlay.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import 'setting/calendar_setting_screen.dart';
import 'widget/agenda_view.dart';
import 'widget/calendar_filter_view.dart';
import 'widget/calendar_month_year_view.dart';
import 'widget/day_or_week_view.dart';
import 'widget/month_view.dart';
import 'package:family_app/screen/main/main_screen.dart' show calendarFabPressed;

@RoutePage()
class CalendarPage extends BaseBlocProvider<CalendarState, CalendarCubit> {
  const CalendarPage({super.key});

  @override
  Widget buildPage() => const CalendarScreenView();

  @override
  CalendarCubit createCubit() => CalendarCubit(eventRepository: locator.get(), accountService: locator.get(), calendarService: locator.get(), eventService: locator.get());
}

class CalendarScreenView extends StatefulWidget {
  const CalendarScreenView({super.key});

  @override
  State<CalendarScreenView> createState() => _CalendarScreenViewState();
}

class _CalendarScreenViewState extends BaseBlocPageState<CalendarScreenView, CalendarState, CalendarCubit> with SingleTickerProviderStateMixin {
  late final CalendarSwitchOverlay overlay;
  final GlobalKey calendarKey = GlobalKey(debugLabel: 'calendarKey');

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get showBack => false;

  @override
  void onTapScreen(BuildContext context) {
    overlay.hideOverlay();
  }

  @override
  void initState() {
    super.initState();
    overlay = CalendarSwitchOverlay(
      provider: this,
      itemOverlayClick: (calendarView) {
        final cubit = context.read<CalendarCubit>();
        cubit.onChangeCalendarView(calendarView);
      },
    );
    calendarFabPressed.addListener(_onFabPressed);
  }

  void _onFabPressed() {
    if (calendarFabPressed.value) {
      final cubit = context.read<CalendarCubit>();
      final selectedDate = cubit.calendarController.selectedDate;
      if (selectedDate != null) {
        if (selectedDate.isBeforeNow) {
          showSimpleToast(LocaleKeys.unable_to_create_event_in_the_past.tr());
          calendarFabPressed.value = false;
          return;
        }
        cubit.onCreateNewEvent(context, selectedDate);
      } else {
        print('No date selected');
      }
      calendarFabPressed.value = false;
    }
  }

  @override
  void dispose() {
    overlay.dispose();
    calendarFabPressed.removeListener(_onFabPressed);
    super.dispose();
  }

  @override
  Widget buildAppBar(BuildContext context, CalendarCubit cubit, CalendarState state) {
    return CustomAppBar2(
      title: LocaleKeys.calendar.tr(),
      showBack: true,
      actions: [
        CircleItem(
          onTap: () async {
            await BottomSheetUtils.showHeight(context, child: const CalendarSettingPage());
          },
          backgroundColor: appTheme.backgroundV2,
          padding: padding(all: 2),
          child: Assets.icons.icSettingOutline.svg(width: 32, height: 32),
        ),
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, CalendarCubit cubit, CalendarState state) {
    cubit.calendarController.selectedDate = DateTime.now();

    final headerBar = _buildCalendarHeaderBar(cubit, state);

    return Stack(children: [
      Padding(
        padding: padding(horizontal: 6.0, top: 8.0),
        child: _buildMainContent(context, cubit, state, headerBar),
      ),
    ]);
  }

  Widget _buildCalendarHeaderBar(CalendarCubit cubit, CalendarState state) {
    return Row(
      children: [
        CalendarMonthYearView(
          calendarViewFL: state.calendarViewFL,
          currentDates: state.currentDates,
          currentMonth: state.currentMonth,
          onMonthYearChanged: (datePicked) {
            cubit.calendarController.displayDate = datePicked;
          },
        ),
        CalendarFilterView(
          overlay: overlay,
          context: context,
          calendarKey: calendarKey,
          calendarViewFL: state.calendarViewFL,
          onSearchTap: () {
            context.pushRoute(const SearchRoute());
            overlay.hideOverlay();
          },
        ),
      ],
    );
  }

  Widget _buildMainContent(BuildContext context, CalendarCubit cubit, CalendarState state, Widget headerBar) {
    switch (state.calendarViewFL) {
      case FLCalendarView.agenda:
        return AgendaView(
          cubit: cubit,
          state: state,
          overlay: overlay,
          calendarKey: calendarKey,
          onCalendarTap: (buildContext, calendarTapDetails, calendarCubit, calendarState) => handleCalendarTap(buildContext, calendarTapDetails, calendarCubit, calendarState, overlay),
          context: context,
          headerBar: headerBar,
        );
      case FLCalendarView.day:
      case FLCalendarView.week:
        return DayOrWeekView(
          cubit: cubit,
          state: state,
          overlay: overlay,
          calendarKey: calendarKey,
          onCalendarTap: (buildContext, calendarTapDetails, calendarCubit, calendarState) => handleCalendarTap(buildContext, calendarTapDetails, calendarCubit, calendarState, overlay),
          context: context,
          headerBar: headerBar,
        );
      case FLCalendarView.month:
        return MonthView(
          cubit: cubit,
          state: state,
          overlay: overlay,
          calendarKey: calendarKey,
          onCalendarTap: (buildContext, calendarTapDetails, calendarCubit, calendarState) => handleCalendarTap(buildContext, calendarTapDetails, calendarCubit, calendarState, overlay),
          context: context,
          headerBar: headerBar,
        );
    }
  }

  void handleCalendarTap(
    BuildContext context,
    CalendarTapDetails calendarTapDetails,
    CalendarCubit cubit,
    CalendarState state,
    CalendarSwitchOverlay overlay,
  ) {
    logd("onCalendarTap: ${calendarTapDetails.targetElement} => calendarTapDetails.date => ${calendarTapDetails.date}");
    overlay.hideOverlay();
    cubit.selectedDateStreamController.sink.add(calendarTapDetails.date ?? DateTime.now());
    if (calendarTapDetails.targetElement == CalendarElement.calendarCell) {
      return;
    }
    logd("calendarTapDetails: $calendarTapDetails , target: ${calendarTapDetails.targetElement}");
    if (calendarTapDetails.appointments?.isNotEmpty ?? false) {
      logd("number of appointments: ${calendarTapDetails.appointments!.length}");
      if (calendarTapDetails.appointments!.length == 1) {
        cubit.onTapEvent(context, calendarTapDetails.appointments!.first as EventModels);
      }
      return;
    }
  }
}
