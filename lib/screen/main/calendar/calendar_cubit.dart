import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/service/holiday_service.dart';
import 'package:family_app/config/service/location_service.dart';
import 'package:family_app/data/model/calendar.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/calendar/calendar_state.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/calendar.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class CalendarCubit extends BaseCubit<CalendarState> {
  final IEventRepository eventRepository;
  final AccountService accountService;
  final CalendarService calendarService;
  final EventService eventService;
  String? _userCountry;
  bool _isInitialized = false;
  static const String _logTag = 'CalendarCubit';

  CalendarCubit({
    required this.eventRepository,
    required this.accountService,
    required this.calendarService,
    required this.eventService,
  }) : super(CalendarState(
          currentMonth: DateTime.now(),
          models: eventService.events.value,
        )) {
    logd("Init: ${state.currentMonth}", tag: _logTag);
  }

  CalendarController calendarController = CalendarController();

  StreamController<DateTime> selectedDateStreamController = StreamController<DateTime>.broadcast();

  StreamSubscription? _eventSub;
  StreamSubscription<bool>? _weekStartSub;

  static const Duration _importThrottleDuration = Duration(seconds: 15);
  DateTime? _lastImportTime;

  @override
  void onInit() async {
    super.onInit();
    await initializeUserCountry();
    await initializeWeekStart();
    await fetchAllCalendarData(importDeviceCalendar: false);
    _subscribeToEventStream();
    _subscribeToWeekStartStream();
  }

  Future<void> initializeUserCountry() async {
    if (_isInitialized) return;
    String? country;
    try {
      country = await LocationService.getUserCountry();
    } catch (e) {
      logd('Error initializing user country: $e', tag: _logTag);
      country = 'US';
    }
    _userCountry = country;
    _isInitialized = true;
  }

  Future<void> initializeWeekStart() async {
    final startOfWeekMonday = CalendarService.instance.isStartOfWeekMonday();
    emit(state.copyWith(weekStartForMonday: startOfWeekMonday));
  }

  Future<void> fetchAllCalendarData({bool importDeviceCalendar = true}) async {
    if (importDeviceCalendar) {
      final now = DateTime.now();
      if (_lastImportTime == null || now.difference(_lastImportTime!) > _importThrottleDuration) {
        _lastImportTime = now;
        calendarService.importEventFromAllDeviceCalendarWhenYearChanged(state.currentMonth);
      }
    }
    await Future.wait([
      onFetchEvent(combineAndDedupe: false),
      onFetchHolidays(),
    ]);
  }

  @override
  Future<void> close() {
    _unsubscribeFromEventStream();
    _unsubscribeFromWeekStartStream();
    _prefetchDebounceTimer?.cancel();
    return super.close();
  }

  void _subscribeToEventStream() {
    _eventSub?.cancel();
    _eventSub = eventService.events.stream.listen((events) {
      emit(state.copyWith(models: events));
    });
  }

  void _unsubscribeFromEventStream() {
    _eventSub?.cancel();
    _eventSub = null;
  }

  void _subscribeToWeekStartStream() {
    _weekStartSub?.cancel();
    _weekStartSub = CalendarService.instance.weekStartMondayStream.stream.listen((isMonday) {
      emit(state.copyWith(weekStartForMonday: isMonday));
    });
  }

  void _unsubscribeFromWeekStartStream() {
    _weekStartSub?.cancel();
    _weekStartSub = null;
  }

  onTapEvent(BuildContext context, EventModels event) async {
    await context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter(model: event)));
  }

  void onChangeCurrentCalendarDate(List<DateTime> date) {
    if (date.isEmpty) return;

    DateTime selectedMonth;
    if (state.calendarViewFL == FLCalendarView.week) {
      selectedMonth = date.first;
    } else {
      selectedMonth = date[date.length ~/ 2];
    }
    // Delegate to onChangeCurrentMonth
    onChangeCurrentMonth(selectedMonth, currentDates: date);
  }

  Future<void> onChangeCurrentMonth(DateTime newMonth, {List<DateTime>? currentDates}) async {
    // Check year change before updating state
    final previousMonth = state.currentMonth;
    final isYearChanged = previousMonth.year != newMonth.year;
    final isMonthChanged = previousMonth.year != newMonth.year || previousMonth.month != newMonth.month;
    final shouldUpdateCurrentDates = currentDates != null && currentDates != state.currentDates;

    // Always update state if month or dates changed
    if (isMonthChanged || shouldUpdateCurrentDates) {
      emit(state.copyWith(
        currentMonth: isMonthChanged ? newMonth : previousMonth,
        currentDates: currentDates ?? state.currentDates,
      ));
      _debouncedPrefetchEventsAndFetchHolidays(newMonth, previousMonth, isMonthChanged);
    }
    // Import device calendar events if the year has changed (check before state update)
    if (isYearChanged) {
      calendarService.importEventFromAllDeviceCalendarWhenYearChanged(newMonth);
    }
  }

  Timer? _prefetchDebounceTimer;
  void _debouncedPrefetchEventsAndFetchHolidays(DateTime newMonth, DateTime previousMonth, bool isMonthChanged) {
    _prefetchDebounceTimer?.cancel();
    _prefetchDebounceTimer = Timer(const Duration(milliseconds: 600), () async {
      final prefetch = CalendarUtils.getPrefetchInfo(newMonth, previousMonth);
      if (prefetch != null) {
        final from = prefetch['from'] as DateTime;
        final to = prefetch['to'] as DateTime;
        final months = ((to.year - from.year) * 12 + (to.month - from.month)) + 1;
        logd('[onChangeCurrentMonth] Pre-fetching ${prefetch['direction']} $months month(s): $from - $to', tag: _logTag);
        await onFetchEvent(currentMonth: prefetch['month'], from: from, to: to);
      }
      onFetchHolidays();
    });
  }

  void onChangeCalendarView(FLCalendarView view) {
    emit(state.copyWith(calendarViewFL: view));
    calendarController.view = state.calendarViewType;
  }

  /// Returns the fetch range (from, to) for event fetching, using either provided or default 3-month range.
  List<DateTime> _getFetchRange({DateTime? currentMonth, DateTime? from, DateTime? to}) {
    final DateTime month = currentMonth ?? state.currentMonth;
    if (from != null && to != null) {
      return [from, to];
    } else {
      final monthRange = CalendarUtils.getThreeMonthRangeUtc(month);
      return [monthRange[0], monthRange[1]];
    }
  }

  Future<void> onFetchEvent({DateTime? currentMonth, DateTime? from, DateTime? to, bool combineAndDedupe = true}) async {
    final range = _getFetchRange(currentMonth: currentMonth, from: from, to: to);
    final fetchFrom = range[0];
    final fetchTo = range[1];

    try {
      await eventService.fetchEventByFamilyId(accountService.familyId, from: fetchFrom, to: fetchTo, combineAndDedupe: combineAndDedupe);
      emit(state.copyWith(models: eventService.events.value));
    } catch (e) {
      logd('Error fetching events: $e', tag: _logTag);
    } finally {
      dismissLoading();
    }
  }

  Future<void> onFetchHolidays() async {
    if (!_isInitialized) return;

    emit(state.copyWith(isLoadingHolidays: true));
    try {
      final holidays = await HolidayService.getHolidays(
        _userCountry ?? 'US',
        state.currentMonth.year,
        state.currentMonth.month,
      );
      emit(state.copyWith(holidays: holidays, isLoadingHolidays: false));
    } catch (e) {
      logd('Error fetching holidays: $e', tag: _logTag);
      emit(state.copyWith(isLoadingHolidays: false));
    }
  }

  void removeEvent(String id) {
    eventService.deleteEventAndRefresh(id, accountService.familyId);
  }

  Future<void> onCreateNewEvent(BuildContext context, DateTime selectedDate) async {
    await context.pushRoute(UpsertEventRoute(upsertEventParameter: UpsertEventParameter(dateTime: selectedDate)));
  }
}
