import 'package:flutter/material.dart';

class OverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var height = 8.0;
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill
      ..strokeWidth = 1;
    final path = Path()
      ..moveTo(size.width * .7, height)
      ..lineTo(size.width * .75, 0)
      ..lineTo(size.width * .8, height)
      ..lineTo(size.width * .7, height)
      ..close();

    canvas.drawPath(path, paint);
    canvas.drawRRect(
        RRect.fromRectAndRadius(
            Rect.fromCenter(
                center: Offset(size.width / 2, size.height / 2),
                width: size.width,
                height: size.height - (height * 2)),
            Radius.circular(12)),
        paint);
  }

  @override
  bool shouldRepaint(OverlayPainter oldDelegate) => false;

  @override
  bool shouldRebuildSemantics(OverlayPainter oldDelegate) => false;
}
