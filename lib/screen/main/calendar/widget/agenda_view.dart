import 'package:flutter/material.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:family_app/screen/main/calendar/calendar_state.dart';
import 'package:family_app/screen/main/calendar/widget/monthly_header.dart';
import 'package:family_app/screen/main/calendar/widget/event_month_calendar.dart';
import 'package:family_app/screen/main/calendar/widget/day_range_event_view.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import '../overlay/calendar_switch_overlay.dart';

class AgendaView extends StatelessWidget {
  final CalendarCubit cubit;
  final CalendarState state;
  final CalendarSwitchOverlay overlay;
  final GlobalKey calendarKey;
  final void Function(BuildContext, CalendarTapDetails, CalendarCubit, CalendarState) onCalendarTap;
  final BuildContext context;
  final Widget? headerBar;

  const AgendaView({
    super.key,
    required this.cubit,
    required this.state,
    required this.overlay,
    required this.calendarKey,
    required this.onCalendarTap,
    required this.context,
    this.headerBar,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: cubit.fetchAllCalendarData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(24)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                spacing: 12.0,
                children: [
                  if (headerBar != null) headerBar!,
                  MonthlyHeader(
                    context: this.context,
                    calendarViewFL: state.calendarViewFL,
                    weekStartForMonday: state.weekStartForMonday,
                    currentDates: state.currentDates,
                    isHoliday: state.isHoliday,
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.4,
                    child: BaseStreamBuilder<List<EventModels>>(
                      controller: cubit.eventService.events,
                      builder: (events) {
                        return EventCalendar(
                          cubit: cubit,
                          state: state.copyWith(models: events),
                          onCalendarTap: (context, calendarTapDetails, cubit, state) => onCalendarTap(context, calendarTapDetails, cubit, state),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            StreamBuilder<DateTime>(
                stream: cubit.selectedDateStreamController.stream,
                initialData: DateTime.now(),
                builder: (context, snapshot) {
                  if (snapshot.data == null) {
                    return const SizedBox.shrink();
                  }
                  return BaseStreamBuilder<List<EventModels>>(
                    controller: cubit.eventService.events,
                    builder: (events) {
                      return DayRangeEventView(
                        key: ValueKey(snapshot.data),
                        events: events,
                        currentDay: snapshot.data!,
                        onTap: (event) {
                          cubit.onTapEvent(context, event);
                        },
                      );
                    },
                  );
                }),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
