import 'package:family_app/main.dart';
import 'package:family_app/screen/main/calendar/widget/calendar_overlay.dart';
import 'package:flutter/material.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/data/model/calendar.dart';

class CalendarFilterView extends StatelessWidget {
  final dynamic overlay;
  final BuildContext context;
  final GlobalKey calendarKey;
  final FLCalendarView calendarViewFL;
  final VoidCallback onSearchTap;

  const CalendarFilterView({
    super.key,
    required this.overlay,
    required this.context,
    required this.calendarKey,
    required this.calendarViewFL,
    required this.onSearchTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => overlay.showOverlay(this.context, calendarKey, calendarViewFL),
          child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: appTheme.blackColor.withOpacity(.05),
              ),
              child: ImageAssetCustom(
                key: calendarKey,
                imagePath: calendarViewFL.imagePath,
                size: 20,
              )),
        ),
        const SizedBox(width: 17),
        GestureDetector(
          onTap: onSearchTap,
          child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: appTheme.blackColor.withOpacity(.05),
              ),
              child: Assets.images.search.image(width: 20, height: 20)),
        ),
      ],
    );
  }
}
