import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';

class CustomCurrentTimeLine extends StatelessWidget {
  const CustomCurrentTimeLine({super.key, this.timeIntervalHeight = 30});

  final double timeIntervalHeight;

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final topPosition = (now.hour * 60 + now.minute) / 60 * timeIntervalHeight - 10; // depends on timeIntervalHeight

    return Positioned(
      top: topPosition,
      left: 0,
      right: 0,
      child: Row(
        children: [
          Container(
            height: 20,
            width: 42,
            margin: const EdgeInsets.only(left: 4),
            decoration: BoxDecoration(
              color: appTheme.errorV2,
              borderRadius: BorderRadius.circular(10),
            ),
            alignment: Alignment.center,
            child: Text(
              now.HH_mm,
              style: AppStyle.bold10V2(color: appTheme.whiteText),
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              color: appTheme.errorV2, // or your custom dashed paint
            ),
          ),
        ],
      ),
    );
  }
}
