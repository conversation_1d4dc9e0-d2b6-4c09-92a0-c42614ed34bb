import 'package:flutter/material.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/screen/main/calendar/calendar_cubit.dart';
import 'package:family_app/screen/main/calendar/calendar_state.dart';
import 'package:family_app/screen/main/calendar/widget/monthly_header.dart';
import 'package:family_app/screen/main/calendar/widget/event_day_week_calendar.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import '../overlay/calendar_switch_overlay.dart';

class DayOrWeekView extends StatelessWidget {
  final CalendarCubit cubit;
  final CalendarState state;
  final CalendarSwitchOverlay overlay;
  final GlobalKey calendarKey;
  final void Function(BuildContext, CalendarTapDetails, CalendarCubit, CalendarState) onCalendarTap;
  final BuildContext context;
  final Widget? headerBar;

  const DayOrWeekView({
    super.key,
    required this.cubit,
    required this.state,
    required this.overlay,
    required this.calendarKey,
    required this.onCalendarTap,
    required this.context,
    this.headerBar,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: cubit.fetchAllCalendarData,
      child: Container(
        padding: const EdgeInsets.only(top: 16, bottom: 8, right: 16, left: 16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(24)),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            spacing: 12.0,
            children: [
              if (headerBar != null) headerBar!,
              MonthlyHeader(
                context: this.context,
                calendarViewFL: state.calendarViewFL,
                weekStartForMonday: state.weekStartForMonday,
                currentDates: state.currentDates,
                isHoliday: state.isHoliday,
              ),
              BaseStreamBuilder<List<EventModels>>(
                controller: cubit.eventService.events,
                builder: (events) {
                  return EventDayCalendar(
                    cubit: cubit,
                    state: state.copyWith(models: events),
                    onCalendarTap: onCalendarTap,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
