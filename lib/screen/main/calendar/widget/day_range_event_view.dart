import 'package:family_app/utils/event.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/calendar/datasource/month_calendar_datasource.dart';
import 'package:family_app/screen/main/calendar/widget/event_widget.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/timezone.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import 'custom_current_timeline.dart';

double _timeSlotIntervalHeight = 30.0;

class DayRangeEventView extends StatelessWidget {
  const DayRangeEventView({
    super.key,
    required this.events,
    required this.currentDay,
    required this.onTap,
  });

  final DateTime currentDay;
  final List<EventModels> events;
  final void Function(EventModels) onTap;

  @override
  Widget build(BuildContext context) {
    List<EventModels> displayEvents = EventUtils.filterCurrentDayEvents(events, currentDay);
    int allDayEventCount = EventUtils.countAllDayEvent(displayEvents);

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(24)),
      ),
      child: Column(
        spacing: 8.0,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(
              currentDay.eventDate_ddMMMCommaYYYY,
              style: AppStyle.bold14(color: appTheme.grayV2),
            ),
          ),
          FutureBuilder<String>(
            future: TimeZoneUtils.getSfCalendarTimeZone(),
            builder: (context, snapshot) {
              final localTimeZone = snapshot.data ?? TimeZoneUtils.kDefaultTimeZone;
              return SizedBox(
                height: _timeSlotIntervalHeight * 24 + allDayEventCount * _timeSlotIntervalHeight,
                child: Stack(
                  children: [
                    SfCalendar(
                      key: ValueKey(currentDay),
                      view: CalendarView.day,
                      initialSelectedDate: currentDay,
                      initialDisplayDate: currentDay,
                      dataSource: MeetingDataSource(
                        displayEvents,
                        calendarTimeZone: localTimeZone,
                      ),
                      headerHeight: 0,
                      viewHeaderHeight: 0,
                      cellBorderColor: Colors.grey.shade400,
                      todayHighlightColor: appTheme.red3CColor,
                      viewNavigationMode: ViewNavigationMode.none,
                      showCurrentTimeIndicator: false,
                      timeSlotViewSettings: TimeSlotViewSettings(
                        timeFormat: 'HH:mm',
                        timeIntervalHeight: _timeSlotIntervalHeight,
                      ),
                      appointmentBuilder: (BuildContext context, details) {
                        final event = details.appointments.first as EventModels;
                        return EventWidget(
                          event: event,
                          onTap: (event) => onTap(event),
                        );
                      },
                      timeZone: localTimeZone,
                    ),
                    const CustomCurrentTimeLine(),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
