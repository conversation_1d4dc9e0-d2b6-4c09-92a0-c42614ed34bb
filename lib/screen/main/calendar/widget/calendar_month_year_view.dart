import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/data/model/calendar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';

class CalendarMonthYearView extends StatelessWidget {
  final FLCalendarView calendarViewFL;
  final List<DateTime> currentDates;
  final DateTime currentMonth;
  final ValueChanged<DateTime> onMonthYearChanged;

  const CalendarMonthYearView({
    super.key,
    required this.calendarViewFL,
    required this.currentDates,
    required this.currentMonth,
    required this.onMonthYearChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(left: 4),
        child: InkWell(
          onTap: () async {
            var now = DateTime.now();
            if (calendarViewFL == FLCalendarView.day || calendarViewFL == FLCalendarView.week) {
              final DateTime? datePicked = await showDatePicker(
                context: context,
                initialDate: currentMonth,
                firstDate: DateTime(now.year - 100),
                lastDate: DateTime(now.year + 100),
              );
              if (datePicked != null) {
                onMonthYearChanged(datePicked);
              }
            } else {
              showMonthPicker(
                context: context,
                initialDate: currentMonth,
              ).then((date) {
                if (date != null) {
                  onMonthYearChanged(date);
                }
              });
            }
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (calendarViewFL == FLCalendarView.day)
                Text(
                  calendarViewFL == FLCalendarView.day ? (currentDates.firstOrNull ?? DateTime.now()).timeddMMMyyyy : currentMonth.MM_YYYY,
                  style: AppStyle.bold20V2(),
                )
              else ...[
                Text(
                  currentMonth.MMM,
                  style: AppStyle.bold20V2(),
                ),
                const SizedBox(width: 4),
                Text(
                  currentMonth.year.toString(),
                  style: AppStyle.bold20V2(color: appTheme.grayV2),
                ),
              ],
              SvgPicture.asset(
                Assets.icons.icVerticalDoubleChevron.path,
                width: 24.w,
                height: 24.h,
                colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
              )
            ],
          ),
        ),
      ),
    );
  }
}
