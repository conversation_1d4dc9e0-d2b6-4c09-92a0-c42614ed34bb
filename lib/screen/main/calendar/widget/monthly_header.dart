import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/data/model/calendar.dart';

class MonthlyHeader extends StatelessWidget {
  final BuildContext context;
  final FLCalendarView calendarViewFL;
  final bool weekStartForMonday;
  final List<DateTime> currentDates;
  final bool Function(DateTime) isHoliday;

  const MonthlyHeader({
    super.key,
    required this.context,
    required this.calendarViewFL,
    required this.weekStartForMonday,
    required this.currentDates,
    required this.isHoliday,
  });

  @override
  Widget build(BuildContext context) {
    switch (calendarViewFL) {
      case FLCalendarView.month:
      case FLCalendarView.agenda:
        return _MonthOrAgendaHeader(weekStartForMonday: weekStartForMonday);
      case FLCalendarView.week:
        return _WeekHeader(currentDates: currentDates, isHoliday: isHoliday);
      default:
        return const SizedBox.shrink();
    }
  }
}

class _MonthOrAgendaHeader extends StatelessWidget {
  final bool weekStartForMonday;
  const _MonthOrAgendaHeader({required this.weekStartForMonday});

  @override
  Widget build(BuildContext context) {
    final daysOfWeek = weekStartForMonday ? kDaysOfWeekMondayStart : kDaysOfWeekSundayStart;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      margin: const EdgeInsets.only(left: 4, right: 4),
      decoration: BoxDecoration(
        color: appTheme.backgroundV2,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Row(
        children: List.generate(
          daysOfWeek.length,
          (index) => Expanded(
            child: Center(
              child: Text(
                daysOfWeek[index],
                style: AppStyle.bold14V2(
                  color: index == 5 || index == 6 ? appTheme.grayV2 : appTheme.blackColor,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _WeekHeader extends StatelessWidget {
  final List<DateTime> currentDates;
  final bool Function(DateTime) isHoliday;
  const _WeekHeader({required this.currentDates, required this.isHoliday});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      margin: const EdgeInsets.only(left: 4, right: 4),
      child: Row(
        children: [
          const SizedBox(width: 50),
          ...List.generate(
            currentDates.length,
            (index) {
              var isToday = currentDates[index].isToday;
              return Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      currentDates[index].EEE,
                      style: AppStyle.regular14V2(
                        color: index == 5 || index == 6 ? appTheme.grayV2 : appTheme.blackColor,
                      ),
                    ),
                    Container(
                      width: 30,
                      height: 30,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isToday ? appTheme.calendarDateBackgroundColor : appTheme.transparentColor,
                      ),
                      child: Text(
                        currentDates[index].day.toString(),
                        style: AppStyle.bold14V2(
                          color: isToday
                              ? appTheme.whiteText
                              : isHoliday(currentDates[index])
                                  ? appTheme.orangeColorV2
                                  : index == 5 || index == 6
                                      ? appTheme.grayV2
                                      : appTheme.blackColor,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          )
        ],
      ),
    );
  }
}
