import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:flutter/material.dart';

class EventWidget extends StatelessWidget {
  const EventWidget({
    super.key,
    required this.event,
    required this.onTap,
  });

  final EventModels event;
  final void Function(EventModels) onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(event),
      child: LayoutBuilder(builder: (context, constraints) {
        if (constraints.maxHeight < 20) {
          return Container(
            decoration: BoxDecoration(
              color: event.color?.toColor ?? appTheme.primaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 5),
            alignment: Alignment.centerLeft,
            child: Text(
              event.name ?? '',
              maxLines: 1,
              style: AppStyle.bold12V2(color: Colors.white),
            ),
          );
        }

        return Container(
          decoration: BoxDecoration(
            color: event.color?.toColor ?? appTheme.primaryColor,
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.all(5),
          child: SingleChildScrollView(
            physics: const NeverScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.name ?? '',
                  maxLines: 1,
                  style: AppStyle.bold12V2(color: Colors.white),
                ),
                Text(
                  event.description ?? '',
                  maxLines: 1,
                  style: AppStyle.regular12V2(color: Colors.white),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
