import 'package:family_app/data/model/event.dart';
import 'package:family_app/utils/event.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:family_app/utils/timezone.dart';

class MeetingDataSource extends CalendarDataSource<EventModels> {
  /// The Windows time zone ID used by SfCalendar (e.g., "SE Asia Standard Time")
  final String? calendarTimeZone;

  /// Creates a meeting data source, which used to set the appointment
  /// collection to the calendar
  MeetingDataSource(List<EventModels> source, {this.calendarTimeZone}) {
    appointments = source;
  }

  @override
  DateTime getStartTime(int index) {
    final EventModels event = _getMeetingData(index);
    final String? fromDateStr = event.fromDate;
    if (fromDateStr == null) return DateTime.now();

    DateTime? start;
    try {
      start = DateTime.parse(fromDateStr);
    } catch (_) {
      return DateTime.now();
    }

    final iana = TimeZoneUtils.resolveToIana(event.timeZone, fallbackWindowsTz: calendarTimeZone);
    return TimeZoneUtils.convertDateTimeToIanaTimezone(start, iana);
  }

  @override
  DateTime getEndTime(int index) {
    final EventModels event = _getMeetingData(index);
    final String? toDateStr = event.toDate;
    final DateTime start = getStartTime(index);

    DateTime? end;
    if (toDateStr != null) {
      try {
        end = DateTime.parse(toDateStr);
      } catch (_) {
        end = null;
      }
    }

    if (end == null || start.millisecondsSinceEpoch == end.millisecondsSinceEpoch) {
      return start.add(const Duration(minutes: 5));
    }

    final iana = TimeZoneUtils.resolveToIana(event.timeZone, fallbackWindowsTz: calendarTimeZone);
    return TimeZoneUtils.convertDateTimeToIanaTimezone(end, iana);
  }

  @override
  String getSubject(int index) {
    return _getMeetingData(index).name ?? '';
  }

  @override
  Color getColor(int index) {
    return _getMeetingData(index).color?.toColor ?? appTheme.primaryColor;
  }

  @override
  bool isAllDay(int index) {
    return EventUtils.isAllDay(_getMeetingData(index));
  }

  @override
  String getNotes(int index) {
    return _getMeetingData(index).notes ?? '';
  }

  EventModels _getMeetingData(int index) {
    return appointments![index];
  }
}
