import 'package:family_app/data/model/calendar.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/screen/main/calendar/widget/calendar_overlay.dart';
import 'package:flutter/material.dart';

typedef ItemOverlayClick = Function(FLCalendarView calendarView);

class CalendarSwitchOverlay {
  final ItemOverlayClick itemOverlayClick;
  OverlayEntry? overlayEntry;
  late final AnimationController animationController;
  late Animation<double> scaleAnim;

  CalendarSwitchOverlay({required this.itemOverlayClick, required TickerProvider provider}) {
    animationController = AnimationController(vsync: provider, duration: const Duration(milliseconds: 300));
    scaleAnim = Tween<double>(begin: 0, end: 1).animate(animationController);
  }

  void dispose() {
    animationController.dispose();
    if (overlayEntry != null) {
      overlayEntry?.remove();
    }
  }

  Future<void> hideOverlay() async {
    if (overlayEntry == null) return;
    await animationController.reverse();
    overlayEntry?.remove();
    overlayEntry = null;
  }

  void showOverlay(BuildContext context, GlobalKey globalKey, FLCalendarView calendarView) async {
    final renderBox = globalKey.currentContext?.findRenderObject() as RenderBox?;
    final offset = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;
    final size = renderBox?.size ?? Size.zero;
    if (size.width == 0 && size.height == 0) return;

    if (overlayEntry != null) {
      await hideOverlay();
      return;
    }

    final screenSize = MediaQuery.of(context).size;
    final top = offset.dy + size.height + 4.h;
    final isOverBottom = top + 50 > screenSize.height;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
          top: top + 2,
          left: offset.dx - 81,
          width: 140,
          child: SizeTransition(
              sizeFactor: scaleAnim,
              axisAlignment: isOverBottom ? 0 : -1,
              axis: isOverBottom ? Axis.horizontal : Axis.vertical,
              child: CalendarOverlay(
                  calendarView: calendarView,
                  onTap: (view) {
                    itemOverlayClick.call(view);
                    hideOverlay();
                  }))),
    );

    Overlay.of(context).insert(overlayEntry!);
    animationController.forward();
  }
}
