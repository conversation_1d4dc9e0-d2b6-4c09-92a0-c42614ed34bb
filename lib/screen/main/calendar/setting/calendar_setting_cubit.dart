import 'package:family_app/utils/calendar.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:family_app/utils/queue_operation.dart';

import 'calendar_setting_state.dart';

class CalendarSettingCubit extends BaseCubit<CalendarSettingState> {
  final IAuthenRepository authenRepository;
  final AccountService accountService;
  final EventService eventService;

  final QueueOperation _calendarOpQueue = QueueOperation();

  CalendarSettingCubit({
    required this.accountService,
    required this.authenRepository,
    required this.eventService,
  }) : super(CalendarSettingState(publicHolidayCalendar: LocaleKeys.england.tr())) {
    init();
  }

  // --- State/Init ---
  Future<void> init() async {
    var pref = await SharedPreferences.getInstance();
    var publicHolidayCalendar = pref.getString('publicHolidayCalendar') ?? LocaleKeys.england.tr();
    var syncWithDeviceCalendar = CalendarService.instance.isEnableCalendarSync();
    var startOfWeekMonday = CalendarService.instance.isStartOfWeekMonday();
    var defaultReminder = locator.get<LocalStorage>().defaultReminder;
    var enabledCalendarIds = CalendarService.instance.enabledCalendarIds;
    emit(state.copyWith(
      syncWithDeviceCalendar: syncWithDeviceCalendar,
      startOfWeekMonday: startOfWeekMonday,
      publicHolidayCalendar: publicHolidayCalendar,
      reminderMinutes: defaultReminder,
      enabledCalendarIds: enabledCalendarIds,
    ));
  }

  // --- Sync with Device Calendar ---
  /// Handles toggling sync with device calendar.
  /// - When enabling: loads device calendars and imports events if enabled calendars exist, showing a toast.
  /// - When disabling: deletes all events from enabled calendars, showing a toast if any exist.
  Future<void> onChangeSyncWithDeviceCalendar(bool enable) async {
    logd('Sync with device calendar: $enable');
    emit(state.copyWith(syncWithDeviceCalendar: enable));
    await CalendarService.instance.setEnableCalendarSync(enable);

    if (enable) {
      await _handleEnableDeviceCalendarSync();
    } else {
      await _handleDisableDeviceCalendarSync();
    }
  }

  /// Internal: Handles enabling device calendar sync.
  Future<void> _handleEnableDeviceCalendarSync() async {
    final (localEnabledIds, calendarsLoaded) = await CalendarService.instance.prepareDeviceCalendarSync();

    emit(state.copyWith(enabledCalendarIds: localEnabledIds));

    if (localEnabledIds.isEmpty || !calendarsLoaded) return;

    final completer = showPersistentToast('Syncing calendars...', type: ToastType.success);
    try {
      final (from, to) = CalendarUtils.getDefaultImportRange();
      await CalendarService.instance.importEventsFromDeviceCalendars(from, to, calendarIds: localEnabledIds);
      emit(state.copyWith(status: CalendarSettingStatus.success));
      completer.complete();
    } catch (error) {
      emit(state.copyWith(status: CalendarSettingStatus.error));
      completer.complete();
      showToast('Calendar sync failed', type: ToastType.error);
    }
  }

  /// Internal: Handles disabling device calendar sync.
  Future<void> _handleDisableDeviceCalendarSync() async {
    final localEnabledCalendarIds = CalendarService.instance.localEnabledCalendarIds;
    emit(state.copyWith(enabledCalendarIds: localEnabledCalendarIds));
    if (localEnabledCalendarIds.isNotEmpty) {
      final completer = showPersistentToast('Removing calendars synced...', type: ToastType.success);
      try {
        await CalendarService.instance.deleteAllEventsFromAllEnabledCalendars();
        emit(state.copyWith(status: CalendarSettingStatus.success));
        completer.complete();
      } catch (error) {
        emit(state.copyWith(status: CalendarSettingStatus.error));
        completer.complete();
        showToast('Removing calendars synced failed', type: ToastType.error);
      }
    }
  }

  // --- Calendar Toggle ---
  /// Toggle a group of calendar IDs on or off in one call.
  Future<void> onToggleCalendarGroup(Set<String> calendarIds, bool enabled) async {
    final updatedIds = Set<String>.from(state.enabledCalendarIds);
    if (enabled) {
      updatedIds.addAll(calendarIds);
    } else {
      updatedIds.removeAll(calendarIds);
    }
    emit(state.copyWith(enabledCalendarIds: updatedIds));

    logd('onToggleCalendarGroup: scheduling _syncEnabledCalendars with debounce, updatedIds=$updatedIds');

    await _calendarOpQueue.enqueueDebounced(
      () => _syncEnabledCalendarsInternal(updatedIds),
      debounceDuration: const Duration(milliseconds: 600),
    );
  }

  // --- Reminder ---
  void onChangeReminderMinutes(int e) {
    logd('Reminder minutes: $e');
    emit(state.copyWith(reminderMinutes: e));
    locator.get<LocalStorage>().cacheDefaultReminder(e);
  }

  // --- Start of Week ---
  Future<void> onChangeStartOfWeekMonday() async {
    var startOfWeekMonday = !state.startOfWeekMonday;
    logd('Start of week Monday: $startOfWeekMonday');
    emit(state.copyWith(startOfWeekMonday: startOfWeekMonday));
    await CalendarService.instance.setStartOfWeekMonday(startOfWeekMonday);
    HomeCubit homeCubit = locator.get<HomeCubit>();
    homeCubit.refreshUIOnly();
  }

  // --- Dev/Delete ---
  void onDeleteAllEvents() async {
    if (!kDebugMode) return; // Disable this in production
    final completer = showPersistentToast('Deleting all calendar events...', type: ToastType.success);
    try {
      await CalendarService.instance.deleteAllEventsOnSystem();
      await eventService.fetchEventByFamilyId(accountService.familyId, combineAndDedupe: false);
      emit(state.copyWith(status: CalendarSettingStatus.actionCompleted));
      completer.complete();
    } catch (error) {
      emit(state.copyWith(status: CalendarSettingStatus.error));
      completer.complete();
      showToast('Delete all events failed', type: ToastType.error);
    }
  }

  // --- Private Helpers ---
  Future<void> _syncEnabledCalendarsInternal(Set<String> enabledCalendarIds) async {
    logd('_syncEnabledCalendars: started for $enabledCalendarIds');
    final completer = showPersistentToast('Syncing calendars...', type: ToastType.success);
    try {
      await CalendarService.instance.onToggleCalendar(enabledCalendarIds);
      await eventService.fetchEventByFamilyId(accountService.familyId, combineAndDedupe: false);
      emit(state.copyWith(status: CalendarSettingStatus.success));
      completer.complete();
      logd('_syncEnabledCalendars: completed for $enabledCalendarIds');
    } catch (error) {
      emit(state.copyWith(status: CalendarSettingStatus.error));
      completer.complete();
      showToast('Calendar sync failed', type: ToastType.error);
      logd('_syncEnabledCalendars: error for $enabledCalendarIds: $error');
    }
  }

  @override
  Future<void> close() {
    _calendarOpQueue.cancelDebounce();
    return super.close();
  }
}
