import 'package:family_app/base/widget/cubit/base_state.dart';

enum CalendarSettingStatus { initial, loading, success, actionCompleted, error, done }

class CalendarSettingState extends BaseState {
  final CalendarSettingStatus status;
  final bool syncWithDeviceCalendar;
  final bool startOfWeekMonday;
  final String? publicHolidayCalendar;
  final int reminderMinutes;
  final Set<String> enabledCalendarIds;

  CalendarSettingState({
    this.status = CalendarSettingStatus.initial,
    this.syncWithDeviceCalendar = false,
    this.startOfWeekMonday = false,
    this.publicHolidayCalendar,
    this.reminderMinutes = 0,
    this.enabledCalendarIds = const {},
  });

  CalendarSettingState copyWith({
    CalendarSettingStatus? status,
    bool? syncWithDeviceCalendar,
    bool? startOfWeekMonday,
    String? publicHolidayCalendar,
    int? reminderMinutes,
    Set<String>? enabledCalendarIds,
  }) {
    return CalendarSettingState(
      status: status ?? this.status,
      syncWithDeviceCalendar: syncWithDeviceCalendar ?? this.syncWithDeviceCalendar,
      startOfWeekMonday: startOfWeekMonday ?? this.startOfWeekMonday,
      publicHolidayCalendar: publicHolidayCalendar ?? this.publicHolidayCalendar,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
      enabledCalendarIds: enabledCalendarIds ?? this.enabledCalendarIds,
    );
  }

  @override
  List<Object?> get props => [
        status,
        syncWithDeviceCalendar,
        startOfWeekMonday,
        publicHolidayCalendar,
        reminderMinutes,
        enabledCalendarIds,
      ];
}
