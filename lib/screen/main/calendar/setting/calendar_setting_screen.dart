import 'package:device_calendar/device_calendar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/widget/cubit/base_bloc_bottomsheet_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:family_app/data/model/calendar.dart';
import 'package:collection/collection.dart'; // For groupBy

import 'calendar_setting_cubit.dart';
import 'calendar_setting_state.dart';

class CalendarSettingPage extends BaseBlocProvider<CalendarSettingState, CalendarSettingCubit> {
  const CalendarSettingPage({
    super.key,
  });

  @override
  Widget buildPage() => const CalendarSettingView();

  @override
  CalendarSettingCubit createCubit() => CalendarSettingCubit(authenRepository: locator.get(), eventService: locator.get<EventService>(), accountService: locator.get<AccountService>());
}

class CalendarSettingView extends StatefulWidget {
  const CalendarSettingView({super.key});

  @override
  State<StatefulWidget> createState() => _CalendarSettingPageState();
}

class _CalendarSettingPageState extends BaseBlocBottomSheetPageState<CalendarSettingView, CalendarSettingState, CalendarSettingCubit> {
  @override
  String get title => LocaleKeys.setting.tr();

  @override
  Widget buildAppBar(BuildContext context, CalendarSettingCubit cubit, CalendarSettingState state) {
    return Container(
      padding: padding(horizontal: 16),
      height: 44,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Text(
                  LocaleKeys.back.tr(),
                  style: AppStyle.medium16(color: appTheme.primaryColorV2),
                ),
              ),
              const Spacer(),
            ],
          ),
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppStyle.medium16(color: appTheme.blackColor),
          )
        ],
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, CalendarSettingCubit cubit, CalendarSettingState state) {
    return BlocListener<CalendarSettingCubit, CalendarSettingState>(
      listener: (context, state) {
        if (state.status == CalendarSettingStatus.actionCompleted) {
          Navigator.of(context).pop();
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (CalendarService.instance.isEnableCalendarSync())
              BaseStreamBuilder<List<Calendar>>(
                controller: CalendarService.instance.calendars,
                builder: (List<Calendar> calendars) {
                  if (calendars.isEmpty) return const SizedBox.shrink();
                  return buildMyCalendarsSettings(context, cubit, state, calendars);
                },
              ),
            buildGeneralSettings(context, cubit, state),
            if (kDebugMode) buildDevSettings(context, cubit, state),
          ],
        ),
      ),
    );
  }

  String getDisplayReminder(int minutes) {
    if (minutes == 0) {
      return LocaleKeys.none.tr();
    } else if (minutes <= 60) {
      return "$minutes ${LocaleKeys.minutes.tr()}";
    } else {
      return "${minutes ~/ 60} ${LocaleKeys.hours.tr()}";
    }
  }

  Widget buildMyCalendarsSettings(BuildContext context, CalendarSettingCubit cubit, CalendarSettingState state, List<Calendar> calendars) {
    final enabledIds = state.enabledCalendarIds;
    final colors = [const Color(0xFF4E46B4), const Color(0xFFD33030), const Color(0xFF2E7D32)];

    // Group calendars by accountName, but use 'Default' for default calendar names
    final grouped = groupBy<Calendar, String>(
      calendars,
      (c) {
        if (c.accountName != null && kDefaultCalendarNames.contains(c.accountName)) {
          return kCalendarGroupDefaultDisplay;
        }
        return (c.accountName?.isNotEmpty == true) ? c.accountName! : kCalendarGroupOther;
      },
    );

    // Always push Default group on top
    final entries = <MapEntry<String, List<Calendar>>>[];
    if (grouped.containsKey(kCalendarGroupDefaultDisplay)) {
      entries.add(MapEntry(kCalendarGroupDefaultDisplay, grouped[kCalendarGroupDefaultDisplay]!));
    }
    entries.addAll(
      grouped.entries.where((e) => e.key != kCalendarGroupDefaultDisplay),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(LocaleKeys.my_calendars.tr(), style: AppStyle.bold14V2()),
        ...entries.asMap().entries.map((entry) {
          final groupIdx = entry.key;
          final groupName = entry.value.key;
          final groupCalendars = entry.value.value;
          final allEnabled = groupCalendars.every((c) => enabledIds.contains(c.id));
          final color = colors[groupIdx % colors.length];

          return CheckboxListTile(
            value: allEnabled,
            tristate: groupCalendars.length > 1,
            onChanged: (val) async {
              final enable = val == true;
              final ids = groupCalendars.map((c) => c.id ?? '').where((id) => id.isNotEmpty).toSet();
              await cubit.onToggleCalendarGroup(ids, enable);
            },
            title: Text(groupName, style: AppStyle.regular14V2()),
            controlAffinity: ListTileControlAffinity.leading,
            activeColor: color,
            checkColor: Colors.white,
            visualDensity: VisualDensity.compact,
            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
          );
        }),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget buildGeneralSettings(BuildContext context, CalendarSettingCubit cubit, CalendarSettingState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(LocaleKeys.general_settings.tr(), style: AppStyle.bold14V2()),
        ListTile(
          leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
          title: Text(LocaleKeys.public_holidays.tr(), style: AppStyle.regular14V2()),
          subtitle: (state.publicHolidayCalendar?.isNotEmpty == true) ? Text(state.publicHolidayCalendar!, style: AppStyle.regular12V2()) : null,
          onTap: () {
            // Handle Public Holidays setting
          },
        ),
        // ListTile(
        //   leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
        //   title: Text(LocaleKeys.week_numbers.tr(), style: AppStyle.regular14V2()),
        //   subtitle: Text(LocaleKeys.off.tr(), style: AppStyle.regular12V2()),
        //   onTap: () {
        //     // Handle Week Numbers setting
        //   },
        // ),
        ListTile(
          leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
          title: Text(LocaleKeys.start_of_week.tr(), style: AppStyle.regular14V2()),
          subtitle: Text(state.startOfWeekMonday ? LocaleKeys.monday.tr() : LocaleKeys.sunday.tr(), style: AppStyle.regular12V2()),
          onTap: () {
            cubit.onChangeStartOfWeekMonday();
          },
        ),
        ListTile(
          leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
          title: Text(LocaleKeys.default_reminder.tr(), style: AppStyle.regular14V2()),
          subtitle: Text(getDisplayReminder(state.reminderMinutes), style: AppStyle.regular12V2()),
          onTap: () {
            BottomSheetUtils.showScrollable(context,
                isDismissible: true,
                child: Container(
                  width: double.infinity,
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: setupReminderMinutes
                        .map((e) => InkWell(
                              child: Container(
                                  height: 50,
                                  alignment: Alignment.center,
                                  color: state.reminderMinutes == e ? appTheme.primaryColorV2.withValues(alpha: 0.1) : Colors.white,
                                  child: Text(getDisplayReminder(e), style: AppStyle.regular14V2())),
                              onTap: () {
                                cubit.onChangeReminderMinutes(e);
                                Navigator.of(context).pop();
                              },
                            ))
                        .toList(),
                  ),
                ));
          },
        ),
        ListTile(
          leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
          title: Text(LocaleKeys.sync_with_device_calendar.tr(), style: AppStyle.regular14V2()),
          subtitle: Text(state.syncWithDeviceCalendar ? LocaleKeys.on.tr() : LocaleKeys.off.tr(), style: AppStyle.regular12V2()),
          onTap: () async {
            final enable = !state.syncWithDeviceCalendar;
            if (!enable) {
              final confirmed = await DialogUtils.showConfirmDialog(
                context,
                title: LocaleKeys.turn_off_sync_with_device_calendar_title.tr(),
                content: LocaleKeys.turn_off_sync_with_device_calendar_content.tr(),
                confirmText: LocaleKeys.confirm.tr(),
                cancelText: LocaleKeys.cancel.tr(),
              );
              if (confirmed != true) return;
            }
            cubit.onChangeSyncWithDeviceCalendar(enable);
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget buildDevSettings(BuildContext context, CalendarSettingCubit cubit, CalendarSettingState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Dev Settings', style: AppStyle.bold14V2()),
        ListTile(
          leading: SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35, color: Colors.red),
          title: Text('Delete All Events', style: AppStyle.regular14V2().copyWith(color: Colors.red)),
          onTap: () async {
            await DialogUtils.showDeleteDialog(
              context,
              title: 'Delete All Events',
              content: 'Are you sure you want to delete all events? This action cannot be undone.',
              confirmText: 'Delete',
              onConfirm: () {
                Navigator.of(context).pop();
                cubit.onDeleteAllEvents();
              },
            );
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
