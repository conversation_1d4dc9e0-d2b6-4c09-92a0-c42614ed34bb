import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/screen/main/check_list/widgets/check_list_type_widget.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/main_cubit.dart';
import 'package:family_app/screen/main/main_state.dart';
import 'package:family_app/screen/main/widget/list_item_view.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ListShoppingTodoView extends StatelessWidget {
  const ListShoppingTodoView({
    super.key,
    required this.cubit,
    required this.state,
    this.shrinkWrap = false,
    this.hasNavigateToUpsertList = false,
    this.viewPadding,
    this.physics,
  });

  final MainCubit cubit;
  final MainState state;
  final bool shrinkWrap;
  final EdgeInsets? viewPadding;
  final bool hasNavigateToUpsertList;
  final ScrollPhysics? physics;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        shrinkWrap: shrinkWrap,
        padding: viewPadding ?? padding(all: 16),
        physics: physics ?? const BouncingScrollPhysics(),
        itemBuilder: (context, index) {
          final listItem = state.listItems[index];
          return _buildListItemView(context, listItem, index);
        },
        separatorBuilder: (context, index) => const SizedBox(height: 12),
        itemCount: state.listItems.length);
  }

  Widget _buildListItemView(BuildContext context, ListItem listItem, int listIndex) {
    return ListItemView(
      listItem: listItem,
      onTap: hasNavigateToUpsertList
          ? () => context
              .pushRoute(ListDetailRoute(parameter: ListDetailParameter(type: listItem.listType, listItem: listItem)))
          : null,
      updateItemInList: (listItem, itemIndex) => cubit.updateItemStatusInList(listItem, listIndex, itemIndex),
    );
  }
}

class ChecklistView extends StatelessWidget {
  const ChecklistView({
    super.key,
    required this.cubit,
    required this.state,
    this.shrinkWrap = false,
    this.hasNavigateToUpsertList = false,
    this.viewPadding,
    this.physics,
  });

  final HomeCubit cubit;
  final HomeState state;
  final bool shrinkWrap;
  final EdgeInsets? viewPadding;
  final ScrollPhysics? physics;
  final bool hasNavigateToUpsertList;

  @override
  Widget build(BuildContext context) {
    final today = DateTime.now();
    final recentlyCreatedItems = state.listItems.where((item) {
      final planDate = item.planDate?.toLocalDT;
      return planDate != null && planDate.isAfter(today);
    }).toList();

    final overdueItems = state.listItems.where((item) {
      final planDate = item.planDate?.toLocalDT;
      return planDate != null && planDate.isBefore(today);
    }).toList();

    return ListView(
      shrinkWrap: shrinkWrap,
      padding: viewPadding ?? padding(all: 16),
      physics: physics ?? const BouncingScrollPhysics(),
      children: [
        _buildSectionTitle(context, 'Recently'),
        ...recentlyCreatedItems.map((item) => _buildListItemView(context, item)),
        _buildSectionTitle(context, 'Overdue'),
        ...overdueItems.map((item) => _buildListItemView(context, item)),
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14, color: Color(0xFF595D62)),
      ),
    );
  }

  Widget _buildListItemView(BuildContext context, ListItem listItem) {
    return GestureDetector(
      onTap: hasNavigateToUpsertList
          ? () => cubit.onTapCheckListItem(context, listItem)
          : null,
      child: Column(
        children: [
          Row(
            children: [
              _buildListIcon(listItem.listType),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CheckListTypeWidget(
                      type: listItem.listType,
                      listItem: listItem,
                    ),
                    Text(
                      listItem.name ?? listItem.description ?? "No Description",
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    Text(
                      '${listItem.items?.length} items',
                      style: const TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Divider(color: Colors.grey),
        ],
      ),
    );
  }

  Widget _buildListIcon(ListType listType) {
    String iconPath;
    switch (listType) {
      case ListType.Shopping:
        iconPath = 'assets/icons/shopping_list_icon.svg';
        break;
      case ListType.Todo:
        iconPath = 'assets/icons/todo_list_icon.svg';
        break;
      default:
        iconPath = 'assets/icons/other_list_icon.svg';
    }
    return SvgPicture.asset(iconPath, width: 40, height: 40);
  }
}
