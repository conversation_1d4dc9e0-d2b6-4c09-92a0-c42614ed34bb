import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/home/<USER>/home_card.dart';
import 'package:family_app/screen/main/home/<USER>/home_card_subtitle.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/check_item_view.dart';
import 'package:flutter/material.dart';

class ListItemView extends StatelessWidget {
  const ListItemView({
    super.key,
    required this.listItem,
    this.onTap,
    this.updateItemInList,
  });

  final ListItem listItem;
  final VoidCallback? onTap;
  final Function(ListItem listItem, int itemIndex)? updateItemInList;

  bool get isViewer {
    final accountService = locator.get<AccountService>();
    return accountService.userRole == Role.viewer;
  }

  String get familyName {
    final accountService = locator.get<AccountService>();
    return accountService.myActiveFamily.value?.familyName ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isViewer ? null : onTap,
      behavior: HitTestBehavior.opaque,
      child: HomeCard(
          // title:  'Sam until Jul 12',
          title: '$familyName ${LocaleKeys.until_time.tr(args: [listItem.planDate?.toLocalDT.shortDay ?? ''])}',
          items: listItem.items ?? <Item>[],
          lineItem: 2,
          backgroundColor: listItem.color?.toColor ?? appTheme.redColor,
          listItemPadding: padding(vertical: 13, horizontal: 12),
          itemBuilder: (index, item) => CheckItemView(
              checkItem: item.checkListItem,
              checkColor: listItem.color?.toColor ?? appTheme.redColor,
              textDecoration: item.status == 1 ? TextDecoration.lineThrough : null,
              updateStatus: () => updateItemInList?.call(listItem, index)),
          subTitle: HomeCardSubtitle(
            imagePath: Assets.images.todo.path,
            content: listItem.listType == ListType.Shopping ? LocaleKeys.shopping_list.tr() : LocaleKeys.todo_list.tr(),
          )),
    );
  }
}
