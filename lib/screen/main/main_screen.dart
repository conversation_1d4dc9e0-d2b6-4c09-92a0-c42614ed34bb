import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:family_app/provider/loading_provider.dart';
import 'package:family_app/screen/main/home/<USER>/draggable_fab.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';

final ValueNotifier<bool> calendarFabPressed = ValueNotifier(false);

const double _kNavBarRadius = 32;
const double _kFabSize = 55;
const double _kIconSize = 25;
const double _kFabIconSize = 40;
const double _kNotchMargin = 10;
const double _kNavBarElevation = 12;
const double _kNavBarShadowOpacity = 0.5;
const double _kNavBarLabelFontSize = 12;
const double _kNavBarLabelSpacing = 2;
const double _kNavBarFabGap = 72;

const List<PageRouteInfo> mainRoutes = [
  HomeRoute(),
  CalendarRoute(),
  CheckListRoute(),
  ProfileRoute(),
  HomeAddRoute(),
];

@RoutePage(name: 'MainRoute')
class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final ValueNotifier<int> _activeTabIndex = ValueNotifier<int>(0);

  @override
  void dispose() {
    _activeTabIndex.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tabItems = [
      TabItem(iconPath: Assets.icons.homeMenu.path, label: LocaleKeys.home.tr()),
      TabItem(iconPath: Assets.icons.calendarMenu.path, label: LocaleKeys.calendar.tr()),
      TabItem(iconPath: Assets.icons.notiMenu.path, label: LocaleKeys.recents.tr()),
      TabItem(iconPath: Assets.icons.settingMenu.path, label: LocaleKeys.setting.tr()),
    ];
    return Stack(
      children: [
        AutoTabsScaffold(
          routes: mainRoutes,
          floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
          floatingActionButton: const _MainFab(),
          drawerScrimColor: Colors.transparent,
          backgroundColor: Colors.transparent,
          bottomNavigationBuilder: (context, tabRouter) {
            _activeTabIndex.value = tabRouter.activeIndex;
            return _MainBottomNavBar(tabItems: tabItems, tabRouter: tabRouter);
          },
        ),
        ValueListenableBuilder<int>(
          valueListenable: _activeTabIndex,
          builder: (context, activeIndex, _) {
            return Consumer<LoadingProvider>(
              builder: (context, loadingProvider, _) {
                if (!loadingProvider.isLoading && activeIndex == 0) {
                  return DraggableFab(child: _buildFloatingAIButton());
                }
                return const SizedBox.shrink();
              },
            );
          },
        ),
        Consumer<LoadingProvider>(
          builder: (context, loadingProvider, _) {
            if (!loadingProvider.isLoading) return const SizedBox.shrink();
            return Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      appTheme.primaryColorV2,
                      appTheme.secondaryColorV2,
                    ],
                    stops: [0.3553, 1.1424],
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.icons.homeMenu.path,
                        width: 70.w,
                        height: 70.h,
                        colorFilter: ColorFilter.mode(appTheme.whiteText, BlendMode.srcATop),
                      ),
                      const SizedBox(height: 18),
                      Text(
                        'Family Link',
                        style: TextStyle(
                          color: appTheme.whiteText,
                          fontWeight: FontWeight.bold,
                          fontSize: 32.w,
                          fontFamily: 'DM Sans',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildFloatingAIButton() {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(44),
      child: InkWell(
        onTap: () => goToChatScreen(context),
        borderRadius: BorderRadius.circular(44),
        child: Container(
          width: 72,
          height: 72,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(44),
            image: DecorationImage(
              image: AssetImage(Assets.images.fabAi.path),
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }
}

class _MainFab extends StatelessWidget {
  const _MainFab();
  @override
  Widget build(BuildContext context) {
    final tabsRouter = AutoTabsRouter.of(context);
    final homeTabIndex = mainRoutes.indexWhere((route) => route is HomeRoute);
    final calendarTabIndex = mainRoutes.indexWhere((route) => route is CalendarRoute);
    final fabTabIndex = mainRoutes.indexWhere((route) => route is HomeAddRoute);
    return SizedBox(
      height: _kFabSize.w,
      width: _kFabSize.w,
      child: FloatingActionButton(
        backgroundColor: appTheme.primaryColorV2,
        elevation: 15,
        shape: const CircleBorder(),
        child: Icon(Icons.add, size: _kFabIconSize, color: appTheme.whiteText),
        onPressed: () {
          if (tabsRouter.activeIndex == calendarTabIndex) {
            calendarFabPressed.value = true;
            return;
          }
          if (tabsRouter.activeIndex == fabTabIndex) {
            tabsRouter.setActiveIndex(homeTabIndex);
          } else {
            tabsRouter.setActiveIndex(fabTabIndex);
          }
        },
      ),
    );
  }
}

class _MainBottomNavBar extends StatelessWidget {
  final List<TabItem> tabItems;
  final TabsRouter tabRouter;
  const _MainBottomNavBar({required this.tabItems, required this.tabRouter});

  @override
  Widget build(BuildContext context) {
    final List<Widget> navItems = [];
    for (int i = 0; i < tabItems.length; i++) {
      final fabTabIndex = mainRoutes.indexWhere((route) => route is HomeAddRoute);
      if (i == fabTabIndex) {
        navItems.add(const SizedBox(width: _kNavBarFabGap));
      }
      navItems.add(_NavBarItem(item: tabItems[i], index: i, tabRouter: tabRouter));
    }
    return PhysicalModel(
      color: Colors.transparent,
      elevation: _kNavBarElevation,
      borderRadius: BorderRadius.circular(_kNavBarRadius),
      shadowColor: Colors.black.withOpacity(_kNavBarShadowOpacity),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(_kNavBarRadius),
        child: BottomAppBar(
          elevation: 0,
          color: Colors.white,
          notchMargin: _kNotchMargin,
          shape: const CircularNotchedRectangle(),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: navItems,
          ),
        ),
      ),
    );
  }
}

class _NavBarItem extends StatelessWidget {
  final TabItem item;
  final int index;
  final TabsRouter tabRouter;
  const _NavBarItem({required this.item, required this.index, required this.tabRouter});

  @override
  Widget build(BuildContext context) {
    final theme = appTheme;
    final isSelected = index == tabRouter.activeIndex;
    final checkListIndex = mainRoutes.indexWhere((route) => route is CheckListRoute);
    final isDisabled = index == checkListIndex;
    final homeTabIndex = mainRoutes.indexWhere((route) => route is HomeRoute);
    if (index == homeTabIndex) {
      // Set light status bar
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarIconBrightness: Brightness.light),
      );
    } else {
      // Set dark status bar
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarIconBrightness: Brightness.dark),
      );
    }
    return Expanded(
      child: InkWell(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: isDisabled ? null : () {
          tabRouter.setActiveIndex(index);
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              item.iconPath,
              colorFilter: ColorFilter.mode(
                isDisabled 
                  ? theme.grayV2.withOpacity(0.4) 
                  : (isSelected ? theme.primaryColorV2 : theme.grayV2),
                BlendMode.srcIn,
              ),
              width: _kIconSize,
              height: _kIconSize,
            ),
            const SizedBox(height: _kNavBarLabelSpacing),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 250),
              style: TextStyle(
                color: isDisabled 
                  ? theme.grayV2.withOpacity(0.4)
                  : (isSelected ? theme.primaryColorV2 : theme.grayV2),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: _kNavBarLabelFontSize,
              ),
              child: Text(item.label),
            ),
          ],
        ),
      ),
    );
  }
}

class TabItem {
  final String iconPath;
  final String label;
  const TabItem({required this.iconPath, required this.label});
}

Future<void> goToChatScreen(BuildContext context) async {
  LocalStorage localStorage = locator.get();
  final token = await localStorage.accessToken();
  if (token!.isEmpty) {
    await localStorage.clear();
    context.replaceRoute(const AuthRoute());
  } else {
    context.pushRoute(ChatRoute(
        parameter: ChatParameter(
            chatContext: ChatContext.getGeneralChatContext(token))));
  }
}
