import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:flutter/material.dart';
import 'cubit.dart';
import 'state.dart';
import 'widget/app_bar.dart';
import 'widget/list.dart';
import 'package:family_app/main.dart';

@RoutePage()
class DailyTasksPage extends BaseBlocProvider<DailyTasksState, DailyTasksCubit> {
  const DailyTasksPage({super.key});

  @override
  Widget buildPage() => const DailyTasksView();

  @override
  DailyTasksCubit createCubit() => DailyTasksCubit(
        checklistService: locator.get(),
        accountService: locator.get(),
      );
}

class DailyTasksView extends StatefulWidget {
  const DailyTasksView({super.key});

  @override
  State<DailyTasksView> createState() => _DailyTasksViewState();
}

class _DailyTasksViewState extends BaseBlocPageState<DailyTasksView, DailyTasksState, DailyTasksCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  Widget buildAppBar(BuildContext context, DailyTasksCubit cubit, DailyTasksState state) {
    return const DailyTasksAppBar();
  }

  @override
  Widget buildBody(BuildContext context, DailyTasksCubit cubit, DailyTasksState state) {
    switch (state.status) {
      case DailyTasksStatus.loading:
        return Center(child: CircularProgressIndicator(backgroundColor: appTheme.backgroundV2));
      case DailyTasksStatus.success:
        return DailyTasksList(
          dueTodayTasks: state.dueTodayTasks,
          overdueTasks: state.overdueTasks,
          onToggleTaskStatus: cubit.toggleTaskStatus,
          onRefresh: () async {
            await cubit.fetchDueTodayTasks(cubit.accountService.familyId, showLoading: false, forceRefresh: true);
          },
          completedListIds: state.completedListIds,
          sortOrder: state.sortOrder,
          onSortChanged: cubit.updateSortOrder,
          showCompletedTasks: state.showCompletedTasks,
          onToggleShowCompletedTasks: cubit.toggleShowCompletedTasks,
          selectedListTypeIndex: state.selectedListTypeIndex,
          onSelectedListType: cubit.updateSelectedListType,
        );
      default:
        return Container();
    }
  }
}
