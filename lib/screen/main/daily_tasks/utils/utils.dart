import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/screen/main/daily_tasks/state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:intl/intl.dart';

enum TaskGroupType { dueToday, overdue }

class DailyTasksUtils {
  /// Returns the earliest due time from tasks, with optional format. Default is 'HH:mm'.
  static String getEarliestDueTime(List<Item> tasks, {String format = 'HH:mm'}) {
    DateTime? earliestDueDate;
    for (final item in tasks) {
      if (!item.isDone && item.due_date != null) {
        try {
          final dt = item.due_date!.toLocalDT;
          if (earliestDueDate == null || dt.isBefore(earliestDueDate)) {
            earliestDueDate = dt;
          }
        } catch (_) {}
      }
    }
    if (earliestDueDate != null) {
      return DateFormat(format).format(earliestDueDate);
    }
    return '';
  }

  /// Returns the formatted due date for an overdue task as 'd MMMM, HH:mm'.
  static String getOverdueTaskTime(Item item) {
    if (item.due_date == null) return '';
    try {
      final dt = item.due_date!.toLocalDT;
      return DateFormat('d MMMM, HH:mm').format(dt);
    } catch (_) {
      return '';
    }
  }

  /// Returns all unique assignees (Account) for the given tasks, using assignment UUIDs and includedMembers.
  static List<Account> getAssigneesFromTasksAndChecklist(List<Item> tasks, List<Account> includedMembers) {
    final Set<String> assigneeUuids = <String>{};
    for (final item in tasks) {
      if (item.assignment != null) {
        for (final acc in item.assignment!) {
          if (acc.uuid != null) {
            assigneeUuids.add(acc.uuid!);
          }
        }
      }
    }
    final List<Account> result = [];
    for (final uuid in assigneeUuids) {
      final acc = includedMembers.firstWhere(
        (a) => a.uuid == uuid,
        orElse: () => Account(uuid: uuid),
      );
      result.add(acc);
    }
    if (result.isEmpty) {
      return includedMembers;
    }
    return result;
  }

  /// Returns a new ListItem with the status of the given item toggled (by uuid).
  static ListItem toggleTaskStatusInList(ListItem list, Item item) {
    final newStatus = item.status == 0 ? 1 : 0;
    final updatedItems = (list.items ?? []).map((i) {
      if (i.uuid == item.uuid) {
        return Item(
          i: i.i,
          uuid: i.uuid,
          status: newStatus,
          familyUuid: i.familyUuid,
          planUuid: i.planUuid,
          listUuid: i.listUuid,
          createdBy: i.createdBy,
          name: i.name,
          point: i.point,
          description: i.description,
          due_date: i.due_date,
          timezone: i.timezone,
          assignment: i.assignment,
          includedMembers: i.includedMembers,
          listCategory: i.listCategory,
        );
      }
      return i;
    }).toList();
    return list.copyWith(items: updatedItems);
  }

  /// Calculates the progress (0.0 to 1.0) for the given list of tasks.
  static double calculateProgress(List<Item> tasks) {
    if (tasks.isEmpty) return 0.0;
    final doneCount = tasks.where((item) => item.isDone).length;
    return doneCount / tasks.length;
  }

  /// Updates the task in the group map for the given checklist UUID and new Item.
  /// Returns a new map with the updated ListItemData.
  static Map<String, ListItemData> updateTaskInGroup(
    Map<String, ListItemData> group,
    String checklistUuid,
    Item updatedItem,
  ) {
    final newGroup = Map<String, ListItemData>.from(group);
    if (newGroup.containsKey(checklistUuid)) {
      final oldData = newGroup[checklistUuid]!;
      final updatedItems = oldData.items.map((i) => i.uuid == updatedItem.uuid ? updatedItem : i).toList();
      newGroup[checklistUuid] = ListItemData(listItem: oldData.listItem, items: updatedItems);
    }
    return newGroup;
  }

  /// Returns the display time for a group of tasks, based on group type.
  /// - If isOverdueGroup is true: show earliest overdue time in 'd MMMM, HH:mm' format
  /// - If isOverdueGroup is false: show earliest due time in 'HH:mm' format
  static String getTaskDisplayTime(List<Item> tasks, {required bool isOverdueGroup}) {
    if (tasks.isEmpty) return '';
    if (isOverdueGroup) {
      // Show earliest overdue time in 'd MMMM, HH:mm' format
      final overdueTimes = tasks
          .where((item) => item.due_date != null && item.due_date!.isNotEmpty)
          .map((item) => getOverdueTaskTime(item))
          .where((t) => t.isNotEmpty)
          .toList();
      if (overdueTimes.isNotEmpty) {
        return overdueTimes.reduce((a, b) => a.compareTo(b) < 0 ? a : b);
      }
      return '';
    } else {
      // Show earliest due time in 'HH:mm' format
      return getEarliestDueTime(tasks);
    }
  }
}
