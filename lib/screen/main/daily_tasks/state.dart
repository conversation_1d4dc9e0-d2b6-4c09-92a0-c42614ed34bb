import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/base/widget/cubit/base_state.dart';

enum DailyTasksStatus { none, loading, success, error }

class ListItemData {
  final ListItem listItem;
  final List<Item> items;
  ListItemData({required this.listItem, required this.items});
}

class DailyTasksState extends BaseState {
  final DailyTasksStatus status;
  final Map<String, ListItemData> dueTodayTasks;
  final Map<String, ListItemData> overdueTasks;
  final String? errorMessage;
  final List<String> completedListIds;
  final int sortOrder;
  final bool showCompletedTasks;

  /// The currently selected ListType filter index. -1 means no filter (all types), 0-3 correspond to ListType enum values.
  final int selectedListTypeIndex;

  DailyTasksState({
    this.status = DailyTasksStatus.none,
    Map<String, ListItemData>? dueTodayTasks,
    Map<String, ListItemData>? overdueTasks,
    this.errorMessage,
    List<String>? completedListIds,
    this.sortOrder = 0,
    this.showCompletedTasks = false,
    this.selectedListTypeIndex = -1,
  })  : dueTodayTasks = dueTodayTasks ?? const {},
        overdueTasks = overdueTasks ?? const {},
        completedListIds = completedListIds ?? const [];

  DailyTasksState copyWith({
    DailyTasksStatus? status,
    Map<String, ListItemData>? dueTodayTasks,
    Map<String, ListItemData>? overdueTasks,
    String? errorMessage,
    List<String>? completedListIds,
    int? sortOrder,
    bool? showCompletedTasks,
    int? selectedListTypeIndex,
  }) {
    return DailyTasksState(
      status: status ?? this.status,
      dueTodayTasks: dueTodayTasks ?? this.dueTodayTasks,
      overdueTasks: overdueTasks ?? this.overdueTasks,
      errorMessage: errorMessage ?? this.errorMessage,
      completedListIds: completedListIds ?? this.completedListIds,
      sortOrder: sortOrder ?? this.sortOrder,
      showCompletedTasks: showCompletedTasks ?? this.showCompletedTasks,
      selectedListTypeIndex: selectedListTypeIndex ?? this.selectedListTypeIndex,
    );
  }

  @override
  List<Object?> get props => [
        status,
        dueTodayTasks,
        overdueTasks,
        errorMessage,
        completedListIds,
        sortOrder,
        showCompletedTasks,
        selectedListTypeIndex,
      ];
}
