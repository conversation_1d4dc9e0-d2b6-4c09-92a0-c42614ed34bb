import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/checklist_service.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'dart:async';

import 'state.dart';
import '../check_list/utils/utils.dart';
import 'utils/utils.dart';
import 'package:family_app/config/constant/app_constant.dart';

class DailyTasksCubit extends BaseCubit<DailyTasksState> {
  final ChecklistService checklistService;
  final AccountService accountService;

  DailyTasksCubit({
    required this.checklistService,
    required this.accountService,
  }) : super(DailyTasksState());

  @override
  void onInit() {
    super.onInit();
    accountService.initMyProfile();
    fetchDueTodayTasks(accountService.familyId, forceRefresh: true);
  }

  /// Updates the sort order for displaying daily tasks.
  ///
  /// [sortOrder] - The new sort order value.
  void updateSortOrder(int sortOrder) {
    emit(state.copyWith(sortOrder: sortOrder));
  }

  /// Updates the selected ListType filter for daily tasks.
  ///
  /// [typeIndex] - The new ListType index to filter by. -1 means no filter (all types).
  Future<void> updateSelectedListType(int typeIndex) async {
    // Update state immediately
    emit(state.copyWith(selectedListTypeIndex: typeIndex));

    // Then update the data with the new filter
    final checklists = await _fetchChecklistsWithItems(accountService.familyId, forceRefresh: false);
    final filtered = _filterChecklistsByTypeIndex(checklists, typeIndex);
    final grouped = ChecklistUtils.groupTasksByDueDate(
      filtered,
      state.showCompletedTasks,
    );
    final updatedCompleted = _recalculateCompletedListIds(grouped.dueToday, grouped.overdue);

    // Update data while preserving selectedListTypeIndex
    emit(state.copyWith(
      dueTodayTasks: grouped.dueToday,
      overdueTasks: grouped.overdue,
      completedListIds: updatedCompleted,
    ));
  }

  /// Filters the given checklists by ListType index. If index is -1, returns all.
  List<ListItem> _filterChecklistsByTypeIndex(List<ListItem> checklists, int typeIndex) {
    if (typeIndex == -1) return checklists;
    final listType = indexToListType(typeIndex);
    if (listType == null) return checklists;

    return checklists.where((list) {
      if (list.categoryId == null) return false;
      return list.categoryId == listType.typeStr();
    }).toList();
  }

  /// Toggles the filter for showing completed tasks.
  ///
  /// When toggled, the task lists are regrouped and the UI is updated accordingly.
  /// This method fetches checklists and their items from cache (not server),
  /// then regroups tasks using the new filter value and recalculates completedListIds for overlays.
  Future<void> toggleShowCompletedTasks() async {
    final newShowCompleted = !state.showCompletedTasks;
    // Refetch all checklists and their items (from cache)
    final checklists = await _fetchChecklistsWithItems(accountService.familyId, forceRefresh: false);
    final filtered = _filterChecklistsByTypeIndex(checklists, state.selectedListTypeIndex);
    final grouped = ChecklistUtils.groupTasksByDueDate(
      filtered,
      newShowCompleted,
    );
    // Recalculate completedListIds for the new groupings
    final updatedCompleted = _recalculateCompletedListIds(grouped.dueToday, grouped.overdue);
    emit(state.copyWith(
      showCompletedTasks: newShowCompleted,
      dueTodayTasks: grouped.dueToday,
      overdueTasks: grouped.overdue,
      completedListIds: updatedCompleted,
    ));
  }

  /// Fetches and groups daily tasks for the given [familyId].
  ///
  /// - [familyId]: The family identifier to fetch tasks for.
  /// - [showLoading]: If true, sets the loading state before fetching.
  /// - [forceRefresh]: If true, always fetches fresh data from the server; otherwise, uses cached data if available.
  ///
  /// This method fetches checklists (and their items) using [_fetchChecklistsWithItems],
  /// then groups tasks into due today and overdue, applying the current completed filter.
  /// Emits loading, success, or error states as appropriate.
  Future<void> fetchDueTodayTasks(String familyId, {bool showLoading = true, bool forceRefresh = false}) async {
    try {
      if (showLoading) {
        emit(state.copyWith(status: DailyTasksStatus.loading));
      }
      final checklists = await _fetchChecklistsWithItems(familyId, forceRefresh: forceRefresh);
      final filtered = _filterChecklistsByTypeIndex(checklists, state.selectedListTypeIndex);
      final grouped = ChecklistUtils.groupTasksByDueDate(
        filtered,
        state.showCompletedTasks,
      );
      final updatedCompleted = _recalculateCompletedListIds(grouped.dueToday, grouped.overdue);
      emit(state.copyWith(
        status: DailyTasksStatus.success,
        dueTodayTasks: grouped.dueToday,
        overdueTasks: grouped.overdue,
        completedListIds: updatedCompleted,
      ));
    } catch (e) {
      emit(state.copyWith(status: DailyTasksStatus.error, errorMessage: e.toString()));
    }
  }

  /// Toggles the status (done/incomplete) of a task within a checklist.
  ///
  /// - [list]: The checklist containing the task.
  /// - [item]: The task item to toggle.
  ///
  /// Updates the status of the task in the local state, updates the checklist's items in the relevant group(s),
  /// and recalculates completedListIds for all checklists in the current state. The checklist remains visible in its group.
  /// The completed overlay appears if all tasks are done. The change is persisted to the backend, and any error will revert the local update.
  Future<void> toggleTaskStatus(ListItem list, Item item) async {
    if (!_canToggleStatus(item)) return;

    final prevStatus = item.status;
    final newStatus = item.status == 0 ? 1 : 0;
    item.status = newStatus;

    final newDueToday = DailyTasksUtils.updateTaskInGroup(state.dueTodayTasks, list.uuid!, item);
    final newOverdue = DailyTasksUtils.updateTaskInGroup(state.overdueTasks, list.uuid!, item);
    final updatedCompleted = _recalculateCompletedListIds(newDueToday, newOverdue);

    _emitTaskStatusState(newDueToday, newOverdue, updatedCompleted);
    await _persistTaskStatusChange(item, newStatus, prevStatus, list, newDueToday, newOverdue);
  }

  /// Emits the new state after a task status change.
  void _emitTaskStatusState(
    Map<String, ListItemData> dueToday,
    Map<String, ListItemData> overdue,
    List<String> completedListIds,
  ) {
    emit(state.copyWith(
      dueTodayTasks: dueToday,
      overdueTasks: overdue,
      completedListIds: completedListIds,
    ));
  }

  /// Handles backend persistence and rollback for a task status change.
  Future<void> _persistTaskStatusChange(
    Item item,
    int newStatus,
    int? prevStatus,
    ListItem list,
    Map<String, ListItemData> dueToday,
    Map<String, ListItemData> overdue,
  ) async {
    try {
      await checklistService.listRepository.changeStatusItemInList(item.uuid ?? '', newStatus);
    } catch (e) {
      item.status = prevStatus;
      final revertedDueToday = DailyTasksUtils.updateTaskInGroup(dueToday, list.uuid!, item);
      final revertedOverdue = DailyTasksUtils.updateTaskInGroup(overdue, list.uuid!, item);
      final revertedCompleted = _recalculateCompletedListIds(revertedDueToday, revertedOverdue);
      _emitTaskStatusState(revertedDueToday, revertedOverdue, revertedCompleted);
      AppLogger.d('$e');
      showSimpleToast(LocaleKeys.action_fail.tr());
    }
  }

  /// Recalculates completedListIds for all checklists in the provided group maps.
  ///
  /// The completed overlay for a checklist is shown in a group only if all
  /// filtered tasks for that checklist in that group are completed.
  /// This is calculated per group, based only on the tasks actually displayed
  /// in that group (i.e., the filtered tasks for that group).
  ///
  /// - [dueToday]: Map of due today tasks grouped by checklist UUID
  /// - [overdue]: Map of overdue tasks grouped by checklist UUID
  ///
  /// Returns a list of checklist UUIDs where ALL filtered tasks in the group are completed.
  List<String> _recalculateCompletedListIds(
    Map<String, ListItemData> dueToday,
    Map<String, ListItemData> overdue,
  ) {
    final completed = <String>{};

    // Check due today group
    for (final entry in dueToday.values) {
      final filteredItems = entry.items;
      if (filteredItems.isNotEmpty && filteredItems.every((task) => task.isDone)) {
        completed.add(entry.listItem.uuid!);
      }
    }

    // Check overdue group
    for (final entry in overdue.values) {
      final filteredItems = entry.items;
      if (filteredItems.isNotEmpty && filteredItems.every((task) => task.isDone)) {
        completed.add(entry.listItem.uuid!);
      }
    }

    return completed.toList();
  }

  bool _canToggleStatus(Item item) {
    final currentUserId = accountService.account?.uuid ?? '';
    if (!item.isOwner(currentUserId) && !item.isAssignee(currentUserId)) {
      showSimpleToast(LocaleKeys.only_owner_can_change_status.tr());
      return false;
    }
    return true;
  }

  /// Fetches checklists for the given family and ensures all items are populated.
  ///
  /// - [familyId]: The family identifier to fetch checklists for.
  /// - [forceRefresh]: If true, always fetches fresh data from the server; otherwise, uses cached data if available.
  ///
  /// Returns a list of checklists with their items populated.
  Future<List<ListItem>> _fetchChecklistsWithItems(String familyId, {bool forceRefresh = false}) async {
    final checklists = await checklistService.getChecklistsWithRefresh(familyId, forceRefresh: forceRefresh);
    for (final list in checklists) {
      if (list.items == null || list.items!.isEmpty) {
        list.items = await checklistService.getTasksWithRefresh(list.uuid!, forceRefresh: forceRefresh);
      }
    }
    return checklists;
  }
}
