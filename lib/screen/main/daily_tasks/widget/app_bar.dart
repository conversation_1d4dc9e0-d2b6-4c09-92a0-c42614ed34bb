import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';

class DailyTasksAppBar extends StatelessWidget {
  final List<Widget>? actions;
  final String? title;
  final Widget? titleView;
  final Function? onBack;

  const DailyTasksAppBar({super.key, this.actions, this.title, this.titleView, this.onBack});

  @override
  Widget build(BuildContext context) {
    return CustomAppBar2(
      title: titleView == null ? title ?? 'Daily tasks' : null,
      titleStyle: AppStyle.medium18(),
      titleView: titleView,
      showBack: true,
      actions: actions ?? [],
      height: 60.h2,
      backgroundColor: appTheme.transparentColor,
      backColor: appTheme.transparentColor,
      backIcon: ButtonIcon(
        Assets.icons.arrowLeft.path,
        size: 44.h2,
        sizeIcon: 24.h2,
        colorIcon: appTheme.grayV2,
        () {
          context.maybePop();
        },
        bg: appTheme.transparentColor,
      ),
      onBack: onBack,
    );
  }
}
