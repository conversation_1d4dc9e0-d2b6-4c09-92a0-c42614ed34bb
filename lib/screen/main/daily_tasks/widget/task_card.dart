import 'package:family_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/thread_detail/widget/message_poll/poll_card_voter_list.dart';
import 'package:family_app/screen/main/thread/widget/rotating_check_icon_success.dart';
import 'package:flutter_svg/svg.dart';
import 'package:family_app/data/model/account.dart';
import '../utils/utils.dart';

class TaskCard extends StatelessWidget {
  final ListItem checklist;
  final List<Item> tasks;
  final VoidCallback? onTap;
  final void Function(ListItem, Item)? onToggleTaskStatus;
  final List<String>? completedChecklistIdsForGroup;
  final bool isOverdueGroup;
  final double iconSize;
  final bool enableSpacer;
  final int? maxTasksToShow;
  final int? maxLinesTaskName;

  const TaskCard({
    super.key,
    required this.checklist,
    required this.tasks,
    this.onTap,
    this.onToggleTaskStatus,
    this.completedChecklistIdsForGroup,
    this.isOverdueGroup = false,
    this.iconSize = 44,
    this.enableSpacer = false,
    this.maxTasksToShow,
    this.maxLinesTaskName,
  });

  @override
  Widget build(BuildContext context) {
    final color = checklist.color?.toColor ?? appTheme.primaryColorV2;
    final headerText = (checklist.categoryId ?? 'Task');
    final capitalizedHeader =
        headerText.isNotEmpty ? headerText[0].toUpperCase() + headerText.substring(1).toLowerCase() : headerText;

    // Use utility for time display logic
    final time = DailyTasksUtils.getTaskDisplayTime(tasks, isOverdueGroup: isOverdueGroup);

    final List<Account> assignees =
        DailyTasksUtils.getAssigneesFromTasksAndChecklist(tasks, checklist.includedMembers ?? []);

    // Calculate progress based on displayed tasks (not all tasks in the checklist)
    final progress = DailyTasksUtils.calculateProgress(tasks);

    // Check if this list was completed for the current group
    final isListCompleted = completedChecklistIdsForGroup?.contains(checklist.uuid) ?? false;

    return Stack(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: appTheme.blackTextV2.withAlpha(64),
                  offset: const Offset(0, 4),
                  blurRadius: 12,
                  spreadRadius: 0,
                ),
              ],
              image: DecorationImage(
                image: const AssetImage('assets/images/task-bg.png'),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  color.withAlpha(46),
                  BlendMode.srcATop,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category pill
                Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      capitalizedHeader,
                      style: AppStyle.bold12(color: color),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Checklist name
                Text(
                  checklist.name ?? '',
                  style: AppStyle.bold16(color: Colors.white),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                // Tasks
                ...((maxTasksToShow != null ? tasks.take(maxTasksToShow!) : tasks).map((item) => KeyedSubtree(
                      key: ValueKey(item.uuid),
                      child: GestureDetector(
                        onTap: onToggleTaskStatus != null ? () => onToggleTaskStatus!(checklist, item) : null,
                        child: Row(
                          children: [
                            Container(
                              width: iconSize,
                              height: iconSize,
                              alignment: Alignment.center,
                              child: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 300),
                                  transitionBuilder: (child, animation) =>
                                      ScaleTransition(scale: animation, child: child),
                                  child: item.isDone
                                      ? SvgPicture.asset(
                                          key: ValueKey('checked-${item.uuid}'),
                                          Assets.icons.iconCheck.path,
                                          width: iconSize,
                                          height: iconSize,
                                          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcATop),
                                        )
                                      : SvgPicture.asset(
                                          key: ValueKey('unchecked-${item.uuid}'),
                                          Assets.icons.iconUnCheck.path,
                                          width: iconSize,
                                          height: iconSize,
                                          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcATop),
                                        )),
                            ),
                            Flexible(
                              child: Text(
                                item.name ?? '',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.w400,
                                ),
                                maxLines: maxLinesTaskName ?? 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ))),
                if (maxTasksToShow != null && tasks.length > maxTasksToShow!)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0, left: 4.0),
                    child: Text(
                      '+${tasks.length - maxTasksToShow!} items',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                const SizedBox(height: 8),
                // Time and assignees
                if (enableSpacer) const Spacer(),
                Row(
                  children: [
                    if (time.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(77),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              Assets.icons.icTime.path,
                              width: 16,
                              height: 16,
                              colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              time,
                              style: AppStyle.regular12(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    const Spacer(),
                    if (assignees.isNotEmpty)
                      SizedBox(
                        width: 80, // Fixed width to provide finite constraints
                        child: PollCardVoterList(
                          voters: assignees,
                          voterSize: 24.0,
                          optionVerticalPadding: 0.0,
                          totalVoterDisplayConfig: 3,
                          voterOverlapWidth: 12.0,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                // Progress bar
                Row(
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(3),
                        child: LinearProgressIndicator(
                          value: progress,
                          minHeight: 6,
                          backgroundColor: Colors.white.withAlpha(77),
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${(progress * 100).round()}%',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        // Success overlay for completed list
        if (isListCompleted)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(204), // 80% opacity
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: RotatingCheckIconSuccess(
                  text: "Completed",
                  size: 80,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
