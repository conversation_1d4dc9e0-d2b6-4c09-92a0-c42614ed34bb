import 'package:auto_route/auto_route.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/main.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:flutter_svg/svg.dart';
import '../state.dart';
import 'sort_bottom_sheet.dart';
import 'task_card.dart';
import 'package:family_app/config/constant/app_constant.dart';
import '../utils/utils.dart';

class DailyTasksList extends StatelessWidget {
  final Map<String, ListItemData> dueTodayTasks;
  final Map<String, ListItemData> overdueTasks;
  final Future<void> Function()? onRefresh;
  final void Function(ListItem, Item) onToggleTaskStatus;
  final List<String>? completedListIds;
  final int sortOrder; // 0: Default, 1: Latest to Oldest, 2: Oldest to Latest
  final void Function(int)? onSortChanged;
  final bool showCompletedTasks;
  final VoidCallback? onToggleShowCompletedTasks;

  /// The currently selected ListType filter index. -1 means no filter (all types), 0-3 correspond to ListType enum values.
  final int selectedListTypeIndex;

  /// Callback when a ListType filter is selected.
  final Future<void> Function(int)? onSelectedListType;

  const DailyTasksList({
    super.key,
    required this.dueTodayTasks,
    required this.overdueTasks,
    required this.onToggleTaskStatus,
    this.onRefresh,
    this.completedListIds,
    this.sortOrder = 0,
    this.onSortChanged,
    this.showCompletedTasks = false,
    this.onToggleShowCompletedTasks,
    this.selectedListTypeIndex = -1,
    this.onSelectedListType,
  });

  Widget _buildGroup({
    required BuildContext context,
    required String title,
    required Map<String, ListItemData> group,
    required TaskGroupType groupType,
    required int originalGroupCount,
  }) {
    final groupCompletedListIds = group.entries
        .where((entry) => entry.value.items.isNotEmpty && entry.value.items.every((item) => item.isDone))
        .map((entry) => entry.key)
        .toSet();

    List<Widget> children = [
      Padding(
        padding: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
        child: Text(title, style: AppStyle.bold16V2(color: appTheme.blackText)),
      ),
    ];

    if (group.isEmpty) {
      if (originalGroupCount > 0) {
        // All tasks are done
        children.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                SvgPicture.asset(
                  Assets.icons.iconCheckCircle.path,
                  width: 24,
                  height: 24,
                  colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcATop),
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    'You have completed all the tasks!',
                    style: AppStyle.regular16(color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        // No tasks at all
        children.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const Icon(Icons.inbox, color: Colors.black, size: 24),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    'No tasks to show',
                    style: AppStyle.regular16(color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        );
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    }

    children.addAll(group.entries.map((entry) => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
          child: TaskCard(
            checklist: entry.value.listItem,
            tasks: entry.value.items,
            onToggleTaskStatus: onToggleTaskStatus,
            completedChecklistIdsForGroup: groupCompletedListIds.toList(),
            isOverdueGroup: groupType == TaskGroupType.overdue,
            onTap: () {
              context.router.push(ListDetailRoute(
                parameter: ListDetailParameter(
                  type: entry.value.listItem.listType,
                  listItem: entry.value.listItem,
                ),
              ));
            },
          ),
        )));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Sort logic for dueTodayTasks and overdueTasks
    Map<String, ListItemData> sortedDueToday = _sortGroup(dueTodayTasks);
    Map<String, ListItemData> sortedOverdue = _sortGroup(overdueTasks);
    return RefreshIndicator(
      onRefresh: onRefresh ?? () async {},
      child: ListView(
        children: [
          const SizedBox(height: 20),
          // Combined filter row (sort, type filters, completed)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: SizedBox(
              height: 44,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  // Sort button
                  InkWell(
                    onTap: () async {
                      final selected = await SortBottomSheet.show(context, initial: sortOrder);
                      if (selected != null && onSortChanged != null) {
                        onSortChanged!(selected);
                      }
                    },
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      decoration: BoxDecoration(
                        color: appTheme.borderColorV2,
                        borderRadius: const BorderRadius.all(Radius.circular(24)),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      child: Row(
                        children: [SvgPicture.asset(Assets.icons.iconSort.path), const Text('Sort')],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Show Completed Tasks button
                  InkWell(
                    onTap: onToggleShowCompletedTasks,
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      decoration: BoxDecoration(
                        color: showCompletedTasks ? appTheme.primaryColorV2 : appTheme.borderColorV2,
                        borderRadius: const BorderRadius.all(Radius.circular(24)),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 24,
                            child: SvgPicture.asset(
                              Assets.icons.iconCheckCircle.path,
                              colorFilter: ColorFilter.mode(
                                showCompletedTasks ? Colors.white : Colors.black,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Show Completed Tasks',
                            style: TextStyle(
                              color: showCompletedTasks ? Colors.white : Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // ListType filter buttons
                  _buildTypeFilterButton(context, ListType.Shopping),
                  const SizedBox(width: 8),
                  _buildTypeFilterButton(context, ListType.Todo),
                  const SizedBox(width: 8),
                  _buildTypeFilterButton(context, ListType.Trip),
                  const SizedBox(width: 8),
                  _buildTypeFilterButton(context, ListType.Other),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
          _buildGroup(
            context: context,
            title: 'Due Today',
            group: sortedDueToday,
            groupType: TaskGroupType.dueToday,
            originalGroupCount: dueTodayTasks.length,
          ),
          _buildGroup(
            context: context,
            title: 'Overdue',
            group: sortedOverdue,
            groupType: TaskGroupType.overdue,
            originalGroupCount: overdueTasks.length,
          ),
        ],
      ),
    );
  }

  Widget _buildTypeFilterButton(
    BuildContext context,
    ListType? type,
  ) {
    final typeIndex = type?.toIndex() ?? -1;
    final isSelected = selectedListTypeIndex == typeIndex;

    // Map ListType to icon
    final iconData = {
      ListType.Shopping: Assets.images.icTypeShopping,
      ListType.Todo: Assets.images.icTypeTodos,
      ListType.Trip: Assets.images.icTypeOthers,
      ListType.Other: Assets.images.icTypeOthers,
    };

    final icon = iconData[type] ?? Assets.images.icTypeOthers;
    final label = type != null ? '${type.typeStr()[0].toUpperCase()}${type.typeStr().substring(1)}' : '';

    return InkWell(
      onTap: () async {
        final newValue = isSelected ? -1 : typeIndex;
        await onSelectedListType?.call(newValue);
      },
      borderRadius: BorderRadius.circular(20),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? appTheme.primaryColorV2 : appTheme.borderColorV2,
          borderRadius: const BorderRadius.all(Radius.circular(24)),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            icon.svg(
              colorFilter: ColorFilter.mode(
                isSelected ? Colors.white : Colors.black,
                BlendMode.srcATop,
              ),
              width: 24,
            ),
            const SizedBox(width: 4),
            Text(label, style: TextStyle(color: isSelected ? Colors.white : Colors.black)),
          ],
        ),
      ),
    );
  }

  Map<String, ListItemData> _sortGroup(Map<String, ListItemData> group) {
    if (sortOrder == 0) return group;
    final entries = group.entries.toList();
    entries.sort((a, b) {
      final aDue = a.value.items.isNotEmpty ? a.value.items.first.due_date ?? '' : '';
      final bDue = b.value.items.isNotEmpty ? b.value.items.first.due_date ?? '' : '';
      if (sortOrder == 1) {
        // Latest to Oldest
        return bDue.compareTo(aDue);
      } else if (sortOrder == 2) {
        // Oldest to Latest
        return aDue.compareTo(bDue);
      }
      return 0;
    });
    return {for (var e in entries) e.key: e.value};
  }
}
