import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'app_bar.dart';

class SortBottomSheet extends StatefulWidget {
  final int initial;
  const SortBottomSheet({super.key, required this.initial});

  static Future<int?> show(BuildContext context, {int initial = 0}) async {
    return await showModalBottomSheet<int>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SortBottomSheet(initial: initial),
    );
  }

  @override
  State<SortBottomSheet> createState() => _SortBottomSheetState();
}

class _SortBottomSheetState extends State<SortBottomSheet> {
  late int _selected;
  final List<String> _options = [
    'Default',
    'Due time (Latest to Oldest)',
    'Due time (Oldest to Latest)',
  ];

  @override
  void initState() {
    super.initState();
    _selected = widget.initial;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: _buildDialog(context),
    );
  }

  Widget _buildDialog(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          _buildDivider(context),
          _buildBody(context),
          _buildActionButton(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.only(top: 12.0, bottom: 8.0),
      child: DailyTasksAppBar(
        title: 'Sort by',
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Container(height: 1, color: Theme.of(context).dividerColor),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...List.generate(
              _options.length,
              (i) => ListTile(
                    contentPadding: EdgeInsets.zero,
                    title: Text(_options[i]),
                    trailing: SvgPicture.asset(
                      _selected == i ? Assets.icons.iconCheck.path : Assets.icons.iconUnCheck.path,
                      width: 44,
                      height: 44,
                      colorFilter: ColorFilter.mode(
                        appTheme.primaryColorV2,
                        BlendMode.srcATop,
                      ),
                    ),
                    onTap: () => setState(() => _selected = i),
                  )),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => {Navigator.of(context).pop(_selected)},
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: appTheme.primaryColorV2,
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.all(16.0),
            child: Text(
              LocaleKeys.search_text.tr(),
              textAlign: TextAlign.center,
              style: AppStyle.medium16(color: appTheme.whiteText),
            ),
          ),
        ),
      ),
    );
  }
}
