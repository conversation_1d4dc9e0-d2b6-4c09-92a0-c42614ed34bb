import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/main/profile/privacy_policy/policy_parameter.dart';
import 'package:family_app/screen/main/profile/privacy_policy/policy_state.dart';
import 'package:family_app/utils/loading.dart';

class PolicyCubit extends BaseCubit<ProfileStats> {
  final IFamilyRepository familyRepository;
  final PolicyParameter parameter;

  PolicyCubit({
    required this.familyRepository,
    required this.parameter,
  }) : super(ProfileStats());

  @override
  void onInit() {
    super.onInit();
    fetchPrivacy();
  }

  void fetchPrivacy() async {
    try {
      showLoading();

      final response = await familyRepository.privacy();

      emit(state.copyWith(configValue: response.configValue));
    } catch (e) {
      print(e);
    } finally {
      dismissLoading();
    }
  }
}
