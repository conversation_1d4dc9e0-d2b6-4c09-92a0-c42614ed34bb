import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/profile/privacy_policy/policy_cubit.dart';
import 'package:family_app/screen/main/profile/privacy_policy/policy_parameter.dart';
import 'package:family_app/screen/main/profile/privacy_policy/policy_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';

@RoutePage()
class PolicyPage extends BaseBlocProvider<ProfileStats, PolicyCubit> {
  const PolicyPage({super.key, required this.parameter});

  final PolicyParameter parameter;

  @override
  Widget buildPage() => const ProfileView();

  @override
  PolicyCubit createCubit() => PolicyCubit(
        familyRepository: locator.get(),
        parameter: parameter,
      );
}

class ProfileView extends StatefulWidget {
  const ProfileView({super.key});

  @override
  State<ProfileView> createState() => _ProfileViewState();
}

class _ProfileViewState extends BaseBlocPageState<ProfileView, ProfileStats, PolicyCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  String get title => LocaleKeys.privacy_policy.tr();

  @override
  Widget buildBody(BuildContext context, PolicyCubit cubit, ProfileStats state) {
    return SingleChildScrollView(
      child: Padding(
        padding: padding(all: 16),
        child: Center(
          child: HtmlWidget(
            state.configValue,
            key: Key(state.configValue.toString()),
            onTapUrl: (String url) {
              return false;
            },
          ),
        ),
      ),
    );
  }
}
