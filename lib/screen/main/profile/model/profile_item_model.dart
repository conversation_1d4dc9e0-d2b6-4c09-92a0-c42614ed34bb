// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

class ProfileGroupItem {
  final String title;
  final List<ProfileItemModel> items;

  ProfileGroupItem({
    this.title = '',
    required this.items,
  });
}

class ProfileItemModel {
  final String title;
  final String imagePath;
  final String? content;
  final PageRouteInfo? pageRouteInfo;
  final Widget? action;
  final VoidCallback? onTap;

  ProfileItemModel({
    required this.title,
    required this.imagePath,
    this.content,
    this.pageRouteInfo,
    this.action,
    this.onTap,
  });
}
