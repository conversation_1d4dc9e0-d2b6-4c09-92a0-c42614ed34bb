import 'dart:io';
import 'dart:ui' show Locale;

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/language_service.dart';
import 'package:family_app/config/service/log_service.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/usecase/sign_out_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/profile/profile_state.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/timezone.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import 'package:family_app/utils/flash/toast.dart';

class ProfileCubit extends BaseCubit<ProfileState> {
  final IFamilyRepository familyRepository;
  final AccountService accountService;
  final SignOutUsecase signOutUsecase;
  final LanguageService languageService;
  late final LogService _logService;
  int _helpTapCount = 0;
  DateTime? _firstHelpTap;
  Timer? _helpTapTimer;

  ProfileCubit({
    required this.familyRepository,
    required this.accountService,
    required this.signOutUsecase,
    required this.languageService,
  }) : super(ProfileState()) {
    _initLogService();
    detectAndSetTimezone();
  }

  Future<void> _initLogService() async {
    final appDir = await getApplicationDocumentsDirectory();
    final logDir = Directory('${appDir.path}/logs');
    if (!await logDir.exists()) {
      await logDir.create(recursive: true);
    }
    _logService = LogService(logDir);
  }

  void onLogout() async {
    try {
      showLoading();
      await signOutUsecase.call();
      emit(state.copyWith(status: ProfileStatus.logoutSuccess));
    } catch (e) {
      print(e);
    } finally {
      dismissLoading();
    }
  }

  Future<void> shareAppLog() async {
    try {
      showLoading();
      // Write a test log entry before exporting
      // await _logService.writeLog('Test log entry from shareAppLog at: '
      //     + DateTime.now().toIso8601String());
      await _logService.exportAndShareLogs();
    } catch (e) {
      print('Error sharing logs: $e');
    } finally {
      dismissLoading();
    }
  }

  void handleNotification(bool v) {
    state.notification.value = v;
  }

  void handleLocale(Locale v) {
    languageService.onSaveLanguage(v.languageCode);
  }

  void handleTimezone(Timezone v) {
    emit(state.copyWith(timezone: v));
  }

  void detectAndSetTimezone() {
    final timezone = TimeZoneUtils.getDefaultTimezone();

    emit(state.copyWith(timezone: timezone));
  }

  onTapEditProfile(BuildContext context) async {
    logd("onTapEditProfile");
    await context.pushRoute(const AccountEditRoute());
    await accountService.initMyProfile();
    emit(state.copyWith(updateCount: state.updateCount + 1));
  }

  Future<void> toggleTripPlanDebugEnabled() async {
    final localStorage = GetIt.I<LocalStorage>();
    final current = await localStorage.getTripPlanDebugEnabled();
    final newValue = !current;
    await localStorage.setTripPlanDebugEnabled(newValue);
    showSimpleToast('tripPlanDebugEnabled: $newValue');
  }

  void onHelpTap() {
    final now = DateTime.now();
    if (_firstHelpTap == null || now.difference(_firstHelpTap!) > Duration(seconds: 5)) {
      _firstHelpTap = now;
      _helpTapCount = 1;
      _helpTapTimer?.cancel();
      _helpTapTimer = Timer(Duration(seconds: 5), () {
        _helpTapCount = 0;
        _firstHelpTap = null;
      });
    } else {
      _helpTapCount++;
      if (_helpTapCount >= 3) {
        toggleTripPlanDebugEnabled();
        _helpTapCount = 0;
        _firstHelpTap = null;
        _helpTapTimer?.cancel();
      }
    }
  }
}
