import 'package:auto_route/auto_route.dart';
import 'package:device_calendar/device_calendar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:family_app/screen/main/search/search_cubit.dart';
import 'package:family_app/screen/main/search/search_state.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/button_line_left_widget.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/search_custom_filed.dart';
import 'package:family_app/widget/event_card_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';

@RoutePage()
class SearchScreen extends BaseBlocProvider<SearchState, SearchCubit> {
  const SearchScreen({super.key});

  @override
  Widget buildPage() => const SearchView();

  @override
  SearchCubit createCubit() => SearchCubit(
        accountService: locator.get(),
        eventService: locator.get<EventService>(),
      );
}

class SearchView extends StatefulWidget {
  const SearchView({super.key});

  @override
  State<SearchView> createState() => _SearchViewState();
}

class _SearchViewState
    extends BaseBlocPageState<SearchView, SearchState, SearchCubit> {
  Timer? _debounceTimer;
  String _currentSearchQuery = '';
  SearchCubit? _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<SearchCubit>();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (!mounted) return; // Check if widget is still mounted

      final newQuery = query.trim();
      if (newQuery != _currentSearchQuery) {
        _currentSearchQuery = newQuery;
        if (newQuery.isEmpty) {
          // If search is cleared, refetch all events
          _cubit?.loadInitialEvents();
        } else {
          _cubit?.search(newQuery);
        }
      }
    });
  }

  @override
  bool get showBack => false;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  Widget buildAppBar(
      BuildContext context, SearchCubit cubit, SearchState state) {
    return Padding(
      padding: padding(vertical: 12, horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: SearchCustomField(
              hintText: LocaleKeys.search_text.tr(),
              onGetSearchValue: _onSearchChanged,
              getSearchStatus: (isSearching) {},
              backgroundColor: appTheme.whiteText,
              radius: 8,
              textStyle: AppStyle.regular16(),
              paddingTextfield: EdgeInsets.zero,
              prefixIcon: IconButton(
                onPressed: null,
                icon: ImageAssetCustom(
                    imagePath: Assets.images.searchBorder.path, size: 24.w),
              ),
            ),
          ),
          SizedBox(width: 7.w),
          GestureDetector(
            onTap: context.maybePop,
            child: Text(LocaleKeys.cancel_text.tr(),
                style: AppStyle.regular14(color: appTheme.primaryColor)),
          ),
        ],
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, SearchCubit cubit, SearchState state) {
    return ColoredBox(
      color: appTheme.whiteText,
      child: Padding(
        padding: padding(top: 6, horizontal: 16),
        child: _buildContent(context, cubit, state),
      ),
    );
  }

  Widget _buildContent(
      BuildContext context, SearchCubit cubit, SearchState state) {
    // Show error message if there's an error
    if (state.errorMessage != null) {
      return Container(
        padding: padding(top: 39),
        width: double.infinity,
        alignment: Alignment.topCenter,
        child: Column(
          children: [
            Text(
              'Error: ${state.errorMessage}',
              style: AppStyle.regular14(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => cubit.loadInitialEvents(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // Show loading indicator if searching or if no events loaded yet
    if (state.isSearching ||
        (state.models.isEmpty && state.errorMessage == null)) {
      return Container(
        padding: padding(top: 39),
        width: double.infinity,
        alignment: Alignment.topCenter,
        child: const CircularProgressIndicator(),
      );
    }

    // Show events if available
    if (state.models.isNotEmpty) {
      return ListView(
        children: (state.groupedEvents?.entries ?? []).map((entry) {
          final date = entry.key;
          final events = entry.value;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  date,
                  style: AppStyle.regular14(color: appTheme.labelColor),
                ),
              ),
              ...events.map((event) {
                return EventCardWidget(
                  event: event,
                  onTap: () => context.pushRoute(
                    DetailEventRoute(
                      parameter: DetailEventParameter(eventModels: event),
                    ),
                  ),
                );
              }).toList(),
              const SizedBox(height: 8),
            ],
          );
        }).toList(),
      );
    }

    // Show no results message
    return Container(
      padding: padding(top: 39),
      width: double.infinity,
      alignment: Alignment.topCenter,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: appTheme.grayV2,
          ),
          const SizedBox(height: 16),
          Text(
            _currentSearchQuery.isEmpty
                ? 'No events found'
                : 'No events match your search',
            style: AppStyle.regular16(color: appTheme.grayV2),
            textAlign: TextAlign.center,
          ),
          if (_currentSearchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Try a different search term',
              style: AppStyle.regular14(color: appTheme.hintColor),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
