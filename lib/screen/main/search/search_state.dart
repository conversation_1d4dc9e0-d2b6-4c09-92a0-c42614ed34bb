import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/event.dart';

class SearchState extends BaseState {
  final String query;
  final bool isSearching;
  final List<EventModels> models;
  final Map<String, List<EventModels>>? groupedEvents;
  final String? errorMessage;

  SearchState({
    this.query = '',
    this.isSearching = false,
    this.models = const [],
    this.groupedEvents,
    this.errorMessage,
  });

  SearchState copyWith({
    String? query,
    bool? isSearching,
    List<EventModels>? models,
    Map<String, List<EventModels>>? groupedEvents,
    String? errorMessage,
  }) {
    return SearchState(
      query: query ?? this.query,
      isSearching: isSearching ?? this.isSearching,
      models: models ?? this.models,
      groupedEvents: groupedEvents ?? this.groupedEvents,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [query, isSearching, models, groupedEvents, errorMessage];
}
