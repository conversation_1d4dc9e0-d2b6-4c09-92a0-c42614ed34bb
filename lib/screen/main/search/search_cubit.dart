import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/screen/main/search/search_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';

class SearchCubit extends BaseCubit<SearchState> {
  final EventService eventService;
  final AccountService accountService;

  SearchCubit({
    required this.accountService,
    required this.eventService,
  }) : super(SearchState()) {
    // Load initial events when cubit is created
    loadInitialEvents();
  }

  void updateQuery(String query) {
    emit(state.copyWith(query: query, isSearching: query.isNotEmpty));
  }

  /// Load all events when the screen first opens
  Future<void> loadInitialEvents() async {
    try {
      // Set loading state
      emit(state.copyWith(isSearching: true, errorMessage: null));

      // Fetch all events for the current year (same as SelectEventThreadBts)
      final result = await _getAllEvents();

      groupEventsByDate(result);
      emit(state.copyWith(models: result, isSearching: false));
    } catch (e) {
      emit(state.copyWith(
          errorMessage: 'Failed to load events: $e', isSearching: false));
    }
  }

  Future<void> search(String query) async {
    try {
      // Set loading state
      emit(state.copyWith(isSearching: true, errorMessage: null));

      // Use server-side search like SelectEventThreadBts
      final result = await _getAllEvents(searchQuery: query);

      groupEventsByDate(result);
      emit(state.copyWith(models: result, isSearching: false));
    } catch (e) {
      emit(state.copyWith(
          errorMessage: 'Search failed: $e', isSearching: false));
    }
  }

  /// Get all events for the current year (same logic as SelectEventThreadBts)
  Future<List<EventModels>> _getAllEvents({String? searchQuery}) async {
    try {
      // Use same date range as SelectEventThreadBts
      final now = DateTime.now();
      final fromDate = DateTime(now.year, 1, 1, 0, 0, 0);
      final toDate = DateTime(now.year, 12, 31, 23, 59, 59);

      final result = await eventService.getEventsByFamilyId(
        accountService.familyId,
        from: fromDate,
        to: toDate,
        limit: 1000, // Same limit as SelectEventThreadBts
        search: searchQuery?.isNotEmpty == true
            ? searchQuery
            : null, // Server-side search
      );

      // Apply same deduplication logic as SelectEventThreadBts
      final uniqueEvents = _deduplicateEventsByUuid(result);

      return uniqueEvents;
    } catch (e) {
      rethrow;
    }
  }

  /// Deduplicate events by UUID, keeping the most recent instance of each event (same logic as SelectEventThreadBts)
  List<EventModels> _deduplicateEventsByUuid(List<EventModels> events) {
    final Map<String, EventModels> uniqueEvents = {};

    for (final event in events) {
      final uuid = event.uuid;
      if (uuid == null || uuid.isEmpty) continue;

      // If we haven't seen this UUID before, or if this event is more recent
      if (!uniqueEvents.containsKey(uuid) ||
          _isMoreRecentEvent(event, uniqueEvents[uuid]!)) {
        uniqueEvents[uuid] = event;
      }
    }

    return uniqueEvents.values.toList();
  }

  /// Compare two events and return true if the first event is more recent (same logic as SelectEventThreadBts)
  bool _isMoreRecentEvent(EventModels event1, EventModels event2) {
    // Compare by fromDate first
    final date1 = event1.fromDate?.toLocalDT;
    final date2 = event2.fromDate?.toLocalDT;

    if (date1 != null && date2 != null) {
      return date1.isAfter(date2);
    }

    // If fromDate is not available, compare by createdAt
    final created1 = event1.createdAt?.toLocalDT;
    final created2 = event2.createdAt?.toLocalDT;

    if (created1 != null && created2 != null) {
      return created1.isAfter(created2);
    }

    // If neither is available, keep the first one
    return false;
  }

  void groupEventsByDate(List<EventModels> events) {
    final grouped = <String, List<EventModels>>{};
    for (final event in events) {
      try {
        // Use fromDate if available, otherwise use createdAt, otherwise use current date
        String dateString = event.fromDate ??
            event.createdAt ??
            DateTime.now().toIso8601String();

        // Handle different date formats (same logic as SelectEventThreadBts)
        DateTime eventDate;
        if (dateString.contains('T')) {
          // ISO format
          eventDate = DateTime.parse(dateString);
        } else if (dateString.contains(' ')) {
          // "yyyy-MM-dd HH:mm:ss" format
          eventDate = DateFormat("yyyy-MM-dd HH:mm:ss").parse(dateString);
        } else {
          // "yyyy-MM-dd" format
          eventDate = DateFormat("yyyy-MM-dd").parse(dateString);
        }

        final dateKey = DateFormat('EEEE, MMMM d, yyyy')
            .format(eventDate); // Same format as SelectEventThreadBts
        grouped.putIfAbsent(dateKey, () => []).add(event);
      } catch (e) {
        // Use current date as fallback
        final dateKey = DateFormat('EEEE, MMMM d, yyyy').format(DateTime.now());
        grouped.putIfAbsent(dateKey, () => []).add(event);
      }
    }
    emit(state.copyWith(groupedEvents: grouped));
  }
}
