import 'dart:convert';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/screen/main/thread/widget/thread_avatar.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

const double kThreadItemBorderRadius = 24.0;
const double kAIThreadItemBorderRadius = 16.0;

class ThreadMessageList extends StatelessWidget {
  final List<ThreadFamily> threads;
  final String? currentUserUuid;
  final void Function(ThreadDetailRoute) onThreadTap;
  final VoidCallback? onCreateThread;

  const ThreadMessageList({
    super.key,
    required this.threads,
    required this.currentUserUuid,
    required this.onThreadTap,
    this.onCreateThread,
  });

  @override
  Widget build(BuildContext context) {
    // Mockup AI thread data as a ThreadFamily object
    /* final aiThread = ThreadFamily(
      name: "LINKA AI",
      latestMessage: "I have generated the list ‘chores’ !",
      latestMessageText: "I have generated the list ‘chores’ !",
      latestMessageTime: DateTime.now().toUtc().toString().threadMessageTime,
      unread: 0,
      members: [],
    ); */

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 10),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(kThreadItemBorderRadius),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  _ThreadListView(
                    threads: threads,
                    currentUserUuid: currentUserUuid,
                    onThreadTap: onThreadTap,
                  ),
                ],
              ),
            ),
          ),
        ),
        if (onCreateThread != null)
          _CreateThreadButton(onCreateThread: onCreateThread!),
      ],
    );
  }
}

/* class _AIThreadItem extends StatelessWidget {
  final ThreadFamily aiThread;
  const _AIThreadItem({required this.aiThread});

  @override
  Widget build(BuildContext context) {
    const String aiPhotoUrl = "";

    Widget avatarWidget;
    if (aiPhotoUrl.isNotEmpty) {
      avatarWidget = Avatar(
        aiPhotoUrl,
        name: aiThread.name ?? "",
        size: 48,
      );
    } else {
      avatarWidget = CircleAvatar(
        backgroundColor: Colors.transparent,
        radius: 24,
        child: Assets.images.aiLinka.image(width: 48, height: 48),
      );
    }

    return InkWell(
      borderRadius: BorderRadius.circular(kAIThreadItemBorderRadius),
      onTap: () {},
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(kAIThreadItemBorderRadius),
          color: appTheme.backgroundWhite,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 48,
              height: 48,
              child: avatarWidget,
            ),
            const SizedBox(width: 16),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _ThreadTitleRow(
                    threadName: aiThread.name ?? "",
                    latestMessageTime: aiThread.latestMessageTime ?? "",
                    useShaderMask: true,
                  ),
                  _ThreadMessageRow(
                    latestMessageText: aiThread.latestMessageText ?? "",
                    totalUnread: aiThread.unread ?? 0,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} */

class _ThreadListView extends StatelessWidget {
  final List<ThreadFamily> threads;
  final String? currentUserUuid;
  final void Function(ThreadDetailRoute) onThreadTap;

  const _ThreadListView({
    required this.threads,
    required this.currentUserUuid,
    required this.onThreadTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(kThreadItemBorderRadius),
        color: appTheme.backgroundWhite,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          for (final message in threads)
            _ThreadMessageItem(
              currentUserUuid: currentUserUuid,
              message: message,
              onTap: onThreadTap,
            ),
        ],
      ),
    );
  }
}

class _ThreadMessageItem extends StatelessWidget {
  final String? currentUserUuid;
  final ThreadFamily message;
  final void Function(ThreadDetailRoute) onTap;

  const _ThreadMessageItem({
    required this.currentUserUuid,
    required this.message,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final String threadName = message.name.toString();
    final bool hasLatestMessage =
        message.latestMessage != null && message.latestMessage!.isNotEmpty;
    Map<String, dynamic>? userMap;
    bool isMe = false;
    if (message.latestMessageUserName != null &&
        message.latestMessageUserName!.isNotEmpty) {
      userMap = jsonDecode(message.latestMessageUserName.toString());
      isMe = userMap?["uuid"] == currentUserUuid;
    }
    final String latestMessageText = hasLatestMessage
        ? "${isMe ? "You" : userMap?["full_name"]}: ${message.latestMessageText}"
        : "";
    final String latestMessageTime =
        message.latestMessageTime?.threadMessageTime ?? "";
    final totalUnread = message.unread ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          borderRadius: BorderRadius.circular(kThreadItemBorderRadius),
          onTap: () {
            onTap(ThreadDetailRoute(parameter: message));
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(kThreadItemBorderRadius),
                color: appTheme.backgroundWhite),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ThreadAvatar(
                    members: message.members ?? [],
                    currentUserId: currentUserUuid),
                const SizedBox(width: 16),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _ThreadTitleRow(
                          threadName: threadName,
                          latestMessageTime: latestMessageTime),
                      _ThreadMessageRow(
                          latestMessageText: latestMessageText,
                          totalUnread: totalUnread),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _ThreadTitleRow extends StatelessWidget {
  final String threadName;
  final String latestMessageTime;
  final bool useShaderMask;
  const _ThreadTitleRow({
    required this.threadName,
    required this.latestMessageTime,
    this.useShaderMask = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget title = Text(
      threadName,
      style: AppStyle.medium16(
          color: useShaderMask ? Colors.white : appTheme.blackText),
      overflow: TextOverflow.ellipsis,
    );

    if (useShaderMask) {
      title = ShaderMask(
        shaderCallback: (bounds) => LinearGradient(
          colors: [
            const Color(0xFF3B97EC),
            appTheme.primaryColorV2,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
        child: title,
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(child: title),
        const SizedBox(width: 4),
        if (latestMessageTime.isNotEmpty)
          Text(latestMessageTime,
              style: AppStyle.regular12(color: appTheme.grayV2)),
      ],
    );
  }
}

class _ThreadMessageRow extends StatelessWidget {
  final String latestMessageText;
  final int totalUnread;
  const _ThreadMessageRow(
      {required this.latestMessageText, required this.totalUnread});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Text(
              latestMessageText,
              style: AppStyle.regular14(color: appTheme.blackText),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        const SizedBox(width: 4),
        if (totalUnread > 0) _UnreadBadge(totalUnread),
      ],
    );
  }
}

class _UnreadBadge extends StatelessWidget {
  final int totalUnread;
  const _UnreadBadge(this.totalUnread);

  String getTotalUnreadText(int totalUnread) {
    if (totalUnread > 9) return '9+';
    return '$totalUnread';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(kAIThreadItemBorderRadius),
        color: appTheme.primaryColorV2,
      ),
      child: Text(getTotalUnreadText(totalUnread),
          style: AppStyle.bold12(color: appTheme.whiteText)),
    );
  }
}

class _CreateThreadButton extends StatelessWidget {
  final VoidCallback onCreateThread;
  const _CreateThreadButton({required this.onCreateThread});

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    const bottomRatio = 142 / 874;
    final calculatedBottom = screenHeight * bottomRatio;

    return Positioned(
      bottom: calculatedBottom,
      right: 12,
      child: SafeArea(
        child: IconButton(
          onPressed: onCreateThread,
          icon: SvgPicture.asset(Assets.icons.messagePlus.path),
        ),
      ),
    );
  }
}
