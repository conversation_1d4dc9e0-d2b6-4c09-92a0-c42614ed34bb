import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/assets/shadow_util.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

import '../thread_screen.dart';

class ThreadEmptyView extends StatelessWidget {
  final VoidCallback onCreateThread;
  const ThreadEmptyView({super.key, required this.onCreateThread});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: kThreadScreenBackgroundColor,
              boxShadow: ShadowUtil.backgroundShadow,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ImageAssetCustom(
                  imagePath: Assets.icons.messageEmpty.path,
                  width: 140,
                ),
                const SizedBox(height: 16),
                Text("You don’t have any messages yet",
                    style: AppStyle.medium16(
                      color: appTheme.blackColor,
                    )),
                const SizedBox(height: 16),
                Text("Start the conversation and keep everyone connected. Send a message now!",
                    textAlign: TextAlign.center,
                    style: AppStyle.regular14(
                      color: appTheme.grayColor,
                    )),
                const SizedBox(height: 16),
                InkWell(
                  onTap: onCreateThread,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: appTheme.lightBotColor,
                      boxShadow: ShadowUtil.backgroundShadow,
                    ),
                    child: Text("New message",
                        style: AppStyle.medium16(
                          color: appTheme.whiteText,
                        )),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
