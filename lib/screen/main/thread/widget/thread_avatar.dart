import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/widget/image/avatar.dart';

/// Displays a group/thread avatar, supporting single, double, or multi-member avatars.
class ThreadAvatar extends StatelessWidget {
  final List<Members> members;
  final String? currentUserId;
  final double size;
  final Color? backgroundColor;

  const ThreadAvatar({
    Key? key,
    required this.members,
    this.currentUserId,
    this.size = 48.0,
    this.backgroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Color resolvedBg = backgroundColor ?? const Color(0xFFEAE9F6);

    // Exclude current user for group avatars (if needed)
    final displayMembers = (currentUserId != null && members.length >= 2)
        ? members.where((m) => m.uuid != currentUserId).toList()
        : members;

    Widget avatarWidget;
    switch (displayMembers.length) {
      case 0:
        avatarWidget = const SizedBox.shrink();
        break;
      case 1:
      case 2:
        avatarWidget = SingleAvatar(
          fullname: displayMembers[0].fullName,
          photoUrl: displayMembers[0].photoUrl,
          radius: size / 2,
          backgroundColor: resolvedBg,
          textStyle: AppStyle.bold14(color: Colors.black),
        );
        break;
      default:
        avatarWidget = _MultiAvatar(
          firstFullName: displayMembers[0].fullName,
          firstPhotoUrl: displayMembers[0].photoUrl,
          secondFullName: displayMembers[1].fullName,
          secondPhotoUrl: displayMembers[1].photoUrl,
          moreCount: displayMembers.length - 2,
          size: size,
          backgroundColor: resolvedBg,
        );
    }
    return SizedBox(width: size, height: size, child: avatarWidget);
  }
}

/// Displays a single avatar, with optional border and custom text style.
class SingleAvatar extends StatelessWidget {
  final String? fullname;
  final String? photoUrl;
  final double? radius;
  final Color? backgroundColor;
  final TextStyle? textStyle;
  final Color? borderColor;
  final double borderWidth;

  const SingleAvatar({
    super.key,
    this.fullname,
    this.photoUrl,
    this.radius = 24.0,
    this.backgroundColor,
    this.textStyle,
    this.borderColor,
    this.borderWidth = 0,
  });

  @override
  Widget build(BuildContext context) {
    final avatar = Avatar(
      photoUrl,
      name: fullname,
      size: (radius ?? 24.0) * 2,
      backgroundColor: backgroundColor,
      textStyle: textStyle,
    );
    if (borderColor != null && borderWidth > 0) {
      return Center(
        child: Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: borderColor!, width: borderWidth),
          ),
          child: avatar,
        ),
      );
    }
    return Center(child: avatar);
  }
}

/// Displays a stacked avatar for group threads with more than two members.
class _MultiAvatar extends StatelessWidget {
  final String? firstFullName;
  final String? firstPhotoUrl;
  final String? secondFullName;
  final String? secondPhotoUrl;
  final int moreCount;
  final double size;
  final Color backgroundColor;

  const _MultiAvatar({
    required this.firstFullName,
    this.firstPhotoUrl,
    required this.secondFullName,
    this.secondPhotoUrl,
    required this.moreCount,
    this.size = 48.0,
    required this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    String getTotalCountText(int moreCount) => "+$moreCount";
    return Stack(
      children: [
        Positioned(
          left: 0,
          top: 0,
          child: SingleAvatar(
            fullname: firstFullName,
            photoUrl: firstPhotoUrl,
            radius: size / 4,
            backgroundColor: backgroundColor,
            textStyle: AppStyle.bold10(color: Colors.black),
            borderColor: Colors.white,
            borderWidth: 2,
          ),
        ),
        Positioned(
          left: 0,
          bottom: 0,
          child: SingleAvatar(
            fullname: secondFullName,
            photoUrl: secondPhotoUrl,
            radius: size / 4,
            backgroundColor: backgroundColor,
            textStyle: AppStyle.bold10(color: Colors.black),
            borderColor: Colors.white,
            borderWidth: 2,
          ),
        ),
        if (moreCount > 0)
          Positioned(
            right: 0,
            top: size / 4,
            child: Container(
              width: size * 0.58,
              height: size * 0.58,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: backgroundColor,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: Text(getTotalCountText(moreCount),
                  style: AppStyle.bold10(color: appTheme.primaryColorV2)),
            ),
          ),
      ],
    );
  }
}
