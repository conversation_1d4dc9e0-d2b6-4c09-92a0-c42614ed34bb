import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'dart:math' as math;

class RotatingCheckIconSuccess extends StatefulWidget {
  final String text;
  final double size;
  const RotatingCheckIconSuccess(
      {Key? key, required this.text, this.size = 120})
      : super(key: key);

  @override
  State<RotatingCheckIconSuccess> createState() => _RotatingCheckIconSuccessState();
}

class _RotatingCheckIconSuccessState extends State<RotatingCheckIconSuccess> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 3500),
      vsync: this,
    );
    _animation = Tween<double>(begin: math.pi / 2, end: 0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001)
                ..rotateY(_animation.value),
              child: child,
            );
          },
          child: SvgPicture.asset(
            Assets.icons.iconCheckCircle.path,
            width: widget.size,
            height: widget.size,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          widget.text,
          style: AppStyle.medium20(color: appTheme.greenV2),
        ),
      ],
    );
  }
}
