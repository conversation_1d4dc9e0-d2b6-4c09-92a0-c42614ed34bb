import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/router/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'thread_cubit.dart';
import 'thread_state.dart';
import 'widget/thread_app_bar.dart';
import 'widget/thread_empty_view.dart';
import 'widget/thread_message_list.dart';
import 'thread_upsert/thread_upsert_bottom_sheet.dart';

//TODO: Update to appTheme data
const kThreadScreenBackgroundColor = Color(0x00f7f7f7);

@RoutePage()
class ThreadPage extends BaseBlocProvider<ThreadState, ThreadCubit> {
  const ThreadPage({super.key});

  @override
  Widget buildPage() => const ThreadView();

  @override
  ThreadCubit createCubit() => ThreadCubit(
      familyRepository: locator.get(), accountService: locator.get());
}

class ThreadView extends StatefulWidget {
  const ThreadView({super.key});

  @override
  State<ThreadView> createState() => _ThreadViewState();
}

class _ThreadViewState
    extends BaseBlocPageState<ThreadView, ThreadState, ThreadCubit> {
  @override
  Color get backgroundColor => kThreadScreenBackgroundColor;

  late ThreadCubit cubit;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    cubit = context.read<ThreadCubit>();
    // Register notification callback for thread list refresh
    ThreadCubit.onThreadListNotification = (threadId) {
      // Only refresh if the thread belongs to the current family
      cubit.refreshThreads(cubit.accountService.familyId);
    };
  }

  @override
  void dispose() {
    // Unregister notification callback
    ThreadCubit.onThreadListNotification = null;
    super.dispose();
  }

  @override
  Widget buildAppBar(
      BuildContext context, ThreadCubit cubit, ThreadState state) {
    return const ThreadAppBar();
  }

  @override
  Widget buildBody(BuildContext context, ThreadCubit cubit, ThreadState state) {
    onCreateThread() async {
      final result =
          await ThreadUpsertBottomSheet.show<ThreadFamily?>(context: context);
      if (result != null) {
        await context.pushRoute(ThreadDetailRoute(parameter: result));
        cubit.onFetchAllThread(cubit.accountService.familyId);
      }
    }

    switch (state.status) {
      case ThreadStatus.loading:
        // Show standard loading indicator
        return const Center(child: CircularProgressIndicator());
      case ThreadStatus.error:
        return Center(
            child: Text(state.errorMessage ?? LocaleKeys.error_title.tr()));
      case ThreadStatus.success:
        if (state.threads.isEmpty) {
          return ThreadEmptyView(onCreateThread: onCreateThread);
        }
        return RefreshIndicator(
          onRefresh: () async {
            await cubit.refreshThreads(cubit.accountService.familyId);
          },
          child: ThreadMessageList(
            threads: state.threads,
            currentUserUuid: cubit.accountService.account?.uuid,
            onThreadTap: (thread) async {
              await context.pushRoute(thread);
              cubit.onFetchAllThread(cubit.accountService.familyId);
            },
            onCreateThread: onCreateThread,
          ),
        );
      default:
        return Container();
    }
  }
}
