import 'package:family_app/data/model/thread_family.dart';

import 'package:family_app/base/widget/cubit/base_state.dart';

enum ThreadStatus { none, loading, success, error }

class ThreadState extends BaseState {
  final ThreadStatus status;
  final List<ThreadFamily> threads;
  final String? errorMessage;

  ThreadState({
    this.status = ThreadStatus.none,
    this.threads = const [],
    this.errorMessage,
  });

  ThreadState copyWith({
    ThreadStatus? status,
    List<ThreadFamily>? threads,
    String? errorMessage,
  }) {
    return ThreadState(
      status: status ?? this.status,
      threads: threads ?? this.threads,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, threads, errorMessage];
}
