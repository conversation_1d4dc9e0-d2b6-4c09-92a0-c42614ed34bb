import 'package:family_app/screen/main/thread/thread_state.dart';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/image_cache_service.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'dart:async';

class ThreadCubit extends BaseCubit<ThreadState> {
  final IThreadRepository familyRepository;
  final AccountService accountService;

  /// Static callback for notification-triggered thread list refresh
  static void Function(String threadId)? onThreadListNotification;

  ThreadCubit({
    required this.familyRepository,
    required this.accountService,
  }) : super(ThreadState());

  @override
  void onInit() {
    super.onInit();
    accountService.initMyProfile();
    onFetchAllThread(accountService.familyId);
  }

  @override
  Future<void> close() {
    return super.close();
  }

  onFetchAllThread(String familyId) async {
    try {
      // Only show loading indicator if this is the first load (no existing threads)
      final isFirstLoad = state.threads.isEmpty;
      if (isFirstLoad) {
        emit(state.copyWith(status: ThreadStatus.loading));
      }

      final result = await familyRepository.getAllThread(familyId);
      for (var element in result) {
        element.members ??= [];
      }
      var allThreads = result.toList();
      //Sort all thread by the latest message time
      allThreads.sort((a, b) {
        if (a.latestMessageTime == null) {
          return 1;
        }
        if (b.latestMessageTime == null) {
          return -1;
        }
        return b.latestMessageTime!.compareTo(a.latestMessageTime!);
      });

      // Preload avatar URLs for better performance
      _preloadAvatarUrls(allThreads);

      emit(state.copyWith(status: ThreadStatus.success, threads: allThreads));
    } catch (e) {
      emit(state.copyWith(
          status: ThreadStatus.error, errorMessage: e.toString()));
    }
  }

  /// Refresh threads without showing loading indicator
  Future<void> refreshThreads(String familyId) async {
    try {
      final result = await familyRepository.getAllThread(familyId);
      for (var element in result) {
        element.members ??= [];
      }
      var allThreads = result.toList();
      //Sort all thread by the latest message time
      allThreads.sort((a, b) {
        if (a.latestMessageTime == null) {
          return 1;
        }
        if (b.latestMessageTime == null) {
          return -1;
        }
        return b.latestMessageTime!.compareTo(a.latestMessageTime!);
      });

      // Preload avatar URLs for better performance
      _preloadAvatarUrls(allThreads);

      emit(state.copyWith(status: ThreadStatus.success, threads: allThreads));
    } catch (e) {
      // Don't show error on refresh, just log it
      AppLogger.e('Error refreshing threads: $e');
    }
  }

  /// Preload avatar URLs for all thread members to improve performance
  void _preloadAvatarUrls(List<dynamic> threads) {
    try {
      // Collect all unique avatar UUIDs from thread members
      final avatarUuids = <String>{};
      for (final thread in threads) {
        if (thread.members != null) {
          for (final member in thread.members!) {
            if (member.photoUrl != null &&
                member.photoUrl!.isNotEmpty &&
                member.photoUrl!.length == 36) {
              // UUID format
              avatarUuids.add(member.photoUrl!);
            }
          }
        }
      }

      if (avatarUuids.isNotEmpty) {
        // Preload in background to avoid blocking UI
        imageCacheService.preloadAvatarUrls(avatarUuids.toList());
      }
    } catch (e) {
      // Don't let avatar preloading errors affect the main thread loading
      AppLogger.e('ThreadCubit: Error preloading avatar URLs: $e');
    }
  }
}
