import 'package:equatable/equatable.dart';
import '../member_model.dart';

class ThreadUpsertState extends Equatable {
  final List<MemberModel> members;
  final List<MemberModel> selectedMembers;
  final String topic;
  final bool isLoading;
  final String? errorMessage;
  const ThreadUpsertState({
    this.members = const [],
    this.selectedMembers = const [],
    this.topic = '',
    this.isLoading = false,
    this.errorMessage,
  });

  ThreadUpsertState copyWith({
    List<MemberModel>? members,
    List<MemberModel>? selectedMembers,
    String? topic,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ThreadUpsertState(
      members: members ?? this.members,
      selectedMembers: selectedMembers ?? this.selectedMembers,
      topic: topic ?? this.topic,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        members,
        selectedMembers,
        topic,
        isLoading,
        errorMessage,
      ];
}
