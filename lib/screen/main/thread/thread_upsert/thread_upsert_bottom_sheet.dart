import 'package:auto_route/auto_route.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/textfield/title_text_field_v3.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'thread_upsert_cubit.dart';
import 'thread_upsert_state.dart';
import 'package:flutter/material.dart';
import '../member_model.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/main.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/widget/image/avatar.dart';
import 'package:family_app/utils/flash/flash.dart';
import '../widget/rotating_check_icon_success.dart';

class ThreadUpsertBottomSheet extends StatefulWidget {
  static Future<T?> show<T>({
    required BuildContext context,
  }) {
    final upsertCubit = ThreadUpsertCubit(
      threadRepository: locator.get<IThreadRepository>(),
      accountService: locator.get<AccountService>(),
    );
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      builder: (ctx) => BlocProvider.value(
        value: upsertCubit,
        child: const ThreadUpsertBottomSheet(),
      ),
    );
  }

  const ThreadUpsertBottomSheet({Key? key}) : super(key: key);

  @override
  State<ThreadUpsertBottomSheet> createState() => _ThreadUpsertBottomSheetState();
}

class _ThreadUpsertBottomSheetState extends State<ThreadUpsertBottomSheet> {
  final TextEditingController _topicController = TextEditingController();
  final FocusNode _topicFocusNode = FocusNode();
  bool _isTopicEmpty = false;

  @override
  void initState() {
    super.initState();
    _setupTopicListener();
    Future.delayed(const Duration(milliseconds: 300), () {
      _topicFocusNode.requestFocus();
    });
  }

  void _setupTopicListener() {
    _topicController.addListener(() {
      _onTopicChanged(_topicController.text);
    });
  }

  void _onTopicChanged(String value) {
    setState(() {
      _isTopicEmpty = value.isEmpty;
    });
    context.read<ThreadUpsertCubit>().updateTopic(value);
  }

  @override
  void dispose() {
    _topicController.dispose();
    _topicFocusNode.dispose();
    super.dispose();
  }

  void _validateAndCreateThread(BuildContext context, List<MemberModel> selectedMembers) async {
    setState(() {
      _isTopicEmpty = _topicController.text.isEmpty;
    });
    if (!_isTopicEmpty && selectedMembers.isNotEmpty) {
      final cubit = context.read<ThreadUpsertCubit>();
      final result = await cubit.createThread();
      if (result != null) {
        showFullScreenFlash(
          context,
          child: const RotatingCheckIconSuccess(text: "New Group Chat Created"),
        );
        Navigator.of(context).pop(result);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThreadUpsertCubit, ThreadUpsertState>(
      builder: (context, state) {
        final members = state.members;
        final selectedMembers = state.selectedMembers;
        final isCreateEnabled = selectedMembers.isNotEmpty && _topicController.text.trim().isNotEmpty && !state.isLoading;
        return Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: SingleChildScrollView(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24.0),
                  topRight: Radius.circular(24.0),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _HeaderRow(
                    isCreateEnabled: isCreateEnabled,
                    onBack: () => context.maybePop(),
                    onCreate: () => _validateAndCreateThread(context, selectedMembers),
                  ),
                  const SizedBox(height: 8),
                  Column(
                    children: [
                      const SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32.0),
                        child: _TopicTextField(
                          controller: _topicController,
                          isTopicEmpty: _isTopicEmpty,
                        ),
                      ),
                      const SizedBox(height: 20),
                      _MemberList(
                        members: members,
                        selectedMembers: selectedMembers,
                        onToggle: (member) => context.read<ThreadUpsertCubit>().toggleSelection(member),
                      ),
                      const SizedBox(height: 8),
                      Divider(height: 1, color: appTheme.borderColorV2),
                      const SizedBox(height: 8),
                      _SelectedMembersRow(
                        selectedMembers: selectedMembers,
                        onRemove: (member) => context.read<ThreadUpsertCubit>().toggleSelection(member),
                      ),
                      const SizedBox(height: 10),
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// --- Internal Widgets ---

class _HeaderRow extends StatelessWidget {
  final bool isCreateEnabled;
  final VoidCallback onBack;
  final VoidCallback onCreate;

  const _HeaderRow({
    required this.isCreateEnabled,
    required this.onBack,
    required this.onCreate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 12.0),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ButtonIcon(
            Assets.icons.arrowLeft.path,
            size: 44.h2,
            sizeIcon: 24.h2,
            colorIcon: appTheme.grayV2,
            onBack,
            bg: appTheme.transparentColor,
          ),
          Text(
            "New Group Chat",
            style: AppStyle.textLgS,
          ),
          ButtonIcon(
            Assets.icons.check.path,
            () {
              if (isCreateEnabled) onCreate();
            },
            size: 44.h2,
            sizeIcon: 24.h2,
            colorIcon: isCreateEnabled ? appTheme.primaryColorV2 : Colors.grey,
            bg: appTheme.transparentColor,
          ),
        ],
      ),
    );
  }
}

class _TopicTextField extends StatelessWidget {
  final TextEditingController controller;
  final bool isTopicEmpty;

  const _TopicTextField({
    required this.controller,
    required this.isTopicEmpty,
  });

  @override
  Widget build(BuildContext context) {
    return TitleTextFieldV3(
      controller: controller,
      hintText: "Group name",
      hintStyle: AppStyle.regular16(color: appTheme.grayV2),
    );
  }
}

class _MemberList extends StatefulWidget {
  final List<MemberModel> members;
  final List<MemberModel> selectedMembers;
  final ValueChanged<MemberModel> onToggle;

  const _MemberList({
    required this.members,
    required this.selectedMembers,
    required this.onToggle,
  });

  @override
  State<_MemberList> createState() => _MemberListState();
}

class _MemberListState extends State<_MemberList> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {}); // Triggers rebuild on every text change
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<MemberModel> _filterMembers(String query) {
    if (query.isEmpty) return widget.members;
    final q = query.toLowerCase();
    return widget.members.where((m) {
      final name = m.account.fullName?.toLowerCase() ?? '';
      final role = m.account.role?.toLowerCase() ?? '';
      return name.contains(q) || role.contains(q);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final filtered = _filterMembers(_searchController.text.trim());
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: TitleTextFieldV3(
            controller: _searchController,
            prefix: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SvgPicture.asset(Assets.icons.icSearch.path, width: 16, height: 16),
            ),
            prefixIconConstraints: const BoxConstraints(maxWidth: 32),
            hintText: "Search by member",
            hintStyle: AppStyle.regular12(color: appTheme.grayV2),
            radius: BorderRadius.circular(32),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            filled: true,
          ),
        ),
        const SizedBox(height: 8),
        Divider(height: 1, color: appTheme.borderColorV2),
        const SizedBox(height: 8),
        SizedBox(
          height: 250,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: filtered.length,
            itemBuilder: (context, index) {
              final member = filtered[index];
              return _MemberTile(
                member: member,
                selected: widget.selectedMembers.contains(member),
                onToggle: widget.onToggle,
              );
            },
          ),
        ),
      ],
    );
  }
}

class _MemberTile extends StatelessWidget {
  final MemberModel member;
  final bool selected;
  final ValueChanged<MemberModel> onToggle;

  const _MemberTile({
    required this.member,
    required this.selected,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: InkWell(
        onTap: () => onToggle(member),
        child: Row(
          children: [
            Checkbox(
              value: selected,
              onChanged: (bool? value) => onToggle(member),
              activeColor: appTheme.primaryColorV2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
              side: const BorderSide(width: 2),
            ),
            Expanded(
              child: ListTile(
                title: Text(member.account.fullName.toString(), style: AppStyle.bold16()),
                subtitle: Text(member.account.role.toString(), style: AppStyle.regular12(color: appTheme.grayV2)),
                leading: Avatar(
                  member.account.photoUrl,
                  name: member.account.fullName,
                  size: 40,
                  textStyle: AppStyle.textSmS,
                ),
                contentPadding: EdgeInsets.zero,
                minTileHeight: 48,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SelectedMembersRow extends StatelessWidget {
  final List<MemberModel> selectedMembers;
  final ValueChanged<MemberModel> onRemove;

  const _SelectedMembersRow({
    required this.selectedMembers,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 66,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: ListView(
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                children: selectedMembers
                    .map(
                      (member) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: Stack(
                          children: [
                            Avatar(
                              member.account.photoUrl,
                              name: member.account.fullName,
                              size: 48,
                              textStyle: AppStyle.textSmS,
                            ),
                            Positioned(
                              right: 0,
                              top: 0,
                              child: GestureDetector(
                                onTap: () => onRemove(member),
                                child: const CircleAvatar(
                                  radius: 10,
                                  backgroundColor: Colors.black,
                                  child: Icon(Icons.close, size: 12, color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
