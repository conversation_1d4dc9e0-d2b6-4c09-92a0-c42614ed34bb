import 'package:family_app/data/model/thread_family.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'thread_upsert_state.dart';
import '../member_model.dart';
import 'package:family_app/data/repository/thread/ithread_repository.dart';
import 'package:family_app/data/repository/thread/model/thread_parameter.dart';
import 'package:family_app/config/service/account_service.dart';

class ThreadUpsertCubit extends Cubit<ThreadUpsertState> {
  final IThreadRepository threadRepository;
  final AccountService accountService;

  ThreadUpsertCubit({
    required this.threadRepository,
    required this.accountService,
  }) : super(const ThreadUpsertState()) {
    initMembers();
  }

  void initMembers() {
    final listMember = accountService.memberInFamily.value;
    final listMemberModel = listMember.map((e) => MemberModel(e, true)).where((element) => element.account.familyMemberUuid?.isNotEmpty ?? false).toList();
    emit(state.copyWith(members: listMemberModel, selectedMembers: listMemberModel));
  }

  void toggleSelection(MemberModel member) {
    final updated = List<MemberModel>.from(state.selectedMembers);
    if (updated.contains(member)) {
      updated.remove(member);
    } else {
      updated.add(member);
    }
    emit(state.copyWith(selectedMembers: updated));
  }

  void updateTopic(String topic) {
    emit(state.copyWith(topic: topic));
  }

  Future<ThreadFamily?> createThread() async {
    if (state.topic.isEmpty || state.selectedMembers.isEmpty) return null;
    emit(state.copyWith(isLoading: true, errorMessage: null));
    try {
      final result = await threadRepository.createThread(
        ThreadParameter(
          familyId: accountService.familyId,
          name: state.topic,
          members: state.selectedMembers.map((e) => e.account.familyMemberUuid.toString()).toList(),
          color: '',
          image: '',
          setting: [],
        ),
      );
      return result;
    } catch (e) {
      emit(state.copyWith(isLoading: false, errorMessage: e.toString()));
      return null;
    }
  }
}
