import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_cubit.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_state.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/check_list/widgets/check_list_subitem_widget.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/dotted_border/dotted_border.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/persons_view.dart';
import 'package:family_app/widget/round_item_view.dart';
import 'package:flutter/material.dart';

import '../widgets/check_list_type_widget.dart';

@RoutePage()
class ListDetailPage
    extends BaseBlocProvider<ListDetailState, ListDetailCubit> {
  const ListDetailPage({super.key, required this.parameter});

  final ListDetailParameter parameter;

  @override
  Widget buildPage() => ListDetailView(parameter: parameter);

  @override
  ListDetailCubit createCubit() => ListDetailCubit(
        parameter: parameter,
        accountService: locator.get(),
        categoryRepository: locator.get(),
        activityRepository: locator.get(),
        mainCubit: locator.get(),
        usecase: locator.get(),
      );
}

class ListDetailView extends StatefulWidget {
  const ListDetailView({super.key, required this.parameter});

  final ListDetailParameter parameter;

  @override
  State<ListDetailView> createState() => _ListDetailViewState();
}

class _ListDetailViewState extends BaseBlocPageState<ListDetailView,
    ListDetailState, ListDetailCubit> {
  @override
  String get title => LocaleKeys.checklist_details.tr();

  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get isResizeToAvoidBottomInset => false;

  @override
  bool listenWhen(ListDetailState previous, ListDetailState current) {
    if (current.notFound) {
      showSimpleToast(LocaleKeys.list_not_found.tr());
      context.maybePop();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(
      BuildContext context, ListDetailCubit cubit, ListDetailState state) {
    return CustomAppBar2(
      title: title,
      showBack: true,
      actions: [
        PopupMenuButton<String>(
          icon: Container(
            padding: padding(all: 2),
            child: Assets.icons.buttonThreeDot.svg(width: 32.w, height: 32.w),
          ),
          color: appTheme.whiteText,
          onOpened: () => cubit.emit(state.copyWith(isShowMenu: true)),
          onCanceled: () => cubit.emit(state.copyWith(isShowMenu: false)),
          onSelected: (String value) => cubit.onMore(context, value),
          padding: EdgeInsets.zero,
          itemBuilder: (ctx) => <PopupMenuEntry<String>>[
            PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Assets.icons.icEdit.svg(width: 24, height: 24),
                  const SizedBox(width: 8),
                  Text(
                    LocaleKeys.editList.tr(),
                    style: AppStyle.regular14(color: appTheme.blackText),
                  ),
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'check_all_items',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Assets.icons.icCheckAllItems.svg(width: 24, height: 24),
                  const SizedBox(width: 8),
                  Text(
                    LocaleKeys.checkAllItems.tr(),
                    style: AppStyle.regular14(color: appTheme.blackText),
                  ),
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'duplicate',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Assets.icons.icDuplicate.svg(width: 24, height: 24),
                  const SizedBox(width: 8),
                  Text(
                    LocaleKeys.duplicate_list.tr(),
                    style: AppStyle.regular14(color: appTheme.blackText),
                  ),
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Assets.icons.icDelete.svg(
                    width: 24,
                    height: 24,
                    colorFilter:
                        ColorFilter.mode(appTheme.red3CColor, BlendMode.srcIn),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    LocaleKeys.delete_list.tr(),
                    style: AppStyle.regular14(color: appTheme.red3CColor),
                  ),
                ],
              ),
            ),
          ],
        )
      ],
    );
  }

  @override
  List<Widget>? appBarActions(ListDetailCubit cubit, ListDetailState state) {
    if (isViewer) return [];
    return [
      GestureDetector(
        onTap: () async {
          DialogUtils.showDeleteDialog(
            context,
            title: LocaleKeys.delete_list.tr(),
            content: LocaleKeys.are_you_sure_you_want_to_delete_this_list.tr(),
            confirmText: LocaleKeys.confirm.tr(),
            onConfirm: () async {
              final result = await cubit.deleteListItem();
              if (result) {
                showSimpleToast(LocaleKeys.delete_list_success.tr());
                context.back();
              } else {
                showSimpleToast(LocaleKeys.action_fail.tr());
              }
            },
          );
        },
        child: RoundItemView(
          child: ImageAssetCustom(
              imagePath: Assets.images.trash.path,
              size: 16,
              color: appTheme.redColor),
        ),
      ),
      const SizedBox(width: 12),
      GestureDetector(
        onTap: () async {
          final result = await context.pushRoute(
            UpsertListItemRoute(
              parameter: UpsertListItemParameter(
                  type: widget.parameter.type, listItem: state.listItem),
            ),
          );
          if (result != null && result is ListItem) {
            cubit.updateListItem(result);
          }
        },
        child: RoundItemView(
            child: ImageAssetCustom(
                imagePath: Assets.images.iconEdit.path, size: 16)),
      )
    ];
  }

  @override
  Widget buildBody(
      BuildContext context, ListDetailCubit cubit, ListDetailState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return SingleChildScrollView(
      padding: padding(bottom: 16),
      child: Column(
        children: [
          Container(
            padding: padding(horizontal: 8, vertical: 24),
            margin: padding(horizontal: 5.0, top: 8),
            decoration: BoxDecoration(
              color: appTheme.whiteText,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    if (state.listItem != null)
                      CheckListTypeWidget(
                        type: state.type,
                        listItem: state.listItem!,
                      ),
                    const Spacer(),
                    PersonsView(
                      accounts: state.listItem?.includedMembers ?? <Account>[],
                      avatarSize: 26,
                      spacing: -13,
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                Text(state.listItem?.name ?? '',
                    style: AppStyle.medium20(color: appTheme.blackColor)),
                SizedBox(height: 8.h),
                // if ((state.listItem?.items?.length ?? 0) > 0) ...[
                //   Row(
                //     children: [
                //       Expanded(
                //         child: ClipRRect(
                //           borderRadius: BorderRadius.circular(4.h2),
                //           child: LinearProgressIndicator(
                //               value: state.progress / 100,
                //               color: state.listItem?.color.toColor,
                //               backgroundColor: Colors.grey[200]),
                //         ),
                //       ),
                //       SizedBox(width: 4.w2),
                //       Text('${(state.progress).toStringAsFixed(0)}%',
                //           style: AppStyle.regular10V2(color: appTheme.grayV2)),
                //     ],
                //   ),
                //   SizedBox(height: 8.h),
                // ],
                Text(LocaleKeys.activity.tr(),
                    style: AppStyle.regular12(color: appTheme.hintColor)),
                state.activity == null
                    ? SizedBox(height: 16.h)
                    : InkWell(
                        onTap: () => cubit.onTapActivity(context),
                        child: Row(
                          children: [
                            Text(state.activity?.name ?? "",
                                style: AppStyle.medium14(
                                    color: appTheme.blackColor)),
                            const SizedBox(width: 4),
                            Assets.icons.icViewDetail.svg(width: 16, height: 16)
                          ],
                        ),
                      ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.point.tr(),
                            style:
                            AppStyle.regular12(color: appTheme.hintColor),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                             "${state.listItem?.point ?? 0}",
                            style:
                            AppStyle.medium14(color: appTheme.blackColor),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.assign_to.tr(),
                            style:
                            AppStyle.regular12(color: appTheme.hintColor),
                          ),
                          SizedBox(height: 4.h),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircleAvatarCustom(
                                color: appTheme.whiteText,
                                imageUrl: state.createdBy?.photoUrl ?? '',
                                accountName: state.createdBy?.fullName ?? '',
                                size: 26,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  state.assignment.isNotEmpty == true
                                      ? state.assignment.first.fullName ??
                                      "-"
                                      : "-",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppStyle.medium14(
                                      color: appTheme.blackColor),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.createdDate.tr(),
                            style:
                                AppStyle.regular12(color: appTheme.hintColor),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            state.listItem?.createdAt?.MMMDDyyyyTime ?? '',
                            style:
                                AppStyle.medium14(color: appTheme.blackColor),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.createdBy.tr(),
                            style:
                                AppStyle.regular12(color: appTheme.hintColor),
                          ),
                          SizedBox(height: 4.h),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircleAvatarCustom(
                                color: appTheme.whiteText,
                                imageUrl: state.createdBy?.photoUrl ?? '',
                                accountName: state.createdBy?.fullName ?? '',
                                size: 26,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  state.createdBy?.fullName ?? "-",
                                  style: AppStyle.medium14(
                                      color: appTheme.blackColor),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
          // ColoredBox(
          //   color: state.listItem?.color?.toColor ?? appTheme.redColor,
          //   child: Stack(
          //     children: [
          //       Padding(
          //         padding: padding(vertical: 14, horizontal: 16),
          //         child: Column(
          //           crossAxisAlignment: CrossAxisAlignment.start,
          //           children: [
          //             Text(state.listItem?.name ?? '', style: AppStyle.medium20(color: appTheme.whiteText)),
          //             SizedBox(height: 17.h),
          //             Row(
          //               children: [
          //                 ImageAssetCustom(
          //                     imagePath: Assets.images.calendar.path, color: appTheme.whiteText, width: 16.w),
          //                 SizedBox(width: 8.w),
          //                 Text(
          //                   state.listItem?.planDate?.until_time_MMMDD ?? '',
          //                   style: AppStyle.regular14(color: appTheme.whiteText),
          //                 ),
          //                 const Spacer(),
          //                 ImageAssetCustom(
          //                   imagePath:
          //                       (state.type == ListType.Shopping) ? Assets.images.shopping.path : Assets.images.todo.path,
          //                   color: appTheme.whiteText,
          //                   width: 16.w,
          //                 ),
          //                 SizedBox(width: 8.w),
          //                 Text(state.type.name, style: AppStyle.regular14(color: appTheme.whiteText)),
          //                 SizedBox(width: 32.w),
          //               ],
          //             ),
          //             SizedBox(height: 12.h),
          //             Row(
          //               children: [
          //                 ImageAssetCustom(imagePath: Assets.images.folder.path, color: appTheme.whiteText, width: 16.w),
          //                 SizedBox(width: 8.w),
          //                 Text(
          //                   state.listItem?.activity?.name ?? '',
          //                   style: AppStyle.regular14(color: appTheme.whiteText),
          //                 ),
          //               ],
          //             ),
          //           ],
          //         ),
          //       ),
          //       Positioned(
          //           right: -45,
          //           bottom: -108,
          //           child: Assets.images.circleBackground.image(width: 222, height: 183, fit: BoxFit.cover))
          //     ],
          //   ),
          // ),
          // SizedBox(height: 12.h),
          // Container(
          //   padding: padding(all: 13),
          //   color: appTheme.whiteText,
          //   child: Row(
          //     children: [
          //       ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w),
          //       SizedBox(width: 10.w),
          //       Expanded(
          //         child: MemberHorizontalView(
          //           createBy: state.listItem?.createdBy ?? '',
          //           accounts: state.listItem?.includedMembers ?? <Account>[],
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          SizedBox(height: 8.h),
          Container(
            // padding: padding(all: 13),
            // color: appTheme.whiteText,
            padding: padding(horizontal: 8, vertical: 24),
            margin: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 2),
            decoration: BoxDecoration(
              color: appTheme.whiteText,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        LocaleKeys.subItems.tr(),
                        style: AppStyle.medium20(color: appTheme.blackColor),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => cubit.addSubItem(context),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.add,
                              color: appTheme.primaryColorV2, size: 16),
                          SizedBox(width: 4.w),
                          Text(
                            LocaleKeys.add_item.tr(),
                            style: AppStyle.medium14(
                                color: appTheme.primaryColorV2),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                SizedBox(height: 8.h),
                if (state.listItem?.items?.isEmpty ?? true)
                  DottedBorder(
                    color: appTheme.hintColor,
                    borderType: BorderType.RRect,
                    strokeWidth: 1,
                    dashPattern: const [6, 6],
                    radius: const Radius.circular(24),
                    child: GestureDetector(
                      onTap: () => cubit.addSubItem(context),
                      child: Container(
                        padding: padding(vertical: 32, horizontal: 16),
                        decoration: BoxDecoration(
                          color: appTheme.whiteText,
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.noSubItemsYet.tr(),
                              style:
                                  AppStyle.bold16(color: appTheme.blackColor),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              LocaleKeys.noSubItemsYetMsg.tr(),
                              textAlign: TextAlign.center,
                              style:
                                  AppStyle.regular12(color: appTheme.hintColor),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) => ChecklistSubItemWidget(
                      subItem: state.listItem!.items![index],
                      backgroundColor: state.listItem!.items![index].isDone
                          ? appTheme.activeSubItemColor.withValues(alpha: .12)
                          : appTheme.backgroundV2,
                      // checkColor: state.listItem?.color?.toColor ?? appTheme.redColor,
                      // textDecoration: (state.items[index].isDone ?? false) ? TextDecoration.lineThrough : null,
                      onChanged: () {
                        if (isViewer) {
                          showSimpleToast(LocaleKeys
                              .viewer_not_permission_update_list
                              .tr());
                          return;
                        }
                        cubit.updateItemStatusInList(index);
                      },
                      onEdit: () => cubit.onEditSubItem(
                        context,
                        state.listItem!.items![index],
                      ),
                    ),
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 12),
                    itemCount: state.listItem?.items?.length ?? 0,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
