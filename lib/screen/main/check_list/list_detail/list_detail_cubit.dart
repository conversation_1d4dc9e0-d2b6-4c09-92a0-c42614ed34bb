import 'dart:developer';

import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/category/icategory_repository.dart';
import 'package:family_app/data/usecase/model/upsert_item_param.dart';
import 'package:family_app/data/usecase/model/upsert_list_item_param.dart';
import 'package:family_app/data/usecase/upsert_list_item_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/birthday_registry/birthday_registry_detail_parameter.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_state.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_screen.dart';
import 'package:family_app/screen/main/check_list/upsert_sub_item/upsert_sub_item_parameter.dart';
import 'package:family_app/screen/main/check_list/upsert_sub_item/upsert_sub_item_screen.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_parameter.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/utils/mixin/list_item_mixin.dart';
import 'package:family_app/utils/snackbar.dart';
import 'package:flutter/material.dart';

class ListDetailCubit extends BaseCubit<ListDetailState> with ListItemMixin {
  final HomeCubit mainCubit;

  final AccountService accountService;
  final ListDetailParameter parameter;
  final IActivityRepository activityRepository;
  final ICategoryRepository categoryRepository;
  final UpsertListItemUsecase usecase;

  ListDetailCubit({
    required this.parameter,
    required this.accountService,
    required this.activityRepository,
    required this.categoryRepository,
    required this.mainCubit,
    required this.usecase,
  }) : super(ListDetailState());

  @override
  void onInit() async {
    super.onInit();
    emit(state.copyWith(listItem: parameter.listItem, type: parameter.type, isLoading: true));
    try {
      if (parameter.listItem?.uuid != null || parameter.uuid.isNotEmpty) {
        // showLoading();
        await onReloadLog();
      }
    } catch (e) {
      log(e.toString());
    }
    emit(state.copyWith(isLoading: false));

    // dismissLoading();
    if (state.listItem == null || (state.listItem?.uuid ?? '').isEmpty) {
      emit(state.copyWith(notFound: true));
      return;
    }
    // onReloadLog();
  }

  onReloadLog() async {
    // final result = await listRepository.getAllItemLogInList(state.listItem?.uuid ?? '');
    // emit(state.copyWith(logs: result));
    String uuid = '';
    if (state.listItem != null) {
      uuid = state.listItem!.uuid!;
    } else {
      uuid = parameter.uuid;
    }

    var res = await Future.wait(
      [
        listRepository.getListDetail(uuid),
        listRepository.getAllItemInList(uuid),
      ],
    );
    final result = res[0] as ListItem?;
    final listItems = res[1] as List<Item>;

    if (result == null) {
      return;
    }

    fetchActivity(result.activityId ?? '');
    // final categories = await categoryRepository.getAllCategory();
    // final result = await listRepository.getListDetail(parameter.listItem!.uuid!);
    // final listItems = await listRepository.getAllItemInList(parameter.listItem!.uuid!);
    // final categories = await categoryRepository.getAllCategory();

    if (listItems.isNotEmpty) {
      result.items = listItems;
    }
    // final newListItem = await getDetailListItem(categories, result);
    final newListItem = result;

    for (Account member in newListItem.includedMembers ?? []) {
      if (member.familyMemberUuid == newListItem.createdBy) {
        emit(state.copyWith(createdBy: member));
        break;
      }
    }

    emit(state.copyWith(listItem: newListItem, type: newListItem.listType, assignment: result.assignment));
  }

  Future<void> fetchActivity(String uuid) async {
    if(uuid.isEmpty) return;
    try{
      var result = await activityRepository.getActivityById(uuid);
      emit(state.copyWith(activity: result));
    }catch(e){
      log(e.toString());
    }

  }

  void updateListItem(ListItem? listItem) {
    emit(state.copyWith(listItem: listItem));
  }

  Future<void> updateItemStatusInList(int index) async {
    late ListItem? list = state.listItem;
    final items = <Item>[...(list?.items ?? <Item>[])];
    final originalStatus = items[index].status;
    String currentUserId = accountService.account?.uuid ?? '';
    //only owner and assignees can change status
    if (!items[index].isOwner(currentUserId) && !items[index].isAssignee(currentUserId)) {
      showSimpleToast(LocaleKeys.only_owner_can_change_status.tr());
      return;
    }

    final newStatus = items[index].status == 0 ? 1 : 0;
    items[index].status = newStatus;
    list?.items = items;
    _updateListItemByType(list);
    try {
      final result = await listRepository.changeStatusItemInList(items[index].uuid ?? '', newStatus);
      if (result != null) {
        items[index] = result;
      } else {
        items[index].status = originalStatus;
      }
      _updateListItemByType(list);
      onReloadLog();
    } catch (e) {
      items[index].status = originalStatus;
      _updateListItemByType(list);
    }
  }

  void _updateListItemByType(ListItem? list) {
    mainCubit.onRefresh();
    emit(state.copyWith(
      listItem: list,
      updateCount: state.updateCount + 1,
    ));
  }

  Future<bool> deleteListItem() async {
    final result = await listRepository.deleteList(state.listItem?.uuid ?? '');
    if (result) {
      mainCubit.deleteListItem(state.listItem?.uuid ?? '');
      return true;
    }
    return false;
  }

  addSubItem(BuildContext context) async {
    var result = await BottomSheetUtils.showScrollable(context,
        child: UpsertSubItemPage(
          parameter: UpsertSubItemParameter(
            listUuid: state.listItem?.uuid ?? '',
            listItem: state.listItem,
          ),
        ));

    if (result != null && result is Item) {
      final items = <Item>[...(state.listItem?.items ?? <Item>[])];
      items.add(result);
      state.listItem?.items = items;
      mainCubit.updateListItemByType(state.listItem!);
      emit(state.copyWith(
        listItem: state.listItem,
        updateCount: state.updateCount + 1,
      ));
      onReloadLog();
    }
  }

  onEditSubItem(BuildContext context, Item item) async {
    var result = await BottomSheetUtils.showScrollable(context,
        child: UpsertSubItemPage(
          parameter: UpsertSubItemParameter(
            listUuid: state.listItem?.uuid ?? '',
            item: item,
          ),
        ));
    if (result != null && result is Item) {
      final items = <Item>[...(state.listItem?.items ?? <Item>[])];
      final index = items.indexWhere((element) => element.uuid == item.uuid);
      if (index != -1) {
        items[index] = result;
        state.listItem?.items = items;
        mainCubit.updateListItemByType(state.listItem!);
        emit(state.copyWith(
          listItem: state.listItem,
          updateCount: state.updateCount + 1,
        ));
        onReloadLog();
      }
    }
  }

  checkAllItems(BuildContext context) async {
    late ListItem? list = state.listItem;
    final items = <Item>[...(list?.items ?? <Item>[])];
    String currentUserId = accountService.account?.uuid ?? '';
    for (int i = 0; i < items.length; i++) {
      if(items[i].isOwner(currentUserId) || items[i].isAssignee(currentUserId)) {
        items[i].status = 1;
        final result = await listRepository.changeStatusItemInList(
            items[i].uuid ?? '', 1);
        if (result != null) {
          items[i] = result;
        }
      }
    }
    list?.items = items;
    _updateListItemByType(list);
    await mainCubit.onRefresh();
    onReloadLog();
    mainCubit.updateListItemByType(state.listItem!);
  }

  onDeleteListItem(BuildContext context) {
    DialogUtils.showDeleteDialog(
      context,
      title: LocaleKeys.delete_list.tr(),
      content: LocaleKeys.are_you_sure_you_want_to_delete_this_list.tr(),
      confirmText: LocaleKeys.confirm.tr(),
      onConfirm: () async {
        final result = await deleteListItem();
        await mainCubit.onRefresh();
        if (result) {
          showSimpleToast(LocaleKeys.delete_list_success.tr());
          context.back();
        } else {
          showSimpleToast(LocaleKeys.action_fail.tr());
        }
      },
    );
  }

  duplicate(BuildContext context) async {
    showLoading();
    List<UpsertItemParam> items = state.listItem?.items?.map((e) => e.duplicateParam).toList() ?? [];
    try {
      UpsertListItemParam param = UpsertListItemParam(
        name: "Copy from ${state.listItem?.name ?? ''}",
        planDate: '',
        color: state.listItem?.color ?? '',
        description: state.listItem?.description ?? "",
        assignment: state.listItem?.assignment?.map((e) => e.familyMemberUuid ?? '').toList() ?? [],
        includedMembers: state.listItem?.includedMembers?.map((e) => e.uuid ?? '').toList() ?? [],
        familyId: accountService.familyId,
        items: items,
        type: state.type,
        point: num.tryParse("${state.listItem?.point ?? 0}")?.toInt() ?? 0,
        activityId: state.listItem?.activityId,
      );
      // print("call usecase with uuid: ${param.uuid} param: $param");

      //the result is supposed to be a LIST object,
      final ListItem result = await usecase.call(param);

      // call API in mainCubit to update the LISTs
      await mainCubit.onRefresh();
      SnackBarUtils.showSucceed(
        context,
        title: LocaleKeys.new_list_is_added.tr(),
        content: LocaleKeys.new_list_is_added_msg.tr(),
      );
      emit(state.copyWith(listItem: result));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    }
    dismissLoading();
  }

  onMore(BuildContext context, String value) async {
    switch (value) {
      case 'edit':
        await BottomSheetUtils.showScrollable(context,
            child: UpsertListItemPage(
                parameter: UpsertListItemParameter(
              listItem: state.listItem,
              type: state.type,
            )));
        onReloadLog();
        break;
      case 'check_all_items':
        checkAllItems(context);
        break;
      case 'duplicate':
        var res = await DialogUtils.showConfirmDialog(
          context,
          title: LocaleKeys.duplicate_list.tr(),
          content: LocaleKeys.are_you_sure_you_want_to_duplicate_this_list.tr(),
          confirmText: LocaleKeys.confirm.tr(),
          cancelText: LocaleKeys.cancel.tr(),
        );
        if (res == true) {
          duplicate(context);
        }
        break;
      case 'delete':
        onDeleteListItem(context);
        break;
    }
  }

  Future<void> onTapActivity(BuildContext context) async {
    logd('activity: ${state.activity?.uuid}');
    if (state.activity != null) {
      var activity = state.activity!;
      LocalStorage localStorage = locator.get();
      final token = await localStorage.accessToken();
      if (token!.isEmpty) {
        await localStorage.clear();
        context.replaceRoute(const AuthRoute());
      } else {
        switch (state.activity!.activityType) {
          case 'birthday_registry':
            context.pushRoute(BirthdayRegistryDetailRoute(
                parameter: BirthdayRegistryDetailParameter(
                    activityId: activity.uuid, activity: activity)));
            break;
          default:
            context.pushRoute(TripDetailRoute(
                parameter: TripDetailParameter(
                    activityId: activity.uuid, activity: activity)));
            break;
        }
      }
    } else {

    }
  }
}
