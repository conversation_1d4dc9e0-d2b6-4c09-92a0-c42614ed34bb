import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/list_log_item.dart';

class ListDetailState extends BaseState {
  final ListItem? listItem;
  final ActivityModel? activity;
  final ListType type;
  final int updateCount;
  final bool notFound;
  final List<ListLog> logs;
  final List<Account> assignment;
  final Account? createdBy;
  final double progress;
  final bool isShowMenu;

  ListDetailState({
    this.listItem,
    this.activity,
    this.createdBy,
    this.type = ListType.Shopping,
    this.updateCount = 0,
    this.progress = 50,
    this.notFound = false,
    this.isShowMenu = false,
    this.assignment = const [],
    this.logs = const [],
    super.isLoading = false,
  });

  @override
  List<Object?> get props =>
      [createdBy, listItem, activity, type, listItem, updateCount, notFound, logs, progress, isShowMenu,assignment, ...super.props];

  ListDetailState copyWith(
      {Account? createdBy,
      ListItem? listItem,
      ActivityModel? activity,
      ListType? type,
      double? progress,
      int? updateCount,
      bool? notFound,
      bool? isShowMenu,
      bool? isLoading,
        List<Account>? assignment,
      List<ListLog>? logs}) {
    return ListDetailState(
        createdBy: createdBy ?? this.createdBy,
        listItem: listItem ?? this.listItem,
        activity: activity ?? this.activity,
        progress: progress ?? this.progress,
        type: type ?? this.type,
        updateCount: updateCount ?? this.updateCount,
        isShowMenu: isShowMenu ?? this.isShowMenu,
        notFound: notFound ?? this.notFound,
        isLoading: isLoading ?? this.isLoading,
        assignment: assignment ?? this.assignment,
        logs: logs ?? this.logs);
  }
}
