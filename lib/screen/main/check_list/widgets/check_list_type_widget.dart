import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:flutter/material.dart';

import '../../../../config/constant/app_constant.dart';

class CheckListTypeWidget extends StatelessWidget {
  final ListType type;
  final ListItem listItem;

  const CheckListTypeWidget({super.key, required this.type, required this.listItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 24,
      padding: padding(horizontal: 8),
      decoration: BoxDecoration(
        color: listItem.color?.toColor ?? appTheme.redColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (ListType.Shopping == type) ...[
            Assets.images.icTypeShopping.svg(color: appTheme.whiteText, width: 16),
            Text(LocaleKeys.shopping.tr(), style: AppStyle.regular10(color: appTheme.whiteText))
          ] else if (ListType.Todo == type) ...[
            Assets.images.icTypeTodos.svg(color: appTheme.whiteText, width: 16),
            Text(LocaleKeys.todo.tr(), style: AppStyle.regular10(color: appTheme.whiteText))
          ] else ...[
            Assets.images.icTypeOthers.svg(color: appTheme.whiteText, width: 16),
            Text(LocaleKeys.others.tr(), style: AppStyle.regular10(color: appTheme.whiteText))
          ],
        ],
      ),
    );
  }
}
