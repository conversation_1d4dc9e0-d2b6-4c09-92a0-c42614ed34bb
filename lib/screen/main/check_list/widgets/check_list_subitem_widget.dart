import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/persons_view.dart';
import 'package:family_app/widget/square_checkbox.dart';
import 'package:flutter/material.dart';

class ChecklistSubItemWidget extends StatelessWidget {
  const ChecklistSubItemWidget({
    super.key,
    required this.subItem,
    required this.onChanged,
    required this.onEdit,
    this.backgroundColor,
  });

  final Item subItem;
  final VoidCallback onChanged;
  final VoidCallback onEdit;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: !subItem.isDone ? onEdit : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            SquareCheckbox(
              isChecked: subItem.isDone,
              onTap: onChanged,
              checkColor: appTheme.activeSubItemColor,
              borderColor: appTheme.blackColor,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subItem.name ?? '',
                    style: TextStyle(
                      decoration: subItem.isDone ? TextDecoration.lineThrough : null,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      Assets.icons.icTime.svg(width: 16, height: 16),
                      const SizedBox(width: 2),
                      Text(
                        subItem.due_date?.d_MMMM_comma_hhmm ?? LocaleKeys.noDue.tr(),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.black,
                        ),
                      ),
                      // const SizedBox(width: 8),
                      // Assets.icons.icShare.svg(width: 16, height: 16),
                      // const SizedBox(width: 2),
                      // const Text(
                      //   "0",
                      //   style: TextStyle(
                      //     fontSize: 12,
                      //     color: Colors.black,
                      //   ),
                      // ),
                      // const SizedBox(width: 8),
                      // Assets.icons.icGalery.svg(width: 16, height: 16),
                      // const SizedBox(width: 2),
                      // const Text(
                      //   "0",
                      //   style: TextStyle(
                      //     fontSize: 12,
                      //     color: Colors.black,
                      //   ),
                      // ),
                    ],
                  )
                ],
              ),
            ),
            const SizedBox(width: 8),
            subItem.isDone
                ? Assets.icons.icLock.svg(width: 24, height: 24)
                : PersonsView(
                    accounts: subItem.assignment ?? [],
                    spacing: -12,
                    avatarSize: 24,
                  )
          ],
        ),
      ),
    );
  }
}
