import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/screen/main/daily_tasks/state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:intl/intl.dart';

/// Utility functions for checklist grouping and filtering logic.
class ChecklistUtils {
  /// Groups tasks in checklists into due today and overdue categories.
  ///
  /// - [checklists]: The list of ListItem objects to process. Each ListItem should have its items populated.
  /// - [showCompletedTasks]:
  ///   - If false: Only includes checklists in each group (due today or overdue) if at least one task in the group is incomplete. Both completed and incomplete tasks with a matching due date are included in the group.
  ///   - If true: Only includes checklists where all relevant tasks are completed for each group:
  ///     - Due Today: All tasks with a due date of today are completed.
  ///     - Overdue: All tasks with a due date before today are completed.
  ///
  /// Returns a record with two maps:
  ///   - [dueToday]: Map of list UUID to ListItemData for checklists matching the due today rule.
  ///   - [overdue]: Map of list UUID to ListItemData for checklists matching the overdue rule.
  static ({Map<String, ListItemData> dueToday, Map<String, ListItemData> overdue}) groupTasksByDueDate(
    List<ListItem> checklists,
    bool showCompletedTasks,
  ) {
    final now = DateTime.now();
    final todayStr = DateFormat('yyyy-MM-dd').format(now);
    final Map<String, ListItemData> dueToday = {};
    final Map<String, ListItemData> overdue = {};

    for (final list in checklists) {
      final items = list.items ?? [];
      final filteredItems = items.where((item) => item.due_date != null && item.due_date!.isNotEmpty).toList();
      final todayTasks = <Item>[];
      final overdueTasks = <Item>[];
      for (final item in filteredItems) {
        try {
          final date = item.due_date!.toLocalDT;
          final itemDateStr = DateFormat('yyyy-MM-dd').format(date);
          if (itemDateStr == todayStr) {
            todayTasks.add(item);
          } else if (date.isBefore(DateTime(now.year, now.month, now.day))) {
            overdueTasks.add(item);
          }
        } catch (_) {
          // If parsing fails, exclude from both groups
        }
      }
      if (showCompletedTasks) {
        // Only include if ALL tasks in the group are completed
        if (todayTasks.isNotEmpty && todayTasks.every((item) => item.isDone)) {
          dueToday[list.uuid!] = ListItemData(listItem: list, items: todayTasks);
        }
        if (overdueTasks.isNotEmpty && overdueTasks.every((item) => item.isDone)) {
          overdue[list.uuid!] = ListItemData(listItem: list, items: overdueTasks);
        }
      } else {
        // Only include checklists with at least one incomplete task in the group
        if (todayTasks.isNotEmpty && todayTasks.any((item) => !item.isDone)) {
          dueToday[list.uuid!] = ListItemData(listItem: list, items: todayTasks);
        }
        if (overdueTasks.isNotEmpty && overdueTasks.any((item) => !item.isDone)) {
          overdue[list.uuid!] = ListItemData(listItem: list, items: overdueTasks);
        }
      }
    }
    return (dueToday: dueToday, overdue: overdue);
  }

  static Map<ListItem, List<Item>> getChecklistsWithTasksDueToday(List<ListItem> listItems, [DateTime? day]) {
    final now = day ?? DateTime.now();
    final todayStr = DateFormat('yyyy-MM-dd').format(now);
    final Map<ListItem, List<Item>> result = {};
    for (final listItem in listItems) {
      final items = listItem.items ?? [];
      final todayTasks = <Item>[];
      for (final item in items) {
        if (item.due_date == null || item.due_date!.isEmpty) continue;
        try {
          final date = item.due_date!.toLocalDT;
          final itemDateStr = DateFormat('yyyy-MM-dd').format(date);
          if (itemDateStr == todayStr) {
            todayTasks.add(item);
          }
        } catch (_) {
          // skip invalid date
        }
      }
      if (todayTasks.isNotEmpty) {
        result[listItem] = todayTasks;
      }
    }
    return result;
  }
}
