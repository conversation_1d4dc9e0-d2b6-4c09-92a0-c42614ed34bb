import 'package:auto_route/auto_route.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_bottomsheet_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/utils/snackbar.dart';
import 'package:family_app/widget/bottom_sheet/select_activity_bts.dart';
import 'package:family_app/widget/bottom_sheet/select_member_bts.dart';
import 'package:family_app/widget/check_item_view.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/list_horizontal_items.dart';
import 'package:family_app/widget/persons_view.dart';
import 'package:family_app/widget/square_checkbox.dart';
import 'package:family_app/widget/textfield/row_text_field_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'upsert_list_item_cubit.dart';
import 'upsert_list_item_state.dart';

@RoutePage()
class UpsertListItemPage extends BaseBlocProvider<UpsertListItemState, UpsertListItemCubit> {
  const UpsertListItemPage({super.key, this.parameter});

  final UpsertListItemParameter? parameter;

  @override
  Widget buildPage() => UpsertListItemView(parameter: parameter);

  @override
  UpsertListItemCubit createCubit() => UpsertListItemCubit(
        categoryRepository: locator.get(),
        familyRepository: locator.get(),
        accountService: locator.get(),
        listRepository: locator.get(),
        usecase: locator.get(),
        parameter: parameter,
        mainCubit: locator.get(),
        activityRepository: locator.get(),
      );
}

class UpsertListItemView extends StatefulWidget {
  const UpsertListItemView({super.key, this.parameter});

  final UpsertListItemParameter? parameter;

  @override
  State<UpsertListItemView> createState() => _UpsertListItemViewState();
}

class _UpsertListItemViewState
    extends BaseBlocBottomSheetPageState<UpsertListItemView, UpsertListItemState, UpsertListItemCubit> {
  @override
  bool get isBackIcon => true;

  @override
  String get title => LocaleKeys.new_checklist.tr();

  @override
  Color get backgroundColor => appTheme.backgroundWhite;

  @override
  bool get isResizeToAvoidBottomInset => true;

  @override
  bool listenWhen(UpsertListItemState previous, UpsertListItemState current) {
    if (current.listItem != null && previous.listItem == null) {
      context.maybePop(current.listItem);
      if(current.listItem?.uuid == null) {
        SnackBarUtils.showSucceed(
          context,
          title: LocaleKeys.new_list_is_added.tr(),
          content: LocaleKeys.new_list_is_added_msg.tr(),
        );
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, UpsertListItemCubit cubit, UpsertListItemState state) {
    return Container(
      padding: padding(horizontal: 16),
      height: 44,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Text(
                  LocaleKeys.cancel.tr(),
                  style: AppStyle.medium16(color: appTheme.primaryColorV2),
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: cubit.createList,
                child: Text(
                  cubit.parameter == null ? LocaleKeys.add.tr() : LocaleKeys.save.tr(),
                  style: AppStyle.medium16(color: appTheme.primaryColorV2),
                ),
              ),
            ],
          ),
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppStyle.medium16(color: appTheme.blackColor),
          )
        ],
      ),
    );
  }

  @override
  List<Widget>? appBarActions(UpsertListItemCubit cubit, UpsertListItemState state) {
    return null;
    // return [
    //   GestureDetector(
    //     onTap: cubit.createList,
    //     child: RoundItemView(
    //       text: LocaleKeys.save.tr(),
    //       textColor: appTheme.primaryColor,
    //     ),
    //   )
    // ];
  }

  @override
  Widget buildBody(BuildContext context, UpsertListItemCubit cubit, UpsertListItemState state) {
    return Column(
      // shrinkWrap: true,
      // padding: padding(),
      children: [
        RowTextFieldView(
          handler: cubit.name,
          formHandler: cubit.form,
          hintStyle: AppStyle.bold20(color: appTheme.hintColor),
          textStyle: AppStyle.bold20(),
        ),
        _buildAddItem(cubit, state),
        Divider(color: appTheme.hintColor, indent: 16, endIndent: 16),
        _buildListColor(cubit, state),
        _buildSelectionListType(cubit, state),
        _buildAssignees(context, cubit, state),
        _buildDivider(),
        _buildVisible(cubit, state),
        Padding(
          padding: padding(horizontal: 16, top: 4),
          child: Text(
            '${LocaleKeys.viewAndEditTodoMsg.tr()}:',
            style: AppStyle.regular12(color: appTheme.grayV2),
          ),
        ),
        _buildDivider(),
        _buildPoint(context, cubit, state),
        _buildDivider(),
        _buildActivity(cubit, state),



        // GestureDetector(
        //   onTap: () {
        //     DatePickerBottomSheet.show(
        //       context,
        //       initialDate: state.dateTime,
        //       title: LocaleKeys.due_date.tr(),
        //       onSelected: (date) => cubit.updateDateTime(date),
        //     );
        //   },
        //   behavior: HitTestBehavior.opaque,
        //   child: Container(
        //     padding: padding(horizontal: 16, bottom: 16, top: 15),
        //     color: appTheme.whiteText,
        //     child: Row(
        //       children: [
        //         Assets.icons.calendarOff.svg(width: 20, height: 20),
        //         const SizedBox(width: 7),
        //         Expanded(
        //           child: Text(
        //             state.dateTime != null ? state.dateTime?.until_time ?? '' : LocaleKeys.due_date.tr(),
        //             style: AppStyle.regular14(color: state.dateTime != null ? null : appTheme.hintColor),
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),
        // ),

        _buildDivider(),
        // _buildDivider(),
        // RowTextFieldView(handler: cubit.name, formHandler: cubit.form),
        // Container(
        //   color: appTheme.whiteText,
        //   padding: padding(horizontal: 16),
        //   child: Column(
        //     children: [
        //       _buildListColor(cubit, state),
        //       const SizedBox(height: 20),
        //       Divider(color: appTheme.hintColor),
        //       LineItemSelectionView(
        //         imagePath: Assets.images.folder.path,
        //         title: '${LocaleKeys.activity.tr()}:',
        //         content: state.activity?.name ?? LocaleKeys.none.tr(),
        //         onTap: (widget.parameter?.listItem?.activityId ?? '').isNotEmpty
        //             ? null
        //             : () {
        //                 SelectActivityBts.show(context, item: state.activity, onSelected: cubit.updateActivityModel);
        //               },
        //       ),
        //     ],
        //   ),
        // ),
        // _buildDivider(),
        // _buildDivider(),
      ],
    );
  }

  Widget _buildDivider() => const SizedBox(height: 12);

  Widget _buildPoint(BuildContext context, UpsertListItemCubit cubit, UpsertListItemState state){
    return Container(
      padding: padding(left: 16, right: 16),
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.point.tr(),
              style: AppStyle.regular14(color: appTheme.labelColor),
            ),
          ),
          SizedBox(
            width: 100,
            child: TextField(
              controller: cubit.pointEditingController,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              decoration: InputDecoration(
                hintStyle: AppStyle.regular14(color: appTheme.hintColor),
                hintText: "0",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: appTheme.borderColorV2, width: 1.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(color: appTheme.primaryColorV2, width: 2.0),
                ),
                contentPadding: const EdgeInsets.only(bottom: 12),
              ),
            ),
          )
        ],
      ),
    );
  }


  Widget _buildAssignees(BuildContext context, UpsertListItemCubit cubit, UpsertListItemState state){
    return InkWell(
      onTap: () => SelectMemberBts.show(
        context,
        isMultipleChoose: false,
        selectedMembers: state.assignees.map((e) => e.familyMemberUuid ?? '').toList(),
        onSelected: cubit.updateAssigneeSelected,
      ),
      child: Container(
        padding: padding(left: 16, right: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                LocaleKeys.assign_to.tr(),
                style: AppStyle.regular14(color: appTheme.labelColor),
              ),
            ),
            PersonsView(accounts: state.assignees),
            Assets.images.arrowRight.image(width: 20, height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildVisible(UpsertListItemCubit cubit, UpsertListItemState state) {
    return InkWell(
      onTap: () => SelectMemberBts.show(
        context,
        selectedMembers: state.accountList.map((e) => e.familyMemberUuid ?? '').toList(),
        onSelected: cubit.updateMemberSelected,
      ),
      child: Container(
        padding: padding(left: 16, right: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                LocaleKeys.visibleTo.tr(),
                style: AppStyle.regular14(color: appTheme.labelColor),
              ),
            ),
            PersonsView(accounts: state.accountList),
            Assets.images.arrowRight.image(width: 20, height: 20),
          ],
        ),
      ),
    );

    // return LineItemSelectionView(
    //   // imagePath: Assets.images.person.path,
    //   onTap: () => SelectMemberBts.show(
    //     context,
    //     selectedMembers: state.accountList.map((e) => e.familyMemberUuid ?? '').toList(),
    //     onSelected: cubit.updateMemberSelected,
    //   ),
    //   title: LocaleKeys.visibleTo.tr(),
    //   colorText: appTheme.labelColor,
    //   paddingHorizontal: 16,
    //   paddingVertical: 4,
    //   content: state.accountList.isEmpty
    //       ? '0'
    //       : state.accountList.length == accountService.memberInFamily.length
    //       ? LocaleKeys.everyone.tr()
    //       : '${state.accountList.length}',
    //   contentView: state.accountList.isNotEmpty
    //       ? SingleChildScrollView(
    //     scrollDirection: Axis.horizontal,
    //     child: Row(
    //       children: state.accountList
    //           .map(
    //             (e) => Padding(
    //           padding: padding(right: 8),
    //           child: CircleAvatarCustom(
    //             color: appTheme.whiteText,
    //             imageUrl: e.photoUrl ?? '',
    //             accountName: e.fullName ?? '',
    //             size: 28,
    //           ),
    //         ),
    //       )
    //           .toList(),
    //     ),
    //   )
    //       : null,
    // );
  }

  Widget _buildActivity(UpsertListItemCubit cubit, UpsertListItemState state) {
    return Container(
      color: appTheme.whiteText,
      padding: padding(horizontal: 16, top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.linkToActivity.tr(),
            style: AppStyle.regular14(color: appTheme.labelColor),
          ),
          Container(
            width: double.infinity,
            margin: padding(top: 8),
            padding: padding(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              color: appTheme.whiteText,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: appTheme.hintColor),
            ),
            child: DropdownSearch<ActivityModel>(
              mode: Mode.custom,
              items: (filter, props) =>  cubit.getAllActivities(name: filter),
              selectedItem: state.activity,
              compareFn: (c1, c2) => c1.uuid == c2.uuid,
              popupProps: PopupProps.menu(
                showSelectedItems: true,
                showSearchBox: true,
                searchFieldProps: TextFieldProps(
                  cursorColor: appTheme.primaryColor,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: appTheme.transparentWhiteColor,
                    hintText: LocaleKeys.searchByActivity.tr(),
                    hintStyle: AppStyle.regular14(color: appTheme.hintColor),
                    prefixIcon: Container(
                        width: 20,
                        height: 20,
                        alignment: Alignment.center,
                        child: Assets.icons.icSearch.svg(width: 20, height: 20)),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
                itemBuilder: (_, item, isDisabled, isSelected) {
                  return Container(
                    color: isSelected ? appTheme.dropdownSelectedColor : appTheme.whiteText,
                    padding: padding(top: 15, bottom: 14, horizontal: 16),
                    child: Row(
                      children: [
                        Expanded(child: Text(item.name ?? '', style:  AppStyle.regular14())),
                        const SizedBox(width: 12),
                        if (isSelected)
                          Icon(Icons.check, color: appTheme.primaryColorV2)
                      ],
                    ),
                  );
                },
              ),
              onChanged: (value) => {
                if(value != null)
                  cubit.updateActivityModel(value)
              },
              itemAsString: (item) => item.name ?? '',
              dropdownBuilder: (context, selectedItem) {
                return Text(
                  selectedItem?.name ?? LocaleKeys.selectAnActivity.tr(),
                  style: AppStyle.regular14(color: selectedItem == null ?  appTheme.hintColor : appTheme.blackColor),
                );
              },
            ),
          ),


          // Builder(
          //   builder: (context) {
          //     return InkWell(
          //       onTap: () {
          //         SelectActivityBts.show(context, item: state.activity, onSelected: cubit.updateActivityModel);
          //       },
          //       child: Container(
          //         margin: padding(top: 8),
          //         padding: padding(horizontal: 12, vertical: 12),
          //         decoration: BoxDecoration(
          //           color: appTheme.whiteText,
          //           borderRadius: BorderRadius.circular(12),
          //           border: Border.all(color: appTheme.hintColor.withOpacity(0.2)),
          //         ),
          //         child: Row(
          //           children: [
          //             Expanded(
          //               child: Text(
          //                 state.activity?.name ?? LocaleKeys.none.tr(),
          //                 style: AppStyle.regular14(color: appTheme.hintColor),
          //               ),
          //             ),
          //             SvgPicture.asset(Assets.images.arrowDown.path, width: 30, height: 30),
          //           ],
          //         ),
          //       ),
          //     );
          //   }
          // )
          // LineItemSelectionView(
          //   imagePath: Assets.images.folder.path,
          //   title: '${LocaleKeys.activity.tr()}:',
          //   content: state.activity?.name ?? LocaleKeys.none.tr(),
          //   onTap: (widget.parameter?.listItem?.activityId ?? '').isNotEmpty
          //       ? null
          //       : () {
          //     SelectActivityBts.show(context, item: state.activity, onSelected: cubit.updateActivityModel);
          //   },
          // ),
        ],
      ),
    );
  }

  Widget _buildSelectionListType(UpsertListItemCubit cubit, UpsertListItemState state) {
    // final type = widget.parameter?.type ?? ListType.Shopping;
    return Container(
      height: 56,
      width: MediaQuery.of(context).size.width,
      padding: padding(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '${LocaleKeys.listType.tr()}:',
              style: AppStyle.regular14(color: appTheme.labelColor),
            ),
          ),
          // if (widget.parameter?.type != null && widget.parameter?.listItem != null)
          //   Container(
          //     padding: padding(horizontal: 16, bottom: 16, top: 15),
          //     color: appTheme.whiteText,
          //     child: Text(
          //       type == ListType.Shopping ? LocaleKeys.shopping_list.tr() : LocaleKeys.todo_list.tr(),
          //       style: AppStyle.regular14(color: state.dateTime != null ? null : appTheme.hintColor),
          //     ),
          //   )
          // else
            DropdownButton<ListType>(
              value: state.type,
              underline: const SizedBox(),
              elevation: 3,
              // isExpanded: true,
              icon: SvgPicture.asset(Assets.images.arrowDown.path, width: 35, height: 35),
              alignment: Alignment.bottomCenter,
              dropdownColor: appTheme.whiteText,
              borderRadius: BorderRadius.circular(12),
              style: AppStyle.regular14(),
              // padding: padding(horizontal: 16, bottom: 4),
              items: [
                DropdownMenuItem(
                  onTap: () => cubit.onUpdateListType(ListType.Shopping),
                  value: ListType.Shopping,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(Assets.images.icTypeShopping.path, width: 35, height: 35),
                      const SizedBox(width: 8),
                      Text(LocaleKeys.shopping_list.tr(), style: AppStyle.regular14())
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: ListType.Todo,
                  onTap: () => cubit.onUpdateListType(ListType.Todo),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(Assets.images.icTypeTodos.path, width: 35, height: 35),
                      const SizedBox(width: 8),
                      Text(LocaleKeys.todo_list.tr(), style: AppStyle.regular14())
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: ListType.Other,
                  onTap: () => cubit.onUpdateListType(ListType.Other),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(Assets.images.icTypeOthers.path, width: 35, height: 35),
                      const SizedBox(width: 8),
                      Text(LocaleKeys.others.tr(), style: AppStyle.regular14())
                    ],
                  ),
                ),
              ],
              onChanged: (value) {},
            )
        ],
      ),
    );

    // if (widget.parameter?.type != null && widget.parameter?.listItem != null) {
    //   final type = widget.parameter?.type ?? ListType.Shopping;
    //   return Container(
    //     padding: padding(horizontal: 16, bottom: 16, top: 15),
    //     color: appTheme.whiteText,
    //     child: Text(
    //       type == ListType.Shopping ? LocaleKeys.shopping_list.tr() : LocaleKeys.todo_list.tr(),
    //       style: AppStyle.regular14(color: state.dateTime != null ? null : appTheme.hintColor),
    //     ),
    //   );
    // }
    // return ColoredBox(
    //   color: appTheme.whiteText,
    //   child: DropdownButton<ListType>(
    //     value: state.type,
    //     underline: const SizedBox(),
    //     elevation: 3,
    //     isExpanded: true,
    //     icon: Icon(Icons.keyboard_arrow_down, color: appTheme.blackColor),
    //     alignment: Alignment.bottomCenter,
    //     dropdownColor: appTheme.whiteText,
    //     borderRadius: BorderRadius.circular(12),
    //     style: AppStyle.regular14(),
    //     padding: padding(horizontal: 16, bottom: 4),
    //     items: [
    //       DropdownMenuItem(
    //         onTap: () => cubit.onUpdateListType(ListType.Shopping),
    //         value: ListType.Shopping,
    //         child: Text(LocaleKeys.shopping_list.tr(), style: AppStyle.regular14()),
    //       ),
    //       DropdownMenuItem(
    //         value: ListType.Todo,
    //         onTap: () => cubit.onUpdateListType(ListType.Todo),
    //         child: Text(LocaleKeys.todo_list.tr(), style: AppStyle.regular14()),
    //       ),
    //     ],
    //     onChanged: (value) {},
    //   ),
    // );
  }

  Widget _buildListColor(UpsertListItemCubit cubit, UpsertListItemState state) {
    final colors = [
      appTheme.typeCyanAccentColor,
      appTheme.typeOrangeColor,
      appTheme.typeGreenColor,
      appTheme.typeRedAccentColor,
      appTheme.typeRedColor,
      appTheme.typeRaditzColor,
      appTheme.typeNappaColor,
      appTheme.typeWhisColor,
      appTheme.typeFriezaColor,
      appTheme.typePiccoloColor,

    ];
    return Container(
      padding: padding(left: 16, right: 16, top: 16),
      child: ListHorizontalItems(
        items: colors,
        maxLength: 8,
        rightPadding: 16,
        itemBuilder: (index, item, width, isLastOverLength, remainLength) => GestureDetector(
          onTap: () => cubit.updateSelectedColor(item),
          behavior: HitTestBehavior.opaque,
          child: Container(
            width: width,
            height: width,
            decoration: BoxDecoration(
              color: item,
              shape: BoxShape.circle,
              border: state.selectedColor == item
                  ? Border.all(color: appTheme.blackColor.withValues(alpha: 0.56), width: 1)
                  : null,
            ),
            child: state.selectedColor == item
                ? Icon(Icons.check, color: appTheme.blackColor.withValues(alpha: 0.56), size: 18)
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildAddItem(UpsertListItemCubit cubit, UpsertListItemState state) {
    return ColoredBox(
      color: appTheme.whiteText,
      child: Padding(
        padding: padding(horizontal: 16, bottom: 16),
        child: Column(
          children: [
            if (state.items.isNotEmpty) ...[
              ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) => _buildSelectionItem(cubit, state, index),
                  separatorBuilder: (context, index) => const SizedBox(height: 14),
                  itemCount: state.items.length),
              const SizedBox(height: 17),
            ],
            GestureDetector(
              onTap: () => cubit.onAddNewCheckItem(),
              behavior: HitTestBehavior.opaque,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: appTheme.primaryColorV2.withOpacity(0.3)),
                  color: appTheme.primaryColorV2.withOpacity(0.05),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_circle_outline,
                      color: appTheme.primaryColorV2,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      LocaleKeys.add_item.tr(),
                      style: AppStyle.regular14(color: appTheme.primaryColorV2),
                    ),
                  ],
                ),
              ),
            ),
            // CheckItemView(
            //   checkItem: CheckListItem(
            //     name: LocaleKeys.add_item.tr(),
            //     isDone: false,
            //   ),
            //   updateStatus: () {
            //     print("CheckItemView 1111: ${state.items.length}");
            //     cubit.onAddNewCheckItem();
            //   },
            // )
            // Row(
            //   children: [
            //     PrimaryButton(
            //         onTap: cubit.onAddNewCheckItem,
            //         isFullWidth: false,
            //         backgroundColor: appTheme.primaryColor,
            //         buttonPadding: padding(top: 4, bottom: 7, horizontal: 13),
            //         textStyle: AppStyle.regular14(color: appTheme.whiteText),
            //         icon: Icon(Icons.add, color: appTheme.whiteText),
            //         text: LocaleKeys.add_item.tr())
            //   ],
            // )
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionItem(UpsertListItemCubit cubit, UpsertListItemState state, int index) {
    final item = state.items[index];
    final isCurrentItem = state.currentIndexEdit == index;
    return CheckItemView(
      checkItem: item,
      updateStatus: () => cubit.onUpdateStatusCheckItem(index),
      onDelete: () => cubit.onRemoveItem(index),
      textView: isCurrentItem
          ? TextField(
              controller: cubit.textEditingController,
              focusNode: cubit.textNode,
              style: AppStyle.regular14(),
              decoration: InputDecoration(
                  hintText: LocaleKeys.input.tr(),
                  hintStyle: AppStyle.regular14(color: appTheme.hintColor),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.only()))
          : null,
    );
  }
}
