import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/message_data.dart';

class UpsertListItemParameter {
  final ListType? type;
  final MessageData? messageData;
  final ListItem? listItem;
  final String activityId;

  UpsertListItemParameter({this.type, this.messageData, this.listItem, this.activityId = ''});
}
