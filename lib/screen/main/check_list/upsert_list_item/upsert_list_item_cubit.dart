import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/theme/app_theme_util.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/message_data.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/category/icategory_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';
import 'package:family_app/data/usecase/model/upsert_list_item_param.dart';
import 'package:family_app/data/usecase/upsert_list_item_usecase.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

import 'upsert_list_item_state.dart';

class UpsertListItemCubit extends BaseCubit<UpsertListItemState> {
  final AccountService accountService;
  final ICategoryRepository categoryRepository;
  final IFamilyRepository familyRepository;
  final IListRepository listRepository;
  final UpsertListItemUsecase usecase;
  final UpsertListItemParameter? parameter;
  final HomeCubit mainCubit;
  final IActivityRepository activityRepository;

  UpsertListItemCubit({
    required this.categoryRepository,
    required this.accountService,
    required this.familyRepository,
    required this.listRepository,
    required this.usecase,
    required this.mainCubit,
    required this.activityRepository,
    this.parameter,
  }) : super(UpsertListItemState(type: parameter?.type ?? ListType.Shopping));

  late final TextFieldHandler name;
  late final FormTextFieldHandler form;

  final TextEditingController textEditingController = TextEditingController();
  final TextEditingController pointEditingController = TextEditingController();
  final textNode = FocusNode();

  @override
  void onInit() async {
    super.onInit();
    name = TextFieldHandler(
      field: 'name',
      isRequired: true,
      hintText: LocaleKeys.untitled_checklist.tr(),
      initializeText:
          parameter?.messageData?.name ?? parameter?.listItem?.name ?? '',
    );
    form = FormTextFieldHandler(
        handlers: [name], validateForm: (map) => createList());
    textNode.addListener(() {
      if (!textNode.hasFocus && state.currentIndexEdit != -1) {
        final items = [...state.items];
        if (textEditingController.text.isEmpty) {
          items.removeAt(state.currentIndexEdit);
          emit(state.copyWith(items: items, currentIndexEdit: -1));
        } else {
          items[state.currentIndexEdit].name = textEditingController.text;
          emit(state.copyWith(items: items, currentIndexEdit: -1));
        }
      }
    });
    if ((parameter?.listItem?.uuid ?? '').isNotEmpty) {
      listRepository.getAllItemLogInList(parameter?.listItem?.uuid ?? '');
    }

    // state.selectedColor?.text ?? '',
    Color initListColor = parameter?.listItem?.color?.toColor ??
        AppThemeUtil().selectionColor().first;

    // logd("initListColor: $initListColor");

    var items =
        parameter?.messageData?.items?.map((e) => e.checkListItem).toList() ??
            parameter?.listItem?.items?.map((e) => e.checkListItem).toList();
    DateTime? dateTime = parameter?.messageData?.fromDate?.toLocalDT ??
        parameter?.listItem?.planDate?.toDateTime();

    List<Account> assignees = [];
    if (parameter?.listItem?.assignment != null) {
      assignees = parameter?.listItem?.assignment ?? [];
    } else {
      Account? my = accountService.memberInFamily.value.firstWhereOrNull(
        (element) => element.familyMemberUuid == accountService.account?.uuid,
      );
      if (my != null) {
        assignees.add(my);
      }
    }
    pointEditingController.text = parameter?.listItem?.point.toString() ?? '0';
    emit(state.copyWith(
      activity: parameter?.listItem?.activity,
      items: items,
      dateTime: dateTime,
      selectedColor: initListColor,
      accountList: parameter?.listItem?.includedMembers ??
          accountService.memberInFamily.value,
      assignees: assignees,
    ));

    if ((parameter?.activityId ?? '').isEmpty) return;
    showLoading();
    try {
      final activityModels =
          await activityRepository.getActivityById(parameter?.activityId ?? '');
      emit(state.copyWith(activity: activityModels));
    } catch (e) {
      print(e);
    } finally {
      dismissLoading();
    }
  }

  void onUpdateListType(ListType type) {
    emit(state.copyWith(type: type));
  }

  void updateMemberSelected(List<Account> members) {
    emit(state.copyWith(accountList: members));
  }

  void updateAssigneeSelected(List<Account> members) {
    emit(state.copyWith(assignees: members));
  }

  void onAddNewCheckItem() {
    final items = [...state.items];
    if (textNode.hasFocus && state.currentIndexEdit < items.length) {
      if (textEditingController.text.isEmpty) return;
      items[state.currentIndexEdit].name = textEditingController.text;
      textNode.unfocus();
    }
    items.add(CheckListItem());
    emit(state.copyWith(items: items, currentIndexEdit: items.length - 1));
    textEditingController.text = '';
    textNode.requestFocus();
  }

  void onUpdateStatusCheckItem(int index) {
    final items = [...state.items];
    items[index].isDone = !(items[index].isDone ?? false);
    emit(state.copyWith(
      items: items,
      updateCount: state.updateCount + 1,
    ));
  }

  void onRemoveItem(int index) {
    final items = [...state.items];
    items.removeAt(index);
    emit(state.copyWith(items: items));
  }

  void updateSelectedColor(Color color) {
    emit(state.copyWith(selectedColor: color));
  }

  void updateDateTime(DateTime newDateTime) {
    emit(state.copyWith(dateTime: newDateTime));
  }

  void updateActivityModel(ActivityModel model) {
    emit(state.copyWith(activity: model));
  }

  Future<void> createList() async {
    textNode.unfocus();
    name.validateField();

    if (!name.isValid) {
      // if (state.dateTime == null) {
      //   showSimpleToast(LocaleKeys.error_please_select_date_time.tr());
      // }
      return;
    }
    var items = [...state.items];
    if (textEditingController.text.isNotEmpty && state.currentIndexEdit != -1) {
      items[state.currentIndexEdit].name = textEditingController.text;
    }
    var now = DateTime.now();
    if (parameter?.listItem == null) {
      items = items.map((e) {
        e.dueDate ??= now.toUtc().toIso8601String();
        return e;
      }).toList();
    }
    emit(state.copyWith(items: items));

    showLoading();
    try {
      UpsertListItemParam param = UpsertListItemParam(
        uuid: parameter?.listItem?.uuid,
        name: name.text,
        // planDate: state.dateTime?.toUtc().toIso8601String() ?? '',
        color: state.selectedColor?.text ?? '',
        description: name.text,
        assignment:
            state.assignees.map((e) => e.familyMemberUuid ?? '').toList(),
        includedMembers:
            state.accountList.map((e) => e.familyMemberUuid ?? '').toList(),
        familyId: accountService.familyId,
        oldItems: parameter?.listItem?.items?.map((e) => e.param).toList(),
        items: state.items.map((e) => e.param).toList(),
        type: state.type,
        point: int.tryParse(pointEditingController.text) ?? 0,
        activityId: state.activity?.uuid,
      );
      // print("call usecase with uuid: ${param.uuid} param: $param");

      //the result is supposed to be a LIST object,
      final ListItem result = await usecase.call(param);

      // call API in mainCubit to update the LISTs
      await mainCubit.onRefresh();
      emit(state.copyWith(listItem: result));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    }
    dismissLoading();
  }

  Future<List<ActivityModel>> getAllActivities(
      {int page = 1, int limit = LIMIT, String? name}) async {
    try {
      final result = await activityRepository
          .getAllActivities(accountService.familyId, name: name);
      return result;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> close() {
    textNode.dispose();
    textEditingController.dispose();
    name.dispose();
    return super.close();
  }
}
