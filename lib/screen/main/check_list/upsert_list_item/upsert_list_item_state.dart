// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/check_list_item.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:flutter/material.dart';

class UpsertListItemState extends BaseState {
  final ListType type;
  final DateTime? dateTime;
  final Color? selectedColor;
  final List<CheckListItem> items;
  final int currentIndexEdit;
  final int updateCount;
  final List<Account> assignees;
  final List<Account> accountList;
  final ListItem? listItem;
  final ActivityModel? activity;

  UpsertListItemState({
    this.type = ListType.Shopping,
    this.dateTime,
    this.selectedColor,
    this.currentIndexEdit = -1,
    this.updateCount = 0,
    this.items = const [],
    this.assignees = const [],
    this.accountList = const [],
    this.listItem,
    this.activity,
  });

  @override
  List<Object?> get props => [
        type,
        dateTime,
        selectedColor,
        items,
        assignees,
        updateCount,
        currentIndexEdit,
        listItem,
        accountList,
        activity,
      ];

  UpsertListItemState copyWith({
    ListType? type,
    DateTime? dateTime,
    Color? selectedColor,
    List<CheckListItem>? items,
    List<Account>? assignees,
    int? currentIndexEdit,
    int? updateCount,
    List<Account>? accountList,
    ListItem? listItem,
    ActivityModel? activity,
  }) {
    return UpsertListItemState(
      type: type ?? this.type,
      dateTime: dateTime ?? this.dateTime,
      selectedColor: selectedColor ?? this.selectedColor,
      items: items ?? this.items,
      assignees: assignees ?? this.assignees,
      updateCount: updateCount ?? this.updateCount,
      currentIndexEdit: currentIndexEdit ?? this.currentIndexEdit,
      accountList: accountList ?? this.accountList,
      listItem: listItem ?? this.listItem,
      activity: activity ?? this.activity,
    );
  }
}
