import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:flutter/material.dart';

class UpsertSubItemState extends BaseState {
  final String title;
  final List<Account> assignees;
  final DateTime? dueDate;
  final TimeOfDay? time;
  final Timezone? timezone;
  final int? reminder;
  final String? photo;
  final MemoryModel? memory;
  final MemoryModel? removeMemory;
  final String? note;
  final bool isSetDateAndReminder;
  final bool isSetPhoto;
  final bool isSetNote;
  final bool updated;
  final Item? item;



  UpsertSubItemState({
    this.item,
    this.title = '',
    this.assignees = const [],
    this.dueDate,
    this.time,
    this.timezone,
    this.reminder,
    this.memory,
    this.removeMemory,
    this.photo,
    this.note,
    this.isSetDateAndReminder = false,
    this.isSetPhoto = false,
    this.isSetNote = false,
    this.updated = false,
  });

  @override
  List<Object?> get props => [
        item,
        title,
        assignees,
        dueDate,
        time,
        timezone,
        reminder,
        photo,
        memory,
        removeMemory,
        note,
        isSetDateAndReminder,
        isSetPhoto,
        isSetNote,
        updated,
      ];

  UpsertSubItemState copyWith({
    Item? item,
    String? title,
    List<Account>? assignees,
    DateTime? dueDate,
    TimeOfDay? time,
    Timezone? timezone,
    int? reminder,
    String? photo,
    MemoryModel? memory,
    String? note,
    bool? updated,
    bool? isSetDateAndReminder,
    bool? isSetPhoto,
    bool? isSetNote,
  }) {
    return UpsertSubItemState(
      item: item ?? this.item,
      title: title ?? this.title,
      assignees: assignees ?? this.assignees,
      dueDate: dueDate ?? this.dueDate,
      time: time ?? this.time,
      timezone: timezone ?? this.timezone,
      reminder: reminder ?? this.reminder,
      photo: photo ?? this.photo,
      memory: memory ?? this.memory,
      note: note ?? this.note,
      isSetDateAndReminder: isSetDateAndReminder ?? this.isSetDateAndReminder,
      isSetPhoto: isSetPhoto ?? this.isSetPhoto,
      isSetNote: isSetNote ?? this.isSetNote,
      updated: updated ?? this.updated,
    );
  }

  UpsertSubItemState clearPhoto() => UpsertSubItemState(
        title: title,
        assignees: assignees,
        dueDate: dueDate,
        time: time,
        timezone: timezone,
        reminder: reminder,
        photo: null,
        memory: null,
        removeMemory: memory ?? removeMemory,
        note: note ,
        isSetDateAndReminder: isSetDateAndReminder,
        isSetPhoto: isSetPhoto,
        isSetNote: isSetNote,
        updated: updated,
      );


}