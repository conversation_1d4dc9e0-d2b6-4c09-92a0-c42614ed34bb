import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_bottomsheet_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/event/widget/timezone_select.dart';
import 'package:family_app/widget/bottom_sheet/select_member_bts.dart';
import 'package:family_app/widget/button.dart';
import 'package:family_app/widget/persons_view.dart';
import 'package:family_app/widget/popup/popup.dart';
import 'package:family_app/widget/textfield/row_text_field_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';

import 'upsert_sub_item_cubit.dart';
import 'upsert_sub_item_parameter.dart';
import 'upsert_sub_item_state.dart';

class UpsertSubItemPage extends BaseBlocProvider<UpsertSubItemState, UpsertSubItemCubit> {
  const UpsertSubItemPage({super.key, this.parameter});

  final UpsertSubItemParameter? parameter;

  @override
  Widget buildPage() => UpsertSubItemView(parameter: parameter);

  @override
  UpsertSubItemCubit createCubit() => UpsertSubItemCubit(
        parameter: parameter,
        listRepository: locator.get(),
        familyRepository: locator.get(),
        usecase: locator.get(),
      );
}

class UpsertSubItemView extends StatefulWidget {
  final UpsertSubItemParameter? parameter;

  const UpsertSubItemView({super.key, this.parameter});

  @override
  State<UpsertSubItemView> createState() => _UpsertSubItemViewState();
}

class _UpsertSubItemViewState
    extends BaseBlocBottomSheetPageState<UpsertSubItemView, UpsertSubItemState, UpsertSubItemCubit> {
  @override
  bool get isBackIcon => true;

  @override
  String get title => widget.parameter?.item != null ? LocaleKeys.editItem.tr() : LocaleKeys.new_item.tr();

  @override
  bool get isResizeToAvoidBottomInset => true;



  @override
  bool listenWhen(UpsertSubItemState previous, UpsertSubItemState current) {
    if (current.updated && !previous.updated) {
      context.maybePop(current.item);
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Container(
      padding: padding(horizontal: 16),
      height: 44,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Text(
                  LocaleKeys.cancel.tr(),
                  style: AppStyle.medium16(color: appTheme.primaryColorV2),
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: cubit.createSubItem,
                child: Text(
                  cubit.parameter == null ? LocaleKeys.add.tr() : LocaleKeys.save.tr(),
                  style: AppStyle.medium16(color: appTheme.primaryColorV2),
                ),
              ),
            ],
          ),
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppStyle.medium16(color: appTheme.blackColor),
          )
        ],
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.title.tr(),
            style: AppStyle.regular14(color: appTheme.labelColor),
          ),
          RowTextFieldView(
            handler: cubit.name,
            formHandler: cubit.form,
            hintStyle: AppStyle.bold20(color: appTheme.hintColor),
            textStyle: AppStyle.bold20(),
            bottomSizedBox: 0,
            topPadding: 0,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: appTheme.borderColor),
            backgroundColor: backgroundColor,
          ),
          const SizedBox(height: 16),
          _buildAssign(cubit, state),
          const SizedBox(height: 16),
          _buildPoint(context, cubit, state),
          const SizedBox(height: 16),
          _buildDateAndReminder(context, cubit, state),
          const SizedBox(height: 16),
          _buildPhotoSet(context, cubit, state),
          const SizedBox(height: 16),
          _buildNoteSet(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildAssign(UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return InkWell(
      onTap: () => SelectMemberBts.show(
        context,
        isMultipleChoose: false,
        selectedMembers: state.assignees.map((e) => e.familyMemberUuid ?? '').toList(),
        onSelected: cubit.updateMemberSelected,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.assign_to.tr(),
              style: AppStyle.regular14(color: appTheme.labelColor),
            ),
          ),
          PersonsView(accounts: state.assignees),
          Assets.images.arrowRight.image(width: 20, height: 20),
        ],
      ),
    );
  }

  Widget _buildPoint(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state){
    return Row(
      children: [
        Expanded(
          child: Text(
            LocaleKeys.point.tr(),
            style: AppStyle.regular14(color: appTheme.labelColor),
          ),
        ),
        SizedBox(
          width: 100,
          child: TextField(
            controller: cubit.pointEditingController,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            decoration: InputDecoration(
              hintStyle: AppStyle.regular14(color: appTheme.hintColor),
              hintText: "0",
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(color: appTheme.borderColorV2, width: 1.0),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(color: appTheme.primaryColorV2, width: 2.0),
              ),
              contentPadding: const EdgeInsets.only(bottom: 12),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildDateAndReminder(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Column(
      children: [
        InkWell(
          onTap: () => cubit.onSetDateAndReminder(),
          child: Row(
            children: [
              Icon(state.isSetDateAndReminder ? Icons.remove : Icons.add, color: appTheme.primaryColorV2, size: 14),
              const SizedBox(width: 4),
              Text(
                LocaleKeys.set_date_and_reminder.tr(),
                style: AppStyle.regular14(color: appTheme.primaryColorV2),
              ),
            ],
          ),
        ),
        if (state.isSetDateAndReminder) ...[
          const SizedBox(height: 16),
          _buildSelectDate(context, cubit, state),
          const SizedBox(height: 16),
          _buildSelectTime(context, cubit, state),
          const SizedBox(height: 16),
          _buildTimeZone(context, cubit, state),
          const SizedBox(height: 16),
          _buildReminder(context, cubit, state),
        ]
      ],
    );
  }

  Widget _buildSelectDate(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Container(
      padding: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.date.tr(),
              style: AppStyle.regular14(color: appTheme.labelColor),
            ),
          ),
          InkWell(
            onTap: () => cubit.onSelectDate(context),
            child: Text(
              state.dueDate != null ? DateFormat('dd/MM/yyyy').format(state.dueDate!) : 'dd / mm / yyyy',
              style: TextStyle(
                fontWeight: FontWeight.normal,
                color: state.dueDate != null ? Colors.black : appTheme.grayV2,
              ),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => cubit.onSelectDate(context),
            child: SvgPicture.asset(
              Assets.icons.icCalendar32.path,
              width: 24.w,
              height: 24.h,
              colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
            ),
          ),
        ],
      ),
    );
  }

  _buildSelectTime(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Container(
      padding: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.time.tr(),
              style: AppStyle.regular14(color: appTheme.labelColor),
            ),
          ),
          InkWell(
            onTap: () => cubit.onSelectTime(context),
            child: Text(
              state.time != null
                  ? DateFormat('HH:mm').format(DateTime(0, 0, 0, state.time!.hour, state.time!.minute))
                  : 'HH:mm',
              style: TextStyle(
                fontWeight: FontWeight.normal,
                color: state.time != null ? Colors.black : appTheme.grayV2,
              ),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () => cubit.onSelectTime(context),
            child: SvgPicture.asset(
              Assets.icons.icClock32.path,
              width: 24.w,
              height: 24.h,
              colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
            ),
          ),
        ],
      ),
    );
  }

  _buildTimeZone(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Container(
      padding: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.timezone.tr(),
              style: AppStyle.regular14(color: appTheme.labelColor),
            ),
          ),
          const SizedBox(width: 8),
          TimezoneSelect(
            timezone: state.timezone,
            onChanged: cubit.handleTimezone,
          ),
        ],
      ),
    );
  }

  _buildReminder(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Container(
      padding: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.reminder.tr(),
              style: AppStyle.regular14(color: appTheme.labelColor),
            ),
          ),
          _dropdownBuilder(setupReminderMinutes, state.reminder ?? setupReminderMinutes.first, cubit.onChangeReminder,
              (e) {
            if (e == 0) {
              return LocaleKeys.none.tr();
            } else if (e <= 60) {
              return "$e ${LocaleKeys.minutes.tr()}";
            } else {
              return "${e ~/ 60} ${LocaleKeys.hours.tr()}";
            }
          }),
        ],
      ),
    );
  }

  Widget _dropdownBuilder<T>(Iterable<T> data, T value, ValueChanged<T> onChanged, String Function(T) textBuilder) {
    return CustomPopup(
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: data
              .map((e) => GestureDetector(
                  onTap: () {
                    context.maybePop();
                    onChanged(e);
                  },
                  child: Padding(padding: paddingV2(all: 8), child: Text(textBuilder(e)))))
              .toList(),
        ),
      ),
      child: Row(
        children: [
          Text(
            textBuilder(value),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(width: 8),
          SvgPicture.asset(
            Assets.icons.icVerticalDoubleChevron.path,
            width: 24.w,
            height: 24.h,
            colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
          )
        ],
      ),
    );
  }

  Widget _buildPhotoSet(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    buildTitlePhotoSet(context, cubit, state) {
      return Row(
        children: [
          Icon(state.isSetPhoto ? Icons.remove : Icons.add, color: appTheme.primaryColorV2, size: 14),
          const SizedBox(width: 4),
          Text(
            LocaleKeys.add_photo.tr(),
            style: AppStyle.regular14(color: appTheme.primaryColorV2),
          ),
        ],
      );
    }

    return Column(
      children: [
        if (state.photo == null && state.memory == null)
          PopupMenuButton<ImageSource>(
            icon: buildTitlePhotoSet(context, cubit, state),
            onSelected: (ImageSource value) => cubit.onPickPhoto(value),
            padding: EdgeInsets.zero,
            itemBuilder: (ctx) => _pickImageMenuBuild(ctx),
          )
        else
          InkWell(
            onTap: () => cubit.onExpandPhotoSet(),
            child: buildTitlePhotoSet(context, cubit, state),
          ),
        if (state.isSetPhoto && (state.photo != null || state.memory != null)) ...[
          const SizedBox(height: 16),
          _buildPhoto(context, cubit, state),
        ]
      ],
    );
  }

  _pickImageMenuBuild(BuildContext context) => <PopupMenuEntry<ImageSource>>[
        PopupMenuItem<ImageSource>(
          value: ImageSource.camera,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Assets.icons.icCamera32.svg(),
              const SizedBox(width: 8),
              Text(LocaleKeys.camera.tr()),
            ],
          ),
        ),
        PopupMenuItem<ImageSource>(
          value: ImageSource.gallery,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Assets.icons.icPicture32.svg(),
              const SizedBox(width: 8),
              Text(LocaleKeys.album.tr()),
            ],
          ),
        ),
      ];

  _buildPhoto(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(24.h2),
          child: SizedBox(
            width: 120.w,
            height: 96.h,
            child: state.photo != null
                ? Image.file(
                    File(state.photo!),
                    fit: BoxFit.cover,
                  )
                : state.memory != null
                    ? CachedNetworkImage(
                        imageUrl: state.memory!.files?.first.fileUrlSm ?? '',
                        fit: BoxFit.cover,
                        placeholder: (context, url) => const Center(child: CircularProgressIndicator()),
                        errorWidget: (context, url, error) => const Icon(Icons.error),
                      )
                    : Container(),
          ),
        ),
        PopupMenuButton<ImageSource>(
          icon: Container(
            padding: paddingV2(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: appTheme.primaryColorV2.withValues(alpha: 0.12),
              borderRadius: BorderRadius.circular(24.h2),
            ),
            child: Text(
              LocaleKeys.change.tr(),
              style: AppStyle.medium12(color: appTheme.primaryColorV2),
            ),
          ),
          onSelected: (ImageSource value) => cubit.onPickPhoto(value),
          itemBuilder: (ctx) => _pickImageMenuBuild(ctx),
        ),
        Button(
          color: appTheme.errorV2.withValues(alpha: 0.12),
          padding: paddingV2(horizontal: 12, vertical: 4),
          borderRadius: BorderRadius.circular(24.h2),
          onTap: () => cubit.onDeletePhoto(),
          child: Text(
            LocaleKeys.delete.tr(),
            style: AppStyle.medium12(color: appTheme.errorV2),
          ),
        ),
      ],
    );
  }

  _buildNoteSet(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return Column(
      children: [
        InkWell(
          onTap: () => cubit.onExpandNotSet(),
          child: Row(
            children: [
              Icon(state.isSetNote ? Icons.remove : Icons.add, color: appTheme.primaryColorV2, size: 14),
              const SizedBox(width: 4),
              Text(
                LocaleKeys.add_note.tr(),
                style: AppStyle.regular14(color: appTheme.primaryColorV2),
              ),
            ],
          ),
        ),
        if (state.isSetNote) ...[const SizedBox(height: 16), _buildNote(context, cubit, state)]
      ],
    );
  }

  _buildNote(BuildContext context, UpsertSubItemCubit cubit, UpsertSubItemState state) {
    return InkWell(
      onTap: () => cubit.focusNote.requestFocus(),
      child: Container(
        padding: padding(horizontal: 16),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: appTheme.borderColor),
        ),
        constraints: const BoxConstraints(
          minHeight: 132,
        ),
        child: TextField(
          controller: cubit.noteTextEditingController,
          focusNode: cubit.focusNote,
          maxLines: null, // allows multiple lines
          decoration: InputDecoration(
            hintText: LocaleKeys.note.tr(),
            hintStyle: AppStyle.regular14(color: appTheme.hintColor),
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}
