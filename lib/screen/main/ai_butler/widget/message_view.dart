import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/ai_butler/ai_butler_cubit.dart';
import 'package:family_app/screen/main/ai_butler/model/message_model.dart';
import 'package:family_app/screen/main/ai_butler/widget/event_message_view.dart';
import 'package:family_app/screen/main/ai_butler/widget/list_message_view.dart';
import 'package:family_app/screen/main/ai_butler/widget/trip_message_view.dart';
import 'package:family_app/utils/extension/string_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:family_app/widget/custom_border_button.dart';
import 'package:family_app/widget/gradient_button.dart';
import 'package:family_app/widget/image/custom_image.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/user_border_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class MessageView extends StatelessWidget {
  const MessageView({
    super.key,
    required this.model,
    required this.cubit,
    required this.messageIndex,
  });

  final MessageModel model;
  final int messageIndex;
  final AiButlerCubit cubit;

  @override
  Widget build(BuildContext context) {
    final messageText = model.isGenerateType ? model.message.command ?? '' : model.content.clearSpace;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: model.isAI ? MainAxisAlignment.start : MainAxisAlignment.end,
      children: [
        Opacity(
          opacity: model.isAI ? 1 : 0,
          child: Padding(
            padding: padding(right: 10),
            child: Assets.images.ai.image(
              width: 30,
              height: 30,
            ),
          ),
        ),
        Flexible(
          child: Column(
            crossAxisAlignment: model.isAI ? CrossAxisAlignment.start : CrossAxisAlignment.end,
            children: [
              if (model.message.data == null)
                if (model.message.imageFile != null || (model.message.file ?? '').isNotEmpty) ...[
                  CustomImage(
                    imageUrl: model.message.file?.startsWith('https') == true ? model.message.file ?? '' : '',
                    imageFile: model.message.imageFile,
                    size: 64,
                  ),
                  const SizedBox(height: 8),
                ],
              if (messageText.isNotEmpty)
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Positioned(
                      left: model.isAI ? -6 : null,
                      right: model.isAI ? null : -6,
                      top: 0,
                      child: ClipPath(
                        clipper: _MessageClipper(model.isAI),
                        child: Container(
                          width: 15,
                          height: 15,
                          color: model.isAI ? appTheme.whiteText : appTheme.primaryColor,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onDoubleTap: () {
                        Clipboard.setData(ClipboardData(text: messageText));
                        showSimpleToast(LocaleKeys.copied_successfully_text.tr());
                      },
                      onLongPress: () {
                        Clipboard.setData(ClipboardData(text: messageText));
                        showSimpleToast(LocaleKeys.copied_successfully_text.tr());
                      },
                      child: Container(
                        padding: padding(top: 6, horizontal: 11, bottom: 14),
                        decoration: BoxDecoration(
                          color: model.isAI ? appTheme.whiteText : appTheme.primaryColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          messageText,
                          style: AppStyle.regular14(color: model.isAI ? null : appTheme.whiteText),
                        ),
                      ),
                    ),
                  ],
                ),
              if (model.isFirstMessage && cubit.state.messages.length == 1) ...[
                const SizedBox(height: 14),
                Row(
                  children: [
                    GradientButton(
                      firstColor: appTheme.firstTripColor,
                      secondColor: appTheme.secondTripColor,
                      text: LocaleKeys.trip_text.tr(),
                      onTap: () => cubit.onSendMessageByTemplate(SocketEvent.TRIP),
                    ),
                    const SizedBox(width: 10),
                    GradientButton(
                      firstColor: appTheme.firstListColor,
                      secondColor: appTheme.secondListColor,
                      text: LocaleKeys.list.tr(),
                      onTap: () => cubit.onSendMessageByTemplate(SocketEvent.LIST),
                    ),
                    const SizedBox(width: 10),
                    GradientButton(
                      firstColor: appTheme.firstEventColor,
                      secondColor: appTheme.secondEventColor,
                      text: LocaleKeys.event_text.tr(),
                      onTap: () => cubit.onSendMessageByTemplate(SocketEvent.EVENT),
                    ),
                  ],
                ),
              ],
              _buildMessageData(),
            ],
          ),
        ),
        Opacity(
          opacity: model.isAI ? 0 : 1,
          child: Padding(
            padding: padding(left: 10),
            child: Assets.images.avatar.image(width: 30, height: 30),
          ),
        ),
      ],
    );
  }

  Widget _buildMessageData() {
    if (model.message.data == null) {
      return const SizedBox();
    }
    Widget view = const SizedBox();
    final type = model.message.data?.type;
    if (type == MessageType.TRIP) {
      view = TripMessageView(model: model, cubit: cubit, messageIndex: messageIndex);
    } else if (type == MessageType.LIST) {
      view = ListMessageView(model: model, cubit: cubit, messageIndex: messageIndex);
    } else if (type == MessageType.EVENT) {
      view = EventMessageView(model: model, cubit: cubit, messageIndex: messageIndex);
    }

    return view;
  }

  Widget _buildConfirmWidget() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Positioned(
          left: model.isAI ? -6 : null,
          right: model.isAI ? null : -6,
          top: 0,
          child: ClipPath(
            clipper: _MessageClipper(model.isAI),
            child: Container(
              width: 15,
              height: 15,
              color: model.isAI ? appTheme.whiteText : appTheme.primaryColor,
            ),
          ),
        ),
        Container(
          padding: padding(top: 6, horizontal: 11, bottom: 14),
          decoration: BoxDecoration(
            color: model.isAI ? appTheme.whiteText : appTheme.primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Text(
                model.content,
                style: AppStyle.regular14(color: model.isAI ? null : appTheme.whiteText),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomBorderButton(
                    buttonText: 'Wait',
                    radius: 25,
                    width: 74.w,
                    buttonPadding: padding(top: 6, bottom: 9),
                    onPressed: () {},
                  ),
                  SizedBox(width: 12.w),
                  PrimaryButton(
                    text: 'OK',
                    isFullWidth: false,
                    buttonPadding: padding(top: 6, bottom: 9, horizontal: 27),
                    onTap: () {},
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Widget _buildShoppingWidget() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Container(
  //         decoration: BoxDecoration(
  //           borderRadius: BorderRadius.circular(8),
  //           color: appTheme.red87Color,
  //           boxShadow: [
  //             const BoxShadow(color: Color(0x15000000), offset: Offset(0, 2), blurRadius: 4.0),
  //           ],
  //         ),
  //         child: Column(
  //           children: [
  //             Container(
  //               padding: padding(horizontal: 12, vertical: 4),
  //               decoration: BoxDecoration(
  //                 borderRadius: BorderRadius.circular(8),
  //                 color: appTheme.blueColor,
  //               ),
  //               child: Row(
  //                 children: [
  //                   Text('Sam’s until Jul 12', style: AppStyle.normal14(color: appTheme.whiteText)),
  //                   const Spacer(),
  //                   ImageAssetCustom(imagePath: Assets.images.shopping.path, width: 16.w, height: 16.w),
  //                   SizedBox(width: 8.w),
  //                   Text('Shopping List', style: AppStyle.regular12(color: appTheme.whiteText)),
  //                 ],
  //               ),
  //             ),
  //             Container(
  //               padding: padding(vertical: 12),
  //               decoration: BoxDecoration(
  //                 borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
  //                 color: appTheme.blueFFColor,
  //               ),
  //               child: Column(
  //                 children: [
  //                   Padding(
  //                     padding: padding(horizontal: 12),
  //                     child: Row(
  //                       children: [
  //                         const CircleCheckbox(isChecked: true),
  //                         SizedBox(width: 8.w),
  //                         Text('Running', style: AppStyle.regular12()),
  //                       ],
  //                     ),
  //                   ),
  //                   Padding(
  //                     padding: padding(vertical: 12),
  //                     child: const LineWidget(),
  //                   ),
  //                   Padding(
  //                     padding: padding(horizontal: 12),
  //                     child: Row(
  //                       children: [
  //                         ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w, height: 16.w),
  //                         SizedBox(width: 9.w),
  //                         UserBorderWidget(name: 'Shery', color: appTheme.red3CColor),
  //                         SizedBox(width: 13.w),
  //                         UserBorderWidget(name: 'Jack', color: appTheme.red3CColor),
  //                       ],
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //       SizedBox(height: 12.h),
  //       Container(
  //         padding: padding(vertical: 6, horizontal: 17),
  //         decoration: BoxDecoration(
  //           borderRadius: BorderRadius.circular(50),
  //           border: Border.all(width: 1.w, color: appTheme.hintColor),
  //           color: appTheme.whiteText,
  //         ),
  //         child: Text('Add a List or Event For this trip', style: AppStyle.medium14(color: appTheme.primaryColor)),
  //       ),
  //     ],
  //   );
  // }

  Widget _buildNotificationWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: appTheme.red87Color,
            boxShadow: [
              const BoxShadow(color: Color(0x15000000), offset: Offset(0, 2), blurRadius: 4.0),
            ],
          ),
          child: Column(
            children: [
              Container(
                padding: padding(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: appTheme.green65Color,
                ),
                child: Row(
                  children: [
                    Text('Running', style: AppStyle.normal14(color: appTheme.whiteText)),
                    const Spacer(),
                    ImageAssetCustom(imagePath: Assets.images.notification.path, width: 16.w, height: 16.w),
                  ],
                ),
              ),
              Container(
                padding: padding(all: 12),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
                  color: appTheme.greenF7Color,
                ),
                child: Column(
                  children: [
                    Text(
                      'Running describe Mytrip to paris describe describe',
                      style: AppStyle.regular12(color: appTheme.labelColor),
                    ),
                    SizedBox(height: 12.w),
                    Row(
                      children: [
                        ImageAssetCustom(imagePath: Assets.images.calendar.path, width: 16.w, height: 16.w),
                        SizedBox(width: 9.w),
                        Text('Jul 24 2024 ~ Jul 30 2024', style: AppStyle.regular12()),
                      ],
                    ),
                    SizedBox(height: 13.w),
                    Row(
                      children: [
                        ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w, height: 16.w),
                        SizedBox(width: 9.w),
                        UserBorderWidget(name: 'Shery', color: appTheme.red3CColor),
                        SizedBox(width: 13.w),
                        UserBorderWidget(name: 'Jack', color: appTheme.red3CColor),
                      ],
                    ),
                    SizedBox(height: 12.h),
                    Padding(
                      padding: padding(left: 27),
                      child: Row(
                        children: [
                          const CircleCheckbox(isChecked: true),
                          SizedBox(width: 8.w),
                          Text('Public', style: AppStyle.regular12()),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: padding(vertical: 6, horizontal: 17),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            border: Border.all(width: 1.w, color: appTheme.hintColor),
            color: appTheme.whiteText,
          ),
          child: Text('Edit this schedule', style: AppStyle.medium14(color: appTheme.primaryColor)),
        ),
      ],
    );
  }
}

class _MessageClipper extends CustomClipper<Path> {
  final bool isAI;

  _MessageClipper(this.isAI);

  @override
  Path getClip(Size size) {
    final path = Path();
    if (isAI) {
      path
        ..moveTo(0, 0)
        ..lineTo(size.width, 0)
        ..lineTo(size.width, size.height)
        ..moveTo(0, 0)
        ..close();
    } else {
      path
        ..moveTo(size.width, 0)
        ..lineTo(0, 0)
        ..lineTo(0, size.height)
        ..lineTo(size.width, 0)
        ..close();
    }
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}
