import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class MessageButtonView extends StatelessWidget {
  const MessageButtonView({super.key, this.message = '', this.highlightText = false, this.onTap});

  final String message;
  final bool highlightText;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: padding(vertical: 6, horizontal: 17),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50),
          border: Border.all(width: 1.w, color: appTheme.hintColor),
          color: appTheme.whiteText,
        ),
        child: Text(message, style: AppStyle.medium14(color: highlightText ? appTheme.primaryColor : null)),
      ),
    );
  }
}
