import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/ai_butler/ai_butler_cubit.dart';
import 'package:family_app/screen/main/ai_butler/model/message_model.dart';
import 'package:family_app/screen/main/ai_butler/widget/data_message_view.dart';
import 'package:family_app/screen/main/ai_butler/widget/message_button_view.dart';
import 'package:family_app/screen/main/check_list/upsert_list_item/upsert_list_item_parameter.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/user_border_widget.dart';
import 'package:flutter/material.dart';

class TripMessageView extends StatelessWidget {
  const TripMessageView({super.key, required this.model, required this.cubit, this.messageIndex = -1});

  final MessageModel model;
  final int messageIndex;
  final AiButlerCubit cubit;

  @override
  Widget build(BuildContext context) {
    final data = model.message.data;
    final activity = model.activityModels;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DataMessageView(
          cardColor: activity?.color?.toColor,
          titleView: Row(
            children: [
              Expanded(
                child: Text(
                  activity?.name ?? data?.name ?? '',
                  style: AppStyle.normal14(color: appTheme.whiteText),
                ),
              ),
              ImageAssetCustom(imagePath: Assets.images.trip.path, width: 16.w, height: 16.w),
              SizedBox(width: 8.w),
              Text(LocaleKeys.trip_text.tr(), style: AppStyle.regular12(color: appTheme.whiteText)),
            ],
          ),
          contentView: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity?.description ?? data?.description ?? '',
                style: AppStyle.regular12(color: appTheme.labelColor),
              ),
              SizedBox(height: 12.w),
              Row(
                children: [
                  ImageAssetCustom(imagePath: Assets.images.calendar.path, width: 16.w, height: 16.w),
                  SizedBox(width: 9.w),
                  Text(
                    '${(activity?.fromDate ?? data?.fromDate)?.MMMDDyyyyTime ?? ''} ~ ${(activity?.toDate ?? data?.toDate)?.MMMDDyyyyTime ?? ''}',
                    style: AppStyle.regular12(),
                  ),
                ],
              ),
              SizedBox(height: 13.w),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w, height: 16.w),
                    SizedBox(width: 9.w),
                    if (activity != null)
                      ...(activity.includedMembers ?? <Account>[])
                          .map((e) => Padding(
                              padding: padding(right: 13),
                              child: UserBorderWidget(name: e.fullName ?? '', color: appTheme.primaryColor)))
                          .toList()
                    else
                      ...data?.members
                              ?.map((e) => Padding(
                                  padding: padding(right: 13),
                                  child: UserBorderWidget(name: e.name ?? '', color: appTheme.primaryColor)))
                              .toList() ??
                          [],
                  ],
                ),
              ),
            ],
          ),
        ),
        // Container(
        //   decoration: BoxDecoration(
        //     borderRadius: BorderRadius.circular(8),
        //     color: (activity?.color?.toColor ?? appTheme.primaryColor).withOpacity(.05),
        //     // boxShadow: [
        //     //   const BoxShadow(color: Color(0x15000000), offset: Offset(0, 2), blurRadius: 4.0),
        //     // ],
        //   ),
        //   child: Column(
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [
        //       Container(
        //         padding: padding(horizontal: 12, vertical: 4),
        //         decoration: BoxDecoration(
        //           borderRadius: BorderRadius.circular(8),
        //           color: activity?.color?.toColor ?? appTheme.primaryColor,
        //         ),
        //         child: Row(
        //           children: [
        //             Expanded(
        //                 child: Text(activity?.name ?? data?.name ?? '',
        //                     style: AppStyle.normal14(color: appTheme.whiteText))),
        //             ImageAssetCustom(imagePath: Assets.images.trip.path, width: 16.w, height: 16.w),
        //             SizedBox(width: 8.w),
        //             Text(LocaleKeys.trip_text.tr(), style: AppStyle.regular12(color: appTheme.whiteText)),
        //           ],
        //         ),
        //       ),
        //       Padding(
        //           padding: padding(all: 12),
        //           child: Column(
        //             crossAxisAlignment: CrossAxisAlignment.start,
        //             children: [
        //               Text(
        //                 activity?.description ?? data?.description ?? '',
        //                 style: AppStyle.regular12(color: appTheme.labelColor),
        //               ),
        //               SizedBox(height: 12.w),
        //               Row(
        //                 children: [
        //                   ImageAssetCustom(imagePath: Assets.images.calendar.path, width: 16.w, height: 16.w),
        //                   SizedBox(width: 9.w),
        //                   Text(
        //                       '${(activity?.fromDate ?? data?.fromDate)?.MMMDDyyyyTime ?? ''} ~ ${(activity?.toDate ?? data?.toDate)?.MMMDDyyyyTime ?? ''}',
        //                       style: AppStyle.regular12()),
        //                 ],
        //               ),
        //               SizedBox(height: 13.w),
        //               SingleChildScrollView(
        //                 scrollDirection: Axis.horizontal,
        //                 child: Row(
        //                   crossAxisAlignment: CrossAxisAlignment.start,
        //                   children: [
        //                     ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w, height: 16.w),
        //                     SizedBox(width: 9.w),
        //                     if (activity != null)
        //                       ...(activity.includedMembers ?? <Account>[])
        //                           .map((e) => Padding(
        //                               padding: padding(right: 13),
        //                               child: UserBorderWidget(name: e.fullName ?? '', color: appTheme.primaryColor)))
        //                           .toList()
        //                     else
        //                       ...data?.members
        //                               ?.map((e) => Padding(
        //                                   padding: padding(right: 13),
        //                                   child: UserBorderWidget(name: e.name ?? '', color: appTheme.primaryColor)))
        //                               .toList() ??
        //                           [],
        //                   ],
        //                 ),
        //               ),
        //             ],
        //           )),
        //     ],
        //   ),
        // ),
        if (!model.isHideButton) ...[
          SizedBox(height: 12.h),
          MessageButtonView(
            message: LocaleKeys.add_list_to_trip.tr(),
            highlightText: true,
            onTap: () async {
              // final result = await context.pushRoute(
              //   UpsertActivityRoute(parameter: UpsertActivityParameter(message: model.message)),
              // );
              // if (result is ActivityModels) {
              //   cubit.updateActivityInMessage(messageIndex, result);
              // }
              if ((model.message.data?.uuid ?? '').isEmpty) {
                cubit.sendCommandMessageByTemplate(messageIndex, LocaleKeys.add_list_to_trip.tr());
              } else {
                DialogUtils.showCupertinoModal(context, [
                  CupertinoActionModel(
                      title: LocaleKeys.event_text.tr(),
                      onTap: () => context.pushRoute(UpsertEventRoute(
                          upsertEventParameter: UpsertEventParameter(activityId: model.message.data?.uuid ?? '')))),
                  CupertinoActionModel(
                    title: LocaleKeys.list.tr(),
                    onTap: () => context.pushRoute(UpsertListItemRoute(
                        parameter: UpsertListItemParameter(activityId: model.message.data?.uuid ?? ''))),
                  ),
                ]);
              }
            },
          ),
          SizedBox(height: 12.h),
          MessageButtonView(
            // onTap: () => cubit.createActivityWithMessageBody(messageIndex, model),
            onTap: () => cubit.hideMessageButton(messageIndex),
            message: LocaleKeys.continue_complete_trip.tr(),
          )
        ],
      ],
    );
  }
}
