import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/ai_butler/ai_butler_cubit.dart';
import 'package:family_app/screen/main/ai_butler/model/message_model.dart';
import 'package:family_app/screen/main/ai_butler/widget/data_message_view.dart';
import 'package:family_app/screen/main/ai_butler/widget/message_button_view.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/user_border_widget.dart';
import 'package:flutter/material.dart';

class EventMessageView extends StatelessWidget {
  const EventMessageView({super.key, required this.model, required this.cubit, this.messageIndex = -1});

  final MessageModel model;
  final int messageIndex;
  final AiButlerCubit cubit;

  @override
  Widget build(BuildContext context) {
    final data = model.message.data;
    final event = model.eventModels;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DataMessageView(
          cardColor: event?.color?.toColor,
          titleView: Row(
            children: [
              Expanded(child: Text(data?.name ?? '', style: AppStyle.normal14(color: appTheme.whiteText))),
              ImageAssetCustom(imagePath: Assets.images.notification.path, width: 16.w, height: 16.w),
            ],
          ),
          contentView: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(data?.description ?? '', style: AppStyle.regular12(color: appTheme.labelColor)),
              SizedBox(height: 12.w),
              Row(children: [
                ImageAssetCustom(imagePath: Assets.images.calendar.path, width: 16.w, height: 16.w),
                SizedBox(width: 9.w),
                Text(
                  '${data?.fromDate?.MMMDDyyyyTime ?? ''} ~ ${data?.toDate?.MMMDDyyyyTime ?? ''}',
                  style: AppStyle.regular12(),
                ),
              ]),
              SizedBox(height: 13.w),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w, height: 16.w),
                    SizedBox(width: 9.w),
                    ...data?.members
                            ?.map((e) => Padding(
                                padding: padding(right: 13),
                                child: UserBorderWidget(name: e.name ?? '', color: appTheme.primaryColor)))
                            .toList() ??
                        []
                  ],
                ),
              ),
              // SizedBox(height: 12.h),
              // Padding(
              //   padding: padding(left: 27),
              //   child: Row(
              //     children: [
              //       const CircleCheckbox(isChecked: true),
              //       SizedBox(width: 8.w),
              //       Text('Public', style: AppStyle.regular12()),
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
        // Container(
        //   decoration: BoxDecoration(
        //     borderRadius: BorderRadius.circular(8),
        //     color: (event?.color?.toColor ?? appTheme.primaryColor).withOpacity(.05),
        //     boxShadow: [
        //       const BoxShadow(color: Color(0x15000000), offset: Offset(0, 2), blurRadius: 4.0),
        //     ],
        //   ),
        //   child: Column(
        //     children: [
        //       Container(
        //         padding: padding(horizontal: 12, vertical: 4),
        //         decoration: BoxDecoration(
        //           borderRadius: BorderRadius.circular(8),
        //           color: event?.color?.toColor ?? appTheme.primaryColor,
        //         ),
        //         child: Row(
        //           children: [
        //             Expanded(child: Text(data?.name ?? '', style: AppStyle.normal14(color: appTheme.whiteText))),
        //             ImageAssetCustom(imagePath: Assets.images.notification.path, width: 16.w, height: 16.w),
        //           ],
        //         ),
        //       ),
        //       Padding(
        //         padding: padding(all: 12),
        //         child: Column(
        //           crossAxisAlignment: CrossAxisAlignment.start,
        //           children: [
        //             Text(data?.description ?? '', style: AppStyle.regular12(color: appTheme.labelColor)),
        //             SizedBox(height: 12.w),
        //             Row(children: [
        //               ImageAssetCustom(imagePath: Assets.images.calendar.path, width: 16.w, height: 16.w),
        //               SizedBox(width: 9.w),
        //               Text('${data?.fromDate?.MMMDDyyyyTime ?? ''} ~ ${data?.toDate?.MMMDDyyyyTime ?? ''}',
        //                   style: AppStyle.regular12())
        //             ]),
        //             SizedBox(height: 13.w),
        //             SingleChildScrollView(
        //                 scrollDirection: Axis.horizontal,
        //                 child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        //                   ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w, height: 16.w),
        //                   SizedBox(width: 9.w),
        //                   ...data?.members
        //                           ?.map((e) => Padding(
        //                               padding: padding(right: 13),
        //                               child: UserBorderWidget(name: e.name ?? '', color: appTheme.primaryColor)))
        //                           .toList() ??
        //                       []
        //                 ])),
        //             // SizedBox(height: 12.h),
        //             // Padding(
        //             //   padding: padding(left: 27),
        //             //   child: Row(
        //             //     children: [
        //             //       const CircleCheckbox(isChecked: true),
        //             //       SizedBox(width: 8.w),
        //             //       Text('Public', style: AppStyle.regular12()),
        //             //     ],
        //             //   ),
        //             // ),
        //           ],
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
        if (!model.isHideButton) ...[
          SizedBox(height: 12.h),
          MessageButtonView(
            message: LocaleKeys.i_want_to_update_some_detail_for_this_event.tr(),
            highlightText: true,
            onTap: () async {
              // final result = await context.pushRoute(
              //   UpsertEventRoute(upsertEventParameter: UpsertEventParameter(messageData: data)),
              // );
              // if (result != null && result is EventModels) {
              //   cubit.updateEventItem(messageIndex, result);
              // }
              cubit.sendCommandMessageByTemplate(
                  messageIndex, LocaleKeys.i_want_to_update_some_detail_for_this_event.tr());
            },
          ),
          SizedBox(height: 12.h),
          MessageButtonView(
            message: LocaleKeys.i_want_to_create_another_event.tr(),
            // onTap: () => cubit.createEventWithMessageBody(messageIndex, model),
            onTap: () =>
                cubit.sendCommandMessageByTemplate(messageIndex, LocaleKeys.i_want_to_create_another_event.tr()),
          ),
        ]
      ],
    );
  }
}
