import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/message_data.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/ai_butler/ai_butler_cubit.dart';
import 'package:family_app/screen/main/ai_butler/model/message_model.dart';
import 'package:family_app/screen/main/ai_butler/widget/data_message_view.dart';
import 'package:family_app/screen/main/ai_butler/widget/message_button_view.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/circle_checkbox.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/line_widget.dart';
import 'package:family_app/widget/list_vertical_item.dart';
import 'package:family_app/widget/user_border_widget.dart';
import 'package:flutter/material.dart';

class ListMessageView extends StatelessWidget {
  const ListMessageView({super.key, required this.model, required this.cubit, this.messageIndex = -1});

  final MessageModel model;
  final int messageIndex;
  final AiButlerCubit cubit;

  @override
  Widget build(BuildContext context) {
    final accountService = locator.get<AccountService>();
    final data = model.message.data;
    final listItem = model.listItem;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DataMessageView(
          cardColor: listItem?.color?.toColor,
          titleView: Row(
            children: [
              Expanded(
                child: Text(
                  '${accountService.myActiveFamily.value?.familyName ?? ''} ${LocaleKeys.until_time.tr(
                    args: [data?.toDate?.toLocalDT.shortDay ?? ''],
                  )}',
                  style: AppStyle.normal14(color: appTheme.whiteText),
                ),
              ),
              ImageAssetCustom(imagePath: Assets.images.shopping.path, width: 16.w, height: 16.w),
              SizedBox(width: 8.w),
              Text(data?.name ?? '', style: AppStyle.regular12(color: appTheme.whiteText)),
            ],
          ),
          contentView: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListVerticalItem(
                lineItemCount: 2,
                physics: const NeverScrollableScrollPhysics(),
                items: data?.items ?? <ItemMessage>[],
                itemBuilder: (index, item) => Row(
                  children: [
                    CircleCheckbox(isChecked: item.isDone),
                    SizedBox(width: 8.w),
                    Flexible(
                      child: Text(item.name ?? '', style: AppStyle.regular12()),
                    ),
                  ],
                ),
              ),
              Padding(padding: padding(vertical: 12), child: const LineWidget()),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ImageAssetCustom(imagePath: Assets.images.person.path, width: 16.w, height: 16.w),
                    SizedBox(width: 9.w),
                    ...data?.members
                            ?.map((e) => Padding(
                                padding: padding(right: 13),
                                child: UserBorderWidget(name: e.name ?? '', color: appTheme.primaryColor)))
                            .toList() ??
                        [],
                  ],
                ),
              ),
            ],
          ),
        ),
        if (!model.isHideButton) ...[
          SizedBox(height: 12.h),
          MessageButtonView(
            message: LocaleKeys.i_want_to_add_more_item_to_this_list.tr(),
            highlightText: true,
            onTap: () async {
              // final result = await context
              //     .pushRoute(UpsertListItemRoute(parameter: UpsertListItemParameter(messageData: model.message.data)));
              // if (result is ListItem) {
              //   cubit.updateListItem(messageIndex, result);
              // }
              cubit.sendCommandMessageByTemplate(messageIndex, LocaleKeys.i_want_to_add_more_item_to_this_list.tr());
            },
          ),
          SizedBox(height: 12.h),
          MessageButtonView(
            message: LocaleKeys.add_new_list.tr(),
            onTap: () => cubit.sendCommandMessageByTemplate(messageIndex, LocaleKeys.i_want_to_create_new_list.tr()),
            // onTap: () => cubit.createListWithMessageBody(messageIndex, model),
          )
        ],
      ],
    );
  }
}
