import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class DataMessageView extends StatelessWidget {
  const DataMessageView({super.key, this.cardColor, this.titleView, this.contentView});

  final Color? cardColor;
  final Widget? titleView;
  final Widget? contentView;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: (cardColor ?? appTheme.primaryColor).withOpacity(.05),
            // boxShadow: [
            //   const BoxShadow(color: Color(0x15000000), offset: Offset(0, 2), blurRadius: 4.0),
            // ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  padding: padding(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: cardColor ?? appTheme.primaryColor,
                  ),
                  child: titleView ?? const SizedBox()),
              Padding(padding: padding(all: 12), child: contentView ?? const SizedBox()),
            ],
          ),
        ),
      ],
    );
  }
}
