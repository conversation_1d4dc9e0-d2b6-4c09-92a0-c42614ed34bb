// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/socket/base_socket_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/message.dart';
import 'package:family_app/data/usecase/activity_usecase.dart';
import 'package:family_app/data/usecase/event_usecase.dart';
import 'package:family_app/data/usecase/upsert_list_item_usecase.dart';
import 'package:family_app/screen/main/ai_butler/ai_butler_state.dart';
import 'package:family_app/screen/main/ai_butler/model/message_model.dart';
import 'package:family_app/screen/main/ai_butler/model/record_controller.dart';
import 'package:family_app/utils/file_util.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:flutter/material.dart';

class AiButlerCubit extends BaseCubit<AiButlerState> {
  final BaseSocketService socketService;
  final ActivityUsecase activityUsecase;
  final EventUsecase eventUsecase;
  final UpsertListItemUsecase upsertListItemUsecase;

  AiButlerCubit({
    required this.socketService,
    required this.activityUsecase,
    required this.eventUsecase,
    required this.upsertListItemUsecase,
  }) : super(AiButlerState());

  final textEditCtrl = TextEditingController();
  final recordController = RecordController();

  late StreamSubscription<List<Message>> _subscription;

  @override
  Future<void> close() {
    textEditCtrl.dispose();
    recordController.dispose();
    _subscription.cancel();
    socketService.onDisconnect();
    return super.close();
  }

  @override
  void onInit() async {
    super.onInit();
    showLoading();
    try {
      await socketService.onConnect();
      socketService.onStartListen();
      final messages = socketService.messageStream.value
          .asMap()
          .entries
          .map((e) => e.value.messageModel.copyWith(isFirstMessage: e.key == 0))
          .toList();
      emit(state.copyWith(messages: messages));
      _subscription = socketService.messageStream.stream.listen((event) {
        if (isClose) return;
        List<MessageModel> messages = <MessageModel>[];
        if (event.isEmpty) {
          return;
        }
        final lastModel = event.last.messageModel;

        for (var i = 0; i < event.length; i++) {
          MessageModel model = event[i].messageModel.copyWith(isFirstMessage: i == 0);
          if (model.message.data != null && lastModel.isAI) {
            if ((i == event.length - 1 || i == event.length - 2) && model.isAI) {
              model = model.copyWith(isHideButton: false);
            }
          }
          messages.add(model);
        }
        // final messages =
        //     event.asMap().entries.map((e) => e.value.messageModel.copyWith(isFirstMessage: e.key == 0)).toList();
        emit(state.copyWith(messages: messages, isLoading: !messages.last.isAI));
      });
    } catch (e) {}
    dismissLoading();
  }

  void startRecord() {
    recordController.startRecord();
  }

  void onSendMessage() async {
    emit(state.copyWith(isLoading: true));
    // if (recordController.startRecordCtrl.value) {
    //   final messages = [...state.messages];
    //   final duration = recordController.timeRecord.value;
    //   final path = await recordController.stopRecord();
    //   final model =
    //       MessageModel(isAI: false, content: duration.toString(), itemType: MessageItemType.record, recordPath: path);
    //   messages.add(model);
    //   emit(state.copyWith(messages: messages));
    // } else {
    //   final text = textEditCtrl.text;
    //   final messages = [...state.messages];
    //   messages.add(MessageModel(isAI: false, content: text));
    //   emit(state.copyWith(messages: messages));
    // }
    // emit(state.copyWith(isLoading: false));
    // socketService.onSendMessage(textEditCtrl.text);

    File? imageFile = null;
    if (state.files.isNotEmpty) {
      imageFile = state.files.first;
    }
    if (textEditCtrl.text.isEmpty && imageFile == null) {
      showSimpleToast('Please enter message');
      return;
    }
    sendMessageToSocket(text: textEditCtrl.text, imageFile: imageFile);
    textEditCtrl.text = '';
    deleteFile();
  }

  void sendCommandMessageByTemplate(int messageIndex, String text) {
    sendMessageToSocket(text: text);
    hideMessageButton(messageIndex);
  }

  void hideMessageButton(int messageIndex) {
    final list = [...state.messages];
    list[messageIndex] = list[messageIndex].copyWith(isHideButton: true);
    emit(state.copyWith(messages: list));
  }

  void onSendMessageByTemplate(String command) {
    sendMessageToSocket(command: command);
    textEditCtrl.text = '';
  }

  void sendMessageToSocket({String text = '', String command = '', File? imageFile}) {
    try {
      socketService.onSendMessage(text, command: command, imageFile: imageFile);
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
      emit(state.copyWith(isLoading: true));
      _subscription.cancel();
      onInit();
    }
  }

  void onExportConversationFile() async {
    try {
      final message = [...state.messages];
      final list = message.map((e) => e.toReviewFile()).toList();
      final todayConversation = 'conversation_${DateTime.now().toLocal().toIso8601String()}.json';
      final filePath = await FileUtil.getFilePath(todayConversation);
      final newFile = File(filePath);
      await newFile.writeAsString(list.toString());
      showSimpleToast('Export successfully');
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    }
  }

  void updateFile(List<File> file) {
    emit(state.copyWith(files: file));
  }

  void deleteFile() {
    emit(state.copyWith(files: []));
  }

  void createActivityWithMessageBody(int index, MessageModel model) async {
    showLoading();
    try {
      final result = await activityUsecase.call(model.message.createActivityParameter);
      final listMess = [...state.messages];
      listMess[index] = model.copyWith(activityModels: result);
      emit(state.copyWith(messages: listMess));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    }
    dismissLoading();
  }

  void updateActivityInMessage(int index, ActivityModel? model) {
    final listMess = [...state.messages];
    listMess[index] = listMess[index].copyWith(activityModels: model);
    emit(state.copyWith(messages: listMess));
  }

  void createListWithMessageBody(int index, MessageModel model) async {
    showLoading();
    try {
      final result = await upsertListItemUsecase.call(model.message.upsertListItemParam);
      final listMess = [...state.messages];
      listMess[index] = model.copyWith(listItem: result);
      emit(state.copyWith(messages: listMess));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    }
    dismissLoading();
  }

  void updateListItem(int index, ListItem? listItem) {
    final listMess = [...state.messages];
    listMess[index] = listMess[index].copyWith(listItem: listItem);
    emit(state.copyWith(messages: listMess));
  }

  void createEventWithMessageBody(int index, MessageModel model) async {
    showLoading();
    try {
      final result = await eventUsecase.call(model.message.eventParameter);
      final listMess = [...state.messages];
      listMess[index] = model.copyWith(eventModels: result);
      emit(state.copyWith(messages: listMess));
    } catch (e) {
      showSimpleToast(LocaleKeys.action_fail.tr());
    }
    dismissLoading();
  }

  void updateEventItem(int index, EventModels? eventModels) {
    final listMess = [...state.messages];
    listMess[index] = listMess[index].copyWith(eventModels: eventModels);
    emit(state.copyWith(messages: listMess));
  }
}
