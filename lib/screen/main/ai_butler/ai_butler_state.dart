// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/screen/main/ai_butler/model/message_model.dart';

class AiButlerState extends BaseState {
  final List<MessageModel> messages;
  final List<File> files;
  final bool isDisconnect;

  AiButlerState({
    this.messages = const [],
    super.isLoading,
    this.files = const [],
    this.isDisconnect = false,
  });

  AiButlerState copyWith({
    List<MessageModel>? messages,
    bool? isLoading,
    List<File>? files,
    bool? isDisconnect,
  }) {
    return AiButlerState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      files: files ?? this.files,
      isDisconnect: isDisconnect ?? this.isDisconnect,
    );
  }

  @override
  List<Object?> get props => [messages, files, isLoading, isDisconnect];
}
