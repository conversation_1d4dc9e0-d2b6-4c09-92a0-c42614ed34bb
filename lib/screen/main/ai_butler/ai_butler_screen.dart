import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/ai_butler/widget/message_view.dart';
import 'package:family_app/utils/extension/int_ext.dart';
import 'package:family_app/widget/image/image_picker_handler.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';

import 'ai_butler_cubit.dart';
import 'ai_butler_state.dart';

@RoutePage()
class AiButlerPage extends BaseBlocProvider<AiButlerState, AiButlerCubit> {
  const AiButlerPage({super.key});

  @override
  Widget buildPage() => const AiButlerView();

  @override
  AiButlerCubit createCubit() => AiButlerCubit(
        socketService: locator.get(),
        activityUsecase: locator.get(),
        eventUsecase: locator.get(),
        upsertListItemUsecase: locator.get(),
      );
}

class AiButlerView extends StatefulWidget {
  const AiButlerView({super.key});

  @override
  State<AiButlerView> createState() => _AiButlerViewState();
}

class _AiButlerViewState extends BaseBlocPageState<AiButlerView, AiButlerState, AiButlerCubit> {
  @override
  String get title => 'AI butler';

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  Color get backgroundColor => appTheme.backgroundChat;

  @override
  bool get isSafeArea => false;

  final scrollController = ScrollController();

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  List<Widget>? appBarActions(AiButlerCubit cubit, AiButlerState state) {
    return [
      if (isDebug)
        IconButton(
          icon: const Icon(Icons.data_exploration),
          onPressed: cubit.onExportConversationFile,
        )
    ];
  }

  @override
  bool listenWhen(AiButlerState previous, AiButlerState current) {
    if (previous.messages.length != current.messages.length &&
        mounted &&
        scrollController.position.maxScrollExtent != 0) {
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        scrollController.animateTo(scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300), curve: Curves.linear);
      });
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, AiButlerCubit cubit, AiButlerState state) {
    return Column(
      children: [
        Expanded(
          child: BaseStreamBuilder(
            controller: cubit.recordController.isRecording,
            builder: (isRecording) => Stack(
              children: [
                Positioned.fill(
                  child: ListView.separated(
                    controller: scrollController,
                    itemCount: state.messages.length,
                    itemBuilder: (context, index) => MessageView(
                      messageIndex: index,
                      model: state.messages[index],
                      cubit: cubit,
                    ),
                    padding: padding(horizontal: 15, vertical: 12),
                    separatorBuilder: (context, index) => const SizedBox(height: 16),
                  ),
                ),
                if (isRecording) ...[
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Opacity(
                      opacity: .49,
                      child: Container(
                        height: 192,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [appTheme.firstRecordColor.withOpacity(0), appTheme.secondRecordColor],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 115,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: padding(horizontal: 16, top: 16, bottom: 22),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage(Assets.images.backgroundRecord.path),
                              fit: BoxFit.fill,
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(children: [
                                Assets.images.recordFirstLine.image(width: 66, height: 28),
                                const SizedBox(width: 2),
                                Assets.images.recordSecondLine.image(width: 74, height: 22)
                              ]),
                              const SizedBox(height: 12),
                              BaseStreamBuilder(
                                controller: cubit.recordController.timeRecord,
                                builder: (seconds) => Text(
                                  seconds.recordTime,
                                  style: AppStyle.medium14(color: appTheme.whiteText),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
      ],
    );
  }

  OutlineInputBorder get _border => OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      );

  @override
  Widget buildBottomView(BuildContext context, AiButlerCubit cubit, AiButlerState state) {
    return TwoBaseStreamBuilder(
      firstController: cubit.recordController.startRecordCtrl,
      secondController: cubit.recordController.isRecording,
      builder: (startRecord, isRecording) => Container(
        padding: padding(vertical: 10, horizontal: 14),
        color: appTheme.whiteText,
        child: SafeArea(
          top: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              state.files.isNotEmpty
                  ? Stack(
                      children: [
                        Image.file(state.files.first, width: 100.w, height: 100.w, fit: BoxFit.cover),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: InkWell(
                            onTap: cubit.deleteFile,
                            borderRadius: BorderRadius.circular(1000),
                            child: Container(
                              padding: padding(all: 4),
                              decoration: BoxDecoration(shape: BoxShape.circle, color: appTheme.grayColor),
                              child: Icon(Icons.clear, color: appTheme.whiteText, size: 16),
                            ),
                          ),
                        ),
                      ],
                    )
                  : const SizedBox(),
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      ImagePickerHandler.showCupertinoImages(
                        context,
                        getOneImage: true,
                        getImages: cubit.updateFile,
                      );
                    },
                    child: Icon(Icons.image, size: 32, color: appTheme.primaryColor),
                  ),
                  SizedBox(width: 10.w),
                  GestureDetector(
                    onTap: () {
                      if (startRecord) {
                        cubit.recordController.stopRecord();
                      } else {
                        cubit.recordController.startRecord();
                      }
                    },
                    child: ImageAssetCustom(
                      imagePath: startRecord ? Assets.images.keyboard.path : Assets.images.record.path,
                      size: 32,
                    ),
                  ),
                  SizedBox(width: 10.w),
                  if (startRecord) ...[
                    Expanded(
                      child: GestureDetector(
                        onLongPressStart: (details) {
                          cubit.recordController.isHoldToSpeak(true);
                        },
                        onLongPressEnd: (details) {
                          cubit.recordController.isHoldToSpeak(false);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                          padding: padding(top: 10, bottom: 13),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: isRecording ? appTheme.fadePrimaryColor : appTheme.background,
                          ),
                          child: Text('Hold to speak', style: AppStyle.medium14()),
                        ),
                      ),
                    ),
                  ] else ...[
                    Expanded(
                      child: TextField(
                        controller: cubit.textEditCtrl,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: appTheme.background,
                          contentPadding: padding(top: 10, bottom: 13, horizontal: 13),
                          hintText: 'I want to do something',
                          hintStyle: AppStyle.medium14(color: appTheme.fadeTextColor),
                          border: _border,
                          errorBorder: _border,
                          enabledBorder: _border,
                          disabledBorder: _border,
                          focusedBorder: _border,
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(width: 10),
                  if (state.isLoading || state.messages.isEmpty) ...[
                    SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: appTheme.primaryColor))
                  ] else ...[
                    GestureDetector(
                      onTap: cubit.onSendMessage,
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        padding: padding(left: 7.1, bottom: 7.1, right: 5.1, top: 5.1),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(colors: [appTheme.primaryColor, appTheme.secondaryPrimaryColor]),
                        ),
                        child: Assets.images.send.image(width: 14, height: 14),
                      ),
                    )
                  ]
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
