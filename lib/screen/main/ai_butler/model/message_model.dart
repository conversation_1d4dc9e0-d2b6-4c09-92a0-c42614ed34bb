// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/message.dart';

enum MessageItemType { none, trip, record, shopping, event }

class MessageModel {
  final bool isAI;
  final String content;
  final bool isFirstMessage;
  final MessageItemType itemType;
  final String recordPath;
  final Message message;
  final String file;
  final ActivityModel? activityModels;
  final EventModels? eventModels;
  final ListItem? listItem;
  final String? fileUrl;
  final bool isHideButton;

  MessageModel(
      {this.isAI = true,
      this.content = '',
      this.isFirstMessage = false,
      this.itemType = MessageItemType.none,
      required this.message,
      this.recordPath = '',
      this.file = '',
      this.activityModels,
      this.eventModels,
      this.fileUrl,
      this.isHideButton = true,
      this.listItem});

  MessageModel copyWith({
    bool? isAI,
    String? content,
    bool? isFirstMessage,
    MessageItemType? itemType,
    Message? message,
    String? recordPath,
    String? file,
    ActivityModel? activityModels,
    EventModels? eventModels,
    ListItem? listItem,
    bool? isHideButton,
  }) {
    return MessageModel(
      isAI: isAI ?? this.isAI,
      message: message ?? this.message,
      content: content ?? this.content,
      isFirstMessage: isFirstMessage ?? this.isFirstMessage,
      itemType: itemType ?? this.itemType,
      recordPath: recordPath ?? this.recordPath,
      file: file ?? this.file,
      activityModels: activityModels ?? this.activityModels,
      eventModels: eventModels ?? this.eventModels,
      listItem: listItem ?? this.listItem,
      isHideButton: isHideButton ?? this.isHideButton,
    );
  }

  Map<String, dynamic> toReviewFile() => {
        'isAI': isAI,
        'content': content,
        'itemType': itemType.name,
      };
}

extension MessageModelExtension on MessageModel {
  bool get isGenerateType {
    final command = message.command;
    return command == SocketEvent.LIST || command == SocketEvent.EVENT || command == SocketEvent.TRIP;
  }

  bool get isDone {
    return activityModels != null || eventModels != null || listItem != null;
  }
}
