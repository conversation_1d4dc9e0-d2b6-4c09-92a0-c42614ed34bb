import 'dart:async';

import 'package:family_app/base/stream/base_stream_controller.dart';
// import 'package:record/record.dart';

class RecordController {
  // final record = AudioRecorder();

  final startRecordCtrl = BaseStreamController<bool>(false);
  final isRecording = BaseStreamController<bool>(false);
  final timeRecord = BaseStreamController<int>(0);

  Timer? _timer = null;

  void dispose() {
    if (_timer != null) {
      _timer!.cancel();
    }
    isRecording.dispose();
    timeRecord.dispose();
  }

  Future<void> startRecord() async {
    // Check and request permission if needed

    // if (await record.hasPermission()) {
    //   startRecordCtrl.value = true;
    //   await record.start(const RecordConfig(), path: 'aFullPath/myFile.m4a');
    //   await record.pause();
    // }
  }

  Future<String> stopRecord() async {
    // record.stop();
    // startRecordCtrl.value = false;
    // isRecording.value = false;
    // timeRecord.value = 0;
    // if (_timer != null) _timer?.cancel();
    // final path = await record.stop();
    // return path ?? '';
    return '';
  }

  void isHoldToSpeak(bool value) async {
    // isRecording.value = value;
    // if (value) {
    //   await record.resume();
    //   _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
    //     timeRecord.value = timeRecord.value + 1;
    //   });
    // } else {
    //   record.pause();
    //   if (_timer != null) _timer?.cancel();
    // }
  }
}
