import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/screen/main/memories/memory_select_activity_state.dart';
import 'package:family_app/utils/log/app_logger.dart';

class MemorySelectActivityCubit extends BaseCubit<MemorySelectActivityState> {
  final IActivityRepository activityRepository;
  MemorySelectActivityCubit({required this.activityRepository}) : super(MemorySelectActivityState());
  final AccountService accountService = locator.get();

  void filterActivities(String query) {
    List<ActivityModel> filteredActivities = [];
    if (query.isEmpty) {
      filteredActivities = state.activityList;
    } else {
      filteredActivities = state.activityList
          .where((activity) => activity.name?.toLowerCase().contains(query.toLowerCase()) ?? false)
          .toList();
    }
    emit(state.copyWith(filteredActivityList: filteredActivities));
  }

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();
    fetchActivity();
  }

  @override
  Future<void> close() {
    locator.unregister<MemorySelectActivityCubit>();
    return super.close();
  }

  Future<void> fetchActivity() async {
    emit(state.copyWith(status: MemorySelectActivityStatus.loading));
    try {
      final result = await activityRepository.getAllActivities(accountService.familyId);
      emit(state.copyWith(
          status: MemorySelectActivityStatus.success, activityList: result, filteredActivityList: result));
    } catch (e) {
      AppLogger.d('fetchActivity error: $e');
      emit(state.copyWith(status: MemorySelectActivityStatus.error));
    }
  }

  void selectActivity(ActivityModel activity) {
    AppLogger.d('Selected activity: ${activity.name} , uuid: ${activity.uuid}');
    emit(state.copyWith(selectedActivity: activity));
  }
}
