import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';

enum MemorySelectActivityStatus { initial, loading, success, error }

class MemorySelectActivityState extends BaseState {
  final MemorySelectActivityStatus status;
  final List<ActivityModel> activityList;
  final List<ActivityModel> filteredActivityList;
  final ActivityModel? selectedActivity;
  final String? errorMessage;

  MemorySelectActivityState({
    this.status = MemorySelectActivityStatus.initial,
    this.activityList = const [],
    this.filteredActivityList = const [],
    this.selectedActivity,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [status, activityList, filteredActivityList, selectedActivity, errorMessage];

  MemorySelectActivityState copyWith({
    MemorySelectActivityStatus? status,
    List<ActivityModel>? activityList,
    List<ActivityModel>? filteredActivityList,
    ActivityModel? selectedActivity,
    String? errorMessage,
  }) {
    return MemorySelectActivityState(
      status: status ?? this.status,
      activityList: activityList ?? this.activityList,
      filteredActivityList: filteredActivityList ?? this.filteredActivityList,
      selectedActivity: selectedActivity ?? this.selectedActivity,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
