import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/memories/memory_comment_parameter.dart';
import 'package:family_app/screen/main/memories/memory_comment_screen.dart';
import 'package:family_app/screen/main/memories/memory_upsert_screen.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/bottom_sheet/memory_action.dart';
import 'package:family_app/widget/bottom_sheet/memory_action_bts.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/expandable_caption.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/image_viewer.dart/image_viewer_pager.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

import 'memories_cubit.dart';
import 'memories_state.dart';
import 'widget/memories_activity_info.dart';

@RoutePage()
class MemoriesPage extends BaseBlocProvider<MemoriesState, MemoriesCubit> {
  const MemoriesPage({super.key});

  @override
  Widget buildPage() => const MemoriesScreen();

  @override
  MemoriesCubit createCubit() => MemoriesCubit(
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
      );
}

class MemoriesScreen extends StatefulWidget {
  const MemoriesScreen({super.key});

  @override
  State<MemoriesScreen> createState() => _MemoriesScreenState();
}

class _MemoriesScreenState
    extends BaseBlocPageState<MemoriesScreen, MemoriesState, MemoriesCubit> {
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  bool listenWhen(MemoriesState previous, MemoriesState current) {
    if (current.status == MemoriesStatus.loading) {
      _refreshIndicatorKey.currentState?.show();
    } else if (current.status == MemoriesStatus.error) {
      _refreshIndicatorKey.currentState?.deactivate();
      showSimpleToast(current.errorMessage ?? 'Failed to load memories');
    } else if (current.status == MemoriesStatus.success) {
      _refreshIndicatorKey.currentState?.deactivate();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, MemoriesCubit c, MemoriesState __) {
    return CustomAppBar2(
      title: LocaleKeys.memories.tr(),
      showBack: true,
      backgroundColor: Colors.transparent,
      backIcon: GestureDetector(
        onTap: () async {
          // Go back to the previous screen
          context.maybePop();
        },
        behavior: HitTestBehavior.opaque,
        child: CircleItem(
          backgroundColor: Colors.transparent,
          padding: padding(all: 8),
          child: SvgPicture.asset(Assets.icons.iconActionBack.path),
        ),
      ),
      actions: [
        GestureDetector(
          onTap: () async {
            MemoryUpsertBts.show(context).then(
              (result) {
                if (result == true) {
                  c.fetchMemories();
                }
              },
            );
          },
          behavior: HitTestBehavior.opaque,
          child: CircleItem(
            backgroundColor: const Color(0xFF4E46B4),
            padding: padding(all: 8),
            child: SvgPicture.asset(Assets.icons.icAdd.path),
          ),
        )
      ],
    );
  }

  @override
  Widget buildBody(
      BuildContext context, MemoriesCubit cubit, MemoriesState state) {
    return BlocBuilder<MemoriesCubit, MemoriesState>(
      builder: (context, state) {
        if (state.status == MemoriesStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.status == MemoriesStatus.success) {
          return RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: cubit.fetchMemories,
            child: _buildMemoryView(cubit, state),
          );
        } else if (state.status == MemoriesStatus.error) {
          return Center(child: Text(state.errorMessage!));
        } else {
          return Container(); // Or show something else in the initial state
        }
      },
    );
  }

  Widget _buildMemoryView(MemoriesCubit cubit, MemoriesState state) {
    if (state.memories.isEmpty) {
      return _buildEmptyView(cubit);
    } else {
      return Column(
        children: [
          _buildSearchBox(),
          Expanded(
            child: _buildMemoryList(cubit, state),
          ),
        ],
      );
    }
  }

  Widget _buildSearchBox() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(8, 12, 8, 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(10),
          onTap: () {
            goToMemoryFilter();
          },
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFE2E2E2),
              borderRadius: BorderRadius.circular(10),
            ),
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8),
                  child: SvgPicture.asset(
                    Assets.icons.iconSearch.path,
                    width: 20,
                    height: 20,
                  ),
                ),
                const Expanded(
                  child: Text(
                    'Search posts...',
                    style: TextStyle(fontSize: 16, color: Colors.black54),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void goToMemoryFilter() async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(const MemoryFilterRoute());
    }
  }

  Widget _buildEmptyView(MemoriesCubit c) {
    return InkWell(
      onTap: () async {
        MemoryUpsertBts.show(context).then(
          (result) {
            if (result == true) {
              c.fetchMemories();
            }
          },
        );
      },
      child: Column(
        children: [
          const Icon(Icons.list, size: 64),
          const SizedBox(height: 16),
          const Text('You don\'t have any memories yet'),
          TextButton(
            onPressed: () async {
              MemoryUpsertBts.show(context).then(
                (result) {
                  if (result == true) {
                    c.fetchMemories();
                  }
                },
              );
            },
            style: TextButton.styleFrom(
              backgroundColor: Colors.blue, // Add background color
            ),
            child: const Text(
              'Add new memory',
              style: TextStyle(color: Colors.white), // Set text color to white
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryList(MemoriesCubit cubit, MemoriesState state) {
    return ListView.builder(
      itemCount: state.memories.length,
      itemBuilder: (context, index) {
        final memory = state.memories[index];
        return MemoryCard(cubit: cubit, state: state, memory: memory);
      },
    );
  }

  //Widget buildBottomView() => const SizedBox();

// //   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Memories'),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.camera_alt),
//             onPressed: () {
//               // Handle camera action
//             },
//           )
//         ],
//       ),
//       body: CustomBlocBuilder<MainState, MainCubit>(
//         cubit: mainCubit,
//         viewBuilder: (context, cubit, state) {
//           if (state.status == MemoriesStatus.loading) {
//             return const Center(child: CircularProgressIndicator());
//           } else if (state.status == MemoriesStatus.success) {
//             return ListView.builder(
//               itemCount: state.memories.length,
//               itemBuilder: (context, index) {
//                 final memory = state.memories[index];
//                 return MemoryCard(memory: memory); // Custom widget for each memory
//               },
//             );
//           } else if (state.status == MemoriesStatus.error) {
//             return Center(child: Text(state.errorMessage!));
//           } else {
//             return Container(); // Or show something else in the initial state
//           }
//         },
//       ),
//     );
//   }
}

class MemoryCard extends StatefulWidget {
  final MemoriesCubit cubit;
  final MemoriesState state;
  final MemoryModel memory;

  const MemoryCard(
      {super.key,
      required this.cubit,
      required this.state,
      required this.memory});

  @override
  State<MemoryCard> createState() => _MemoryCardState();
}

class _MemoryCardState extends State<MemoryCard> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  void handleMemorySettings(context, MemoryModel memory) {
    MemoryActionBts.showHeightReturnValue(context).then((action) {
      if (action != null) {
        final memoryAction = action;
        switch (memoryAction.actionType) {
          case ActionType.edit:
            handleEdit(context, memory);
            break;
          case ActionType.delete:
            handleDelete(context, memory);
            break;
        }
      }
    });
  }

  void handleEdit(context, MemoryModel memory) {
    MemoryUpsertBts.show(context, memory: memory).then(
      (result) {
        if (result == true) {
          widget.cubit.fetchMemories();
        }
      },
    );
  }

  void handleDelete(context, MemoryModel memory) async {
    // Show confirmation dialog
    final shouldDelete = await DialogUtils.showConfirmDialog(
      context,
      title: 'Delete post',
      content: 'Are you sure you want to delete this post?',
      confirmText: 'Yes',
      cancelText: 'No',
    );
    if (shouldDelete == true) {
      widget.cubit.deleteMemory(memory);
    }
  }

  GestureTapCallback? onTapCard(
      BuildContext context, List<ImagePagerItem> items) {
    if (widget.memory.userId == widget.state.accountUuid) {
      return () => handleEdit(context, widget.memory);
    } else
      return onTapImage(context, items, 0);
  }

  GestureTapCallback? onTapImage(
      BuildContext context, List<ImagePagerItem> items, int? initialIndex) {
    return () => ImageViewerPager.show(
          context: context,
          items: items,
          initialIndex: initialIndex ?? 0,
          doubleTapZoomable: true,
          swipeDismissible: true,
        );
  }

  @override
  Widget build(BuildContext context) {
    final imageItems = widget.memory.files!
        .map((item) => ImagePagerItem(
              imageUrl: item.fileUrlMd!,
              // title: widget.memory.name ?? '',
              title: '',
              description: widget.memory.caption ?? '',
              dateTime: item.createdAt!.toDateTime(),
              activityId: widget.memory.activityId,
              activityName:
                  widget.cubit.getActivityById(widget.memory.activityId!)?.name,
            ))
        .toList();
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      color: Colors.white,
      child: InkWell(
        // onTap: onTapCard(context, imageItems),
        borderRadius: BorderRadius.circular(16.0),
        child: _buildCardBody(context, widget.cubit, widget.memory, imageItems),
      ),
    );
  }

  Widget _buildCardBody(BuildContext context, MemoriesCubit c,
      MemoryModel memory, List<ImagePagerItem> imageItems) {
    return Stack(
      children: [
        /* if (memory.userId == state.accountUuid)
          Positioned(
            top: 2,
            right: 12,
            child: SizedBox(
              width: 36,
              height: 36,
              child: PopupMenuButton(
                onSelected: (command) {
                  switch (command) {
                    case 'edit':
                      handleEdit(context);
                      break;
                    default:
                      break;
                  }
                },
                padding: const EdgeInsets.all(4.0),
                icon: ImageAssetCustom(imagePath: Assets.icons.threeDot.path, color: Colors.black, size: 32),
                //Menu
                itemBuilder: (BuildContext context) => [
                  PopupMenuItem(value: 'edit', child: Text(LocaleKeys.edit.tr(), style: AppStyle.textSmR)),
                ],
                color: Colors.white,
                menuPadding: const EdgeInsets.symmetric(vertical: 4),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ), */
        Column(
          children: [
            _buildTopRow(context, c, memory),
            _buildPhotoViewer(context, memory, imageItems),
            _buildReactionRow(memory),
            _buildCaption(memory),
            _buildTagInfo(memory),
          ],
        )
      ],
    );
  }

  Widget _buildReactionRow(MemoryModel memory) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Row(
        children: [
          // Heart icon with count
          InkWell(
            onTap: () {
              if (memory.isLiked) {
                widget.cubit.unlikeMemory(memory);
              } else {
                widget.cubit.likeMemory(memory);
              }
            },
            borderRadius: BorderRadius.circular(4),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Row(
                children: [
                  SvgPicture.asset(
                    memory.isLiked
                        ? Assets.icons.iconHeartActive.path
                        : Assets.icons.iconHeartNormal.path,
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    memory.interact?.length.toString() ?? '0',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Comment icon with count
          InkWell(
            onTap: () {
              MemoryCommentBts.show(context,
                      memory: memory, users: widget.state.users)
                  .then(
                (result) {
                  if (result == true) {
                    widget.cubit.fetchMemories();
                  }
                },
              );
            },
            borderRadius: BorderRadius.circular(4),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Row(
                children: [
                  SvgPicture.asset(
                    Assets.icons.iconComment.path,
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    memory.comments?.length.toString() ?? '0',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaption(MemoryModel memory) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Align(
        alignment: Alignment.centerLeft,
        child: ExpandableCaption(
          text: memory.caption ?? '',
          style: const TextStyle(fontSize: 14.0, color: Colors.black),
        ),
      ),
    );
  }

  Widget _buildTagInfo(MemoryModel memory) {
    final tags = memory.extraTags;
    if (tags == null || tags.isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Wrap(
              alignment: WrapAlignment.start,
              spacing: 8.0,
              runSpacing: 8.0,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: tags
                  .map((tag) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12.0, vertical: 8.0),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE2E2E2),
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Text(
                          tag,
                          style: const TextStyle(
                            color: Color(0xFF595D62),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildPhotoViewer(BuildContext context, MemoryModel memory,
      List<ImagePagerItem> imageItems) {
    return Column(
      children: [
        Container(
          height: 200,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemCount: memory.files?.length ?? 0,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: onTapImage(context, imageItems, index),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: _buildImage(
                        context, widget.cubit, memory.files![index].fileUrlMd!),
                  ),
                ),
              );
            },
          ),
        ),
        // Dot indicators below the photo
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              memory.files?.length ?? 0,
              (index) => GestureDetector(
                onTap: () {
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  child: SvgPicture.asset(
                    index == _currentPage
                        ? Assets.icons.iconDotActive.path
                        : Assets.icons.iconDotInactive.path,
                    width: 8,
                    height: 8,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImage(BuildContext context, MemoriesCubit c, String url,
      {double height = 100, double width = double.infinity}) {
    return CachedNetworkImage(
      imageUrl: url,
      height: height,
      width: width,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Icon(Icons.error, color: Colors.red),
      ),
    );
  }

  Widget _buildTopRow(
      BuildContext context, MemoriesCubit c, MemoryModel memory) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          // Avatar
          CircleAvatarCustom(
            imageUrl: c.getUserInfo(memory.userId!)?.photoUrl ?? '',
            size: 40,
            borderColor: appTheme.borderColorV2,
            borderWidth: 1,
          ),
          const SizedBox(width: 12),
          // User name
          Expanded(
            child: Text(
              c.getUserInfo(memory.userId!)?.fullName ?? '',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Menu icon
          if (memory.userId == widget.state.accountUuid)
            GestureDetector(
              onTap: () => handleMemorySettings(context, memory),
              behavior: HitTestBehavior.opaque,
              child: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  Assets.icons.iconMenu.path,
                  width: 16,
                  height: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
