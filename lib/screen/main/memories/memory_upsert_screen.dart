import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/memories/memory_select_activity_screen.dart';
import 'package:family_app/screen/main/memories/memory_upsert_cubit.dart';
import 'package:family_app/screen/main/memories/memory_upsert_parameter.dart';
import 'package:family_app/screen/main/memories/memory_upsert_state.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class MemoryUpsertBts extends BaseBlocProvider<MemoryUpsertState, MemoryUpsertCubit> {
  static Future<bool?> show(BuildContext context, {MemoryModel? memory}) async {
    return BottomSheetUtils.showHeightReturnBool(context,
        height: 0.8, child: MemoryUpsertBts(parameter: MemoryUpsertParameter(memory)));
  }

  const MemoryUpsertBts({required this.parameter, super.key});
  final MemoryUpsertParameter parameter;

  @override
  Widget buildPage() => const MemoryUpsertScreen();

  @override
  MemoryUpsertCubit createCubit() => MemoryUpsertCubit(
        parameter: parameter,
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
        homeCubit: locator.get(),
      );
}

class MemoryUpsertScreen extends StatefulWidget {
  const MemoryUpsertScreen({super.key});

  @override
  State<MemoryUpsertScreen> createState() => _MemoryUpsertScreenState();
}

class _MemoryUpsertScreenState extends BaseBlocPageState<MemoryUpsertScreen, MemoryUpsertState, MemoryUpsertCubit> {
  // @override
  // Widget buildView(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
  //   // TODO: implement buildBody
  //   return _buildDialog(context, cubit, state);
  // }

  @override
  bool listenWhen(MemoryUpsertState previous, MemoryUpsertState current) {
    if (current.status == MemoryUpsertStatus.loading) {
      showLoading();
    } else if (current.status == MemoryUpsertStatus.done) {
      dismissLoading();
    } else if (current.status == MemoryUpsertStatus.error) {
      dismissLoading();
      showSimpleToast(current.errorMessage ?? 'Failed to add memory');
    } else if (current.status == MemoryUpsertStatus.success) {
      dismissLoading();
      Navigator.of(context).pop(true); // Return true on success
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    return SingleChildScrollView(
      // Added for scrollability
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildHeader(context, cubit, state),
          _buildDivider(context),
          _buildTextBox(context, cubit, state),
          // const SizedBox(height: 8),
          _buildImagePicker(context, cubit, state),
          // const SizedBox(height: 8),
          _buildActivityPicker(context, cubit, state),
          const SizedBox(height: 16),
          _buildShareOrSaveButton(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildShareOrSaveButton(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    final isEnabled = state.caption != null &&
        state.caption!.isNotEmpty &&
        state.caption != state.memory?.caption &&
        state.isModified;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: isEnabled ? const Color(0xFF4E46B4) : const Color(0xFFBDB8E2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.symmetric(vertical: 14),
          ),
          onPressed: isEnabled
              ? () async {
                  cubit.upsertMemory(state.memory, state.caption, state.images, state.activity);
                }
              : null,
          child: Text(
            state.memory == null ? 'Share' : 'Save Changes',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: SvgPicture.asset(
              Assets.icons.iconActionBack.path,
              width: 24,
              height: 24,
            ),
          ),
          Expanded(
            child: Center(
              child: Text(state.memory == null ? 'New memory' : 'Edit memory',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
          ),
          const SizedBox(width: 76), // Keep space for alignment (width of publish button)
        ],
      ),
    );
  }

  Widget _buildPublishButton(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    final text = 'Publish';
    return TextButton(
      onPressed: state.caption == null || state.caption!.isEmpty
          ? null
          : () async {
              // Handle publish logic here
              cubit.upsertMemory(state.memory, state.caption, state.images, state.activity);
            },
      child: state.caption == null || state.caption!.isEmpty
          ?
          // Disable the button if caption is empty
          Text(
              text,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: appTheme.greyColor,
              ),
            )
          :
          // Enable the button if caption is not empty
          Text(
              text,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 1,
      color: Theme.of(context).dividerColor,
    );
  }

  void _showImageSourceDialog(BuildContext context, MemoryUpsertCubit cubit) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Upload Image'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.photo_library),
                title: Text('Select from Gallery'),
                onTap: () {
                  Navigator.of(context).pop();
                  cubit.pickImage();
                },
              ),
              ListTile(
                leading: Icon(Icons.camera_alt),
                title: Text('Take Photo'),
                onTap: () {
                  Navigator.of(context).pop();
                  cubit.takePhoto();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTextBox(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextFormField(
        initialValue: state.caption,
        maxLines: null,
        decoration: InputDecoration(
          hintText: 'How did you feel?',
          border: InputBorder.none, // Hide the border
        ),
        onChanged: (value) {
          cubit.updateCaption(value);
        },
      ),
    );
  }

  Widget _buildImagePicker(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.all(8.0),
        padding: const EdgeInsets.all(16.0),
        child: Wrap(
          // Use Wrap for horizontal image display with wrapping
          spacing: 8.0, // gap between adjacent chips
          runSpacing: 4.0, // gap between lines
          children: [
            if (state.memory?.files != null)
              ...state.memory!.files!
                  .map((storageModel) => _buildEditImageItemView(context, cubit, state, storageModel)),
            ...state.images.map((image) => _buildImageItemView(context, cubit, state, image)),
            _buildAddPhotoView(context, cubit, state),
          ],
        ),
      ),
    );
  }

  Widget _buildEditImageItemView(
      BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state, StorageModel storageModel) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: _buildNetworkImage(storageModel.fileUrlMd!),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
              onTap: () {
                // Implement remove memory image logic here
                cubit.removeStorage(storageModel);
              },
              child: Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(color: Colors.black.withOpacity(0.5), shape: BoxShape.circle),
                  child: Icon(Icons.close, size: 16, color: Colors.white))),
        ),
      ],
    );
  }

  Widget _buildNetworkImage(String url, {double height = 100, double width = 100}) {
    return CachedNetworkImage(
      imageUrl: url,
      height: height,
      width: width,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Icon(Icons.error, color: Colors.red),
      ),
    );
  }

  Widget _buildImageItemView(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state, File image) {
    return Stack(
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: FileImage(image),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
              onTap: () {
                // Implement remove image logic here
                cubit.removeImage(image);
              },
              child: Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(color: Colors.black.withOpacity(0.5), shape: BoxShape.circle),
                  child: Icon(Icons.close, size: 16, color: Colors.white))),
        ),
      ],
    );
  }

  Widget _buildAddPhotoView(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    return GestureDetector(
      onTap: () => _showImageSourceDialog(context, cubit),
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: SvgPicture.asset(
            Assets.icons.icPhoto.path,
            width: 16,
            height: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildActivityPicker(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
    return Align(
      alignment: Alignment.centerLeft,
      child: GestureDetector(
        onTap: () async {
          // Implement activity picker logic here
          BottomSheetUtils.showHeightReturnValue(context, height: 0.9, child: const MemorySelectActivityBts())
              .then((activity) {
            // Cast activity to ActivityModel
            if (activity != null && activity is ActivityModel) {
              cubit.updateActivity(activity);
            }
          });
        },
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(Assets.icons.icBookmark.path),
              SizedBox(width: 8),
              if (state.activity != null) ...[
                Flexible(
                  child: Text(state.activity!.name ?? '', maxLines: 2, overflow: TextOverflow.ellipsis),
                ),
                SizedBox(width: 8),
                InkWell(
                    onTap: () {
                      cubit.removeActivity();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: SvgPicture.asset(
                        Assets.icons.icClose.path,
                      ),
                    )),
              ] else ...[
                const Text('Select activity'),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 0.0),
                  child: const SizedBox(
                    height: 10,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
