import 'package:auto_route/auto_route.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';

class MemoriesActivityInfo extends StatelessWidget {
  final String? activityId;
  final String? activityName;
  final Color? textColor;

  const MemoriesActivityInfo({
    super.key,
    required this.activityId,
    required this.activityName,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    if (activityId == null || activityId!.isEmpty) {
      return const SizedBox();
    }

    const Color mainColor = Color(0xFFCA3857);
    const Color bgColor = Color(0x4DCA3857); // 30% alpha

    return InkWell(
      onTap: () {
        // Navigate to trip_detail_screen
        context.pushRoute(TripDetailRoute(
          parameter: TripDetailParameter(activityId: activityId!),
        ));
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              Assets.icons.iconActivityInfo.path,
              width: 16,
              height: 16,
              color: mainColor,
            ),
            const SizedBox(width: 8),
            Text(
              activityName ?? '',
              style: const TextStyle(color: mainColor),
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
    );
  }
}
