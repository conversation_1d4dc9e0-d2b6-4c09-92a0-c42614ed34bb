import 'dart:convert';
import 'dart:io';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/storage_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/data/usecase/model/upsert_memory_param.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/memories/memory_upsert_parameter.dart';
import 'package:family_app/screen/main/memories/memory_upsert_state.dart';
import 'package:family_app/utils/log/app_logger.dart';
// import 'package:family_app/utils/permission_hanlder.dart';
import 'package:family_app/utils/upload.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class MemoryUpsertCubit extends BaseCubit<MemoryUpsertState> {
  final MemoryUpsertParameter parameter;
  final IFamilyRepository familyRepository;
  final IUploadRepository uploadRepository;
  final IActivityRepository activityRepository;
  final HomeCubit homeCubit;

  MemoryUpsertCubit(
      {required this.parameter,
      required this.familyRepository,
      required this.uploadRepository,
      required this.activityRepository,
      required this.homeCubit})
      : super(MemoryUpsertState(memory: parameter.memory, caption: parameter.memory?.caption));
  final AccountService accountService = locator.get();

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();

    logd("asking for permission .... ");

    await _requestPermissions();

    initMemoryData();
  }

  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      final cameraStatus = await Permission.camera.request();
      final storageStatus = await Permission.photos.request();
      final storageStatus2 = await Permission.manageExternalStorage.request();

      if (cameraStatus.isDenied) {
        // Handle the case when the permission is denied
        logd("Camera Permission denied");
      }

      if (storageStatus.isDenied) {
        logd("Storage Permission denied");
      }

      if (cameraStatus.isPermanentlyDenied || storageStatus.isPermanentlyDenied) {
        // Handle the case when the permission is permanently denied
        await openAppSettings();
      }
    }
  }

  Future<void> initMemoryData() async {
    if (parameter.memory != null) {
      final memory = parameter.memory!;
      ActivityModel? activity = null;
      emit(state.copyWith(status: MemoryUpsertStatus.loading));
      try {
        if (memory.activityId != null && memory.activityId!.isNotEmpty) {
          final activityList = await activityRepository.getAllActivities(accountService.familyId);
          activity = activityList.firstWhere((element) => element.uuid == memory.activityId);
        }
      } catch (e) {
        AppLogger.d('fetchActivity error: $e');
        // emit(state.copyWith(status: MemoryUpsertStatus.error, errorMessage: 'Failed to fetch activity'));
      } finally {
        emit(state.copyWith(
          status: MemoryUpsertStatus.done,
          memory: memory,
          caption: memory.caption,
          activity: activity,
        ));
      }
    }
  }

  @override
  Future<void> close() {
    locator.unregister<MemoryUpsertCubit>();
    return super.close();
  }

  void updateCaption(String caption) {
    emit(state.copyWith(caption: caption, isModified: true));
  }

  void updateActivity(ActivityModel activity) {
    emit(state.copyWith(activity: activity, isModified: true));
  }

  void removeActivity() {
    emit(state.clearActivity());
  }

  void addImage(File imageFile) {
    final updatedImages = List<File>.from(state.images)..add(imageFile);
    emit(state.copyWith(images: updatedImages, isModified: true));
  }

  void removeImage(File imageFile) {
    final updatedImages = List<File>.from(state.images)..remove(imageFile);
    emit(state.copyWith(images: updatedImages, isModified: true));
  }

  void removeStorage(StorageModel storageModel) {
    final memory = state.memory;
    final updatedStorage = List<StorageModel>.from(memory!.files!)
      ..removeWhere((element) => element.uuid == storageModel.uuid!);
    AppLogger.d('Updated storage: ${jsonEncode(updatedStorage)}');
    emit(state.copyWith(memory: memory.copyWith(files: updatedStorage), isModified: true));
  }

  Future<void> pickImage() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      final updatedImages = List<File>.from(state.images)..add(File(image.path));

      emit(state.copyWith(images: updatedImages, isModified: true));
    }
  }

  Future<void> takePhoto() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      final updatedImages = List<File>.from(state.images)..add(File(image.path));
      emit(state.copyWith(images: updatedImages, isModified: true));
    }
  }

  Future<void> pickMultiImage() async {
    final picker = ImagePicker();
    final pickedFiles = await picker.pickMultiImage();

    if (pickedFiles != null && pickedFiles.isNotEmpty) {
      final imageFiles = pickedFiles.map((pickedFile) => File(pickedFile.path)).toList();
      uploadImages(imageFiles);
    } else {
      AppLogger.d('No images selected.');
    }
  }

  Future<List<StorageModel>> uploadImages(List<File> imageFiles) async {
    // AppLogger.d('Uploading images: $imageFiles');
    try {
      final storageModels = <StorageModel>[];
      for (final imageFile in imageFiles) {
        final storageModel = await uploadImage(imageFile);
        if (storageModel.uuid != null) {
          storageModels.add(storageModel);
        }
      }
      return storageModels;
    } catch (e) {
      AppLogger.d('Error uploading images: $e');
    }
    return [];
  }

  Future<StorageModel> uploadImage(File imageFile) async {
    try {
      final upload = Upload(
        familyId: accountService.familyId,
      );
      final storageModel = await upload.uploadImage(imageFile, null);
      if (storageModel.uuid != null) {
        AppLogger.d('Image uploaded successfully.');
        return storageModel;
      } else {
        AppLogger.d('Image upload failed.');
      }
    } catch (e) {
      AppLogger.d('Image upload error: $e');
    }
    return StorageModel();
  }

  Future<void> upsertMemory(MemoryModel? memory, String? caption, List<File>? images, ActivityModel? activity) async {
    emit(state.copyWith(status: MemoryUpsertStatus.loading));
    try {
      final storageModels = <StorageModel>[];
      if (memory != null && memory.files != null) {
        storageModels.addAll(memory.files!);
      }
      if (images != null) {
        final newStorageModels = await uploadImages(images);
        storageModels.addAll(newStorageModels);
      }
      if (storageModels.isNotEmpty) {
        // Get list of uuid from storageModels
        final fileIdList = storageModels.map((e) => e.uuid!).toList();
        final upsertMemoryParam = const UpsertMemoryParam().copyWith(
          familyId: accountService.familyId,
          activityId: activity?.uuid,
          caption: caption,
          attachmentType: 'list',
          fileId: fileIdList,
        );
        MemoryModel? memoryModel;
        if (memory?.uuid != null) {
          memoryModel = await familyRepository.updateMemory(memory!.uuid!, upsertMemoryParam);
        } else {
          memoryModel = await familyRepository.attachStorageToMemory(upsertMemoryParam);
        }
        if (memoryModel.uuid != null) {
          AppLogger.d('Memory upserted successfully.');
          emit(state.copyWith(status: MemoryUpsertStatus.success));
          homeCubit.onRefresh();
        } else {
          emit(state.copyWith(status: MemoryUpsertStatus.error, errorMessage: 'Failed to update memory'));
        }
      } else {
        emit(state.copyWith(status: MemoryUpsertStatus.error, errorMessage: 'Failed to upload images'));
      }
    } catch (e) {
      AppLogger.d('Error upsert memory: $e');
      emit(state.copyWith(status: MemoryUpsertStatus.error, errorMessage: 'Failed to update memory'));
    }
  }

  // Convert XFile to a file in the application temp folder for upload
  Future<File> _createFileFromXFile(XFile xFile) async {
    try {
      // Get the temporary directory of the app
      final directory = await getTemporaryDirectory();
      // Create a new file in the temporary directory

      final file = File(path.join(directory.path, xFile.name));
      // Read the bytes from the XFile and write them to the new file
      final bytes = await xFile.readAsBytes();
      await file.writeAsBytes(bytes);

      logd("new file path: ${file.path}");
      return file;
    } catch (e) {
      throw Exception('Error creating file from XFile: $e');
    }
  }
}
