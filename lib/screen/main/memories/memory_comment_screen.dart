import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/interaction_model.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/memories/memory_comment_cubit.dart';
import 'package:family_app/screen/main/memories/memory_comment_parameter.dart';
import 'package:family_app/screen/main/memories/memory_comment_state.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:family_app/gen/assets.gen.dart';

class MemoryCommentBts
    extends BaseBlocProvider<MemoryCommentState, MemoryCommentCubit> {
  static Future<bool?> show(BuildContext context,
      {MemoryModel? memory, Map<String, Account>? users}) async {
    return BottomSheetUtils.showHeightReturnBool(context,
        height: 0.8,
        child:
            MemoryCommentBts(parameter: MemoryCommentParameter(memory, users)));
  }

  const MemoryCommentBts({required this.parameter, super.key});
  final MemoryCommentParameter parameter;

  @override
  Widget buildPage() => const MemoryCommentScreen();

  @override
  MemoryCommentCubit createCubit() => MemoryCommentCubit(
        parameter: parameter,
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
      );
}

class MemoryCommentScreen extends StatefulWidget {
  const MemoryCommentScreen({super.key});

  @override
  State<MemoryCommentScreen> createState() => _MemoryCommentScreenState();
}

class _MemoryCommentScreenState extends BaseBlocPageState<MemoryCommentScreen,
    MemoryCommentState, MemoryCommentCubit> {
  final TextEditingController _commentController = TextEditingController();

  // @override
  // Widget buildView(BuildContext context, MemoryUpsertCubit cubit, MemoryUpsertState state) {
  //   // TODO: implement buildBody
  //   return _buildDialog(context, cubit, state);
  // }

  @override
  bool listenWhen(MemoryCommentState previous, MemoryCommentState current) {
    if (current.status == MemoryCommentStatus.loading) {
      showLoading();
    } else if (current.status == MemoryCommentStatus.done) {
      dismissLoading();
    } else if (current.status == MemoryCommentStatus.error) {
      dismissLoading();
      showSimpleToast(current.errorMessage ?? 'Failed to add memory');
    } else if (current.status == MemoryCommentStatus.success) {
      dismissLoading();
      _commentController.clear();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildView(BuildContext context, MemoryCommentCubit cubit,
      MemoryCommentState state) {
    return _buildDialog(context, cubit, state);
  }

  Widget _buildDialog(BuildContext context, MemoryCommentCubit cubit,
      MemoryCommentState state) {
    return SingleChildScrollView(
      // Added for scrollability
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildHeader(context, cubit, state),
          // _buildDivider(context),
          _buildCommentList(context, cubit, state),
          _buildTextBox(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, MemoryCommentCubit cubit,
      MemoryCommentState state) {
    return Container(
      padding: EdgeInsets.only(top: 16, bottom: 16, left: 0, right: 16),
      child: Row(
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(cubit.state.isChanged);
            },
            child: SvgPicture.asset(
              Assets.icons.iconActionBack.path,
              width: 24,
              height: 24,
            ),
          ),
          Expanded(
            child: Center(
              child: Text("Comments",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
          ),
          SizedBox(width: 64), // Keep space for alignment (approximate width of button)
        ],
      ),
    );
  }

  Widget _buildPublishButton(BuildContext context, MemoryCommentCubit cubit,
      MemoryCommentState state) {
    final text = 'Publish';
    final comment = state.comment;
    return TextButton(
      onPressed: comment == null || comment.isEmpty
          ? null
          : () async {
              // Handle publish logic here
              cubit.upsertMemoryComment(state.memory!, comment);
            },
      child: comment == null || comment.isEmpty
          ?
          // Disable the button if caption is empty
          Text(
              text,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: appTheme.greyColor,
              ),
            )
          :
          // Enable the button if caption is not empty
          Text(
              text,
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 1,
      color: Theme.of(context).dividerColor,
    );
  }

  Widget _buildCommentList(BuildContext context, MemoryCommentCubit cubit,
      MemoryCommentState state) {
    final comments = state.memory?.comments ?? [];
    final users = cubit.parameter.users ?? {};

    if (comments.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const SizedBox.shrink(),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: comments.length,
        itemBuilder: (context, index) {
          final comment = comments[index];
          return _buildCommentItem(context, comment, users, cubit);
        },
      ),
    );
  }

  Widget _buildCommentItem(
      BuildContext context, InteractionModel comment, Map<String, Account> users, MemoryCommentCubit cubit) {
    // Get user info from the userMap
    final userInfo = users[comment.userId];
    final isOwner = cubit.state.accountUuid == comment.userId;

    return GestureDetector(
      onLongPress: isOwner
          ? () async {
              final shouldDelete = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Delete comment'),
                  content: const Text('Are you sure you want to delete this comment?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text('Delete'),
                    ),
                  ],
                ),
              );
              if (shouldDelete == true) {
                cubit.deleteComment(comment);
              }
            }
          : null,
      child: Container(
        padding: padding(top: 8, bottom: 8),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Left part: User avatar
            CircleAvatarCustom(
              imageUrl: userInfo?.photoUrl ?? '',
              size: 48,
              borderColor: appTheme.redE9Color,
            ),
            const SizedBox(width: 12),
            // Middle part: User name and comment content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // User name
                  Text(
                    userInfo?.fullName ?? comment.userName ?? 'Unknown User',
                    style: AppStyle.bold14(),
                  ),
                  const SizedBox(height: 4),
                  // Comment content
                  Text(
                    comment.activityContent ?? '',
                    style: AppStyle.regular14(),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            // Right part: Timestamp
            Text(
              comment.createdAt?.getRelativeTimeFromNow(locale: 'en') ?? '',
              style: AppStyle.regular12(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextBox(BuildContext context, MemoryCommentCubit cubit,
      MemoryCommentState state) {
    final user = cubit.parameter.users?[cubit.parameter.memory?.userId];
    final comment = state.comment ?? '';
    // Keep controller in sync with state
    if (_commentController.text != comment) {
      _commentController.text = comment;
      _commentController.selection = TextSelection.fromPosition(TextPosition(offset: _commentController.text.length));
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircleAvatarCustom(
            imageUrl: user?.photoUrl ?? '',
            size: 48,
            borderColor: appTheme.redE9Color,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(24),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: TextFormField(
                controller: _commentController,
                minLines: 1,
                maxLines: null, // Expands vertically as needed
                decoration: InputDecoration(
                  hintText: 'Leave a comment...',
                  border: InputBorder.none, // Hide the border
                ),
                onChanged: (value) {
                  cubit.updateComment(value);
                },
              ),
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: comment.isNotEmpty
                ? () {
                    cubit.upsertMemoryComment(state.memory!, comment);
                  }
                : null,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
              ),
              padding: const EdgeInsets.all(8),
              child: const Icon(Icons.send),
            ),
          ),
        ],
      ),
    );
  }
}
