import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/memories/memory_comment_screen.dart';
import 'package:family_app/screen/main/memories/memory_filter_cubit.dart';
import 'package:family_app/screen/main/memories/memory_filter_state.dart';
import 'package:family_app/screen/main/memories/memory_upsert_screen.dart';
import 'package:family_app/utils/dialog.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/bottom_sheet/memory_action.dart';
import 'package:family_app/widget/bottom_sheet/memory_action_bts.dart';
import 'package:family_app/widget/expandable_caption.dart';
import 'package:family_app/widget/image_viewer.dart/image_viewer_pager.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

import 'widget/memories_activity_info.dart';

@RoutePage()
class MemoryFilterPage
    extends BaseBlocProvider<MemoryFilterState, MemoryFilterCubit> {
  const MemoryFilterPage({super.key});

  @override
  Widget buildPage() => const MemoryFilterScreen();

  @override
  MemoryFilterCubit createCubit() => MemoryFilterCubit(
        familyRepository: locator.get(),
        uploadRepository: locator.get(),
        activityRepository: locator.get(),
        localStorage: locator.get(),
      );
}

class MemoryFilterScreen extends StatefulWidget {
  const MemoryFilterScreen({super.key});

  @override
  State<MemoryFilterScreen> createState() => _MemoryFilterScreenState();
}

class _MemoryFilterScreenState extends BaseBlocPageState<MemoryFilterScreen,
    MemoryFilterState, MemoryFilterCubit> {
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  final FocusNode _searchFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  late MemoryFilterCubit _cubit;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _cubit = BlocProvider.of<MemoryFilterCubit>(context);
    _searchFocusNode.addListener(() {
      _cubit.setShowHistory(_searchFocusNode.hasFocus ? true : false);
    });
  }

  @override
  void dispose() {
    _searchFocusNode.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // @override
  // Widget build(BuildContext context) {
  //   return PopScope(
  //     canPop: true,
  //     onPopInvokedWithResult: (bool didPop, Object? result) {
  //       if (!didPop) {
  //         if (_cubit.state.modified) {
  //           Navigator.of(context).pop(true);
  //         } else {
  //           Navigator.of(context).pop();
  //         }
  //       }
  //     },
  //     child: super.build(context),
  //   );
  // }

  @override
  bool listenWhen(MemoryFilterState previous, MemoryFilterState current) {
    if (current.status == MemoryFilterStatus.loading) {
      _refreshIndicatorKey.currentState?.show();
    } else if (current.status == MemoryFilterStatus.error) {
      _refreshIndicatorKey.currentState?.deactivate();
      showSimpleToast(current.errorMessage ?? 'Failed to load memories');
    } else if (current.status == MemoryFilterStatus.success) {
      _refreshIndicatorKey.currentState?.deactivate();
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(
      BuildContext context, MemoryFilterCubit c, MemoryFilterState state) {
    // Remove the AppBar entirely
    return SizedBox.shrink();
  }

  @override
  Widget buildBody(
      BuildContext context, MemoryFilterCubit cubit, MemoryFilterState state) {
    if (state.status == MemoryFilterStatus.loading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state.status == MemoryFilterStatus.success) {
      return RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: cubit.fetchMemories,
        child: Column(
          children: [
            _buildSearchBox(cubit, state),
            if (state.showHistory && state.searchHistory.isNotEmpty)
              _buildSearchHistoryDropdown(cubit, state)
            else
              Expanded(child: _buildMemoryView(cubit, state)),
          ],
        ),
      );
    } else if (state.status == MemoryFilterStatus.error) {
      return Center(child: Text(state.errorMessage!));
    } else {
      return Container(); // Or show something else in the initial state
    }
  }

  Widget _buildMemoryView(MemoryFilterCubit cubit, MemoryFilterState state) {
    return state.memories.isEmpty
        ? _buildEmptyView(cubit)
        : _buildMemoryList(cubit, state);
  }

  Widget _buildSearchBox(MemoryFilterCubit cubit, MemoryFilterState state) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(8, 12, 8, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Back icon (outside the search box)
              InkWell(
                onTap: () {
                  if (cubit.state.modified) {
                    Navigator.of(context).pop(true);
                  } else {
                    Navigator.of(context).pop();
                  }
                },
                borderRadius: BorderRadius.circular(24),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                  child: SvgPicture.asset(
                    Assets.icons.iconActionBack.path,
                    width: 24,
                    height: 24,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Search box
              Expanded(
                child: _buildSearchBoxContainer(cubit, state),
              ),
              // Only show the right search icon outside the box if there is NO current search term
              if ((state.searchText ?? '').isEmpty) ...[
                const SizedBox(width: 8),
                InkWell(
                  onTap: () {
                    final searchText = _searchController.text.trim();
                    if (searchText.isNotEmpty) {
                      cubit.fetchMemoriesByFilter(searchText);
                      cubit.setShowHistory(false);
                    }
                  },
                  borderRadius: BorderRadius.circular(24),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: SvgPicture.asset(
                      Assets.icons.iconSearch.path,
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBoxContainer(MemoryFilterCubit cubit, MemoryFilterState state) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFE2E2E2),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Row(
        children: [
          if ((state.searchText ?? '').isNotEmpty) ...[
            SvgPicture.asset(
              Assets.icons.iconSearch.path,
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade400),
                borderRadius: BorderRadius.circular(20),
                color: Colors.white,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    state.searchText!,
                    style: const TextStyle(fontSize: 13, color: Colors.black),
                  ),
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: () {
                      _searchController.clear();
                      cubit.fetchMemoriesByFilter('');
                      cubit.setShowHistory(false);
                    },
                    child: Icon(Icons.close, size: 16, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ] else ...[
            Expanded(
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  hintText: 'Search posts...',
                  border: InputBorder.none,
                  isDense: true,
                ),
                style: TextStyle(fontSize: 16),
                onSubmitted: (text) {
                  if (text.isNotEmpty) {
                    cubit.fetchMemoriesByFilter(text);
                    cubit.setShowHistory(false);
                  }
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchHistoryDropdown(MemoryFilterCubit cubit, MemoryFilterState state) {
    final suggestions = state.suggestions;
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      color: Colors.white,
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Search History',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: state.searchHistory
                    .take(5)
                    .map((text) => InkWell(
                          onTap: () {
                            _searchController.text = text;
                            cubit.fetchMemoriesByFilter(text);
                            cubit.setShowHistory(false);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: const Color(0xFFE2E2E2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              text,
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                        ))
                    .toList(),
              ),
              if (suggestions.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'You may be looking for',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: suggestions
                      .map((suggestion) => InkWell(
                            onTap: () {
                              _searchController.text = suggestion;
                              cubit.fetchMemoriesByFilter(suggestion);
                              cubit.setShowHistory(false);
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE2E2E2),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                suggestion,
                                style: TextStyle(fontSize: 12),
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyView(MemoryFilterCubit c) {
    return InkWell(
      onTap: () async {
        MemoryUpsertBts.show(context).then(
          (result) {
            if (result == true) {
              c.fetchMemories();
            }
          },
        );
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Icon(Icons.list, size: 64),
          const SizedBox(height: 16),
          const Text('No memories found'),
        ],
      ),
    );
  }

  Widget _buildMemoryList(MemoryFilterCubit cubit, MemoryFilterState state) {
    return ListView.builder(
      itemCount: state.memories.length,
      itemBuilder: (context, index) {
        final memory = state.memories[index];
        return MemoryCard(cubit: cubit, state: state, memory: memory);
      },
    );
  }
}

class MemoryCard extends StatefulWidget {
  final MemoryFilterCubit cubit;
  final MemoryFilterState state;
  final MemoryModel memory;

  const MemoryCard(
      {super.key,
      required this.cubit,
      required this.state,
      required this.memory});

  @override
  State<MemoryCard> createState() => _MemoryCardState();
}

class _MemoryCardState extends State<MemoryCard> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  void handleMemorySettings(context, MemoryModel memory) {
    MemoryActionBts.showHeightReturnValue(context).then((action) {
      if (action != null) {
        final memoryAction = action;
        switch (memoryAction.actionType) {
          case ActionType.edit:
            handleEdit(context, memory);
            break;
          case ActionType.delete:
            handleDelete(context, memory);
            break;
        }
      }
    });
  }

  void handleEdit(context, MemoryModel memory) {
    MemoryUpsertBts.show(context, memory: memory).then(
      (result) {
        if (result == true) {
          widget.cubit.fetchMemories();
          widget.cubit.setModified(true);
        }
      },
    );
  }

  void handleDelete(context, MemoryModel memory) async {
    // Show confirmation dialog
    final shouldDelete = await DialogUtils.showConfirmDialog(
      context,
      title: 'Delete post',
      content: 'Are you sure you want to delete this post?',
      confirmText: 'Yes',
      cancelText: 'No',
    );
    if (shouldDelete == true) {
      widget.cubit.deleteMemory(memory);
      widget.cubit.setModified(true);
    }
  }

  GestureTapCallback? onTapCard(
      BuildContext context, List<ImagePagerItem> items) {
    if (widget.memory.userId == widget.state.accountUuid) {
      return () => handleEdit(context, widget.memory);
    } else
      return onTapImage(context, items, 0);
  }

  GestureTapCallback? onTapImage(
      BuildContext context, List<ImagePagerItem> items, int? initialIndex) {
    return () => ImageViewerPager.show(
          context: context,
          items: items,
          initialIndex: initialIndex ?? 0,
          doubleTapZoomable: true,
          swipeDismissible: true,
        );
  }

  @override
  Widget build(BuildContext context) {
    final imageItems = widget.memory.files!
        .map((item) => ImagePagerItem(
              imageUrl: item.fileUrlMd!,
              // title: widget.memory.name ?? '',
              title: '',
              description: widget.memory.caption ?? '',
              dateTime: item.createdAt!.toDateTime(),
              activityId: widget.memory.activityId,
              activityName:
                  widget.cubit.getActivityById(widget.memory.activityId!)?.name,
            ))
        .toList();
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      color: Colors.white,
      child: InkWell(
        // onTap: onTapCard(context, imageItems),
        borderRadius: BorderRadius.circular(16.0),
        child: _buildCardBody(context, widget.cubit, widget.memory, imageItems),
      ),
    );
  }

  Widget _buildCardBody(BuildContext context, MemoryFilterCubit c,
      MemoryModel memory, List<ImagePagerItem> imageItems) {
    return Stack(
      children: [
        Column(
          children: [
            _buildTopRow(context, c, memory),
            _buildPhotoViewer(context, memory, imageItems),
            _buildReactionRow(memory),
            _buildCaption(memory),
            _buildTagInfo(memory),
          ],
        )
      ],
    );
  }

  Widget _buildReactionRow(MemoryModel memory) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Row(
        children: [
          // Heart icon with count
          InkWell(
            onTap: () {
              if (memory.isLiked) {
                // widget.cubit.unlikeMemory(memory);
              } else {
                widget.cubit.likeMemory(memory);
              }
            },
            borderRadius: BorderRadius.circular(4),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Row(
                children: [
                  SvgPicture.asset(
                    memory.isLiked
                        ? Assets.icons.iconHeartActive.path
                        : Assets.icons.iconHeartNormal.path,
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    memory.interact?.length.toString() ?? '0',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Comment icon with count
          InkWell(
            onTap: () {
              MemoryCommentBts.show(context,
                      memory: memory, users: widget.state.users)
                  .then(
                (result) {
                  if (result == true) {
                    widget.cubit.fetchMemories();
                  }
                },
              );
            },
            borderRadius: BorderRadius.circular(4),
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: Row(
                children: [
                  SvgPicture.asset(
                    Assets.icons.iconComment.path,
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    memory.comments?.length.toString() ?? '0',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaption(MemoryModel memory) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Align(
        alignment: Alignment.centerLeft,
        child: ExpandableCaption(
          text: memory.caption ?? '',
          style: const TextStyle(fontSize: 14.0, color: Colors.black),
        ),
      ),
    );
  }

  Widget _buildTagInfo(MemoryModel memory) {
    final tags = memory.extraTags;
    if (tags.isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Wrap(
              alignment: WrapAlignment.start,
              spacing: 8.0,
              runSpacing: 8.0,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: tags
                  .map((tag) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12.0, vertical: 8.0),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE2E2E2),
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Text(
                          tag,
                          style: const TextStyle(
                            color: Color(0xFF595D62),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildPhotoViewer(BuildContext context, MemoryModel memory,
      List<ImagePagerItem> imageItems) {
    return Column(
      children: [
        Container(
          height: 200,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemCount: memory.files?.length ?? 0,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: onTapImage(context, imageItems, index),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: _buildImage(
                        context, widget.cubit, memory.files![index].fileUrlMd!),
                  ),
                ),
              );
            },
          ),
        ),
        // Dot indicators below the photo
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              memory.files?.length ?? 0,
              (index) => GestureDetector(
                onTap: () {
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  child: SvgPicture.asset(
                    index == _currentPage
                        ? Assets.icons.iconDotActive.path
                        : Assets.icons.iconDotInactive.path,
                    width: 8,
                    height: 8,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImage(BuildContext context, MemoryFilterCubit c, String url,
      {double height = 100, double width = double.infinity}) {
    return CachedNetworkImage(
      imageUrl: url,
      height: height,
      width: width,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => Container(
        height: height,
        width: width,
        color: Colors.grey,
        child: const Icon(Icons.error, color: Colors.red),
      ),
    );
  }

  Widget _buildTopRow(
      BuildContext context, MemoryFilterCubit c, MemoryModel memory) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          // Avatar
          CircleAvatarCustom(
            imageUrl: c.getUserInfo(memory.userId!)?.photoUrl ?? '',
            size: 40,
            borderColor: appTheme.borderColorV2,
            borderWidth: 1,
          ),
          const SizedBox(width: 12),
          // User name
          Expanded(
            child: Text(
              c.getUserInfo(memory.userId!)?.fullName ?? '',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Menu icon
          if (memory.userId == widget.state.accountUuid)
            GestureDetector(
              onTap: () => handleMemorySettings(context, memory),
              behavior: HitTestBehavior.opaque,
              child: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                ),
                child: SvgPicture.asset(
                  Assets.icons.iconMenu.path,
                  width: 16,
                  height: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
