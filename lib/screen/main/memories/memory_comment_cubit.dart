import 'dart:convert';
import 'dart:io';

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/interaction_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/data/usecase/model/upsert_memory_activity_param.dart';
import 'package:family_app/data/usecase/model/upsert_memory_param.dart';
import 'package:family_app/screen/main/memories/memory_comment_parameter.dart';
import 'package:family_app/screen/main/memories/memory_comment_state.dart';
import 'package:family_app/utils/log/app_logger.dart';

class MemoryCommentCubit extends BaseCubit<MemoryCommentState> {
  final MemoryCommentParameter parameter;
  final IFamilyRepository familyRepository;
  final IUploadRepository uploadRepository;
  final IActivityRepository activityRepository;
  MemoryCommentCubit(
      {required this.parameter,
      required this.familyRepository,
      required this.uploadRepository,
      required this.activityRepository})
      : super(MemoryCommentState(memory: parameter.memory));
  final AccountService accountService = locator.get();

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();

    initMemoryData();
  }

  Future<void> initMemoryData() async {
    if (parameter.memory != null) {
      final memory = parameter.memory!;
      final accountUuid = accountService.account?.uuid;
      emit(state.copyWith(status: MemoryCommentStatus.loading));
      try {} catch (e) {
        AppLogger.d('fetchActivity error: $e');
        emit(state.copyWith(
            status: MemoryCommentStatus.error,
            errorMessage: 'Failed to fetch activity'));
      } finally {
        emit(state.copyWith(
          status: MemoryCommentStatus.done,
          memory: memory,
          accountUuid: accountUuid,
        ));
      }
    }
  }

  void updateComment(String comment) {
    if (comment.isNotEmpty) {
      emit(state.copyWith(comment: comment));
    } else {
      emit(state.clearComment());
    }
  }

  @override
  Future<void> close() {
    locator.unregister<MemoryCommentCubit>();
    return super.close();
  }

  Future<void> upsertMemoryComment(MemoryModel memory, String comment) async {
    emit(state.copyWith(status: MemoryCommentStatus.loading));
    try {
      final param = UpsertMemoryActivityParam(
        activityType: 'comment',
        activityContent: comment,
        extraData: '',
      );
      AppLogger.d('upsertMemoryComment memoryUuid: ${memory.uuid}, param: $param');
      final interactionModel = await familyRepository.updateMemoryActivity(memory.uuid!, param);
      if (interactionModel.uuid != null) {
        AppLogger.d('Comment upserted successfully.');
        // Update comment list in memory, add to the top of the list
        final updatedMemory = memory.copyWith(
          comments: [interactionModel, ...(memory.comments ?? [])],
        );
        emit(state.clearComment().copyWith(
          status: MemoryCommentStatus.success,
          memory: updatedMemory,
          isChanged: true,
        ));
      } else {
        emit(state.copyWith(
            status: MemoryCommentStatus.error,
            errorMessage: 'Failed to update comment'));
      }
    } catch (e) {
      AppLogger.d('Error upsert comment: $e');
      emit(state.copyWith(
          status: MemoryCommentStatus.error,
          errorMessage: 'Failed to update comment'));
    }
  }

  Future<void> deleteMemoryComment(String commentUuid) async {
    emit(state.copyWith(status: MemoryCommentStatus.loading));
    try {
      final result = await familyRepository.deleteMemoryActivity(commentUuid);
      if (result) {
        AppLogger.d('Comment deleted successfully.');
        // Update comment list in memory, remove the comment
        final updatedMemory = state.memory?.copyWith(
          comments: state.memory?.comments?.where((c) => c.uuid != commentUuid).toList(),
        );
        emit(state.copyWith(
          status: MemoryCommentStatus.success,
          memory: updatedMemory,
          isChanged: true,
        ));
      } else {
        emit(state.copyWith(
            status: MemoryCommentStatus.error,
            errorMessage: 'Failed to delete comment'));
      }
    } catch (e) {
      AppLogger.d('Error delete comment: $e');
      emit(state.copyWith(
          status: MemoryCommentStatus.error,
          errorMessage: 'Failed to delete comment'));
    }
  }

  Future<void> deleteComment(InteractionModel comment) async {
    emit(state.copyWith(status: MemoryCommentStatus.loading));
    try {
      final result = await familyRepository.deleteMemoryActivity(comment.uuid!);
      if (result) {
        AppLogger.d('Comment deleted successfully.');
        // Update comment list in memory, remove the comment
        final updatedMemory = state.memory?.copyWith(
          comments: state.memory?.comments?.where((c) => c.uuid != comment.uuid).toList(),
        );
        emit(state.copyWith(
          status: MemoryCommentStatus.done,
          memory: updatedMemory,
          isChanged: true,
        ));
      } else {
        emit(state.copyWith(
            status: MemoryCommentStatus.error,
            errorMessage: 'Failed to delete comment'));
      }
    } catch (e) {
      AppLogger.d('Error delete comment: $e');
      emit(state.copyWith(
          status: MemoryCommentStatus.error,
          errorMessage: 'Failed to delete comment'));
    }
  }
}
