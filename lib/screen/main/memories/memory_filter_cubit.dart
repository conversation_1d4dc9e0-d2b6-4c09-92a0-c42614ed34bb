import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/interaction_model.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/data/usecase/model/upsert_memory_activity_param.dart';
import 'package:family_app/screen/main/memories/memory_filter_state.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:dartx/dartx.dart';
import 'package:family_app/data/local/local_storage.dart';

class MemoryFilterCubit extends BaseCubit<MemoryFilterState> {
  final IFamilyRepository familyRepository;
  final IUploadRepository uploadRepository;
  final IActivityRepository activityRepository;
  final LocalStorage localStorage;
  MemoryFilterCubit(
      {required this.familyRepository,
      required this.uploadRepository,
      required this.activityRepository,
      required this.localStorage})
      : super(MemoryFilterState());
  final AccountService accountService = locator.get();

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();

    fetchMemories();
    setShowHistory(true);
  }

  @override
  Future<void> close() {
    locator.unregister<MemoryFilterCubit>();
    return super.close();
  }

  Future<void> fetchMemories() async {
    emit(state.copyWith(status: MemoryFilterStatus.loading));
    try {
      final accountUuid = accountService.account?.uuid;
      final activityList =
          await activityRepository.getAllActivities(accountService.familyId);
      // Create a map of activity.uuid to activity
      final activityMap = {
        for (var activity in activityList) activity.uuid: activity
      };

      final memoryList = await familyRepository
          .getMemoryListByFamily(accountService.familyId, type: 'list');

      // Fetch user data for all unique user IDs
      final userMap = <String, Account>{};
      final uniqueUserIds =
          memoryList.map((m) => m.userId).where((id) => id != null).toSet();

      for (final userId in uniqueUserIds) {
        try {
          final user = await familyRepository.getUserInfo(userId!);
          userMap[userId] = user;
        } catch (e) {
          AppLogger.e("Error fetching user info for $userId: $e");
        }
      }

      // Update isLiked & extraTags for each memory
      final updatedMemories = <MemoryModel>[];
      for (final memory in memoryList) {
        updatedMemories
            .add(memory.copyWith(isLiked: isMemoryLikedByMe(memory), extraTags: buildExtraTags(activityMap, memory)));
      }


      final filteredMemories = updatedMemories
          .where((m) => isMemoryMatchSearchText(m, state.searchText))
          .toList();

      final searchHistory = await loadSearchHistory();

      final suggestions = await buildSuggestions(filteredMemories);

      emit(state.copyWith(
          status: MemoryFilterStatus.success,
          memories: filteredMemories,
          activities: activityMap,
          users: userMap,
          accountUuid: accountUuid,
          searchHistory: searchHistory,
          suggestions: suggestions));
    } catch (e) {
      //emit(MemoriesError(message: "Failed to load memories"));
      AppLogger.e("fetchMemories error: $e");
      emit(state.copyWith(
          status: MemoryFilterStatus.error,
          errorMessage: 'Failed to load memories'));
    }
    AppLogger.d("fetchMemories done");
  }

  static const int MAX_SEARCH_HISTORY_COUNT = 5;
  Future<void> fetchMemoriesByFilter(String? searchText) async {
    emit(state.copyWith(status: MemoryFilterStatus.loading));
    try {
      final memoryList = state.memories;
      AppLogger.d(
          'fetchMemoriesByFilter memoryList length: ${memoryList.length}');

      final filteredMemories = memoryList
          .where((m) => isMemoryMatchSearchText(m, searchText))
          .toList();

      final updatedHistory = List<String>.from(state.searchHistory);
      if (searchText?.isNotEmpty ?? false) {
        updatedHistory.remove(searchText);
        updatedHistory.insert(0, searchText!);
        if (updatedHistory.length > MAX_SEARCH_HISTORY_COUNT) {
          updatedHistory.removeRange(MAX_SEARCH_HISTORY_COUNT, updatedHistory.length);
        }
        await saveSearchHistory(updatedHistory);
      }

      emit(state.copyWith(
        status: MemoryFilterStatus.success,
        memories: filteredMemories,
        searchText: searchText,
        searchHistory: updatedHistory,
      ));
    } catch (e) {
      //emit(MemoriesError(message: "Failed to load memories"));
      AppLogger.e("fetchMemoriesByFilter  error: $e");
      emit(state.copyWith(
          status: MemoryFilterStatus.error,
          errorMessage: 'Failed to load memories'));
    }
    AppLogger.d("fetchMemoriesByFilter done");
  }

  ActivityModel? getActivityById(String activityId) {
    return state.activities[activityId];
  }

  Account? getUserInfo(String userId) {
    return state.users[userId];
  }

  bool isMemoryLikedByMe(MemoryModel memory) {
    final interaction = memory.interact
        ?.where((i) => i.userId == accountService.account?.uuid)
        .firstOrNull;
    return interaction?.activityContent == 'like';
  }

  Future<void> likeMemory(MemoryModel memory) async {
    try {
      final param = UpsertMemoryActivityParam(
        activityType: 'interact',
        activityContent: 'like',
        extraData: '',
      );

      final interactionModel =
          await familyRepository.updateMemoryActivity(memory.uuid!, param);

      if (interactionModel.uuid != null) {
        AppLogger.d("Like interaction successfully: ${interactionModel.uuid}");
        // Update the memory in the state with the new interaction
        final updatedMemories = state.memories.map((m) {
          if (m.uuid == memory.uuid) {
            final updatedInteract =
                List<InteractionModel>.from(m.interact ?? []);
            // Remove existing like from current user if exists
            updatedInteract.removeWhere((i) =>
                i.userId == accountService.account?.uuid &&
                i.activityContent == 'like');
            // Add new like interaction to the top of the list
            updatedInteract.insert(0, interactionModel);

            return m.copyWith(
              interact: updatedInteract,
              isLiked: true,
            );
          }
          return m;
        }).toList();

        emit(state.copyWith(memories: updatedMemories));
      }
    } catch (e) {
      AppLogger.e("Error liking memory: $e");
    }
  }

  Future<void> unlikeMemory(MemoryModel memory) async {
    try {
      // Remove the like interaction
      // Loop through the interact list and find all like interactions
      final likeInteractions = memory.interact?.where((i) => i.userId == accountService.account?.uuid && i.activityContent == 'like').toList();
      final interactionIdToDelete = <String>[];
      for (final interaction in likeInteractions ?? []) {
        final result = await familyRepository.deleteMemoryActivity(interaction.uuid!);
        if (result) {
          AppLogger.d("Unlike interaction successfully: ${interaction.uuid}");
          // Store the interaction id to a list to delete later
          interactionIdToDelete.add(interaction.uuid!);
        }
      }
      // Remove the like interaction from the state
      final updatedMemories = state.memories.map((m) {
        if (m.uuid == memory.uuid) {
          final updatedInteract = List<InteractionModel>.from(m.interact ?? []);
          updatedInteract.removeWhere((i) => interactionIdToDelete.contains(i.uuid));
          return m.copyWith(interact: updatedInteract, isLiked: false);
        }
        return m;
      }).toList();
      emit(state.copyWith(memories: updatedMemories));
    } catch (e) {
      AppLogger.e("Error unliking memory: $e");
    }
  }

  Future<void> deleteMemory(MemoryModel memory) async {
    emit(state.copyWith(status: MemoryFilterStatus.loading));
    final result = await familyRepository.deleteMemory(memory.uuid!);
    if (result) {
      fetchMemories();
      emit(state.copyWith(status: MemoryFilterStatus.success));
    } else {
      emit(state.copyWith(
          status: MemoryFilterStatus.error,
          errorMessage: 'Failed to delete post'));
    }
  }

  bool isMemoryMatchSearchText(MemoryModel memory, String? searchText) {
    if (searchText == null || searchText.isEmpty) return true;
    return memory.caption?.toLowerCase().contains(searchText.toLowerCase()) ??
        false;
  }

  Future<List<String>> loadSearchHistory() async {
    try {
      return localStorage.searchHistory;
    } catch (e) {
      AppLogger.e('Error loading search history: $e');
    }
    return [];
  }

  Future<void> saveSearchHistory(List<String> searchHistory) async {
    try {
      await localStorage.cacheSearchHistory(searchHistory);
    } catch (e) {
      AppLogger.e('Error saving search history: $e');
    }
  }

  void setShowHistory(bool? value) {
    emit(state.copyWith(showHistory: value));
  }

  void setModified(bool value) {
    emit(state.copyWith(modified: value));
  }


  Future<List<String>> buildSuggestions(List<MemoryModel> memories) async {
    final Set<String> uniqueSuggestions = {};
    for (final memory in memories) {
      if (memory.caption != null && memory.caption!.isNotEmpty) {
        if (uniqueSuggestions.add(memory.caption!)) {
          if (uniqueSuggestions.length == 10) break;
        }
      }
    }
    return uniqueSuggestions.toList();
  }

  List<String> buildExtraTags(Map<String, ActivityModel> activityMap, MemoryModel memory) {
    final extraTags = <String>[];
    final activity = activityMap[memory.activityId!];
    if (activity != null && activity.name != null && activity.name!.isNotEmpty) {
      extraTags.add(activity.name!);
    }
    // Add date
    final date = DateFormat('d MMM yyyy')
        .format(memory.createdAt?.toDateTime() ?? DateTime.now())
        .toUpperCase();
    if (date.isNotEmpty) {
      extraTags.add(date);
    }
    final tags = memory.tags;
    if (tags != null && tags.isNotEmpty) {
      final tagsList = tags.split(',');
      for (final tag in tagsList) {
        if (tag.isNotEmpty) {
          extraTags.add(tag);
        }
      }
    }
    // Add location
    return extraTags;
  }
}
