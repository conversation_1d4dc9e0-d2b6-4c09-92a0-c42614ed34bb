import 'dart:io';

import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/memory_model.dart';

enum MemoryUpsertStatus { initial, loading, done, success, error }

class MemoryUpsertState extends BaseState {
  final MemoryUpsertStatus status;
  final MemoryModel? memory;
  final List<File> images;
  final String? caption;
  final String? errorMessage;
  final ActivityModel? activity;
  final bool isModified;

  MemoryUpsertState({
    this.status = MemoryUpsertStatus.initial,
    this.memory,
    this.images = const [],
    this.caption,
    this.errorMessage,
    this.activity,
    this.isModified = false,
  });

  @override
  List<Object?> get props => [status, memory, images, caption, errorMessage, activity];

  MemoryUpsertState copyWith({
    MemoryUpsertStatus? status,
    MemoryModel? memory,
    List<File>? images,
    String? caption,
    String? errorMessage,
    ActivityModel? activity,
    bool? isModified,
  }) {
    return MemoryUpsertState(
      status: status ?? this.status,
      memory: memory ?? this.memory,
      images: images ?? this.images,
      caption: caption ?? this.caption,
      errorMessage: errorMessage ?? this.errorMessage,
      activity: activity ?? this.activity,
      isModified: isModified ?? this.isModified,
    );
  }

  MemoryUpsertState clearActivity() {
    return MemoryUpsertState(
      status: status,
      memory: memory,
      images: images,
      caption: caption,
      errorMessage: errorMessage,
      isModified: true,
      activity: null,
    );
  }
}
