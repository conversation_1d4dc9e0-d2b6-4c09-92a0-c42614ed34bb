import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/account.dart';

enum MemoryFilterStatus { initial, loading, success, error }

class MemoryFilterState extends BaseState {
  final MemoryFilterStatus status;
  final List<MemoryModel> memories;
  final Map<String, ActivityModel> activities;
  final Map<String, Account> users;
  final String? accountUuid;
  final String? errorMessage;
  final String? searchText;
  final List<String> searchHistory;
  final bool showHistory;
  final List<String> suggestions;
  final bool modified;

  MemoryFilterState({
    this.status = MemoryFilterStatus.initial,
    this.memories = const [],
    this.activities = const {},
    this.users = const {},
    this.accountUuid,
    this.errorMessage,
    this.searchText,
    this.searchHistory = const [],
    this.showHistory = false,
    this.suggestions = const [],
    this.modified = false,
  });

  MemoryFilterState copyWith({
    MemoryFilterStatus? status,
    List<MemoryModel>? memories,
    Map<String, ActivityModel>? activities,
    Map<String, Account>? users,
    String? accountUuid,
    String? errorMessage,
    String? searchText,
    List<String>? searchHistory,
    bool? showHistory,
    List<String>? suggestions,
    bool? modified,
  }) {
    return MemoryFilterState(
      status: status ?? this.status,
      memories: memories ?? this.memories,
      activities: activities ?? this.activities,
      users: users ?? this.users,
      accountUuid: accountUuid ?? this.accountUuid,
      errorMessage: errorMessage ?? this.errorMessage,
      searchText: searchText ?? this.searchText,
      searchHistory: searchHistory ?? this.searchHistory,
      showHistory: showHistory ?? this.showHistory,
      suggestions: suggestions ?? this.suggestions,
      modified: modified ?? this.modified,
    );
  }

  MemoryFilterState clearSearchText() {
    return MemoryFilterState(
      status: status,
      memories: memories,
      activities: activities,
      users: users,
      errorMessage: errorMessage,
      accountUuid: accountUuid,
      searchText: null,
      searchHistory: searchHistory,
      showHistory: showHistory,
      suggestions: suggestions,
      modified: modified,
    );
  }

  @override
  List<Object?> get props => [
        status,
        memories,
        activities,
        users,
        accountUuid,
        errorMessage,
        searchText,
        searchHistory,
        showHistory,
        suggestions,
        modified,
      ];
}
