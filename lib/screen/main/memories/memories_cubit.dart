import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/account.dart';
import 'package:family_app/data/model/interaction_model.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/data/repository/upload/iupload_repository.dart';
import 'package:family_app/data/usecase/model/upsert_memory_activity_param.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:dartx/dartx.dart';

import 'memories_state.dart';
// Assuming you have a Memory model

class MemoriesCubit extends BaseCubit<MemoriesState> {
  final IFamilyRepository familyRepository;
  final IUploadRepository uploadRepository;
  final IActivityRepository activityRepository;
  MemoriesCubit({required this.familyRepository, required this.uploadRepository, required this.activityRepository})
      : super(MemoriesState());
  final AccountService accountService = locator.get();

  @override
  void onInit() async {
    locator.registerSingleton(this);
    super.onInit();

    fetchMemories();
  }

  @override
  Future<void> close() {
    locator.unregister<MemoriesCubit>();
    return super.close();
  }

  Future<void> fetchMemories() async {
    emit(state.copyWith(status: MemoriesStatus.loading));
    try {
      final accountUuid = accountService.account?.uuid;
      final activityList = await activityRepository.getAllActivities(accountService.familyId);
      // Create a map of activity.uuid to activity
      final activityMap = {for (var activity in activityList) activity.uuid: activity};

      final memoryList = await familyRepository.getMemoryListByFamily(accountService.familyId, type: 'list');

      // Fetch user data for all unique user IDs
      final userMap = <String, Account>{};
      final uniqueUserIds = memoryList.map((m) => m.userId).where((id) => id != null).toSet();

      for (final userId in uniqueUserIds) {
        try {
          final user = await familyRepository.getUserInfo(userId!);
          userMap[userId] = user;
        } catch (e) {
          AppLogger.e("Error fetching user info for $userId: $e");
        }
      }

      // Update isLiked for each memory
      final updatedMemories = <MemoryModel>[];
      for (final memory in memoryList) {
        updatedMemories
            .add(memory.copyWith(isLiked: isMemoryLikedByMe(memory), extraTags: buildExtraTags(activityMap, memory)));
      }

      emit(state.copyWith(
          status: MemoriesStatus.success,
          memories: updatedMemories,
          activities: activityMap,
          users: userMap,
          accountUuid: accountUuid));
    } catch (e) {
      //emit(MemoriesError(message: "Failed to load memories"));
      AppLogger.e("fetchMemories error: $e");
      emit(state.copyWith(status: MemoriesStatus.error, errorMessage: 'Failed to load memories'));
    }
    AppLogger.d("fetchMemories done");
  }

  ActivityModel? getActivityById(String activityId) {
    return state.activities[activityId];
  }

  Account? getUserInfo(String userId) {
    return state.users[userId];
  }

  bool isMemoryLikedByMe(MemoryModel memory) {
    final interaction = memory.interact?.where((i) => i.userId == accountService.account?.uuid).firstOrNull;
    return interaction?.activityContent == 'like';
  }

  Future<void> likeMemory(MemoryModel memory) async {
    try {
      final param = UpsertMemoryActivityParam(
        activityType: 'interact',
        activityContent: 'like',
        extraData: '',
      );

      final interactionModel = await familyRepository.updateMemoryActivity(memory.uuid!, param);

      if (interactionModel.uuid != null) {
        AppLogger.d("Like interaction successfully: ${interactionModel.uuid}");
        // Update the memory in the state with the new interaction
        final updatedMemories = state.memories.map((m) {
          if (m.uuid == memory.uuid) {
            final updatedInteract = List<InteractionModel>.from(m.interact ?? []);
            // Remove existing like from current user if exists
            updatedInteract.removeWhere((i) => i.userId == accountService.account?.uuid && i.activityContent == 'like');
            // Add new like interaction to the top of the list
            updatedInteract.insert(0, interactionModel);

            return m.copyWith(
              interact: updatedInteract,
              isLiked: true,
            );
          }
          return m;
        }).toList();

        emit(state.copyWith(memories: updatedMemories));
      }
    } catch (e) {
      AppLogger.e("Error liking memory: $e");
    }
  }

  Future<void> unlikeMemory(MemoryModel memory) async {
    try {
      // Remove the like interaction
      // Loop through the interact list and find all like interactions
      final likeInteractions = memory.interact
          ?.where((i) => i.userId == accountService.account?.uuid && i.activityContent == 'like')
          .toList();
      final interactionIdToDelete = <String>[];
      for (final interaction in likeInteractions ?? []) {
        final result = await familyRepository.deleteMemoryActivity(interaction.uuid!);
        if (result) {
          AppLogger.d("Unlike interaction successfully: ${interaction.uuid}");
          // Store the interaction id to a list to delete later
          interactionIdToDelete.add(interaction.uuid!);
        }
      }
      // Remove the like interaction from the state
      final updatedMemories = state.memories.map((m) {
        if (m.uuid == memory.uuid) {
          final updatedInteract = List<InteractionModel>.from(m.interact ?? []);
          updatedInteract.removeWhere((i) => interactionIdToDelete.contains(i.uuid));
          return m.copyWith(interact: updatedInteract, isLiked: false);
        }
        return m;
      }).toList();
      emit(state.copyWith(memories: updatedMemories));
    } catch (e) {
      AppLogger.e("Error unliking memory: $e");
    }
  }

  Future<void> deleteMemory(MemoryModel memory) async {
    emit(state.copyWith(status: MemoriesStatus.loading));
    final result = await familyRepository.deleteMemory(memory.uuid!);
    if (result) {
      fetchMemories();
      emit(state.copyWith(status: MemoriesStatus.success));
    } else {
      emit(state.copyWith(status: MemoriesStatus.error, errorMessage: 'Failed to delete post'));
    }
  }

  List<String> buildExtraTags(Map<String, ActivityModel> activityMap, MemoryModel memory) {
    final extraTags = <String>[];
    final activity = activityMap[memory.activityId!];
    if (activity != null && activity.name != null && activity.name!.isNotEmpty) {
      extraTags.add(activity.name!);
    }
    // Add date
    final date = DateFormat('d MMM yyyy').format(memory.createdAt?.toDateTime() ?? DateTime.now()).toUpperCase();
    if (date.isNotEmpty) {
      extraTags.add(date);
    }
    final tags = memory.tags;
    if (tags != null && tags.isNotEmpty) {
      final tagsList = tags.split(',');
      for (final tag in tagsList) {
        if (tag.isNotEmpty) {
          extraTags.add(tag);
        }
      }
    }
    // Add location
    return extraTags;
  }
}
