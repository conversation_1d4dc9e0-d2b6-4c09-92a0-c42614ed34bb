import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/memory_model.dart';
import 'package:family_app/data/model/account.dart';

enum MemoriesStatus { initial, loading, success, error }

class MemoriesState extends BaseState {
  final MemoriesStatus status;
  final List<MemoryModel> memories;
  final Map<String, ActivityModel> activities;
  final Map<String, Account> users;
  final String? errorMessage;
  final String? accountUuid;

  MemoriesState({
    this.status = MemoriesStatus.initial,
    this.memories = const [],
    this.activities = const {},
    this.users = const {},
    this.errorMessage,
    this.accountUuid,
  });

  MemoriesState copyWith({
    MemoriesStatus? status,
    List<MemoryModel>? memories,
    Map<String, ActivityModel>? activities,
    Map<String, Account>? users,
    String? errorMessage,
    String? accountUuid,
  }) {
    return MemoriesState(
      status: status ?? this.status,
      memories: memories ?? this.memories,
      activities: activities ?? this.activities,
      users: users ?? this.users,
      errorMessage: errorMessage ?? this.errorMessage,
      accountUuid: accountUuid ?? this.accountUuid,
    );
  }

  @override
  List<Object?> get props =>
      [status, memories, activities, users, errorMessage, accountUuid];
}
