import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/memory_model.dart';

enum MemoryCommentStatus { initial, loading, done, success, error }

class MemoryCommentState extends BaseState {
  final MemoryCommentStatus status;
  final MemoryModel? memory;
  final String? accountUuid;
  final String? comment;
  final String? errorMessage;
  final bool isChanged;

  MemoryCommentState({
    this.status = MemoryCommentStatus.initial,
    this.memory,
    this.accountUuid,
    this.comment,
    this.errorMessage,
    this.isChanged = false,
  });

  @override
  List<Object?> get props => [status, memory, comment, errorMessage];

  MemoryCommentState copyWith({
    MemoryCommentStatus? status,
    MemoryModel? memory,
    String? accountUuid,
    String? comment,
    String? errorMessage,
    bool? isChanged,
  }) {
    return MemoryCommentState(
      status: status ?? this.status,
      memory: memory ?? this.memory,
      accountUuid: accountUuid ?? this.accountUuid,
      comment: comment ?? this.comment,
      errorMessage: errorMessage ?? this.errorMessage,
      isChanged: isChanged ?? this.isChanged,
    );
  }

  MemoryCommentState clearComment() {
    return MemoryCommentState(
      status: status,
      memory: memory,
      accountUuid: accountUuid,
      comment: null,
      errorMessage: errorMessage,
      isChanged: isChanged,
    );
  }
}
