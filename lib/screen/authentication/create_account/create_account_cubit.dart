import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../base/widget/cubit/base_cubit.dart';
import '../../../config/lang/locale_keys.g.dart';
import '../../../config/service/account_service.dart';
import '../../../data/repository/family/ifamily_repository.dart';
import '../../../utils/flash/toast.dart';
import '../../../utils/loading.dart';
import '../../../utils/log/app_logger.dart';
import '../../../utils/upload.dart';
import '../../../widget/image/image_picker_handler.dart';
import '../../../widget/textfield/text_field_node.dart';
import 'create_account_state.dart';

class CreateAccountCubit extends BaseCubit<CreateAccountState> {

  final AccountService accountService;
  final IFamilyRepository familyRepository;

  CreateAccountCubit({
    required this.accountService,
    required this.familyRepository,
  }) : super(CreateAccountState());

  late TextFieldHandler name;
  late FormTextFieldHandler formHandler;
  @override
  Future<void> onInit() async {
    super.onInit();

    name = TextFieldHandler(
      field: 'name',
      title: LocaleKeys.your_name.tr(),
      inputType: TextInputType.name,
      hintText: LocaleKeys.your_name.tr(),
      errorText: (value) => LocaleKeys.family_name_invalid_text.tr(),
      isRequired: true,
    );
    formHandler = FormTextFieldHandler(handlers: [name], validateForm: onSubmit);

    await accountService.initMyProfile();

    DateTime? birthday = accountService.account?.birthday?.convertDateTime;
    emit(state.copyWith(
      birthday: birthday,
    ));


  }

  @override
  Future<void> close() {
    return super.close();
  }


  void onUpdateAvatar() async {
    try {
      showLoading();

      final pickedFile = await ImagePickerHandler.onGetImage();

      if (pickedFile != null) {
        emit(state.copyWith(imageFile: pickedFile));
      }
    } catch (e) {
      showSimpleToast(LocaleKeys.update_fail_text.tr());
    } finally {
      dismissLoading();
    }
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    if (name.text.isEmpty) {
      showSimpleToast(LocaleKeys.please_select_an_image_text.tr());
      return;
    }
    onSaveChange();
  }


  onTapChangeBirthday(BuildContext context) async {
    final date = state.birthday;
    final now = DateTime.now();
    final DateTime? datePicked = await showDatePicker(
      context: context,
      initialDate: date ?? now,
      firstDate: DateTime(now.year - 150),
      lastDate: DateTime(now.year + 1),
    );
    if (datePicked != null) {
      emit(state.copyWith(birthday: datePicked));
    }
  }

  onSaveChange() async {
    emit(state.copyWith(status: CreateAccountStatus.loading));
    showLoading();
    final accountUuid = accountService.account?.uuid ?? '';
    try {
      if (state.imageFile != null) {
        final upload = Upload(familyId: accountUuid);
        final storageModel = await upload.uploadImage(state.imageFile!, "profile");

        if (storageModel.uuid != null) {
          logd('Image uploaded successfully.');
          // return storageModel;
        } else {
          logd('Image upload failed.');
        }

        //Update avatar name
        final result = await familyRepository.updateAvatarS3(accountUuid, fileUuid: storageModel.uuid!);
        if (!result) {
          emit(state.copyWith(status: CreateAccountStatus.error));
          return;
        }
        logd("updateFamilyInfo storageModel.uuid: ${storageModel.uuid}");
      }

      // Map<String, dynamic> data = {};
      // if (state.birthday != null) {
      //   data['birthday'] = state.birthday!.yyyy_MM_dd;
      // }
      // if (state.isShowYear) {
      //   data['show_year'] = state.isShowYear ? "true" : "false";
      // }
      // if (name.text.isNotEmpty) {
      //   data['full_name'] = name.text;
      // }
      //
      // if (data.isNotEmpty) {
      //
      // }

      final result = await familyRepository.updateProfileById(
        accountUuid,
        familyName: accountService.account?.familyName ?? '',
        full_name: name.text,
        birthday: state.birthday?.yyyy_MM_dd,
      );
      logd("updateProfileInfo result: $result");
      // accountService.setAccount(result);
      accountService.initMyProfile();

      emit(state.copyWith(status: CreateAccountStatus.success));
    } catch (e) {
      emit(state.copyWith(status: CreateAccountStatus.error));
    } finally {
      dismissLoading();
    }

  }

  void onChangedName(String name) {
    if (name.isNotEmpty) {
      emit(state.copyWith());
    } else {
      emit(state.copyWith());
    }
  }

}
