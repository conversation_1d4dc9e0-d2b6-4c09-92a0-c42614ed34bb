import 'dart:io';

import '../../../base/widget/cubit/base_state.dart';

enum CreateAccountStatus {
  none,
  loading,
  success,
  error,
}

class CreateAccountState extends BaseState {
  final File? imageFile;
  final CreateAccountStatus status;
  final DateTime? birthday;
  final String? errorMessage;

  CreateAccountState({
    this.imageFile,
    this.status = CreateAccountStatus.none,
    this.birthday,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [imageFile, status, birthday, errorMessage];

  CreateAccountState copyWith({
    File? imageFile,
    CreateAccountStatus? status,
    DateTime? birthday,
    String? errorMessage,
  }) {
    return CreateAccountState(
      status: status ?? this.status,
      imageFile: imageFile ?? this.imageFile,
      birthday: birthday ?? this.birthday,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

}
