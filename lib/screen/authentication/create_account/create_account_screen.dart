import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../base/widget/cubit/base_bloc_page.dart';
import '../../../base/widget/cubit/base_bloc_provider.dart';
import '../../../config/lang/locale_keys.g.dart';
import '../../../config/service/app_service.dart';
import '../../../config/theme/style/style_theme.dart';
import '../../../extension.dart';
import '../../../gen/assets.gen.dart';
import '../../../main.dart';
import '../../../utils/flash/toast.dart';
import '../../../widget/appbar_custom.dart';
import '../../../widget/image/circle_avatar_custom.dart';
import '../../../widget/primary_button.dart';
import '../../../widget/switch.dart';
import '../../../widget/textfield/row_text_field_view.dart';
import '../../../widget/textfield/title_text_field.dart';
import '../../../widget/textfield/title_text_field_v2.dart';
import '../../account_edit/account_edit_cubit.dart';
import '../../account_edit/account_edit_state.dart';
import 'create_account_cubit.dart';
import 'create_account_state.dart';

@RoutePage()
class CreateAccountPage extends BaseBlocProvider<CreateAccountState, CreateAccountCubit> {
  const CreateAccountPage({super.key});

  @override
  Widget buildPage() => const CreateAccountView();

  @override
  CreateAccountCubit createCubit() => CreateAccountCubit(
    accountService: locator.get(),
    familyRepository: locator.get(),
  );
}

class CreateAccountView extends StatefulWidget {
  const CreateAccountView({super.key});

  @override
  State<CreateAccountView> createState() => _CreateAccountViewState();
}

class _CreateAccountViewState extends BaseBlocNoAppBarPageState<CreateAccountView, CreateAccountState, CreateAccountCubit> {
  // @override
  // Color get backgroundColor => appTheme.whiteText;

  @override
  String get title => LocaleKeys.create_account.tr();

  @override
  bool get isSafeArea => true;

  @override
  Widget buildAppBar(BuildContext context, CreateAccountCubit cubit, CreateAccountState state) {
    return CustomAppBar2(
      title: title,
      showBack: true,
      onBack: () {
        context.router.replaceAll([const HomeRoute()]);
      },
    );
  }

  @override
  bool listenWhen(CreateAccountState previous, CreateAccountState current) {
    if (previous.status != current.status) {
      switch (current.status) {
        case CreateAccountStatus.success:
          context.router.replaceAll([const HomeRoute()]);
          break;
        case CreateAccountStatus.error:
          break;
        case CreateAccountStatus.none:
          break;
        case CreateAccountStatus.loading:
          break;
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, CreateAccountCubit cubit, CreateAccountState state) {
    return Stack(
      children: [
        SingleChildScrollView(
          child: Container(
            margin: const EdgeInsets.only(top: 16, left: 8, right: 8),
            padding: padding(all: 16),
            decoration: BoxDecoration(
              color: appTheme.backgroundWhite,
              borderRadius: const BorderRadius.all(Radius.circular(20)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: cubit.onUpdateAvatar,
                    behavior: HitTestBehavior.opaque,
                    child: Stack(
                      children: [
                        CircleAvatarCustom(
                          imageUrl: cubit.accountService.account?.photoUrl ?? '',
                          color: appTheme.gray2Color,
                          borderWidth: 3.w,
                          size: 80,
                          imageFile: state.imageFile,
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: InkWell(
                            onTap: cubit.onUpdateAvatar,
                            child: Assets.icons.icTakePhoto.svg(
                              width: 32.w,
                              height: 32.h,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20.h),
                // RowTextFieldView(
                //   handler: cubit.email,
                //   topPadding: 0,
                //   bottomSizedBox: 0,
                //   formHandler: cubit.form,
                //   border: Border.all(color: appTheme.borderColor),
                // ),
                // SizedBox(height: 24.h),
                // Text(LocaleKeys.your_name.tr(), style: AppStyle.regular14V2(color: appTheme.grayV2)),
                // TextField(
                //   controller: cubit.textEditingController,
                //   decoration: InputDecoration(
                //     hintText: LocaleKeys.username.tr(),
                //     hintStyle: AppStyle.regular14V2(color: appTheme.hintColor),
                //     filled: true,
                //     fillColor: appTheme.whiteText,
                //     border: OutlineInputBorder(
                //       borderRadius: BorderRadius.circular(8),
                //       borderSide: BorderSide(color: appTheme.hintColor, width: 1),
                //     ),
                //     focusedBorder: OutlineInputBorder(
                //       borderRadius: BorderRadius.circular(8),
                //       borderSide: BorderSide(color: appTheme.primaryColorV2, width: 1),
                //     ),
                //     enabledBorder: OutlineInputBorder(
                //       borderRadius: BorderRadius.circular(8),
                //       borderSide: BorderSide(color: appTheme.hintColor, width: 1),
                //     ),
                //   ),
                //   onChanged: (e) {
                //     cubit.onChangedName(e);
                //   },
                // ),
                _buildTextBox(
                  fieldNode: cubit.name,
                  validatedForm: cubit.formHandler.formValidated,
                  hintText: LocaleKeys.your_name.tr(),
                ),
                SizedBox(height: 24.h),
                Text(LocaleKeys.date_of_birth.tr(), style: AppStyle.regular14V2(color: appTheme.grayV2)),
                Container(
                  padding: padding(horizontal: 16, vertical: 16),
                  decoration: BoxDecoration(
                    color: appTheme.whiteText,
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                    border: Border.all(color: appTheme.borderColorV2, width: 1),
                  ),
                  child: InkWell(
                    // onTap: () => DatePickerBottomSheet.show(
                    //   context,
                    //   title: title,
                    //   onSelected: cubit.onChangeBirthday,
                    //   initialDate: state.birthday,
                    // ),
                    onTap: () => cubit.onTapChangeBirthday(context),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            state.birthday?.ddMMyy ?? 'dd / MM / yyyy',
                            style: AppStyle.regular14V2(color: state.birthday != null ? appTheme.blackText : appTheme.hintColor),
                          ),
                        ),
                        SvgPicture.asset(
                          Assets.icons.icCalendar32.path,
                          width: 24.w,
                          height: 24.h,
                          colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 32.h),
                PrimaryButton(
                  text: LocaleKeys.next.tr(),
                  backgroundColor: appTheme.primaryColorV2,
                  borderRadius: 8,
                  onTap: () => cubit.formHandler.onSubmit(),
                  // isActive: state.isChanging,
                  isLoading: state.status == AccountEditStatus.loading,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }


  Widget _buildTextBox({
    required dynamic fieldNode,
    required ValueNotifier<bool> validatedForm,
    required String hintText,
    int maxLine = 1,
  }) {
    return ValueListenableBuilder(
      valueListenable: validatedForm,
      builder: (context, isValidate, _) => TitleTextFieldV2(
        fieldNode: fieldNode,
        validatedForm: isValidate,
        hintText: hintText,
        maxLine: maxLine,
        showTitle: true,
      ),
    );
  }


}