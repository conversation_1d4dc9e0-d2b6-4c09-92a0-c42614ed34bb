import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/authentication/auth/auth_cubit.dart';
import 'package:family_app/screen/authentication/auth/auth_state.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/loading.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart' show SvgPicture;

@RoutePage()
class AuthPage extends BaseBlocProvider<AuthState, AuthCubit> {
  const AuthPage({super.key});

  @override
  Widget buildPage() => const AuthView();

  @override
  AuthCubit createCubit() => AuthCubit(verifyAccountUsecase: locator.get(), googleSignInUsecase: locator.get(), appleSignInUsecase: locator.get());
}

class AuthView extends StatefulWidget {
  const AuthView({super.key});

  @override
  State<AuthView> createState() => _AuthViewState();
}

class _AuthViewState extends BaseBlocNoAppBarPageState<AuthView, AuthState, AuthCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get isSafeArea => false;

  @override
  bool get isResizeToAvoidBottomInset => false;

  @override
  bool listenWhen(AuthState previous, AuthState current) {
    if (previous.status != current.status) {
      dismissLoading();
      switch (current.status) {
        case AuthStatus.none:
          break;
        case AuthStatus.loading:
          showLoading();
          break;
        case AuthStatus.loginSuccess:
          context.router.replaceAll([const HomeRoute()]);
          break;
        case AuthStatus.signUpFamily:
          context.pushRoute(const SignUpFamilyRoute());
          break;
        case AuthStatus.error:
          showSimpleToast(LocaleKeys.an_error_occurred_text.tr());
          break;
        case AuthStatus.emailCantCheck:
          showSimpleToast(LocaleKeys.email_does_not_exist_text.tr());
          break;
        case AuthStatus.invalidUsernameOrPassword:
          showSimpleToast(LocaleKeys.invalid_username_or_password.tr());
          break;
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, AuthCubit cubit, AuthState state) {
    return Padding(
      padding: paddingV2(all: 24).add(EdgeInsets.only(bottom: context.bottom, top: context.top)),
      child: Column(mainAxisAlignment: MainAxisAlignment.end, children: [
        SvgPicture.asset(Assets.images.logo.path, width: 86.w2),
        SizedBox(height: 16.h2),
        Text(LocaleKeys.family_link_text.tr(), style: AppStyle.bold32V2()),
        SizedBox(height: 48.h2),
        PrimaryButtonV2(
          isLoading: false,
          text: LocaleKeys.sign_up_free.tr(),
          onTap: () => context.pushRoute(const SignUpRoute()),
        ),
        SizedBox(height: 8.h),
        //Google
        PrimaryButtonV2(
          text: LocaleKeys.login_gg.tr(),
          bg: Colors.transparent,
          color: Colors.black,
          icon: Assets.icons.icGoogle.path,
          border: Border.all(width: 1.w2, color: appTheme.borderColorV2),
          onTap: () => cubit.loginWithGoogle(),
        ),
        SizedBox(height: 8.h),
        PrimaryButtonV2(
          text: LocaleKeys.login_fb.tr(),
          bg: Colors.transparent,
          color: Colors.black,
          icon: Assets.icons.icFacebook.path,
          border: Border.all(width: 1.w2, color: appTheme.borderColorV2),
          onTap: () => cubit.loginWithFacebook(),
        ),
        SizedBox(height: 8.h),
        //Apple
        PrimaryButtonV2(
          text: LocaleKeys.login_apple.tr(),
          bg: Colors.transparent,
          color: Colors.black,
          icon: Assets.icons.appleLogo.path,
          border: Border.all(width: 1.w2, color: appTheme.borderColorV2),
          onTap: () => cubit.loginWithApple(),
        ),
        SizedBox(height: 8.h),
        PrimaryButtonV2(
          text: LocaleKeys.login.tr(),
          bg: Colors.transparent,
          color: Colors.black,
          onTap: () => context.pushRoute(const SignInRoute()),
        ),
      ]),
    );
  }
}
