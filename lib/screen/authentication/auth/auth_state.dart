import 'package:family_app/base/widget/cubit/base_state.dart';

enum AuthStatus { none, loading, loginSuccess, signUpFamily, error, emailCantCheck, invalidUsernameOrPassword }

class AuthState extends BaseState {
  final AuthStatus status;
  final bool isLoggedIn;

  AuthState({
    this.status = AuthStatus.none,
    this.isLoggedIn = false,
  });

  AuthState copyWith({
    AuthStatus? status,
    bool? isLoggedIn,
  }) {
    return AuthState(
      status: status ?? this.status,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
    );
  }

  @override
  List<Object?> get props => [status, isLoggedIn];
}
