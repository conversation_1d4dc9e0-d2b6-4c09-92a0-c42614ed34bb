import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';
import 'package:family_app/data/usecase/apple_sign_in_usecase.dart';
import 'package:family_app/data/usecase/google_sign_in_usecase.dart';
import 'package:family_app/data/usecase/verify_account_usecase.dart';
import 'package:family_app/screen/authentication/auth/auth_state.dart';

class AuthCubit extends BaseCubit<AuthState> {
  final VerifyAccountUsecase verifyAccountUsecase;
  final GoogleSignInUsecase googleSignInUsecase;
  final AppleSignInUsecase appleSignInUsecase;

  AuthCubit({required this.verifyAccountUsecase, required this.googleSignInUsecase, required this.appleSignInUsecase}) : super(AuthState());

  Future<void> loginWithGoogle() async {
    try {
      emit(state.copyWith(status: AuthStatus.loading));

      await googleSignInUsecase.call(GoogleSignInParameter(
        isLoggedIn: state.isLoggedIn,
      ));

      if (!(await verifyAccountUsecase.call())) {
        emit(state.copyWith(status: AuthStatus.signUpFamily));
      } else {
        emit(state.copyWith(status: AuthStatus.loginSuccess));
      }
    } catch (e) {
      if (e is InvalidUserNameOrPassword) return emit(state.copyWith(status: AuthStatus.invalidUsernameOrPassword));
      emit(state.copyWith(status: AuthStatus.error));
    }
  }

  Future<void> loginWithApple() async {
    try {
      emit(state.copyWith(status: AuthStatus.loading));

      await appleSignInUsecase.call(AppleSignInParameter(
        isLoggedIn: state.isLoggedIn,
      ));

      if (!(await verifyAccountUsecase.call())) {
        emit(state.copyWith(status: AuthStatus.signUpFamily));
      } else {
        emit(state.copyWith(status: AuthStatus.loginSuccess));
      }
    } catch (e) {
      if (e is InvalidUserNameOrPassword) return emit(state.copyWith(status: AuthStatus.invalidUsernameOrPassword));
      emit(state.copyWith(status: AuthStatus.error));
    }
  }

  Future<void> loginWithFacebook() async {
    try {
      emit(state.copyWith(status: AuthStatus.loading));

      // final account = await authenRepository.loginWithFacebook();

      emit(state.copyWith(status: AuthStatus.loginSuccess));
    } catch (e) {
      emit(state.copyWith(status: AuthStatus.error));
    }
  }
}
