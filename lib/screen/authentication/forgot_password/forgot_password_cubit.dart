import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/screen/authentication/forgot_password/forgot_password_state.dart';
import 'package:family_app/utils/util.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class ForgotPasswordCubit extends BaseCubit<ForgotPasswordState> {
  final IAuthenRepository authenRepository;

  ForgotPasswordCubit({required this.authenRepository}) : super(ForgotPasswordState());

  late TextFieldHandler email;
  late FormTextFieldHandler formHandler;

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }

  @override
  void onInit() {
    super.onInit();

    email = TextFieldHandler(
        field: 'email',
        title: LocaleKeys.email.tr(),
        inputType: TextInputType.emailAddress,
        errorText: (value) => LocaleKeys.email_invalid_text.tr(),
        isFieldValid: (value) => Utils.isEmail(value),
        isRequired: true,
        onListenText: validateFormText);

    formHandler = FormTextFieldHandler(handlers: [email], validateForm: onSubmit);
  }

  void validateFormText() {
    if (!Utils.isEmail(email.text)) {
      emit(state.copyWith(isActive: false));
    } else {
      emit(state.copyWith(isActive: true));
    }
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      emit(state.copyWith(status: ForgotPasswordStatus.loading));

      await authenRepository.forgotPass(email.text);

      emit(state.copyWith(status: ForgotPasswordStatus.otpSuccess));
    } catch (e) {
      if (e is EmailCantCheck) {
        emit(state.copyWith(status: ForgotPasswordStatus.emailCantCheck));
      } else {
        emit(state.copyWith(status: ForgotPasswordStatus.error));
      }
    }
  }
}
