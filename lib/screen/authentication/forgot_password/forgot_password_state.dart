import 'package:family_app/base/widget/cubit/base_state.dart';

enum ForgotPasswordStatus { none, loading, otpSuccess, error, emailCantCheck }

class ForgotPasswordState extends BaseState {
  final ForgotPasswordStatus status;
  final String? email;
  final bool isActive;

  ForgotPasswordState({this.status = ForgotPasswordStatus.none, this.email, this.isActive = false});

  ForgotPasswordState copyWith({ForgotPasswordStatus? status, String? email, bool? isActive}) {
    return ForgotPasswordState(
      status: status ?? this.status,
      email: email ?? this.email,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object?> get props => [status, email, isActive];
}
