import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/authentication/forgot_password/forgot_password_cubit.dart';
import 'package:family_app/screen/authentication/forgot_password/forgot_password_state.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/header.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class ForgotPasswordPage extends BaseBlocProvider<ForgotPasswordState, ForgotPasswordCubit> {
  const ForgotPasswordPage({super.key});

  @override
  Widget buildPage() => const ForgotPasswordView();

  @override
  ForgotPasswordCubit createCubit() => ForgotPasswordCubit(authenRepository: locator.get());
}

class ForgotPasswordView extends StatefulWidget {
  const ForgotPasswordView({super.key});

  @override
  State<ForgotPasswordView> createState() => _ForgotPasswordViewState();
}

class _ForgotPasswordViewState
    extends BaseBlocNoAppBarPageState<ForgotPasswordView, ForgotPasswordState, ForgotPasswordCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get isSafeArea => false;

  @override
  bool get isResizeToAvoidBottomInset => false;

  @override
  bool listenWhen(ForgotPasswordState previous, ForgotPasswordState current) {
    if (previous.status != current.status) {
      switch (current.status) {
        case ForgotPasswordStatus.none:
          break;
        case ForgotPasswordStatus.loading:
          break;
        case ForgotPasswordStatus.otpSuccess:
          BottomSheetUtils.showWrapV2(
            context,
            LocaleKeys.reset_pass.tr(),
            (context) => Padding(
              padding: paddingV2(all: 20, bottom: 8),
              child: Column(children: [
                SvgPicture.asset(Assets.images.emailSent.path, width: 118.w2),
                SizedBox(height: 12.h2),
                Text(LocaleKeys.forgot_pass_sent.tr(), style: AppStyle.regular16V2()),
                SizedBox(height: 20.h2),
                PrimaryButtonV2(text: LocaleKeys.ok_text.tr(), onTap: () => context.maybePop()),
              ]),
            ),
          );
          break;
        case ForgotPasswordStatus.error:
          showSimpleToast(LocaleKeys.an_error_occurred_text.tr());
          break;
        case ForgotPasswordStatus.emailCantCheck:
          showSimpleToast(LocaleKeys.email_does_not_exist_text.tr());
          break;
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, ForgotPasswordCubit cubit, ForgotPasswordState state) {
    return Padding(
      padding: paddingV2(vertical: 16, horizontal: 8).add(EdgeInsets.only(top: context.top)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Header(LocaleKeys.forgot_pass.tr()),
        SizedBox(height: 8.h2),
        Container(
          width: double.infinity,
          padding: paddingV2(all: 16),
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(24.w)),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(LocaleKeys.forgot_desc.tr(), style: AppStyle.regular16V2(color: appTheme.grayV2)),
            SizedBox(height: 8.h2),
            ValueListenableBuilder(
              valueListenable: cubit.formHandler.formValidated,
              builder: (context, isValidate, _) => TitleTextFieldV2(fieldNode: cubit.email, validatedForm: isValidate),
            ),
            SizedBox(height: 24.h2),
            PrimaryButtonV2(
              text: LocaleKeys.forgot_submit.tr(),
              isLoading: state.status == ForgotPasswordStatus.loading,
              isActive: state.isActive,
              onTap: cubit.formHandler.onSubmit,
            ),
          ]),
        ),
      ]),
    );
  }
}
