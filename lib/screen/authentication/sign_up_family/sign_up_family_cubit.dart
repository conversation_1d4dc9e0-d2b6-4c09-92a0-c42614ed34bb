import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/screen/authentication/sign_up_family/sign_up_family_state.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class SignUpFamilyCubit extends BaseCubit<SignUpFamilyState> {
  final IFamilyRepository familyRepository;
  final AccountService accountService;
  final LocalStorage localStorage;

  SignUpFamilyCubit({
    required this.familyRepository,
    required this.accountService,
    required this.localStorage,
  }) : super(SignUpFamilyState());

  late TextFieldHandler familyName;
  late FormTextFieldHandler formHandler;

  @override
  void onInit() {
    super.onInit();
    familyName = TextFieldHandler(
      field: 'family_name',
      title: LocaleKeys.family_name_text.tr(),
      inputType: TextInputType.name,
      errorText: (value) => LocaleKeys.family_name_invalid_text.tr(),
      // isFieldValid: (value) => Utils.isUsername(value),
      isRequired: true,
    );
    formHandler = FormTextFieldHandler(handlers: [familyName], validateForm: onSubmit);
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    emit(state.copyWith(status: SignUpFamilyStatus.loading));
    try {
      final result = await familyRepository.updateFamilyInfo(familyName: familyName.text);
      accountService.setAccount(result);
      emit(state.copyWith(status: SignUpFamilyStatus.success));
    } catch (e) {
      emit(state.copyWith(status: SignUpFamilyStatus.error));
    }
  }

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }
}
