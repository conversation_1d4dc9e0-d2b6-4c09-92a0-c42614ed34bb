import 'package:family_app/base/widget/cubit/base_state.dart';

class SignUpFamilyState extends BaseState {
  final SignUpFamilyStatus status;

  SignUpFamilyState({
    this.status = SignUpFamilyStatus.none,
  });

  SignUpFamilyState copyWith({
    SignUpFamilyStatus? status,
  }) {
    return SignUpFamilyState(
      status: status ?? this.status,
    );
  }

  @override
  List<Object?> get props => [status];
}

enum SignUpFamilyStatus { none, success, error, loading }
