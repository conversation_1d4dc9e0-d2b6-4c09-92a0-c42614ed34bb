import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/authentication/sign_up_family/sign_up_family_cubit.dart';
import 'package:family_app/screen/authentication/sign_up_family/sign_up_family_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/textfield/title_text_field.dart';
import 'package:flutter/material.dart';

@RoutePage()
class SignUpFamilyPage extends BaseBlocProvider<SignUpFamilyState, SignUpFamilyCubit> {
  const SignUpFamilyPage({super.key});

  @override
  Widget buildPage() => const SignUpFamilyView();

  @override
  SignUpFamilyCubit createCubit() => SignUpFamilyCubit(
        familyRepository: locator.get(),
        accountService: locator.get(),
        localStorage: locator.get(),
      );
}

class SignUpFamilyView extends StatefulWidget {
  const SignUpFamilyView({super.key});

  @override
  State<SignUpFamilyView> createState() => _SignUpFamilyViewState();
}

class _SignUpFamilyViewState extends BaseBlocNoAppBarPageState<SignUpFamilyView, SignUpFamilyState, SignUpFamilyCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  bool listenWhen(SignUpFamilyState previous, SignUpFamilyState current) {
    if (previous.status != current.status) {
      if (current.status == SignUpFamilyStatus.success) {
        context.pushRoute(FamilySuccessRoute());
      } else if (current.status == SignUpFamilyStatus.error) {
        showSimpleToast(LocaleKeys.action_fail.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, SignUpFamilyCubit cubit, SignUpFamilyState state) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Image.asset(Assets.images.bgFamily.path),
        SafeArea(
          child: Padding(
            padding: padding(vertical: 16),
            child: Column(
              children: [
                AppBarCustom(
                  onBack: () {
                    cubit.localStorage.clear();
                    context.maybePop();
                  },
                ),
                Padding(
                  padding: padding(top: 8, horizontal: 32),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        clipBehavior: Clip.none,
                        children: [
                          Positioned(
                            left: -15,
                            top: 5,
                            child: Image.asset(Assets.images.circle.path, width: 17.w),
                          ),
                          Text(LocaleKeys.family_text.tr(), style: AppStyle.medium20()),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        LocaleKeys.welcome_you_can_setup_your_family_now_text.tr(namedArgs: {'field': ''}),
                        style: AppStyle.regular14(color: appTheme.labelColor),
                      ),
                      SizedBox(height: 17.h),
                      ValueListenableBuilder(
                        valueListenable: cubit.formHandler.formValidated,
                        builder: (context, isValidate, _) => TitleTextField(
                          fieldNode: cubit.familyName,
                          filledColor: appTheme.bgInputColor,
                          validatedForm: isValidate,
                          radius: 1000,
                          maxLine: 1,
                          textStyle: AppStyle.regular16(),
                          hintStyle: AppStyle.regular16(color: appTheme.hintColor),
                          errorInputBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: appTheme.errorColor, width: 1.w),
                            borderRadius: BorderRadius.circular(1000),
                          ),
                          inputBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: appTheme.borderInputColor, width: 1.w),
                            borderRadius: BorderRadius.circular(1000),
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),
                      PrimaryButton(
                        text: LocaleKeys.build_text.tr(),
                        isLoading: state.status == SignUpFamilyStatus.loading,
                        onTap: cubit.formHandler.onSubmit,
                      ),
                      SizedBox(height: 18.h),
                      Text(
                        LocaleKeys.if_you_want_to_join_an_existing_family_please_tell_the_owner_to_invite_you_text.tr(),
                        style: AppStyle.regular12(color: appTheme.labelColor),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
