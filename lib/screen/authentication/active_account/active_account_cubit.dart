import 'dart:async' show Timer;

import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/data/usecase/sign_in_usecase.dart';
import 'package:family_app/data/usecase/verify_account_usecase.dart';
import 'package:family_app/screen/authentication/active_account/active_account_state.dart';

class ActiveAccountCubit extends BaseCubit<ActiveAccountState> {
  final SignInUsecase signInUsecase;
  final VerifyAccountUsecase verifyAccountUsecase;
  final IAuthenRepository authenRepository;
  final String email, password, uuid;

  Timer? interval;

  @override
  void onInit() {
    interval = Timer.periodic(const Duration(seconds: 10), (timer) async {
      try {
        final r = await authenRepository.verifyEmail(uuid);

        if (!r) return;

        await signInUsecase.call(SignInParameter(email: email, password: password));

        if (!(await verifyAccountUsecase.call())) {
          emit(state.copyWith(status: ActiveAccountStatus.signUpFamily));
        } else {
          emit(state.copyWith(status: ActiveAccountStatus.loginSuccess));
        }
      } catch (e) {
        //
      }
    });

    super.onInit();
  }

  ActiveAccountCubit(this.email, this.password, this.uuid,
      {required this.authenRepository, required this.signInUsecase, required this.verifyAccountUsecase})
      : super(ActiveAccountState());

  @override
  Future<void> close() {
    interval?.cancel();

    return super.close();
  }

  Future<void> onSubmit() async {
    try {
      emit(state.copyWith(status: ActiveAccountStatus.loading));

      await signInUsecase.call(SignInParameter(email: email, password: password));

      if (!(await verifyAccountUsecase.call())) {
        emit(state.copyWith(status: ActiveAccountStatus.signUpFamily));
      } else {
        emit(state.copyWith(status: ActiveAccountStatus.loginSuccess));
      }
    } catch (e) {
      if (e is EmailNotActiveError) return emit(state.copyWith(status: ActiveAccountStatus.getEmailSuccess));

      emit(state.copyWith(status: ActiveAccountStatus.error));
    }
  }
}
