import 'package:family_app/base/widget/cubit/base_state.dart';

enum ActiveAccountStatus { none, loading, getEmailSuccess, signUpFamily, loginSuccess, error }

class ActiveAccountState extends BaseState {
  final ActiveAccountStatus status;
  final bool isActive;

  ActiveAccountState({this.status = ActiveAccountStatus.none, this.isActive = false});

  ActiveAccountState copyWith({ActiveAccountStatus? status, bool? isActive}) {
    return ActiveAccountState(status: status ?? this.status, isActive: isActive ?? this.isActive);
  }

  @override
  List<Object?> get props => [status, isActive];
}
