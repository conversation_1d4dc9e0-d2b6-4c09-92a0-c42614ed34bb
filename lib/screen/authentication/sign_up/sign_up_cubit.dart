import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/data/repository/authen/model/sign_up_parameter.dart';
import 'package:family_app/data/usecase/sign_in_usecase.dart';
import 'package:family_app/screen/authentication/sign_up/sign_up_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/util.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class SignUpCubit extends BaseCubit<SignUpState> {
  final IAuthenRepository authenRepository;
  final SignInUsecase signInUsecase;
  final AccountService accountService;

  SignUpCubit({
    required this.authenRepository,
    required this.signInUsecase,
    required this.accountService,
  }) : super(SignUpState());

  late TextFieldHandler email;
  late TextFieldHandler password;
  late TextFieldHandler confirmPassword;
  late FormTextFieldHandler formHandler;

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }

  @override
  void onInit() {
    super.onInit();

    email = TextFieldHandler(
        field: 'email',
        title: LocaleKeys.email.tr(),
        inputType: TextInputType.emailAddress,
        errorText: (value) => LocaleKeys.email_invalid_text.tr(),
        isFieldValid: (value) => Utils.isEmail(value),
        isRequired: true,
        hintText: LocaleKeys.email.tr(),
        onListenText: validateFormText);

    password = TextFieldHandler(
        field: 'password',
        title: LocaleKeys.password_text.tr(),
        isPassword: true,
        inputType: TextInputType.visiblePassword,
        errorText: (value) => LocaleKeys.password_invalid.tr(),
        isFieldValid: (value) => Utils.validatePassword(value,  email: email.text),
        isRequired: true,
        hintText: LocaleKeys.password_text.tr(),
        onListenText: validateFormText);

    confirmPassword = TextFieldHandler(
        field: 'confirmPassword',
        title: LocaleKeys.reenter_password.tr(),
        isPassword: true,
        inputType: TextInputType.visiblePassword,
        errorText: (value) => validateConfirmPassword(value ?? ''),
        isFieldValid: (value) => Utils.validatePassword(value,  email: email.text),
        isRequired: true,
        hintText: LocaleKeys.reenter_password.tr(),
        onListenText: validateFormText);

    formHandler = FormTextFieldHandler(handlers: [ email, password, confirmPassword], validateForm: onSubmit);
  }

  void validateFormText() {
    if (
        !Utils.isEmail(email.text) ||
        !Utils.validatePassword(password.text, email: email.text) ||
        !Utils.validatePassword(confirmPassword.text,  email: email.text) ||
        confirmPassword.text != password.text) {
      emit(state.copyWith(isActive: false));
    } else {
      emit(state.copyWith(isActive: true));
    }
  }

  String validateConfirmPassword(String value) {
    if (value != password.text) {
      return LocaleKeys.passwords_do_not_match.tr();
    }
    return LocaleKeys.password_invalid.tr();
  }

  void toggleCheckbox() {
    emit(state.copyWith(isChecked: !state.isChecked));
  }

  void togglePassVisibility() {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  void toggleConfirmPassVisibility() {
    emit(state.copyWith(isConfirmPasswordVisible: !state.isConfirmPasswordVisible));
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      if (state.isChecked == false) {
        return showSimpleToast(LocaleKeys.accept_privacy.tr());
      }
      emit(state.copyWith(status: SignUpStatus.loading));

      final response = await authenRepository
          .signUp(SignUpParameter(email: email.text, password: password.text, fullname: ""));

      if (response != null) {
        await signInUsecase.call(SignInParameter(email: email.text, password: password.text));
        if (accountService.myFamilyBelong.value.isNotEmpty) {
          emit(state.copyWith(status: SignUpStatus.home));
        } else {
          emit(state.copyWith(status: SignUpStatus.signUpFamily));
        }
      }
    } catch (e) {
      if (e is EmailNotActiveError)
        return emit(state.copyWith(
            status: SignUpStatus.emailNotActive, email: email.text, password: password.text, uuid: e.uuid));

      if (e is EmailNotAvailableError) return emit(state.copyWith(status: SignUpStatus.emailNotAvailableError));

      emit(state.copyWith(status: SignUpStatus.error));
    }
  }
}
