import 'package:family_app/base/widget/cubit/base_state.dart';

enum SignUpStatus { none, loading, signUpFamily, home, emailNotAvailableError, emailNotActive, error }

class SignUpState extends BaseState {
  final bool isChecked;
  final SignUpStatus status;
  final String? name, email, password, uuid;
  final bool isPasswordVisible;
  final bool isConfirmPasswordVisible;
  final bool isActive;

  SignUpState({
    this.isChecked = false,
    this.status = SignUpStatus.none,
    this.name,
    this.isPasswordVisible = false,
    this.isConfirmPasswordVisible = false,
    this.isActive = false,
    this.email,
    this.password,
    this.uuid,
  });

  SignUpState copyWith({
    bool? isChecked,
    SignUpStatus? status,
    String? name,
    String? email,
    String? uuid,
    String? password,
    bool? isPasswordVisible,
    bool? isConfirmPasswordVisible,
    bool? isActive,
  }) {
    return SignUpState(
      isChecked: isChecked ?? this.isChecked,
      status: status ?? this.status,
      name: name ?? this.name,
      uuid: uuid ?? this.uuid,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isConfirmPasswordVisible: isConfirmPasswordVisible ?? this.isConfirmPasswordVisible,
      isActive: isActive ?? this.isActive,
      email: email ?? this.email,
      password: password ?? this.password,
    );
  }

  @override
  List<Object?> get props => [isChecked, status, name, isPasswordVisible, isConfirmPasswordVisible, isActive];
}
