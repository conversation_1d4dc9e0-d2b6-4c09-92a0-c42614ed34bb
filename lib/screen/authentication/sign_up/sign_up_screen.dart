import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/authentication/sign_up/sign_up_cubit.dart';
import 'package:family_app/screen/authentication/sign_up/sign_up_state.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/condition_check_box.dart';
import 'package:family_app/widget/header.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/textfield/group_text_field.dart';
import 'package:flutter/material.dart';

import '../../../widget/textfield/title_text_field_v2.dart';

@RoutePage()
class SignUpPage extends BaseBlocProvider<SignUpState, SignUpCubit> {
  const SignUpPage({super.key});

  @override
  Widget buildPage() => const SignUpView();

  @override
  SignUpCubit createCubit() => SignUpCubit(
        authenRepository: locator.get(),
        signInUsecase: locator.get(),
        accountService: locator.get(),
      );
}

class SignUpView extends StatefulWidget {
  const SignUpView({super.key});

  @override
  State<SignUpView> createState() => _SignUpViewState();
}

class _SignUpViewState extends BaseBlocNoAppBarPageState<SignUpView, SignUpState, SignUpCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get isSafeArea => false;

  @override
  bool get isResizeToAvoidBottomInset => false;

  @override
  bool listenWhen(SignUpState previous, SignUpState current) {
    if (previous.status != current.status) {
      switch (current.status) {
        case SignUpStatus.none:
          break;
        case SignUpStatus.loading:
          break;
        case SignUpStatus.signUpFamily:
          context.pushRoute(const SignUpFamilyRoute());
          break;
        case SignUpStatus.home:
          context.pushRoute(const MainRoute());
          break;
        case SignUpStatus.emailNotAvailableError:
          showSimpleToast(LocaleKeys.email_exist.tr());
          break;
        case SignUpStatus.emailNotActive:
          context
              .pushRoute(ActiveAccountRoute(email: current.email!, password: current.password!, uuid: current.uuid!));
          break;
        case SignUpStatus.error:
          showSimpleToast(LocaleKeys.action_fail.tr());
          break;
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, SignUpCubit cubit, SignUpState state) {
    return Padding(
      padding: paddingV2(vertical: 16, horizontal: 8).add(EdgeInsets.only(top: context.top)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Header(LocaleKeys.sign_up_text.tr()),
        SizedBox(height: 8.h2),
        Container(
          width: double.infinity,
          padding: paddingV2(all: 16),
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(24.w)),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(LocaleKeys.welcome_to_family_link.tr(), style: AppStyle.bold20V2()),
            Text(LocaleKeys.sign_up_desc.tr(), style: AppStyle.regular16V2(color: appTheme.grayV2)),
            SizedBox(height: 24.h2),
            ValueListenableBuilder(
              valueListenable: cubit.email.isValidNotifier,
              builder: (context, isValid, child) {
                return ValueListenableBuilder(
                  valueListenable: cubit.email.emptyError,
                  builder: (context, isEmpty, child) {
                    return ValueListenableBuilder(
                      valueListenable: cubit.email.customErrorNotifier,
                      builder: (context, customError, child) {
                        String? error;
                        if (customError.isNotEmpty) {
                          error = customError;
                        } else if (isEmpty) {
                          error = LocaleKeys.empty_error.tr(args: [cubit.email.title]);
                        } else if (!isValid) {
                          error = cubit.email.errorText?.call(cubit.email.textCtrl.text);
                        }
                        return TitleTextFieldV2(
                          fieldNode: cubit.email,
                          validatedForm: true,
                          errorFormText: error ?? '',
                          isFormError: error != null,
                        );
                      },
                    );
                  },
                );
              },
            ),
            Padding(
              padding: paddingV2(horizontal: 12, top: 8, bottom: 24),
              child: Container(),
            ),
            ValueListenableBuilder(
              valueListenable: cubit.formHandler.formValidated,
              builder: (context, value, child) => GroupTextField(
                fieldNodeFirst: cubit.password,
                fieldNodeSecond: cubit.confirmPassword,
                isValidate: value,
                obscureTextFirst: !state.isPasswordVisible,
                obscureTextSecond: !state.isConfirmPasswordVisible,
                suffixIconFirst: ButtonIcon(
                  state.isPasswordVisible ? Assets.icons.icEyeEnable.path : Assets.icons.icEye.path,
                  cubit.togglePassVisibility,
                  size: 24,
                  radius: 24,
                ),
                suffixIconSecond: ButtonIcon(
                  state.isConfirmPasswordVisible ? Assets.icons.icEyeEnable.path : Assets.icons.icEye.path,
                  cubit.toggleConfirmPassVisibility,
                  size: 24,
                  radius: 24,
                ),
              ),
            ),
            Padding(
              padding: paddingV2(horizontal: 12, top: 8, bottom: 24),
              child: Text(LocaleKeys.sign_up_pass_desc.tr(), style: AppStyle.regular12V2(color: appTheme.grayV2)),
            ),
            ConditionCheckBox(
              onChanged: (p0) => cubit.toggleCheckbox(),
              value: state.isChecked,
              label: RichText(
                text: TextSpan(style: AppStyle.regular14V2(), children: [
                  TextSpan(text: LocaleKeys.sign_up_term_1.tr()),
                  TextSpan(
                    text: LocaleKeys.sign_up_term_2.tr(),
                    style: TextStyle(color: appTheme.primaryColorV2, fontWeight: FontWeight.w700),
                  ),
                ]),
              ),
              disabled: false,
            ),
            SizedBox(height: 24.h2),
            PrimaryButtonV2(
              text: LocaleKeys.sign_up_text.tr(),
              isActive: state.isActive,
              isLoading: state.status == SignUpStatus.loading,
              onTap: cubit.formHandler.onSubmit,
            ),
          ]),
        ),
      ]),
    );
  }
}
