import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/screen/authentication/sign_up_name/sign_up_name_state.dart';
import 'package:family_app/utils/util.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class SignUpNameCubit extends BaseCubit<SignUpNameState> {
  SignUpNameCubit() : super(SignUpNameState());

  late TextFieldHandler name;
  late FormTextFieldHandler formHandler;

  @override
  void onInit() {
    super.onInit();
    name = TextFieldHandler(
      field: 'Name',
      title: LocaleKeys.name_text.tr(),
      inputType: TextInputType.name,
      errorText: (value) => LocaleKeys.name_invalid_text.tr(),
      isFieldValid: (value) => Utils.isUsername(value),
      isRequired: true,
    );
    formHandler = FormTextFieldHandler(handlers: [name], validateForm: onSubmit);
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    print(json);
  }

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }
}
