import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/authentication/sign_up_name/sign_up_name_cubit.dart';
import 'package:family_app/screen/authentication/sign_up_name/sign_up_name_state.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/textfield/title_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

@RoutePage()
class SignUpNamePage extends BaseBlocProvider<SignUpNameState, SignUpNameCubit> {
  const SignUpNamePage({super.key});

  @override
  Widget buildPage() => const SignUpNameView();

  @override
  SignUpNameCubit createCubit() => SignUpNameCubit();
}

class SignUpNameView extends StatefulWidget {
  const SignUpNameView({super.key});

  @override
  State<SignUpNameView> createState() => _SignUpNameViewState();
}

class _SignUpNameViewState extends BaseBlocNoAppBarPageState<SignUpNameView, SignUpNameState, SignUpNameCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  Widget buildBody(BuildContext context, SignUpNameCubit cubit, SignUpNameState state) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Image.asset(Assets.images.bgFamily.path),
        SafeArea(
          child: Padding(
            padding: padding(vertical: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                IconButton(onPressed: context.back, icon: SvgPicture.asset(Assets.icons.back.path)),
                Padding(
                  padding: padding(horizontal: 32, top: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        clipBehavior: Clip.none,
                        children: [
                          Positioned(
                            left: -15,
                            top: 5,
                            child: Image.asset(Assets.images.circle.path, width: 17.w),
                          ),
                          Text(LocaleKeys.name_text.tr(), style: AppStyle.medium20()),
                        ],
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        '${LocaleKeys.how_should_we_call_you_text.tr()}?',
                        style: AppStyle.regular14(color: appTheme.labelColor),
                      ),
                      SizedBox(height: 17.h),
                      ValueListenableBuilder(
                        valueListenable: cubit.formHandler.formValidated,
                        builder: (context, isValidate, _) => TitleTextField(
                          fieldNode: cubit.name,
                          filledColor: appTheme.bgInputColor,
                          validatedForm: isValidate,
                          radius: 1000,
                          maxLine: 1,
                          textStyle: AppStyle.regular16(),
                          hintStyle: AppStyle.regular16(color: appTheme.hintColor),
                          errorInputBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: appTheme.errorColor, width: 1.w),
                            borderRadius: BorderRadius.circular(1000),
                          ),
                          inputBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: appTheme.borderInputColor, width: 1.w),
                            borderRadius: BorderRadius.circular(1000),
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),
                      PrimaryButton(
                        text: LocaleKeys.next_text.tr(),
                        onTap: () => context.pushRoute(const SignUpFamilyRoute()),
                        // onTap: cubit.formHandler.onSubmit,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
