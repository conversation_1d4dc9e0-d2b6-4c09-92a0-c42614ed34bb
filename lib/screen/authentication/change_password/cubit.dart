import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';
import 'package:family_app/data/repository/family/model/change_password_parameter.dart';
import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/utils/util.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

import 'state.dart';

class ChangePasswordCubit extends BaseCubit<ChangePasswordState> {
  final IFamilyRepository familyRepository;
  final AccountService accountService;

  ChangePasswordCubit({
    required this.familyRepository,
    required this.accountService,
  }) : super(ChangePasswordState());

  late TextFieldHandler password;
  late TextFieldHandler newPassword;
  late TextFieldHandler confirmNewPassword;
  late FormTextFieldHandler formHandler;

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }

  @override
  void onInit() {
    super.onInit();
    final email = accountService.account?.email;
    final username = accountService.account?.fullName;
    password = TextFieldHandler(
        field: 'oldPassword',
        title: LocaleKeys.current_password.tr(),
        inputType: TextInputType.visiblePassword,
        errorText: (value) => LocaleKeys.password_invalid.tr(),
        // isFieldValid: (value) => Utils.validatePassword(value),
        isRequired: true,
        hintText: LocaleKeys.current_password.tr(),
        onListenText: validateFormText);

    newPassword = TextFieldHandler(
        field: 'password',
        title: LocaleKeys.new_password_text.tr(),
        isPassword: true,
        inputType: TextInputType.visiblePassword,
        errorText: (value) => LocaleKeys.password_invalid.tr(),
        isFieldValid: (value) => Utils.validatePassword(
              value,
              email: email,
            ),
        isRequired: true,
        hintText: LocaleKeys.new_password_text.tr(),
        onListenText: validateFormText);

    confirmNewPassword = TextFieldHandler(
        field: 'repassword',
        title: LocaleKeys.reenter_new_password.tr(),
        isPassword: true,
        inputType: TextInputType.visiblePassword,
        errorText: (value) => validateConfirmNewPassword(value ?? ''),
        isFieldValid: (value) => Utils.validatePassword(
              value,
              email: email,
            ),
        isRequired: true,
        hintText: LocaleKeys.reenter_new_password.tr(),
        onListenText: validateFormText);

    formHandler = FormTextFieldHandler(handlers: [password, newPassword, confirmNewPassword], validateForm: onSubmit);
  }

  void validateFormText() {
    final email = accountService.account?.email;
    final username = accountService.account?.fullName;
    if (password.text.isEmpty ||
        newPassword.text.isEmpty ||
        password.text == newPassword.text ||
        newPassword.text != confirmNewPassword.text ||
        !Utils.validatePassword(newPassword.text, email: email) ||
        !Utils.validatePassword(confirmNewPassword.text, email: email)) {
      emit(state.copyWith(isActive: false));
    } else {
      emit(state.copyWith(isActive: true));
    }
  }

  String validateConfirmNewPassword(String value) {
    if (value != newPassword.text) {
      return LocaleKeys.passwords_do_not_match.tr();
    }
    return LocaleKeys.password_invalid.tr();
  }

  void togglePassVisibility() {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  void toggleNewPasswordVisibility() {
    emit(state.copyWith(isNewPasswordVisible: !state.isNewPasswordVisible));
  }

  void toggleConfirmNewPasswordVisibility() {
    emit(state.copyWith(isConfirmNewPasswordVisible: !state.isConfirmNewPasswordVisible));
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      emit(state.copyWith(status: ChangePasswordStatus.loading));
      await familyRepository.changePassword(ChangePasswordParameter(oldPassword: json['oldPassword'], password: json['password']));
      emit(state.copyWith(status: ChangePasswordStatus.success));
    } catch (e) {
      if (e is WrongPassword) {
        emit(state.copyWith(status: ChangePasswordStatus.errorWrongPassword));
      } else {
        emit(state.copyWith(status: ChangePasswordStatus.error));
      }
    }
  }
}
