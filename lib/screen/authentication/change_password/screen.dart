import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/header.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/textfield/group_text_field.dart';
import 'package:family_app/widget/textfield/title_text_field_v2.dart';
import 'package:flutter/material.dart';

import 'cubit.dart';
import 'state.dart';

@RoutePage()
class ChangePasswordPage extends BaseBlocProvider<ChangePasswordState, ChangePasswordCubit> {
  const ChangePasswordPage({super.key});

  @override
  Widget buildPage() => const ChangePasswordView();

  @override
  ChangePasswordCubit createCubit() => ChangePasswordCubit(
        familyRepository: locator.get(),
        accountService: locator.get(),
      );
}

class ChangePasswordView extends StatefulWidget {
  const ChangePasswordView({super.key});

  @override
  State<ChangePasswordView> createState() => _ChangePasswordViewState();
}

class _ChangePasswordViewState extends BaseBlocNoAppBarPageState<ChangePasswordView, ChangePasswordState, ChangePasswordCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get isSafeArea => false;

  @override
  bool get isResizeToAvoidBottomInset => false;

  @override
  bool listenWhen(ChangePasswordState previous, ChangePasswordState current) {
    if (previous.status != current.status) {
      switch (current.status) {
        case ChangePasswordStatus.none:
          break;
        case ChangePasswordStatus.loading:
          break;
        case ChangePasswordStatus.success:
          showSimpleToast(LocaleKeys.change_password_success.tr());
          context.maybePop();
          break;
        case ChangePasswordStatus.error:
          showSimpleToast(LocaleKeys.an_error_occurred_text.tr());
          break;
        case ChangePasswordStatus.errorWrongPassword:
          showSimpleToast(LocaleKeys.incorrect_password_text.tr());
          break;
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, ChangePasswordCubit cubit, ChangePasswordState state) {
    return Padding(
      padding: paddingV2(vertical: 16, horizontal: 8).add(EdgeInsets.only(top: context.top)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Header(LocaleKeys.change_password.tr()),
        SizedBox(height: 8.h2),
        Container(
          width: double.infinity,
          padding: paddingV2(all: 16),
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(24.w)),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(LocaleKeys.please_enter_your_current_password.tr(), style: AppStyle.regular16V2(color: appTheme.grayV2)),
            SizedBox(height: 24.h2),
            ValueListenableBuilder(
              valueListenable: cubit.formHandler.formValidated,
              builder: (context, isValidate, _) => TitleTextFieldV2(
                fieldNode: cubit.password,
                validatedForm: isValidate,
                obscureText: !state.isPasswordVisible,
                suffixIcon: ButtonIcon(
                  state.isPasswordVisible ? Assets.icons.icEye.path : Assets.icons.icEye.path,
                  cubit.togglePassVisibility,
                  size: 24,
                  radius: 24,
                ),
              ),
            ),
            SizedBox(height: 24.h2),
            Text(LocaleKeys.please_enter_your_new_password.tr(), style: AppStyle.regular16V2(color: appTheme.grayV2)),
            SizedBox(height: 24.h2),
            ValueListenableBuilder(
              valueListenable: cubit.formHandler.formValidated,
              builder: (context, value, child) => GroupTextField(
                fieldNodeFirst: cubit.newPassword,
                fieldNodeSecond: cubit.confirmNewPassword,
                isValidate: value,
                obscureTextFirst: !state.isNewPasswordVisible,
                obscureTextSecond: !state.isConfirmNewPasswordVisible,
                suffixIconFirst: ButtonIcon(
                  state.isNewPasswordVisible ? Assets.icons.icEye.path : Assets.icons.icEye.path,
                  cubit.toggleNewPasswordVisibility,
                  size: 24,
                  radius: 24,
                ),
                suffixIconSecond: ButtonIcon(
                  state.isConfirmNewPasswordVisible ? Assets.icons.icEye.path : Assets.icons.icEye.path,
                  cubit.toggleConfirmNewPasswordVisibility,
                  size: 24,
                  radius: 24,
                ),
              ),
            ),
            Padding(
              padding: paddingV2(horizontal: 12, top: 8, bottom: 24),
              child: Text(LocaleKeys.sign_up_pass_desc.tr(), style: AppStyle.regular12V2(color: appTheme.grayV2)),
            ),
            PrimaryButtonV2(
              text: LocaleKeys.change_password.tr(),
              isActive: state.isActive,
              isLoading: state.status == ChangePasswordStatus.loading,
              onTap: cubit.formHandler.onSubmit,
            ),
          ]),
        ),
      ]),
    );
  }
}
