import 'package:family_app/base/widget/cubit/base_state.dart';

enum ChangePasswordStatus { none, loading, success, error, errorWrongPassword }

class ChangePasswordState extends BaseState {
  final ChangePasswordStatus status;
  final String? password, uuid;
  final bool isPasswordVisible;
  final bool isNewPasswordVisible;
  final bool isConfirmNewPasswordVisible;
  final bool isActive;

  ChangePasswordState({
    this.status = ChangePasswordStatus.none,
    this.isPasswordVisible = false,
    this.isNewPasswordVisible = false,
    this.isConfirmNewPasswordVisible = false,
    this.isActive = false,
    this.password,
    this.uuid,
  });

  ChangePasswordState copyWith({
    ChangePasswordStatus? status,
    String? uuid,
    String? password,
    bool? isPasswordVisible,
    bool? isNewPasswordVisible,
    bool? isConfirmNewPasswordVisible,
    bool? isActive,
  }) {
    return ChangePasswordState(
      status: status ?? this.status,
      uuid: uuid ?? this.uuid,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isNewPasswordVisible: isNewPasswordVisible ?? this.isNewPasswordVisible,
      isConfirmNewPasswordVisible: isConfirmNewPasswordVisible ?? this.isConfirmNewPasswordVisible,
      isActive: isActive ?? this.isActive,
      password: password ?? this.password,
    );
  }

  @override
  List<Object?> get props => [status, isPasswordVisible, isNewPasswordVisible, isConfirmNewPasswordVisible, isActive];
}
