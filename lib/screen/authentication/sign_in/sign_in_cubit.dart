import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/data/repository/authen/authen_exception.dart';
import 'package:family_app/data/usecase/sign_in_usecase.dart';
import 'package:family_app/data/usecase/verify_account_usecase.dart';
import 'package:family_app/screen/authentication/sign_in/sign_in_state.dart';
import 'package:family_app/utils/util.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class SignInCubit extends BaseCubit<SignInState> {
  final SignInUsecase signInUsecase;
  final VerifyAccountUsecase verifyAccountUsecase;

  SignInCubit({
    required this.signInUsecase,
    required this.verifyAccountUsecase,
  }) : super(SignInState());

  late TextFieldHandler email;
  late TextFieldHandler password;
  late FormTextFieldHandler formHandler;

  @override
  Future<void> close() {
    formHandler.dispose();
    return super.close();
  }

  @override
  void onInit() {
    super.onInit();
    email = TextFieldHandler(
      field: 'email',
      title: LocaleKeys.email.tr(),
      inputType: TextInputType.emailAddress,
      errorText: (value) => LocaleKeys.invalid_email_or_password.tr(),
      isFieldValid: (value) => Utils.isEmail(value),
      isRequired: true,
      hintText: LocaleKeys.email.tr(),
      onListenText: validateFormText
    );

    password = TextFieldHandler(
      field: 'password',
      title: LocaleKeys.password_text.tr(),
      isPassword: true,
      inputType: TextInputType.emailAddress,
      errorText: (value) => LocaleKeys.invalid_email_or_password.tr(),
      // isFieldValid: (value) => value.isEmpty,
      isRequired: true,
      hintText: LocaleKeys.password_text.tr(),
      onListenText: validateFormText
    );


    password.node.addListener(() {
      var test = Utils.isEmail(email.text);
      if (password.node.hasFocus && password.text.isEmpty) {
        password.customErrorNotifier.value = LocaleKeys.invalid_email_or_password.tr();
      } else if (!Utils.isEmail(email.text)) {
        password.customErrorNotifier.value = LocaleKeys.invalid_email_or_password.tr();
      }
    });

    formHandler = FormTextFieldHandler(handlers: [email, password], validateForm: onSubmit);
  }

  void validateFormText() {
    if (!Utils.isEmail(email.text) || password.text.isEmpty) {
      if (password.node.hasFocus || email.node.hasFocus) {
        password.customErrorNotifier.value = LocaleKeys.invalid_email_or_password.tr();
      }
      emit(state.copyWith(isActive: false));
    } else {
      emit(state.copyWith(isActive: true));
    }
  }

  void togglePassVisibility() {
    emit(state.copyWith(isPasswordVisible: !state.isPasswordVisible));
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      emit(state.copyWith(status: LoginStatus.loading));

      await signInUsecase.call(SignInParameter(
        email: email.text,
        password: password.text,
        isLoggedIn: state.isLoggedIn,
      ));
      if (!(await verifyAccountUsecase.call())) {
        emit(state.copyWith(status: LoginStatus.signUpFamily));
      } else {
        emit(state.copyWith(status: LoginStatus.loginSuccess));
      }
    } catch (e) {
      if (e is EmailNotActiveError)
        return emit(state.copyWith(
            status: LoginStatus.emailNotActive, email: email.text, password: password.text, uuid: e.uuid));

      if (e is InvalidUserNameOrPassword) return emit(state.copyWith(status: LoginStatus.invalidUsernameOrPassword));

      emit(state.copyWith(status: LoginStatus.error));
    }
  }

  void toggleLoggedIn() {
    emit(state.copyWith(isLoggedIn: !state.isLoggedIn));
  }

  // void sendOtp(String email) async {
  //   try {
  //     // emit(state.copyWith(status: LoginStatus.loading));

  //     await authenRepository.sendOTP(email);

  //     emit(state.copyWith(status: LoginStatus.otpSuccess, email: email));
  //   } catch (e) {
  //     if (e is EmailCantCheck) {
  //       emit(state.copyWith(status: LoginStatus.emailCantCheck));
  //     } else {}
  //   }
  // }
}
