import 'package:family_app/base/widget/cubit/base_state.dart';

enum LoginStatus {
  none,
  loading,
  loginSuccess,
  signUpFamily,
  otpSuccess,
  error,
  emailCantCheck,
  invalidUsernameOrPassword,
  emailNotActive
}

class SignInState extends BaseState {
  final LoginStatus status;
  final String? email, password, uuid;
  final bool isPasswordVisible, isLoggedIn, isActive;

  SignInState({
    this.status = LoginStatus.none,
    this.email,
    this.uuid,
    this.password,
    this.isPasswordVisible = false,
    this.isLoggedIn = false,
    this.isActive = false,
  });

  SignInState copyWith({
    LoginStatus? status,
    String? email,
    String? password,
    String? uuid,
    bool? isPasswordVisible,
    bool? isLoggedIn,
    bool? isActive,
  }) {
    return SignInState(
      status: status ?? this.status,
      email: email ?? this.email,
      uuid: uuid ?? this.uuid,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      isActive: isActive ?? this.isActive,
      password: password ?? this.password,
    );
  }

  @override
  List<Object?> get props => [status, email, isPasswordVisible, isLoggedIn, isActive];
}
