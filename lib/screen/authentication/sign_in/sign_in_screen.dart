import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/authentication/sign_in/sign_in_cubit.dart';
import 'package:family_app/screen/authentication/sign_in/sign_in_state.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/button_icon.dart';
import 'package:family_app/widget/header.dart';
import 'package:family_app/widget/primary_button_v2.dart';
import 'package:family_app/widget/textfield/group_text_field.dart';
import 'package:flutter/material.dart';

import '../../../widget/textfield/group_text_fieldV2.dart';

@RoutePage()
class SignInPage extends BaseBlocProvider<SignInState, SignInCubit> {
  const SignInPage({super.key});

  @override
  Widget buildPage() => const SignInView();

  @override
  SignInCubit createCubit() => SignInCubit(signInUsecase: locator.get(), verifyAccountUsecase: locator.get());
}

class SignInView extends StatefulWidget {
  const SignInView({super.key});

  @override
  State<SignInView> createState() => _SignInViewState();
}

class _SignInViewState extends BaseBlocNoAppBarPageState<SignInView, SignInState, SignInCubit> {
  @override
  Color get backgroundColor => appTheme.backgroundV2;

  @override
  bool get isSafeArea => false;

  @override
  bool get isResizeToAvoidBottomInset => false;

  @override
  bool listenWhen(SignInState previous, SignInState current) {
    if (previous.status != current.status) {
      switch (current.status) {
        case LoginStatus.none:
          break;
        case LoginStatus.loading:
          break;
        case LoginStatus.loginSuccess:
          context.router.replaceAll([const HomeRoute()]);
          break;
        case LoginStatus.signUpFamily:
          context.pushRoute(const SignUpFamilyRoute());
          break;
        case LoginStatus.otpSuccess:
          if (current.email != null) {
            context.pushRoute(VerifyCodeRoute(email: current.email ?? ''));
          }
          break;
        case LoginStatus.error:
          showSimpleToast(LocaleKeys.an_error_occurred_text.tr());
          break;
        case LoginStatus.emailCantCheck:
          showSimpleToast(LocaleKeys.email_does_not_exist_text.tr());
          break;
        case LoginStatus.invalidUsernameOrPassword:
          showSimpleToast(LocaleKeys.invalid_username_or_password.tr());
          break;
        case LoginStatus.emailNotActive:
          context
              .pushRoute(ActiveAccountRoute(email: current.email!, password: current.password!, uuid: current.uuid!));
          break;
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, SignInCubit cubit, SignInState state) {
    return Padding(
      padding: paddingV2(vertical: 16, horizontal: 8).add(EdgeInsets.only(top: context.top)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Header(LocaleKeys.login.tr()),
        SizedBox(height: 8.h2),
        Container(
          width: double.infinity,
          padding: paddingV2(all: 16),
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(24.w)),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(LocaleKeys.welcome_home.tr(), style: AppStyle.bold20V2()),
            Text(LocaleKeys.sign_in_desc.tr(), style: AppStyle.regular16V2(color: appTheme.grayV2)),
            SizedBox(height: 24.h2),
            ValueListenableBuilder(
              valueListenable: cubit.formHandler.formValidated,
              builder: (context, isValidate, _) => GroupTextFieldV2(
                fieldNodeFirst: cubit.email,
                fieldNodeSecond: cubit.password,
                isValidate: isValidate,
                obscureTextSecond: !state.isPasswordVisible,
                suffixIconSecond: ButtonIcon(
                  state.isPasswordVisible ? Assets.icons.icEyeEnable.path : Assets.icons.icEye.path,
                  cubit.togglePassVisibility,
                  size: 24,
                  radius: 24,
                ),
              ),
            ),
            SizedBox(height: 8.h2),
            Align(
              alignment: Alignment.centerRight,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  child: Text(LocaleKeys.forgot_pass.tr(), style: AppStyle.bold14V2(color: appTheme.primaryColorV2)),
                  onTap: () => context.router.push(const ForgotPasswordRoute()),
                ),
              ),
            ),
            SizedBox(height: 24.h2),
            PrimaryButtonV2(
              text: LocaleKeys.login.tr(),
              isLoading: state.status == LoginStatus.loading,
              isActive: state.isActive,
              onTap: cubit.formHandler.onSubmit,
            ),
          ]),
        ),
      ]),
    );
  }
}

