import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/screen/member_info/member_info_state.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';

class MemberInfoCubit extends BaseCubit<MemberInfoState> {
  MemberInfoCubit() : super(MemberInfoState());

  late TextFieldHandler relation;
  late FormTextFieldHandler form;

  @override
  void onInit() {
    super.onInit();
    relation = TextFieldHandler(
      field: 'relation',
      title: LocaleKeys.relation_text.tr(),
      hintText: LocaleKeys.select_or_input_text.tr(),
    );
    form = FormTextFieldHandler(handlers: [relation], validateForm: (map) async {});
  }

  @override
  Future<void> close() {
    form.dispose();
    return super.close();
  }
}
