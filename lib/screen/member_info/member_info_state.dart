import 'package:family_app/base/widget/cubit/base_state.dart';

class MemberInfoState extends BaseState {
  final List<String> familyMembers;

  MemberInfoState({
    this.familyMembers = const ['Dad', 'Mom', 'Son', 'Daughter'],
  });

  @override
  List<Object?> get props => [familyMembers];

  MemberInfoState copyWith({
    List<String>? familyMembers,
  }) {
    return MemberInfoState(
      familyMembers: familyMembers ?? this.familyMembers,
    );
  }
}
