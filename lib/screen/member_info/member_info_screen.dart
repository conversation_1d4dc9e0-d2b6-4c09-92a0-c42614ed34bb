import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/family_create/family_invite/widget/item_row_input_widget.dart';
import 'package:family_app/screen/member_info/member_info_cubit.dart';
import 'package:family_app/screen/member_info/member_info_state.dart';
import 'package:family_app/widget/image/circle_avatar_custom.dart';
import 'package:family_app/widget/line_widget.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/select_widget.dart';
import 'package:family_app/widget/textfield/row_text_field_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

@RoutePage()
class MemberInfoPage extends BaseBlocProvider<MemberInfoState, MemberInfoCubit> {
  const MemberInfoPage({super.key});

  @override
  Widget buildPage() => const MemberInfoView();

  @override
  MemberInfoCubit createCubit() => MemberInfoCubit();
}

class MemberInfoView extends StatefulWidget {
  const MemberInfoView({super.key});

  @override
  State<MemberInfoView> createState() => _MemberInfoViewState();
}

class _MemberInfoViewState extends BaseBlocNoAppBarPageState<MemberInfoView, MemberInfoState, MemberInfoCubit> {
  @override
  Color get backgroundColor => appTheme.background;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  bool get isSafeArea => false;

  @override
  Widget buildBody(BuildContext context, MemberInfoCubit cubit, MemberInfoState state) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Image.asset(Assets.images.bgFamilyCreate.path),
        SafeArea(
          child: Padding(
            padding: padding(vertical: 16, horizontal: 16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(onPressed: context.back, icon: SvgPicture.asset(Assets.icons.back.path)),
                    Text('Johnson', style: AppStyle.medium16()),
                    IconButton(onPressed: null, icon: SizedBox()),
                  ],
                ),
                Padding(
                  padding: padding(top: 20),
                  child: Row(
                    children: [
                      CircleAvatarCustom(
                        borderWidth: 1.w,
                        size: 80,
                      ),
                      SizedBox(width: 16.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Andy', style: AppStyle.medium16()),
                          SizedBox(height: 16.h),
                          Text('<EMAIL>', style: AppStyle.regular14(color: appTheme.labelColor)),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 28.h),
                Container(
                  padding: padding(horizontal: 16, bottom: 16),
                  decoration: BoxDecoration(
                    color: appTheme.whiteText,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      const BoxShadow(color: Color(0x185F657C), offset: Offset(0, 0), blurRadius: 9),
                    ],
                  ),
                  child: Column(
                    children: [
                      RowTextFieldView(
                          handler: cubit.relation,
                          selections: state.familyMembers,
                          topPadding: 0,
                          formHandler: cubit.form),
                      Padding(
                        padding: padding(vertical: 15),
                        child: LineWidget(),
                      ),
                      itemRowInputWidget(
                        name: 'Roles',
                        widget: Row(
                          children: [
                            SizedBox(width: 12.w),
                            SelectWidget(status: true),
                            SizedBox(width: 8.w),
                            Text(LocaleKeys.editor.tr(), style: AppStyle.regular14()),
                            SizedBox(width: 29.w),
                            SelectWidget(status: false),
                            SizedBox(width: 8.w),
                            Text(LocaleKeys.viewer.tr(), style: AppStyle.regular14()),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 24.h),
                PrimaryButton(
                  text: LocaleKeys.ok_text.tr(),
                  onTap: context.back,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
