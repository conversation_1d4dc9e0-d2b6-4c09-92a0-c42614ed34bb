import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/screen/verify_code/verify_code_state.dart';
import 'package:family_app/widget/textfield/text_field_node.dart';
import 'package:flutter/material.dart';

class VerifyCodeCubit extends BaseCubit<VerifyCodeState> {
  VerifyCodeCubit({required this.authenRepository, required this.email}) : super(VerifyCodeState());

  final IAuthenRepository authenRepository;
  final String email;

  late TextFieldHandler verifyCode;
  late FormTextFieldHandler formHandler;

  @override
  void onInit() {
    super.onInit();
    verifyCode = TextFieldHandler(
      field: 'code',
      title: LocaleKeys.your_verify_code_text.tr(),
      inputType: TextInputType.phone,
      errorText: (value) {
        return (value != null && !isVerifyCodeValid(value))
            ? LocaleKeys.the_verification_code_must_be_numeric_characters.tr(namedArgs: {'field': '6'})
            : '';
      },
      isFieldValid: isVerifyCodeValid,
      isRequired: true,
    );
    formHandler = FormTextFieldHandler(handlers: [verifyCode], validateForm: onSubmit);
  }

  bool isVerifyCodeValid(String? value) {
    return value != null && value.length == 6 && RegExp(r'^[0-9]+$').hasMatch(value);
  }

  Future<void> onSubmit(Map<String, dynamic> json) async {
    try {
      await authenRepository.verifyOTP(email, json['otp']);

      emit(state.copyWith(status: VerifyCodeStatus.success));
    } catch (e) {
      if (e is VerifyCodeStatus) {
        emit(state.copyWith(status: VerifyCodeStatus.error));
      } else {}
    }
  }
}
