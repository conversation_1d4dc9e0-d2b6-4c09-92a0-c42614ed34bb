import 'package:family_app/base/widget/cubit/base_state.dart';

enum VerifyCodeStatus { none, loading, success, error }

class VerifyCodeState extends BaseState {
  final VerifyCodeStatus status;

  VerifyCodeState({this.status = VerifyCodeStatus.none});

  VerifyCodeState copyWith({
    bool? isChecked,
    VerifyCodeStatus? status,
    String? email,
  }) {
    return VerifyCodeState(
      status: status ?? this.status,
    );
  }

  @override
  List<Object?> get props => [status];
}
