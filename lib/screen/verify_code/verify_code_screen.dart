import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/verify_code/verify_code_cubit.dart';
import 'package:family_app/screen/verify_code/verify_code_state.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/primary_button.dart';
import 'package:family_app/widget/textfield/form_text_field_custom.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

@RoutePage()
class VerifyCodePage extends BaseBlocProvider<VerifyCodeState, VerifyCodeCubit> {
  const VerifyCodePage({required this.email, super.key});

  final String email;

  @override
  Widget buildPage() => const VerifyCodeView();

  @override
  VerifyCodeCubit createCubit() => VerifyCodeCubit(authenRepository: locator.get(), email: email);
}

class VerifyCodeView extends StatefulWidget {
  const VerifyCodeView({super.key});

  @override
  State<VerifyCodeView> createState() => _VerifyCodeViewState();
}

class _VerifyCodeViewState extends BaseBlocNoAppBarPageState<VerifyCodeView, VerifyCodeState, VerifyCodeCubit> {
  @override
  Color get backgroundColor => appTheme.whiteText;

  @override
  bool get isSafeArea => false;

  @override
  bool get isResizeToAvoidBottomInset => false;

  @override
  bool listenWhen(VerifyCodeState previous, VerifyCodeState current) {
    if (previous.status != current.status) {
      if (current.status == VerifyCodeStatus.success) {
        context.pushRoute(const SignUpNameRoute());
      } else if (current.status == VerifyCodeStatus.error) {
        showSimpleToast(LocaleKeys.authentication_failed_text.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, VerifyCodeCubit cubit, VerifyCodeState state) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Image.asset(Assets.images.bgAuth.path),
        SafeArea(
          child: Padding(
            padding: padding(top: 22, horizontal: 16),
            child: Column(
              children: [
                Image.asset(Assets.images.logo.path, width: 77.w),
                SizedBox(height: 4.h),
                Text(LocaleKeys.family_link_text.tr(), style: AppStyle.bold32(color: appTheme.primaryTextColor)),
                SizedBox(height: 6.h),
                Text(
                  LocaleKeys.your_personal_AI_butler_text.tr(),
                  style: AppStyle.regular12(color: appTheme.primaryTextColor),
                ),
                SizedBox(height: 48.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    LocaleKeys.your_verify_code_has_sent_to_your_email_text.tr(),
                    style: AppStyle.regular14(color: appTheme.labelColor),
                  ),
                ),
                SizedBox(height: 17.h),
                FormTextFieldCustom(
                  formHandler: cubit.formHandler,
                  textStyle: AppStyle.regular16(),
                  hintStyle: AppStyle.regular16(color: appTheme.hintColor),
                  inputBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: appTheme.borderInputColor, width: 1.w),
                    borderRadius: BorderRadius.circular(1000),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: appTheme.errorColor, width: 1.w),
                    borderRadius: BorderRadius.circular(1000),
                  ),
                ),
                SizedBox(height: 16.h),
                PrimaryButton(
                  text: LocaleKeys.next_text.tr(),
                  onTap: () => context.pushRoute(const SignUpNameRoute()),
                  // onTap: cubit.formHandler.onSubmit,
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 44,
          left: 0,
          child: IconButton(onPressed: context.back, icon: SvgPicture.asset(Assets.icons.back.path)),
        ),
      ],
    );
  }
}
