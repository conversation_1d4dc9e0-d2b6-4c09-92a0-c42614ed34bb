import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:injectable/injectable.dart';
import 'package:family_app/utils/calendar.dart';
import 'package:family_app/utils/event.dart';

@singleton
class EventService {
  final IEventRepository eventRepository;

  EventService({required this.eventRepository});

  final events = BaseListStreamController<EventModels>([]);
  static const String _logTag = 'EventService';
  static const int _eventFetchLimit = 200;
  DateTime? _lastFrom;
  DateTime? _lastTo;

  // --- Dispose ---
  /// Disposes the stream controller when no longer needed.
  void dispose() {
    events.dispose();
  }

  // --- Get Events (no stream update) ---
  /// Returns a list of events for the given [familyId] without updating the stream.
  ///
  /// - [from] and [to]: Optional date range as [DateTime] (local or UTC).
  ///   If either [from] or [to] is not provided, both default to the current three-month UTC range.
  /// - [limit]: Maximum number of results (default: 200).
  /// - [search]: Optional search query to filter events by name or description.
  ///
  /// Filters out events with invalid UUIDs (null or empty) as a safeguard for bad data.
  Future<List<EventModels>> getEventsByFamilyId(
    String familyId, {
    DateTime? from,
    DateTime? to,
    int? limit,
    String? search,
  }) async {
    DateTime? effectiveFrom = from;
    DateTime? effectiveTo = to;
    if (from == null || to == null) {
      final now = DateTime.now();
      final monthRange = CalendarUtils.getThreeMonthRangeUtc(now);
      effectiveFrom = monthRange[0];
      effectiveTo = monthRange[1];
    }
    logd(
        '[$_logTag] Get events from ${effectiveFrom?.toIso8601String()} to ${effectiveTo?.toIso8601String()} (familyId: $familyId, limit: ${limit ?? _eventFetchLimit})');
    try {
      final result = await eventRepository.getEventInFamily(
        familyId,
        from: effectiveFrom,
        to: effectiveTo,
        limit: limit ?? _eventFetchLimit,
        search: search ?? '',
      );
      return result.where((e) => e.uuid != null && e.uuid!.isNotEmpty).toList();
    } catch (e) {
      print('[$_logTag] Error getting events: $e');
      rethrow;
    }
  }

  // --- Fetch and Update Stream ---
  /// Fetches events by familyId and updates the shared stream, keeping the list distinct by uuid.
  ///
  /// If [from] or [to] are not provided, uses the current month's UTC range by default.
  /// [combineAndDedupe] determines if the result should be combined and deduplicated with the current stream.
  /// [limit] can be specified to limit the number of results (default: 200).
  /// Uses [getEventsByFamilyId] internally.
  Future<void> fetchEventByFamilyId(
    String familyId, {
    DateTime? from,
    DateTime? to,
    bool combineAndDedupe = true,
    int? limit,
  }) async {
    try {
      final result = await getEventsByFamilyId(
        familyId,
        from: from,
        to: to,
        limit: limit,
      );
      _lastFrom = from;
      _lastTo = to;
      if (combineAndDedupe) {
        events.value = EventUtils.combineAndDeduplicate(events.value, result);
      } else {
        events.value = result;
      }
    } catch (e) {
      print('[$_logTag] Error fetching events: $e');
      events.value = [];
      rethrow;
    }
  }

  // --- Refresh Stream ---
  /// Refreshes the shared stream using the last used from/to params (caller must provide familyId).
  ///
  /// [combineAndDedupe] determines if the result should be combined and deduplicated with the current stream.
  Future<void> refreshEvents(String familyId,
      {bool combineAndDedupe = true}) async {
    if (_lastFrom == null || _lastTo == null) return;
    await fetchEventByFamilyId(
      familyId,
      from: _lastFrom,
      to: _lastTo,
      combineAndDedupe: combineAndDedupe,
    );
  }

  // --- Delete Event and Refresh ---
  /// Optimistically removes an event from the local stream, then refreshes from API for consistency.
  ///
  /// [eventUuid] is the uuid of the event to remove.
  /// [familyId] is required for the refresh.
  /// [combineAndDedupe] determines if the result should be combined and deduplicated with the current stream.
  Future<void> deleteEventAndRefresh(String eventUuid, String familyId,
      {bool combineAndDedupe = true}) async {
    events.value = events.value.where((e) => e.uuid != eventUuid).toList();
    await refreshEvents(familyId, combineAndDedupe: combineAndDedupe);
  }

  // --- Remove Multiple Events by UUIDs ---
  /// Removes multiple events from the local stream by their uuids.
  ///
  /// [uuids] is a list of event uuids to remove.
  void removeEventsByUuids(List<String> uuids) {
    try {
      if (uuids.isEmpty) return;
      final uuidSet = uuids.toSet();
      final filtered =
          events.value.where((event) => !uuidSet.contains(event.uuid)).toList();
      events.value = filtered;
    } catch (e) {
      print('[$_logTag] Failed to remove events by uuids: $e');
    }
  }

  // --- Update Event and Refresh ---
  /// Optimistically updates an event in the local stream, then refreshes from API for consistency.
  ///
  /// [updatedEvent] is the event to update.
  /// [familyId] is required for the refresh.
  /// [combineAndDedupe] determines if the result should be combined and deduplicated with the current stream.
  Future<void> updateEventAndRefresh(EventModels updatedEvent, String familyId,
      {bool combineAndDedupe = true}) async {
    // 1. Optimistically update the event in local events
    final models = events.value;
    final index = models.indexWhere((e) => e.uuid == updatedEvent.uuid);
    if (index != -1) {
      models[index] = updatedEvent;
      events.value = List<EventModels>.from(models);
    }
    // 2. Refresh from server for consistency using cached from/to params
    await refreshEvents(familyId, combineAndDedupe: combineAndDedupe);
  }
}
