import 'package:family_app/config/constant/app_config.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:family_app/data/model/holiday.dart';

class HolidayService {
  // Cache to prevent redundant API calls
  static final Map<String, Map<String, String>> _holidayCache = {};

  // API key should ideally be stored in a secure config
  static final String _apiKey = AppConfig.HOLIDAY_API_KEY;

  /// Get holidays as a map of "month-day": "holiday name" for any country
  static Future<Map<String, String>> getHolidays(String countryCode, int year, int month) async {
    final cacheKey = '$countryCode-$year-$month';

    // Return cached results if available
    if (_holidayCache.containsKey(cacheKey)) {
      return _holidayCache[cacheKey]!;
    }

    try {
      final url = 'https://calendarific.com/api/v2/holidays?'
          'api_key=$_apiKey&country=$countryCode&year=$year&month=$month&type=national';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode != 200) {
        throw Exception('Failed to load holidays: ${response.statusCode}');
      }

      final data = jsonDecode(response.body);

      // Convert API response to holiday map
      final holidayMap = <String, String>{};

      if (data['response'] != null && data['response']['holidays'] != null) {
        for (var holiday in data['response']['holidays']) {
          final date = DateTime.parse(holiday['date']['iso']);
          final key = '${date.month}-${date.day}';
          holidayMap[key] = holiday['name'];
        }
      }

      // Cache the results
      _holidayCache[cacheKey] = holidayMap;
      return holidayMap;
    } catch (e) {
      print('Error fetching holidays: $e');

      // Empty map as fallback rather than country-specific hardcoded fallbacks
      return {};
    }
  }

  // For detailed holiday objects if needed elsewhere in the app
  static Future<List<Holiday>> getDetailedHolidays(String countryCode, int year, int month) async {
    try {
      final url = 'https://calendarific.com/api/v2/holidays?'
          'api_key=$_apiKey&country=$countryCode&year=$year&month=$month';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode != 200) {
        return [];
      }

      final data = jsonDecode(response.body);

      if (data['response'] == null || data['response']['holidays'] == null) {
        return [];
      }

      return (data['response']['holidays'] as List)
          .map((holiday) => Holiday(
                name: holiday['name'],
                date: DateTime.parse(holiday['date']['iso']),
                type: _determineHolidayType(holiday['type']),
              ))
          .toList();
    } catch (e) {
      print('Error fetching detailed holidays: $e');
      return [];
    }
  }

  // Helper method to determine holiday type from API response
  static HolidayType _determineHolidayType(List<dynamic> types) {
    if (types.contains('national')) return HolidayType.national;
    if (types.contains('religious')) return HolidayType.religious;
    return HolidayType.observance;
  }

  static void clearCache(String key) {
    if (_holidayCache.containsKey(key)) {
      _holidayCache.remove(key);
      print('Cleared holiday cache for $key');
    } else {
      print('No cache found for $key');
    }
  }
}
