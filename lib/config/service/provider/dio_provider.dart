import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/remote/interceptors/amadeus_interceptor.dart';
import 'package:family_app/data/remote/interceptors/default_interceptor.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class DioProvider {
  Dio? _dio;
  Dio? _amadeusDio;

  Dio get dio {
    _dio ??= _newDio();
    return _dio!;
  }

  Dio get amadeusDio {
    _amadeusDio ??= _newAmadeusDio();
    return _amadeusDio!;
  }

  Dio _newDio() {
    final dio = Dio(BaseOptions(
        baseUrl: AppConfig.API_URL,
        followRedirects: true,
        persistentConnection: true));
    final newDio = Dio(BaseOptions(
        baseUrl: AppConfig.API_URL,
        followRedirects: true,
        persistentConnection: true));
    final interceptors = <Interceptor>[
      DefaultInterceptor(
        dio: newDio,
        localStorage: locator.get(),
      )
    ];

    if (!kReleaseMode) {
      interceptors.add(LogInterceptor(
        request: true,
        responseBody: true,
        requestBody: true,
        requestHeader: true,
        logPrint: (value) {
          log(value.toString());
          // AppLogger.d(value.toString());
        },
      ));
      newDio.interceptors.addAll([
        LogInterceptor(
          request: true,
          responseBody: true,
          requestBody: true,
          requestHeader: true,
          logPrint: (value) {
            log('refresh dio ${value.toString()}');
          },
        )
      ]);
    }
    return dio..interceptors.addAll(interceptors);
  }

  Dio _newAmadeusDio() {
    final newDio = Dio(BaseOptions(
        baseUrl: AppConfig.AMADEUS_API_URL,
        followRedirects: true,
        persistentConnection: true));
    final interceptors = <Interceptor>[
      AmadeusInterceptor(
        dio: newDio,
        localStorage: locator.get(),
      )
    ];

    // Enhanced Amadeus API logging - enabled in both debug and release modes
    interceptors.add(LogInterceptor(
      request: true,
      responseBody: true,
      requestBody: true,
      requestHeader: true,
      responseHeader: true,
      error: true,
      logPrint: (value) {
        //AppLogger.d('🔵 AMADEUS: ${value.toString()}');
      },
    ));

    return newDio..interceptors.addAll(interceptors);
  }
}
