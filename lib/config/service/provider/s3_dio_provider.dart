import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/remote/interceptors/default_interceptor.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class S3DioProvider {
  Dio? _dio;

  Dio get dio {
    _dio ??= _newDio();
    return _dio!;
  }

  Dio _newDio() {
    final dio =
        Dio(BaseOptions(followRedirects: true, persistentConnection: true));
    final interceptors = <Interceptor>[];
    interceptors.add(LogInterceptor(
      request: true,
      responseBody: true,
      requestBody: true,
      requestHeader: true,
      logPrint: (value) {
        // log(value.toString());
        AppLogger.d(value.toString());
      },
    ));
    return dio..interceptors.addAll(interceptors);
  }
}
