// ignore_for_file: public_member_api_docs, sort_constructors_first
// import 'dart:io';

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/socket/base_socket_service.dart';
import 'package:family_app/data/model/message.dart';
import 'package:family_app/widget/image/image_picker_handler.dart';
import 'package:injectable/injectable.dart';
import 'package:web_socket/web_socket.dart';

@Singleton(as: BaseSocketService)
class SocketService extends BaseSocketService {
  SocketService({required super.localStorage});

  late WebSocket? _socket = null;

  late StreamSubscription<WebSocketEvent>? _eventSubscription = null;

  @override
  @disposeMethod
  void dispose() {
    onDisconnect();
    messageStream.dispose();
  }

  @override
  void onDisconnect() {
    if (_socket != null) {
      _eventSubscription?.cancel();
      _eventSubscription = null;
      _socket?.close();
      _socket = null;
      messageStream.value = [];
    }
  }

  @override
  Future<void> onConnect() async {
    try {
      if (_socket != null) {
        return;
      }
      final token = await localStorage.accessToken();
      _socket = await WebSocket.connect(Uri.parse(AppConfig.SOCKET_URL), headers: {'Authorization': token});
    } catch (e) {
      print(e);
    }
  }

  @override
  void onStartListen() {
    if (_socket == null || _eventSubscription != null) return;
    _eventSubscription = _socket!.events.listen((event) {
      if (event is TextDataReceived) {
        log('new message socket ${event.text}');
        final model = jsonDecode(event.text);
        final message = Message.fromJson(model);

        /// hide message image with ai response
        if ((message.file ?? '').isNotEmpty) {
          return;
        }

        messageStream.addValue(message);
      }
    });
  }

  @override
  Future<void> onSendMessage(String message, {String command = '', File? imageFile}) async {
    if (_socket != null) {
      Message model = Message(command: command.isNotEmpty ? command : SocketEvent.SEND_MESSAGE, message: message);

      if (imageFile != null) {
        String base64Image = await ImagePickerHandler.resizeAndEncodeImage(imageFile);
        model = model.copyWith(file: base64Image, imageFile: imageFile);
      }
      messageStream.addValue(model);

      try {
        _socket!.sendText(jsonEncode(model));
      } catch (e) {
        rethrow;
      }
    }
  }
}
