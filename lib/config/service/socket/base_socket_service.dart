import 'dart:io';

import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/message.dart';

abstract class BaseSocketService {
  final LocalStorage localStorage;

  BaseSocketService({required this.localStorage});

  final messageStream = BaseListStreamController<Message>([]);

  void dispose();
  void onDisconnect();
  Future<void> onConnect();
  void onStartListen();
  Future<void> onSendMessage(String message, {String command = '', File? imageFile});
}
