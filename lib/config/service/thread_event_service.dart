import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:get_it/get_it.dart';

/// Service to fetch event data from event UUIDs (no caching)
class ThreadEventService {
  static final ThreadEventService _instance = ThreadEventService._internal();
  factory ThreadEventService() => _instance;
  ThreadEventService._internal();

  /// Fetch event data from API
  Future<EventModels?> getEventById(String uuid) async {
    if (uuid.isEmpty) {
      return null;
    }
    try {
      final eventRepository = GetIt.I<IEventRepository>();
      final event = await eventRepository.getEventById(uuid);
      if (event != null) {
        return event;
      } else {
        return null;
      }
    } catch (e) {
      AppLogger.e(
          'ThreadEventService: Error fetching event for UUID: $uuid, Error: $e');
      return null;
    }
  }

  /// Get multiple events efficiently
  Future<Map<String, EventModels?>> getMultipleEvents(
      List<String> uuids) async {
    if (uuids.isEmpty) return {};

    final futures = uuids.map((uuid) async {
      final event = await getEventById(uuid);
      return MapEntry(uuid, event);
    });

    final results = await Future.wait(futures);
    final resultMap = Map.fromEntries(results);

    return resultMap;
  }
}

final threadEventService = ThreadEventService();
