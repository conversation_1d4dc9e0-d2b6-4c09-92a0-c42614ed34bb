// ignore_for_file: depend_on_referenced_packages

import 'dart:async' show StreamSubscription, Timer;
import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/constant/app_constant.dart' show premiums;
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_lifec_cycle_service.dart';
import 'package:family_app/data/repository/premium/ipremium_repository.dart';
import 'package:flutter/material.dart'
    show ValueListenableBuilder, ValueNotifier, ValueWidgetBuilder, Widget, debugPrint;
import 'package:flutter/services.dart' show PlatformException;
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:injectable/injectable.dart';

@singleton
class InAppPurchaseService {
  InAppPurchaseService(this._premiumrepository, this._accountService, AppLifecycleService _appLifeCycleService) {
    _getPremiumExpiresAt();
    _accountService.addListenerLogged(_getPremiumExpiresAt);
    _premiumSubscription = InAppPurchase.instance.purchaseStream.listen(_onPurchase);
    _appLifeCycleService.addListener(_onUseApp);
  }

  final AccountService _accountService;
  final IPremiumRepository _premiumrepository;
  late final StreamSubscription<List<PurchaseDetails>> _premiumSubscription;
  final _isPremium = ValueNotifier(false);
  late final _premiumExpiresAt = ValueNotifier(0)..addListener(_countdownPremium);
  Iterable<ProductDetails>? _listPremium;
  Timer? _timerPremium;
  var _countPurchase = 0;
  final _isLoading = ValueNotifier(false);
  var _checking = false;

  Iterable<ProductDetails>? get listPremium => _listPremium;
  bool get isPremium => _isPremium.value;

  @disposeMethod
  void dispose() {
    _timerPremium?.cancel();
    _premiumSubscription.cancel();
    _isLoading.dispose();
  }

  Widget premiumBuilder(ValueWidgetBuilder<bool> builder, [Widget? child]) =>
      ValueListenableBuilder(valueListenable: _isPremium, builder: builder, child: child);

  void _setLoading(bool v) => _isLoading.value = _checking || v;

  void _onPurchase(List<PurchaseDetails> list) async {
    ++_countPurchase;
    final countPurchase = _countPurchase + 0;

    if (list.any((e) => e.status == PurchaseStatus.pending)) _setLoading(true);

    final r = await Future.wait(list.where((e) {
      if (e.status == PurchaseStatus.canceled && e.pendingCompletePurchase) InAppPurchase.instance.completePurchase(e);

      return e.status == PurchaseStatus.purchased || e.status == PurchaseStatus.restored;
    }).map(
      (e) => () async {
        try {
          final r = await _premiumrepository.validatePurchase(e.purchaseID!);

          if (e.pendingCompletePurchase) InAppPurchase.instance.completePurchase(e);

          if (DateTime.now().millisecondsSinceEpoch < r) return true;
        } catch (e) {
          debugPrint('validate purchase error: ${e.toString()}');
        }

        return false;
      }(),
    ));

    if (r.any((e) => e) && _accountService.isLogged && !_isPremium.value) {
      var count = 0;
      _checking = true;

      if (!_isPremium.value) {
        while (count < 4) {
          if (_isPremium.value || countPurchase != _countPurchase) break;
          await _getPremiumExpiresAt();

          await Future.delayed(const Duration(seconds: 5));

          ++count;
        }
      }

      _checking = false;

      if (countPurchase == _countPurchase) _setLoading(false);
    } else if (list.every((e) => e.status != PurchaseStatus.pending)) _setLoading(false);
  }

  Future<void> _getPremiumExpiresAt([bool? isLogged]) async {
    if (_accountService.isLogged || (isLogged ?? false)) {
      try {
        final r = await _premiumrepository.getPremiumExpiresAt();

        _premiumExpiresAt.value = r;
      } catch (e) {
        debugPrint('get is premium error: ${e.toString()}');
      }
    }
  }

  Future<void> getListPremium() async {
    if (_listPremium != null) return;

    final isAvailable = await InAppPurchase.instance.isAvailable();

    if (!isAvailable) throw 'error_iap_not_available'.tr();

    final response = await InAppPurchase.instance.queryProductDetails(premiums);

    if (response.error != null) throw response.error!.message;

    if (response.notFoundIDs.isNotEmpty) return;
    _listPremium = response.productDetails;
  }

  Future<void> buyPremium(ProductDetails v) async {
    _setLoading(true);
    try {
      await InAppPurchase.instance.buyNonConsumable(
        purchaseParam: PurchaseParam(productDetails: v, applicationUserName: _accountService.account?.uuid),
      );
    } catch (e) {
      if (e is PlatformException && e.code == 'storekit_duplicate_product_object')
        _restorePurchases();
      else
        debugPrint('buy premium error: ${e.toString()}');
    }
  }

  void _onUseApp(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) _countdownPremium();
  }

  void _countdownPremium() {
    _timerPremium?.cancel();

    final now = DateTime.now().millisecondsSinceEpoch;

    if (_premiumExpiresAt.value < now) {
      _isPremium.value = false;
      return;
    }

    _isPremium.value = true;

    _timerPremium = Timer(Duration(milliseconds: _premiumExpiresAt.value - now), () {
      _isPremium.value = false;
      _getPremiumExpiresAt();
    });
  }

  void _restorePurchases() async {
    try {
      await InAppPurchase.instance.restorePurchases(applicationUserName: _accountService.account?.uuid);
    } catch (e2) {
      debugPrint('restore purchases error: ${e2.toString()}');
    }
  }

  LoadingListener addListenerLoading(void Function(bool isLoading) callback) {
    listener() {
      callback(_isLoading.value);
    }

    _isLoading.addListener(listener);

    return LoadingListener._(() {
      _isLoading.removeListener(listener);
    });
  }
}

class LoadingListener {
  final VoidCallback _remove;

  LoadingListener._(this._remove);

  void dispose() => _remove();
}
