import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:archive/archive_io.dart';
import 'package:archive/archive.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';

class LogService {
  static const int _maxLogSize = 5 * 1024 * 1024; // 5MB
  static const int _maxLogParts = 3; // Active + 2 archived parts
  static const String _activeLogFileName = 'app_log_active.txt';
  static const String _logPartPrefix = 'app_log_part_';
  static const String _logExtension = '.txt';

  final Directory _logDirectory;
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  static const MethodChannel _platformLogChannel = MethodChannel('app_logs/native_logs');

  LogService(this._logDirectory);

  Future<void> writeLog(String message) async {
    final activeLogFile = File('${_logDirectory.path}/$_activeLogFileName');

    // Check if we need to rotate logs
    if (await activeLogFile.exists() &&
        await activeLogFile.length() >= _maxLogSize) {
      await _rotateLogs();
    }

    // Write to active log file
    await activeLogFile.writeAsString(
        '${DateTime.now().toIso8601String()}: $message\n',
        mode: FileMode.append);
  }

  Future<void> _rotateLogs() async {
    // Shift existing log parts
    for (int i = _maxLogParts - 1; i > 0; i--) {
      final oldFile =
          File('${_logDirectory.path}/$_logPartPrefix$i$_logExtension');
      final newFile =
          File('${_logDirectory.path}/$_logPartPrefix${i + 1}$_logExtension');

      if (await oldFile.exists()) {
        if (await newFile.exists()) {
          await newFile.delete();
        }
        await oldFile.rename(newFile.path);
      }
    }

    // Rename active log to part 1
    final activeLogFile = File('${_logDirectory.path}/$_activeLogFileName');
    if (await activeLogFile.exists()) {
      final part1File =
          File('${_logDirectory.path}/$_logPartPrefix${1}$_logExtension');
      if (await part1File.exists()) {
        await part1File.delete();
      }
      await activeLogFile.rename(part1File.path);
    }

    // Create new active log file
    await activeLogFile.create();
  }

  Future<String> _getDeviceInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final deviceInfo = await _deviceInfo.deviceInfo;

    return '''
App Version: ${packageInfo.version} (${packageInfo.buildNumber})
OS Version: ${deviceInfo.data['version']}
Device Model: ${deviceInfo.data['model']}
Export Time: ${DateTime.now().toIso8601String()}
''';
  }

  /// Fetches native platform logs (Android Logcat or iOS system logs)
  Future<String> _getNativePlatformLogs() async {
    try {
      final logs = await _platformLogChannel.invokeMethod<String>('getNativeLogs');
      return logs ?? '[No native platform logs available]';
    } catch (e) {
      return '[Failed to fetch native platform logs: ${e.toString()}]';
    }
  }

  /// Exports all logs, compresses them, and opens the share dialog.
  Future<void> exportAndShareLogs() async {
    try {
      final isIOS = Platform.isIOS;
      final dir = isIOS
        ? await getApplicationDocumentsDirectory()
        : await getTemporaryDirectory();

      // Ensure there is at least some log content
      final activeLogFilePath = '${_logDirectory.path}/$_activeLogFileName';
      final activeLogFile = File(activeLogFilePath);
      if (!await activeLogFile.exists() || await activeLogFile.length() == 0) {
        for (int i = 0; i < 5; i++) {
          await writeLog('Test log entry #$i for export at: '
              + DateTime.now().toIso8601String());
        }
      }

      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final exportFileName = 'app_logs_export_$timestamp.txt';
      final zipFileName = 'app_logs_export_$timestamp.zip';
      final combinedLogFile = File('${dir.path}/$exportFileName');
      await combinedLogFile.create();

      // Write device/app info
      await combinedLogFile.writeAsString(await _getDeviceInfo());

      // Append all log parts in order
      for (int i = _maxLogParts; i > 0; i--) {
        final logFile = File('${_logDirectory.path}/$_logPartPrefix$i$_logExtension');
        if (await logFile.exists()) {
          final content = await logFile.readAsString();
          if (content.isNotEmpty) {
            await combinedLogFile.writeAsString(content, mode: FileMode.append);
          }
        }
      }
      // Append active log file
      if (await activeLogFile.exists()) {
        final content = await activeLogFile.readAsString();
        if (content.isNotEmpty) {
          await combinedLogFile.writeAsString(content, mode: FileMode.append);
        }
      }

      // Append native platform logs
      final nativeLogs = await _getNativePlatformLogs();
      await combinedLogFile.writeAsString(
        '\n\n===== Native Platform Logs =====\n',
        mode: FileMode.append,
      );
      await combinedLogFile.writeAsString(nativeLogs, mode: FileMode.append);

      // Ensure export file is not empty
      if (!await combinedLogFile.exists() || await combinedLogFile.length() == 0) {
        throw Exception('Exported log file is empty');
      }

      // Create ZIP archive using helper
      final zipFile = await _createZipArchive(
        combinedLogFile: combinedLogFile,
        zipFilePath: '${dir.path}/$zipFileName',
        exportFileName: exportFileName,
      );

      // Debug: print file paths and sizes before sharing
      print('Combined log file path: \\${combinedLogFile.path}');
      print('Combined log file size: \\${await combinedLogFile.length()}');
      print('ZIP file path: \\${zipFile.path}');
      print('ZIP file size: \\${await zipFile.length()}');

      // Share the ZIP file
      await Share.shareXFiles([XFile(zipFile.path)], text: 'Application Logs');
    } catch (e) {
      print('Error exporting logs: $e');
      rethrow;
    }
  }

  /// Helper to create a ZIP archive from a single file
  Future<File> _createZipArchive({
    required File combinedLogFile,
    required String zipFilePath,
    required String exportFileName,
  }) async {
    final logBytes = await combinedLogFile.readAsBytes();
    final archive = Archive();
    archive.addFile(ArchiveFile(exportFileName, logBytes.length, logBytes));
    final zipData = ZipEncoder().encode(archive);
    final zipFile = File(zipFilePath);
    await zipFile.writeAsBytes(zipData!);
    if (!await zipFile.exists() || await zipFile.length() == 0) {
      throw Exception('Failed to create ZIP file or ZIP file is empty');
    }
    return zipFile;
  }
}
