import 'package:family_app/data/repository/family/ifamily_repository.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/utils/log/app_logger.dart';

/// Global service to cache image URLs from storage UUIDs
/// This prevents reloading images that have already been fetched
class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();

  final Map<String, String?> _cache = {};
  final Map<String, Future<String?>> _pendingRequests = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Duration _cacheExpiry =
      const Duration(hours: 1); // Cache URLs for 1 hour

  /// Get image URL from UUID, using cache if available
  Future<String?> getImageUrl(String uuid) async {
    // Clean up expired entries first
    _cleanupExpiredEntries();

    // Check if cached URL is still valid
    if (_cache.containsKey(uuid) && _cache[uuid] != null) {
      final timestamp = _cacheTimestamps[uuid];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _cache[uuid];
      } else {
        // Cache expired, remove it
        _cache.remove(uuid);
        _cacheTimestamps.remove(uuid);
      }
    }

    // If there's already a pending request for this UUID, wait for it
    if (_pendingRequests.containsKey(uuid)) {
      final result = await _pendingRequests[uuid]!;
      return result;
    }

    // Create new request
    final future = _fetchAndCacheImage(uuid);
    _pendingRequests[uuid] = future;

    final result = await future;
    _pendingRequests.remove(uuid);
    return result;
  }

  /// Preload avatar URLs for better performance
  Future<void> preloadAvatarUrls(List<String> uuids) async {
    if (uuids.isEmpty) return;
    
    // Filter out already cached UUIDs
    final uncachedUuids = uuids.where((uuid) => 
        !_cache.containsKey(uuid) || _cache[uuid] == null).toList();
    
    if (uncachedUuids.isEmpty) {
      return;
    }
    
    // Preload in parallel
    final futures = uncachedUuids.map((uuid) => getImageUrl(uuid));
    await Future.wait(futures);
  }

  /// Get multiple image URLs efficiently
  Future<Map<String, String?>> getMultipleImageUrls(List<String> uuids) async {
    if (uuids.isEmpty) return {};
    
    final futures = uuids.map((uuid) async {
      final url = await getImageUrl(uuid);
      return MapEntry(uuid, url);
    });
    
    final results = await Future.wait(futures);
    final resultMap = Map.fromEntries(results);
    return resultMap;
  }

  /// Fetch image URL and cache the result
  Future<String?> _fetchAndCacheImage(String uuid) async {
    try {
      final url = await locator.get<IFamilyRepository>().getStorageById(uuid);

      if (url != null && url.isNotEmpty) {
        _cache[uuid] = url;
        _cacheTimestamps[uuid] = DateTime.now();
        return url;
      } else {
        AppLogger.e('🖼️ ImageCache: Empty URL returned for UUID: $uuid');
        _cache[uuid] = null;
        return null;
      }
    } catch (e) {
      AppLogger.e(
          '🖼️ ImageCache: Error fetching URL for UUID: $uuid, Error: $e');
      _cache[uuid] = null;
      return null;
    }
  }

  /// Clean up expired cache entries
  void _cleanupExpiredEntries() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) >= _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  /// Clear the cache (useful for memory management)
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
    _pendingRequests.clear();
    AppLogger.d('🖼️ ImageCache: Cache cleared');
  }

  /// Remove specific UUID from cache
  void removeFromCache(String uuid) {
    _cache.remove(uuid);
    _cacheTimestamps.remove(uuid);
    _pendingRequests.remove(uuid);
  }

  /// Get cache size for debugging
  int get cacheSize => _cache.length;

  /// Get pending requests count for debugging
  int get pendingRequestsCount => _pendingRequests.length;

  /// Get cache expiry duration
  Duration get cacheExpiry => _cacheExpiry;

  /// Check if an image URL is cached in the CachedNetworkImage cache
  bool isImageCached(String imageUrl) {
    // This is a simple check - in a real implementation, you might want to check
    // the actual CachedNetworkImage cache, but for now we'll assume if we have
    // the URL in our cache, the image is likely cached
    return _cache.containsValue(imageUrl);
  }
}

// Global instance
final imageCacheService = ImageCacheService();
