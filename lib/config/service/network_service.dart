import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/provider/dio_provider.dart';
import 'package:family_app/config/service/provider/s3_dio_provider.dart';
import 'package:family_app/data/remote/activity_api.dart';
import 'package:family_app/data/remote/ai_butler_api.dart';
import 'package:family_app/data/remote/amadeus_api.dart';
import 'package:family_app/data/remote/authentication_api.dart';
import 'package:family_app/data/remote/category_api.dart';
import 'package:family_app/data/remote/event_api.dart';
import 'package:family_app/data/remote/family_api.dart';
import 'package:family_app/data/remote/list_api.dart';
import 'package:family_app/data/remote/premium_api.dart';
import 'package:family_app/data/remote/s3_api.dart';
import 'package:family_app/data/remote/thread_api.dart';
import 'package:injectable/injectable.dart';

import 'package:family_app/data/remote/thread_poll_api.dart';

@module
abstract class NetworkService {
  @lazySingleton
  AuthenticationAPI authenticationApiProvider(DioProvider provider) => AuthenticationAPI(provider.dio);

  @lazySingleton
  FamilyApi familyApiProvider(DioProvider provider) => FamilyApi(provider.dio);

  @lazySingleton
  CategoryAPI categoryApiProvider(DioProvider provider) => CategoryAPI(provider.dio);

  @lazySingleton
  ListAPI listApiProvider(DioProvider provider) => ListAPI(provider.dio);

  @lazySingleton
  ActivityAPI activityApiProvider(DioProvider provider) => ActivityAPI(provider.dio);

  @lazySingleton
  EventAPI eventApiProvider(DioProvider provider) => EventAPI(provider.dio);

  @lazySingleton
  AIButlerAPI aiButlerApiProvider(DioProvider provider) => AIButlerAPI(provider.dio);

  @lazySingleton
  ThreadAPI threadApiProvider(DioProvider provider) => ThreadAPI(provider.dio);

  @lazySingleton
  ThreadPollAPI threadPollApiProvider(DioProvider provider) => ThreadPollAPI(provider.dio);

  @lazySingleton
  S3API s3ApiProvider(S3DioProvider provider) => S3API(provider.dio);

  @lazySingleton
  PremiumAPI premiumApiProvider(DioProvider provider) => PremiumAPI(provider.dio);

  @lazySingleton
  AmadeusAPI amadeusApiProvider(DioProvider provider) => AmadeusAPI(provider.amadeusDio);

}
