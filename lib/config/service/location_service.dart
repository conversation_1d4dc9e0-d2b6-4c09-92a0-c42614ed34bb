import 'package:http/http.dart' as http;
import 'dart:convert';

class LocationService {
  /// Get user's country based on IP
  static Future<String> getUserCountry() async {
    try {
      // Option A: ipinfo.io (free tier: 50k requests/month)
      final response = await http.get(Uri.parse('https://ipinfo.io/json'));
      final data = jsonDecode(response.body);
      return data['country'] ?? 'US'; // Returns ISO country code

      // Option B: ip-api.com (free tier: 45 requests/minute)
      // final response = await http.get(Uri.parse('http://ip-api.com/json'));
      // final data = jsonDecode(response.body);
      // return data['countryCode'] ?? 'US';
    } catch (e) {
      print('Error detecting location: $e');
      return 'US'; // Default fallback
    }
  }
}
