import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_controller.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

const vietnamCode = 'vi';
const englishCode = 'en';
const vietnam = Locale('vi', "VN");
const english = Locale('en', "US");

const List<Locale> supportedLocales = <Locale>[english, vietnam];

extension LocaleExtension on Locale {
  String get translate {
    switch (languageCode) {
      case vietnamCode:
        return 'vietnamese'.tr();

      default:
        return 'english'.tr();
    }
  }
}

@singleton
class LanguageService {
  final LocalStorage localStorage;

  LanguageService({required this.localStorage}) {
    final code = localStorage.languageCode;
    _languageStream = BaseStreamController(_getLocaleByCode(code));
  }

  BaseStreamController<Locale> get language => _languageStream;
  late final BaseStreamController<Locale> _languageStream;

  void onSaveLanguage(String code) {
    localStorage.cacheLanguageCode(code);
    _languageStream.value = _getLocaleByCode(code);
    navigatorKey.currentContext!.setLocale(_getLocaleByCode(code));
  }

  Locale _getLocaleByCode(String code) {
    switch (code) {
      case vietnamCode:
        return vietnam;

      default:
        return english;
    }
  }
}
