import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/notification/background_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/family_list/member_list/member_list_parameter.dart';
import 'package:family_app/screen/main/check_list/list_detail/list_detail_parameter.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:family_app/screen/main/home/<USER>/detail_activity_parameter.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:injectable/injectable.dart';
import 'package:family_app/screen/main/thread_detail/thread_detail_cubit.dart';
import 'package:family_app/screen/main/thread/thread_cubit.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/main.dart';
import 'package:family_app/config/service/notification/utils.dart';

/// Service responsible for handling all notification-related functionality
/// including Firebase Cloud Messaging, local notifications, and LSNB (Local Storage Notification Bridge)
/// 
/// This service handles notifications in all app states:
/// - Foreground: Direct processing and UI updates
/// - Background: Storage for later processing when app becomes active
/// - Terminated: Storage and processing when app launches
@singleton
class NotificationService {
  final LocalStorage localStorage;
  final AccountService accountService;

  NotificationService({
    required this.localStorage,
    required this.accountService,
    FirebaseMessaging? messaging,
    FlutterLocalNotificationsPlugin? plugin,
  })  : _messaging = messaging ?? FirebaseMessaging.instance,
        _plugin = plugin ?? FlutterLocalNotificationsPlugin();

  late final StreamSubscription<RemoteMessage> _onMessageSub;
  late final StreamSubscription<RemoteMessage> _onMessageOpenAppSub;
  late final StreamSubscription<String> _onTokenRefresh;
  Timer? _cleanupTimer;

  final FirebaseMessaging _messaging;
  final FlutterLocalNotificationsPlugin _plugin;

  /// Key for storing pending notification data in local storage
  static const String pendingNotificationKey = 'pending_notification_data';
  
  /// Flag to prevent multiple simultaneous initial message handling
  static bool _isHandlingInitialMessage = false;
  
  /// Flag to track if notification processing has been deferred
  static bool _isProcessingDeferred = false;

  @disposeMethod
  void onClose() {
    _onTokenRefresh.cancel();
    _onMessageSub.cancel();
    _onMessageOpenAppSub.cancel();
    _cleanupTimer?.cancel();
  }

  /// Store pending notification data for handling when app is terminated
  Future<void> _storePendingNotification(Map<String, dynamic> data) async {
    try {
      // Add timestamp to the notification data
      final dataWithTimestamp = Map<String, dynamic>.from(data);
      dataWithTimestamp['timestamp'] = DateTime.now().toIso8601String();
      
      await localStorage.savePendingNotification(dataWithTimestamp);
      AppLogger.d('Stored pending notification: $dataWithTimestamp');
    } catch (e) {
      AppLogger.e('Error storing pending notification: $e');
    }
  }

  /// Get stored pending notification data
  Future<Map<String, dynamic>?> _getPendingNotification() async {
    try {
      final data = await localStorage.getPendingNotification();
      if (data != null) {
        AppLogger.d('Retrieved pending notification: $data');
      }
      return data;
    } catch (e) {
      AppLogger.e('Error retrieving pending notification: $e');
    }
    return null;
  }

  /// Clear stored pending notification data
  Future<void> _clearPendingNotification() async {
    try {
      await localStorage.clearPendingNotification();
      AppLogger.d('Cleared pending notification');
    } catch (e) {
      AppLogger.e('Error clearing pending notification: $e');
    }
  }

  /// Request notification permissions from the user
  /// 
  /// Returns true if permission is granted, false otherwise
  /// Handles platform-specific permission requests for Android and iOS
  Future<bool?> onRequestPermission() async {
    try {
      if (Platform.isAndroid) {
        return _plugin
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()!
            .requestNotificationsPermission();
      } else {
        return _plugin
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(alert: true, badge: true, sound: true);
      }
    } catch (e) {
      return false;
    }
  }

  /// Initialize the notification service
  /// 
  /// Sets up Firebase messaging listeners, local notification plugin,
  /// and starts periodic cleanup of stale notifications
  @PostConstruct(preResolve: true)
  Future<void> onInit() async {
    _plugin.initialize(
      const InitializationSettings(
          android: AndroidInitializationSettings('@drawable/ic_notification'),
          iOS: DarwinInitializationSettings(
            requestAlertPermission: true,
            requestSoundPermission: true,
            requestBadgePermission: true,
            // onDidReceiveLocalNotification: (id, title, body, payload) {
            //   _plugin.cancel(id);
            //   onHandleNotification(jsonDecode(payload ?? '{}'));
            // }
          )),
      onDidReceiveBackgroundNotificationResponse: onNotificationTapBackground,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
    );

    _onMessageOpenAppSub = FirebaseMessaging.onMessageOpenedApp.listen((message) {
      AppLogger.d('App opened from notification: ${message.data}');
      onHandleNotification(message.data);
    });

    _onMessageSub = FirebaseMessaging.onMessage.listen((message) {
      if (message.notification != null) {
        showNotification(message);
        final data = message.data;
        // If this is a thread message notification, trigger refresh in thread detail
        if (data['type'] == SocketEvent.THREAD && data['uuid'] != null) {
          // Call the static callback if registered
          ThreadDetailCubit.onNewThreadMessageNotification?.call(data['uuid']);
          // Also refresh the thread list
          ThreadCubit.onThreadListNotification?.call(data['uuid']);
        }
        IsolateNameServer.lookupPortByName(
                AppConfig.notificationRoleReceivePort)
            ?.send(message.toMap());
      }
    });

    _onTokenRefresh = _messaging.onTokenRefresh.listen((token) async {
      final accessToken = await localStorage.accessToken();
      if (accessToken != null && accountService.account != null) {
        // final newAcc = accountService.account!.copyWith(lastToken: token);
        // await updateProfileUsecase.call(newAcc.toJson());
      }
    });

    // Start periodic cleanup of stale notification data
    _startPeriodicCleanup();
  }

  /// Start periodic cleanup of stale notification data
  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(const Duration(hours: 1), (timer) async {
      try {
        await _cleanupStaleNotifications();
      } catch (e) {
        AppLogger.e('Error during periodic cleanup: $e');
      }
    });
  }

  /// Clean up stale notification data (older than 24 hours)
  Future<void> _cleanupStaleNotifications() async {
    try {
      final pendingNotification = await _getPendingNotification();
      if (pendingNotification != null) {
        final timestamp = pendingNotification['timestamp'] as String?;
        if (timestamp != null) {
          final notificationTime = DateTime.parse(timestamp);
          final now = DateTime.now();
          final difference = now.difference(notificationTime);
          
          // Clear notifications older than 24 hours
          if (difference.inHours > 24) {
            AppLogger.d('Cleaning up stale notification data');
            await _clearPendingNotification();
          }
        }
      }
    } catch (e) {
      AppLogger.e('Error cleaning up stale notifications: $e');
    }
  }

  /// Perform health check on the LSNB system
  /// 
  /// Returns a map containing the health status, pending notification info,
  /// and timestamp of the check
  Future<Map<String, dynamic>> healthCheck() async {
    try {
      final pendingNotification = await _getPendingNotification();
      final hasPendingNotification = pendingNotification != null;
      
      final healthStatus = {
        'status': 'healthy',
        'hasPendingNotification': hasPendingNotification,
        'pendingNotificationType': pendingNotification?['type'] ?? 'none',
        'pendingNotificationTimestamp': pendingNotification?['timestamp'] ?? 'none',
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      AppLogger.d('LSNB Health Check: $healthStatus');
      return healthStatus;
    } catch (e) {
      AppLogger.e('LSNB Health Check failed: $e');
      return {
        'status': 'unhealthy',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Enhanced method to handle initial messages and pending notifications with better state management
  Future<void> onHandleInitialMessage() async {
    AppLogger.d('Handling initial message...');
    
    // Prevent multiple simultaneous initial message handling
    if (_isHandlingInitialMessage) {
      AppLogger.d('Initial message handling already in progress, skipping');
      return;
    }
    _isHandlingInitialMessage = true;
    
    try {
      // Wait for app to be fully initialized
      await Future.delayed(const Duration(milliseconds: 1000));
      
      // First, check for Firebase initial message (app opened from notification)
      final lastMessage = await _messaging.getInitialMessage();
      if (lastMessage != null) {
        AppLogger.d('App opened from Firebase notification: ${lastMessage.data}');
        await _storePendingNotification(lastMessage.data);
        await NotificationUtils.handleNotificationWithDelay(
          lastMessage.data,
          storePendingNotification: _storePendingNotification,
          onHandleNotification: onHandleNotification,
        );
        return;
      }

      // Check for local notification that launched the app
      final lastNotification = await _plugin.getNotificationAppLaunchDetails();
      if (lastNotification != null &&
          lastNotification.didNotificationLaunchApp &&
          lastNotification.notificationResponse != null) {
        AppLogger.d('App opened from local notification: ${lastNotification.notificationResponse!.payload}');
        final payload = jsonDecode(lastNotification.notificationResponse!.payload ?? '{}') as Map<String, dynamic>;
        await _storePendingNotification(payload);
        await NotificationUtils.handleNotificationWithDelay(
          payload,
          storePendingNotification: _storePendingNotification,
          onHandleNotification: onHandleNotification,
        );
        return;
      }
    } catch (e) {
      AppLogger.e('Error handling initial message: $e');
    } finally {
      _isHandlingInitialMessage = false;
    }
  }

  /// Handle deferred notification processing after UI is ready
  Future<void> handleDeferredNotifications() async {
    if (_isProcessingDeferred) {
      AppLogger.d('Deferred notification processing already in progress, skipping');
      return;
    }
    
    _isProcessingDeferred = true;
    AppLogger.d('Starting deferred notification processing...');
    
    try {
      // Wait for UI to be fully ready
      await NotificationUtils.waitForUIReady();
      
      // Process initial messages
      await onHandleInitialMessage();
      
      // Perform health check
      final healthStatus = await healthCheck();
      AppLogger.d('LSNB Health Check after deferred processing: $healthStatus');
      
    } catch (e) {
      AppLogger.e('Error in deferred notification processing: $e');
    } finally {
      _isProcessingDeferred = false;
    }
  }

  Future<String?> getFcmToken() async {
    // if (Platform.isIOS) {
    //   return _messaging.getAPNSToken();
    // }
    try {
      return _messaging.getToken();
    } catch (e) {
      return null;
    }
  }

  /// Deletes the current FCM token from the device.
  /// This prevents the device from receiving further push notifications
  /// until a new token is generated and registered.
  Future<void> deleteFcmToken() async {
    try {
      await _messaging.deleteToken();
      AppLogger.d('FCM token deleted successfully');
    } catch (e) {
      AppLogger.e('Error deleting FCM token: $e');
    }
  }

  void onDidReceiveNotificationResponse(NotificationResponse notificationResponse) {
    onHandleNotification((jsonDecode(notificationResponse.payload ?? '{}') as Map<String, dynamic>));
  }

  void showNotification(RemoteMessage message) {
    _plugin.show(
      0,
      message.notification?.title ?? '',
      message.notification?.body ?? '',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'channel id',
          'channel name',
          color: Color(0xFF434336),
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(presentSound: true),
      ),
      payload: jsonEncode(message.data),
    );
  }

  /// Handle notification payload and route to appropriate handler based on type
  /// 
  /// [payload] contains the notification data with type and uuid fields
  /// Routes to specific handlers for different notification types (list, member, thread, etc.)
  Future<void> onHandleNotification(Map<String, dynamic> payload) async {
    AppLogger.d('onHandleNotification $payload');
    final type = payload['type'] as String? ?? payload['event_type'] as String? ?? '';
    final uuid = payload['uuid'] as String? ?? payload['task_id'] as String? ?? '';
    final familyId = payload['family_id'] as String? ?? '';
    AppLogger.d('payload $payload');
    if (type.isEmpty && uuid.isEmpty) return;
    switch (type) {
      case SocketEvent.LIST:
      case SocketEvent.TASK_ASSIGNED:
        navigatorKey.currentContext!.pushRoute(
          ListDetailRoute(parameter: ListDetailParameter(uuid: uuid)),
        );
        break;
      case SocketEvent.EVENT:
        navigatorKey.currentContext!.pushRoute(
          DetailEventRoute(parameter: DetailEventParameter(uuid: uuid)),
        );
        break;
      case SocketEvent.TRIP:
        navigatorKey.currentContext!.pushRoute(
          DetailActivityRoute(parameter: DetailActivityParameter(activityId: uuid)),
        );
        break;
      case SocketEvent.MEMBER:
        navigatorKey.currentContext!.pushRoute(
          MemberListRoute(
            parameter: MemberListParameter(
              familyId: familyId,
            ),
          ),
        );
        break;
      case SocketEvent.THREAD:
        // Trigger refresh in thread detail and thread list (for background/terminated)
        if (uuid.isNotEmpty) {
          ThreadDetailCubit.onNewThreadMessageNotification?.call(uuid);
          ThreadCubit.onThreadListNotification?.call(uuid);
        }
        await NotificationUtils.handleThreadNotification(uuid);
        break;
    }
  }
}
