import 'dart:convert';

import 'package:family_app/utils/log/app_logger.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Handle background notification tap when app is not in foreground
/// 
/// Stores the notification data for later processing when the app becomes active
/// This ensures notifications are not lost when the app is in background
@pragma('vm:entry-point')
void onNotificationTapBackground(NotificationResponse notificationResponse) {
  try {
    AppLogger.d('Background notification tapped: ${notificationResponse.payload}');
    
    final payload = jsonDecode(notificationResponse.payload ?? '{}') as Map<String, dynamic>;
    
    _storePendingNotificationInBackground(payload);
    
    AppLogger.d('Background notification stored for later handling');
  } catch (e) {
    AppLogger.e('Error handling background notification tap: $e');
  }
}

/// Handle background notification messages when app is not in foreground
/// 
/// Stores the notification data for later processing when the app becomes active
/// This is called by the local notification plugin for background messages
@pragma('vm:entry-point')
Future<void> onBackgroundNotificationHandle(RemoteMessage remoteMessage) async {
  try {
    AppLogger.d('Background message received: ${remoteMessage.data}');
    
    await _storePendingNotificationInBackground(remoteMessage.data);
    
    AppLogger.d('Background message stored for later handling');
  } catch (e) {
    AppLogger.e('Error handling background message: $e');
  }
}

/// Store pending notification data in background isolate
Future<void> _storePendingNotificationInBackground(Map<String, dynamic> data) async {
  try {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('pending_notification_data', jsonEncode(data));
    AppLogger.d('Stored pending notification in background: $data');
  } catch (e) {
    AppLogger.e('Error storing pending notification in background: $e');
  }
}

/// Firebase background message handler for terminated app state
/// 
/// This function is called when a Firebase message is received while the app is terminated
/// Stores the notification data for processing when the app launches
/// Must be a top-level function with @pragma('vm:entry-point') annotation
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    AppLogger.d('Firebase background message: ${message.data}');
    
    await _storePendingNotificationInBackground(message.data);
    
    AppLogger.d('Firebase background message stored');
  } catch (e) {
    AppLogger.e('Error in Firebase background handler: $e');
  }
}
