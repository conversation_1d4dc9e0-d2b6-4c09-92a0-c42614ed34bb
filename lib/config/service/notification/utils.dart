import 'package:family_app/utils/navigation_helper.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/data/model/thread_family.dart';
import 'package:family_app/main.dart';

/// Utility class for notification-related helper functions in the app.
class NotificationUtils {
  /// Waits for the navigation context (UI) to be ready before processing notifications.
  /// Returns true if the context is ready within the maxWaitTime, false otherwise.
  static Future<bool> waitForUIReady({
    Duration maxWaitTime = const Duration(seconds: 15),
    Duration checkInterval = const Duration(milliseconds: 200),
  }) async {
    final startTime = DateTime.now();
    while (DateTime.now().difference(startTime) < maxWaitTime) {
      try {
        final contextReady = await NavigationHelper.waitForNavigationContextReady(
          timeout: const Duration(seconds: 2),
          checkInterval: const Duration(milliseconds: 100),
        );
        if (contextReady) return true;
        await Future.delayed(checkInterval);
      } catch (_) {
        await Future.delayed(checkInterval);
      }
    }
    return false;
  }

  /// Handles notification payload with a delay, ensuring the navigation context is ready.
  /// If the context is not ready, stores the notification for later processing.
  static Future<void> handleNotificationWithDelay(
    Map<String, dynamic> payload, {
    required Future<void> Function(Map<String, dynamic>) storePendingNotification,
    required void Function(Map<String, dynamic>) onHandleNotification,
  }) async {
    try {
      final contextReady = await waitForUIReady(
        maxWaitTime: const Duration(seconds: 10),
        checkInterval: const Duration(milliseconds: 100),
      );
      if (!contextReady) {
        await storePendingNotification(payload);
        return;
      }
      onHandleNotification(payload);
    } catch (_) {
      await storePendingNotification(payload);
    }
  }

  /// Fallback navigation when thread detail fetch fails
  static Future<void> handleThreadNavigationFallback(String threadUuid) async {
    try {
      AppLogger.d('Thread notification: Attempting fallback navigation to thread list');
      final navigationSuccess = await NavigationHelper.safeNavigate(
        const ThreadRoute(),
        timeout: const Duration(seconds: 5),
      );
      if (navigationSuccess) {
        showSimpleToast('Thread not found. Please check the thread list.');
      } else {
        showSimpleToast('Thread not found or no longer available');
      }
    } catch (e) {
      AppLogger.e('Thread notification: Fallback navigation also failed: $e');
      showSimpleToast('Unable to navigate to thread');
    }
  }

  /// Handles thread notification navigation, avoiding duplicate navigation if already on the correct screen.
  static Future<void> handleThreadNotification(String? threadUuid) async {
    if (threadUuid == null || threadUuid.isEmpty) {
      AppLogger.e('Thread notification: Invalid thread UUID');
      showSimpleToast('Invalid thread notification');
      return;
    }
    final currentRoute = appRouter.current;
    final args = currentRoute.args;
    if (args is ThreadDetailRouteArgs) {
      final param = args.parameter;
      if (param.uuid == threadUuid) {
        AppLogger.d('Already on the correct ThreadDetail screen, skipping navigation.');
        return;
      }
    }
    final minimalThreadFamily = ThreadFamily(uuid: threadUuid);
    final navigationSuccess = await NavigationHelper.safeNavigate(
      ThreadDetailRoute(parameter: minimalThreadFamily),
      timeout: const Duration(seconds: 5),
    );
    if (!navigationSuccess) {
      await NotificationUtils.handleThreadNavigationFallback(threadUuid);
    }
  }
}
