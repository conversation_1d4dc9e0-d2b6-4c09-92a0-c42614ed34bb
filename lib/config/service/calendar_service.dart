import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:device_calendar/device_calendar.dart';
import 'package:family_app/base/stream/base_list_stream_controller.dart';
import 'package:family_app/base/stream/base_stream_controller.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/repository/authen/iauthen_repository.dart';
import 'package:family_app/data/repository/event/ievent_repository.dart';
import 'package:family_app/data/usecase/event_bulk_usecase.dart';
import 'package:family_app/data/usecase/model/event_parameter.dart';
import 'package:family_app/utils/batch.dart';
import 'package:family_app/utils/extension/calendar/device_calendar.dart';
import 'package:family_app/utils/timezone.dart';
import 'package:family_app/utils/calendar.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'app_service.dart';

@singleton
class CalendarService {
  static CalendarService instance = locator<CalendarService>();

  // Dependencies
  final DeviceCalendarPlugin _deviceCalendarPlugin = DeviceCalendarPlugin();
  final IAuthenRepository authenRepository;
  final IEventRepository eventRepository;
  final EventBulkUsecase eventBulkUsecase;
  final AccountService accountService;
  final EventService eventService;

  // Setting
  /// Shared stream for week start (Monday = true, Sunday = false)
  final BaseStreamController<bool> weekStartMondayStream = BaseStreamController<bool>(false);

  // State
  Calendar? defaultCalendar;
  final BaseListStreamController<Calendar> calendars = BaseListStreamController<Calendar>([]);

  /// Set of enabled calendar IDs for import. On first enable, all non-readonly calendars are enabled.
  Set<String> _enabledCalendarIds = {};
  Set<String> get enabledCalendarIds => _enabledCalendarIds;

  /// Utility getter for enabled calendar IDs from LocalStorage
  Set<String> get localEnabledCalendarIds => locator.get<LocalStorage>().enabledCalendarIds;

  // Permission status cache (null = unknown, true/false = last known)
  bool? _hasCalendarPermission;

  CalendarService({
    required this.authenRepository,
    required this.eventRepository,
    required this.eventBulkUsecase,
    required this.accountService,
    required this.eventService,
  }) {
    // Initialize weekStartMondayStream with persisted value
    final isMonday = locator.get<LocalStorage>().isStartOfWeekMonday;
    weekStartMondayStream.value = isMonday;
  }

  // =====================
  // Permission Handling
  // =====================

  /// Returns last known permission status, or null if never checked.
  bool? get hasCalendarPermission => _hasCalendarPermission;

  /// Checks device calendar permissions and updates the cached value.
  Future<bool> checkDeviceCalendarPermissions() async {
    try {
      final Result<bool> hasPermissions = await _deviceCalendarPlugin.hasPermissions();
      final hasPermissionsData = hasPermissions.isSuccess && hasPermissions.data == true;
      _hasCalendarPermission = hasPermissionsData;
      _logd("Permissions status: $hasPermissionsData");
      return hasPermissionsData;
    } catch (e) {
      _hasCalendarPermission = false;
      _logd("Error checking permissions: $e");
      return false;
    }
  }

  /// Requests device calendar permission and updates the cached value.
  Future<bool> requestDeviceCalendarPermission() async {
    try {
      final Result<bool> result = await _deviceCalendarPlugin.requestPermissions();
      final hasPermissionsData = result.isSuccess && result.data == true;
      _hasCalendarPermission = hasPermissionsData;
      return hasPermissionsData;
    } catch (e) {
      _hasCalendarPermission = false;
      _logd("Error requesting permissions: $e");
      return false;
    }
  }

  // =====================
  // Calendar Sync Settings/State
  // =====================
  bool isEnableCalendarSync() => locator.get<LocalStorage>().isSyncWithDeviceCalendar;
  Future<void> setEnableCalendarSync(bool enable) async => locator.get<LocalStorage>().cacheSyncWithDeviceCalendar(enable);
  bool isStartOfWeekMonday() => weekStartMondayStream.value;
  Future<void> setStartOfWeekMonday(bool enable) async {
    weekStartMondayStream.value = enable;
    await locator.get<LocalStorage>().cacheStartOfWeekMonday(enable);
  }

  // =====================
  // Calendar Operations
  // =====================
  Calendar? getCalendar(String id) => calendars.value.firstWhereOrNull((c) => c.id == id);

  /// Checks permissions and loads device calendars. Returns true if calendars are loaded and available, false otherwise.
  Future<bool> ensureDeviceCalendarsLoaded() async {
    if (!isEnableCalendarSync()) {
      _logd("Calendar sync is disabled, skipping initialization.");
      return false;
    }
    var permissionsGranted = await checkDeviceCalendarPermissions();
    if (!permissionsGranted) {
      permissionsGranted = await requestDeviceCalendarPermission();
      // Re-check permissions after requesting
      if (!permissionsGranted) {
        _logd("Permissions not granted after request, skipping calendar retrieval.");
        return false;
      }
    }
    // _hasCalendarPermission is now up-to-date
    await retriveDeviceCalendars();
    return calendars.value.isNotEmpty;
  }

  /// Initializes device calendars if sync is enabled. Uses prepareDeviceCalendarSync for consistency.
  Future<void> initializeDeviceCalendars() async {
    final (enabledIds, loaded) = await prepareDeviceCalendarSync();
    if (!loaded) return;
    if (enabledIds.isEmpty) {
      _logd('No enabled calendars found in local storage. All calendars will be unchecked by default.');
      return;
    }
    final (from, to) = CalendarUtils.getDefaultImportRange();
    await importEventsFromDeviceCalendars(from, to, calendarIds: enabledIds);
  }

  /// Retrieves device calendars and updates the local list. Only calendars with isReadOnly == false are included.
  Future<void> retriveDeviceCalendars() async {
    try {
      final calendarsResult = await _deviceCalendarPlugin.retrieveCalendars();
      final newCalendars = (calendarsResult.data as List<Calendar>).where((c) => c.isReadOnly == false).toList();
      _logd("Retrieved ${newCalendars.length} calendars: [${newCalendars.map((c) => c.name).join(', ')}]");
      for (final c in newCalendars) {
        _logd("Calendar: id=${c.id}, name=${c.name}, accountType=${c.accountType}, isReadOnly=${c.isReadOnly}, isDefault=${c.isDefault}, color=${c.color}");
      }
      // Only emit if changed
      if (!const DeepCollectionEquality().equals(calendars.value, newCalendars)) {
        calendars.value = newCalendars;
      }
      defaultCalendar = _findDefaultCalendar();
    } on PlatformException catch (e) {
      _logd("Error retrieving calendars: $e");
    }
  }

  /// Finds the default calendar for the platform.
  Calendar? _findDefaultCalendar() {
    final currentCalendars = calendars.value;
    if (Platform.isAndroid) {
      return currentCalendars.firstWhereOrNull(
        (c) => c.accountType == "com.google" && c.isReadOnly == false,
      );
    }
    return currentCalendars.firstWhereOrNull(
      (c) => (c.isDefault == true && c.isReadOnly == false) || c.isReadOnly == false,
    );
  }

  // =====================
  // Device Calendar Sync Preparation
  // =====================

  /// Prepares device calendar sync:
  ///   1. Ensures device calendars are loaded (with permissions).
  ///   2. Retrieves enabled calendar IDs from local storage.
  ///   3. Updates internal state and persists enabled IDs.
  /// Returns a tuple: (enabledCalendarIds, calendarsLoaded)
  Future<(Set<String> enabledCalendarIds, bool calendarsLoaded)> prepareDeviceCalendarSync() async {
    _logd('Preparing device calendar sync...');
    final calendarsLoaded = await ensureDeviceCalendarsLoaded();
    if (!calendarsLoaded) {
      _logd('Device calendars not loaded.');
      _enabledCalendarIds.clear();
      await saveEnabledCalendarIds();
      return (<String>{}, false);
    }
    final localEnabledIds = localEnabledCalendarIds;
    _enabledCalendarIds
      ..clear()
      ..addAll(localEnabledIds);
    await saveEnabledCalendarIds();
    _logd('Device calendars loaded. Enabled IDs: ${localEnabledIds.join(",")}');
    return (localEnabledIds, true);
  }

  // =====================
  // Event Operations (Public API)
  // =====================
  /// Retrieves events from a device calendar within a date range.
  Future<List<Event>> retrieveCalendarEvents(Calendar calendar, DateTime from, DateTime to) async {
    final eventsResult = await _deviceCalendarPlugin.retrieveEvents(
      calendar.id ?? "",
      RetrieveEventsParams(startDate: from, endDate: to),
    );
    return (eventsResult.data as List<Event>?) ?? [];
  }

  /// Updates enabled calendars, saves to storage, and triggers import/delete for diffs.
  Future<void> onToggleCalendar(Set<String> newEnabledCalendarIds) async {
    final now = DateTime.now();
    final from = DateTime(now.year, now.month, 1).subtract(const Duration(days: 30));
    final to = DateTime(now.year, now.month, 1).add(const Duration(days: 60));

    final prev = Set<String>.from(_enabledCalendarIds);
    final added = newEnabledCalendarIds.difference(prev);
    final removed = prev.difference(newEnabledCalendarIds);

    _enabledCalendarIds = Set<String>.from(newEnabledCalendarIds);
    await saveEnabledCalendarIds();

    // Collect all futures for import and delete
    final List<Future<void>> futures = [];

    // Import events for newly enabled calendars
    for (final calendarId in added) {
      futures.add(importEventsFromDeviceCalendars(from, to, calendarIds: {calendarId}));
    }

    // Delete events for newly disabled calendars
    for (final calendarId in removed) {
      final yearFrom = DateTime(now.year, 1, 1, 0, 0, 0);
      final yearTo = DateTime(now.year, 12, 31, 23, 59, 59);
      _logd('Deleting all events for calendar $calendarId in year range $yearFrom - $yearTo');
      futures.add(deleteAllEventByCalendarId(calendarId, yearFrom, yearTo));
    }

    // Wait for all operations to complete
    await Future.wait(futures);
  }

  /// Imports events from device calendars into the system.
  ///
  /// - If [calendarIds] is null, imports from all enabled calendars.
  /// - Loads events from the specified device calendars in the given [from]-[to] date range.
  /// - Deduplicates events by eventId.
  /// - Converts device events to EventParameter and processes them in batches.
  /// - Stores event info (uuid, calendarId, familyId) in local storage for tracking.
  /// - After import, refreshes the event list and removes events that were deleted on the device.
  /// - Throws on error.
  ///
  /// Note: This method is not concurrency-safe; callers should ensure only one import runs at a time if needed.
  Future<void> importEventsFromDeviceCalendars(DateTime from, DateTime to, {Set<String>? calendarIds}) async {
    await _importEventsFromDeviceCalendarsInternal(from, to, calendarIds: calendarIds);
  }

  Future<void> _importEventsFromDeviceCalendarsInternal(DateTime from, DateTime to, {Set<String>? calendarIds}) async {
    await TimeZoneUtils.initializeTimeZone();
    _logd('importEventsFromDeviceCalendars called with params: from=$from, to=$to, calendarIds=${calendarIds?.join(",")}');
    try {
      final allEvents = await _collectEventsFromCalendars(from, to, calendarIds: calendarIds);
      if (allEvents.isEmpty) {
        _logd('No events found for import.');
        return;
      }
      _logd('Collected ${allEvents.length} unique events for import.');
      final allParams = allEvents
          .map((tuple) => tuple.$1.toEventParameter(
                accountService.familyId,
                tuple.$2.color,
              ))
          .toList();
      if (allParams.isEmpty) {
        _logd('No EventParameter objects prepared for batch processing.');
        return;
      }
      _logd('Prepared ${allParams.length} EventParameter objects for batch processing.');
      _logd('Sample EventParameter: ${allParams.first}');
      await _processEventsInBatches(
        allParams: allParams,
        logLabel: 'device calendar',
        chunkSize: 50,
        maxConcurrent: 3,
      );
      // Store event info (uuid, calendarId, familyId) to local storage
      _addEventInfoListToLocalStorage(allEvents
          .where((tuple) => tuple.$1.uuid.isNotEmpty)
          .map((tuple) => {
                'uuid': tuple.$1.uuid,
                'calendarId': tuple.$2.id ?? '',
                'familyId': accountService.familyId,
              })
          .toList());
    } catch (e, stackTrace) {
      _logd('Error importing events: $e');
      _logd('Stack trace: $stackTrace');
      rethrow;
    } finally {
      await eventService.refreshEvents(accountService.familyId);
      removeEventsDeletedOnDeviceCalendarInRange(from, to);
    }
  }

  /// Checks for events that were removed from device calendars in the given range and removes them from the system.
  ///
  /// This method:
  /// - Groups locally stored event info by calendarId.
  /// - For each calendar, fetches device events in the given [from]-[to] range in parallel.
  /// - Compares stored uuids with device uuids using Set for fast lookup.
  /// - Collects all missing uuids, removes them from the local stream, and updates the in-memory event info list.
  /// - After all calendars are processed, calls bulkDeleteEvent for all missing uuids.
  /// - Only after a successful bulk delete, persists the updated event info list to local storage.
  /// - Logs detailed information for debugging and monitoring.
  /// - Catches and logs any errors to avoid app crashes.
  ///
  /// This approach ensures:
  /// - Only events actually deleted from the device calendar are removed from the system.
  /// - Local storage is only updated after backend deletion succeeds, preventing data loss on failure.
  /// - Efficient processing for large numbers of events/calendars.
  Future<void> removeEventsDeletedOnDeviceCalendarInRange(DateTime from, DateTime to) async {
    try {
      final localStorage = locator.get<LocalStorage>();
      final eventInfoList = localStorage.eventInfoList;
      if (eventInfoList.isEmpty) {
        _logd('[removeEventsDeletedOnDeviceCalendarInRange] No event info stored, skipping.');
        return;
      }

      _logd('[removeEventsDeletedOnDeviceCalendarInRange] Checking for deleted events in range $from - $to. Total eventInfoList: ${eventInfoList.length}');

      // Group event info by calendarId for efficient lookup
      final Map<String, List<Map<String, String>>> eventsByCalendar = {};
      for (final info in eventInfoList) {
        final calendarId = info['calendarId'] ?? '';
        if (calendarId.isEmpty) continue;
        eventsByCalendar.putIfAbsent(calendarId, () => []).add(info);
      }

      final Set<String> allUuidsToRemove = {};
      List<Map<String, String>> updatedEventInfoList = List.from(eventInfoList);

      await Future.wait(eventsByCalendar.entries.map((entry) async {
        final calendarId = entry.key;
        final calendar = getCalendar(calendarId);
        if (calendar == null) {
          _logd('[removeEventsDeletedOnDeviceCalendarInRange] Calendar not found for id: $calendarId');
          return;
        }

        final deviceEvents = await retrieveCalendarEvents(calendar, from, to);
        final deviceUuids = deviceEvents.map((e) => e.uuid).toSet();

        _logd('[removeEventsDeletedOnDeviceCalendarInRange] CalendarId: $calendarId, deviceEvents: ${deviceEvents.length}, deviceUuids: ${deviceUuids.length}, storedEvents: ${entry.value.length}');

        final uuidsToRemove = <String>[];
        for (final info in entry.value) {
          final uuid = info['uuid'] ?? '';
          if (uuid.isEmpty) continue;
          if (!deviceUuids.contains(uuid)) {
            uuidsToRemove.add(uuid);
            _logd('[removeEventsDeletedOnDeviceCalendarInRange] Marked for removal: uuid=$uuid, calendarId=$calendarId');
          }
        }

        if (uuidsToRemove.isNotEmpty) {
          allUuidsToRemove.addAll(uuidsToRemove);
          eventService.removeEventsByUuids(uuidsToRemove);

          // Only update in-memory list here, persist once after all calendars processed
          final uuidSet = uuidsToRemove.toSet();
          updatedEventInfoList = updatedEventInfoList.where((e) => !(uuidSet.contains(e['uuid']) && e['calendarId'] == calendarId)).toList();
        }
      }));

      if (allUuidsToRemove.isEmpty) {
        _logd('[removeEventsDeletedOnDeviceCalendarInRange] No deleted events detected.');
        return;
      }

      await eventRepository.bulkDeleteEvent(allUuidsToRemove.toList());
      await localStorage.cacheEventInfoList(updatedEventInfoList);

      _logd('[removeEventsDeletedOnDeviceCalendarInRange] Removed ${allUuidsToRemove.length} events deleted from device calendar in range $from - $to. UUIDs: ${allUuidsToRemove.toList()}');
    } catch (e, stackTrace) {
      _logd('[removeEventsDeletedOnDeviceCalendarInRange] Error: $e');
      _logd('[removeEventsDeletedOnDeviceCalendarInRange] Stack trace: $stackTrace');
    }
  }

  /// Imports events from all device calendars for the entire year of the selected date.
  Future<void> importEventFromAllDeviceCalendarWhenYearChanged(DateTime selectedDate) async {
    if (!isEnableCalendarSync()) {
      _logd("Calendar sync is disabled, skipping import.");
      return;
    }
    final permissionsGranted = await checkDeviceCalendarPermissions();
    if (!permissionsGranted) {
      _logd("Permissions not granted, skipping import.");
      return;
    }
    // Calculate the first and last day of the selected year
    final from = DateTime.utc(selectedDate.year, 1, 1, 0, 0, 0);
    final to = DateTime.utc(selectedDate.year, 12, 31, 23, 59, 59);
    await importEventsFromDeviceCalendars(from, to, calendarIds: enabledCalendarIds);
  }

  /// Imports events from Google Calendar (stub).
  Future<void> importEventFromGoogleCalendar() async {
    /* final events = await authenRepository.getGoogleEventsData();
    if (events.isEmpty) return;

    // Deduplicate by eventId or uuid
    final dedupedEvents = CalendarUtils.deduplicateEventsById(events);

    final allParams = dedupedEvents
        .map((event) => event.toEventParameter(
              accountService.familyId,
              themeUtil.selectionColor(),
            ))
        .toList();

    await _processEventsInBatches(
      allParams: allParams,
      logLabel: 'Google Calendar',
      chunkSize: 50,
      maxConcurrent: 3,
    );*/
  }

  /// Deletes all events for all enabled calendars in the default import range.
  /// Collects all event IDs first, removes from local stream, then calls bulkDeleteEvent once.
  Future<void> deleteAllEventsFromAllEnabledCalendars() async {
    final (from, to) = CalendarUtils.getDefaultImportRange();
    if (_enabledCalendarIds.isEmpty) {
      _logd('No enabled calendar IDs found for deletion.');
      return;
    }
    final List<String> allEventIds = [];
    for (final calendarId in _enabledCalendarIds) {
      final calendar = getCalendar(calendarId);
      if (calendar == null) {
        _logd('No calendar found for id: $calendarId');
        continue;
      }
      final deviceEvents = await retrieveCalendarEvents(calendar, from, to);
      if (deviceEvents.isEmpty) {
        _logd('No events found in device calendar for id: $calendarId');
        continue;
      }
      final eventIds = deviceEvents.map((event) => event.uuid).where((id) => id.isNotEmpty).toList();
      if (eventIds.isEmpty) {
        _logd('No valid event IDs generated for calendar: $calendarId');
        continue;
      }
      allEventIds.addAll(eventIds);
      // Remove from local stream for this calendar
      eventService.removeEventsByUuids(eventIds);
    }
    if (allEventIds.isEmpty) {
      _logd('No valid event IDs found for any enabled calendar.');
      return;
    }
    _logd('Deleting ${allEventIds.length} events in bulk for all enabled calendars...');
    await eventRepository.bulkDeleteEvent(allEventIds);
    _logd('Successfully deleted all device calendar events from all enabled calendars');
  }

  /// Deletes all events for a specific calendar by calendarId.
  /// Collects all event IDs, removes from local stream, then calls bulkDeleteEvent once.
  Future<void> deleteAllEventByCalendarId(String calendarId, DateTime from, DateTime to) async {
    await _deleteAllEventByCalendarIdInternal(calendarId, from, to);
  }

  Future<void> _deleteAllEventByCalendarIdInternal(String calendarId, DateTime from, DateTime to) async {
    final calendar = getCalendar(calendarId);
    if (calendar == null) {
      _logd('No calendar found for id: $calendarId');
      return;
    }
    final deviceEvents = await retrieveCalendarEvents(calendar, from, to);
    if (deviceEvents.isEmpty) {
      _logd('No events found in device calendar for id: $calendarId');
      return;
    }
    final eventIds = deviceEvents.map((event) => event.uuid).where((id) => id.isNotEmpty).toList();
    if (eventIds.isEmpty) {
      _logd('No valid event IDs generated for calendar: $calendarId');
      return;
    }
    // Remove from local stream first
    eventService.removeEventsByUuids(eventIds);
    _logd('Deleting ${eventIds.length} events for calendar $calendarId in bulk...');
    await eventRepository.bulkDeleteEvent(eventIds);
    _logd('Successfully deleted all events for calendar $calendarId');

    // Remove event info from local storage
    _removeEventInfoListFromLocalStorage(eventIds, calendarId);
  }

  /// Deletes all events from the database for the current family in the default import range.
  Future<void> deleteAllEventsOnSystem() async {
    final familyId = accountService.familyId;
    final (from, to) = CalendarUtils.getDefaultImportRange();

    final allEvents = await eventService.getEventsByFamilyId(
      familyId,
      from: from,
      to: to,
    );

    // Filter out events with empty or null uuid
    final validEvents = allEvents.where((event) => (event.uuid ?? '').isNotEmpty).toList();

    // Deduplicate events by uuid (keep the first occurrence)
    final dedupedEvents = CalendarUtils.deduplicateEventsById<EventModels>(
      validEvents,
      getId: (event) => event.uuid ?? '',
    );

    if (dedupedEvents.isEmpty) {
      _logd('No valid events found to delete.');
      return;
    }

    _logd('Deleting ${dedupedEvents.length} events in bulk...');

    // Use bulkDeleteEvent API
    final eventIds = dedupedEvents.map((event) => event.uuid!).toList();

    // Optimistically remove from local stream
    eventService.removeEventsByUuids(eventIds);
    await eventRepository.bulkDeleteEvent(eventIds);

    _logd('Successfully deleted all events from database for this family');
  }

  // =====================
  // Private Helpers
  // =====================

  /// Saves enabled calendar IDs to local storage.
  Future<void> saveEnabledCalendarIds() async {
    await locator.get<LocalStorage>().cacheEnabledCalendarIds(_enabledCalendarIds);
  }

  /// Adds new event info to local storage, merging and deduplicating by uuid.
  Future<void> _addEventInfoListToLocalStorage(List<Map<String, String>> newEventInfoList) async {
    if (newEventInfoList.isEmpty) return;
    final localStorage = locator.get<LocalStorage>();
    final existing = localStorage.eventInfoList;
    final newUuids = newEventInfoList.map((e) => e['uuid']).toSet();
    // Only keep existing entries whose uuid is not in newEventInfoList
    final merged = [...existing.where((e) => !newUuids.contains(e['uuid'])), ...newEventInfoList];
    await localStorage.cacheEventInfoList(merged);
  }

  /// Removes event info from local storage by uuids and calendarId.
  Future<void> _removeEventInfoListFromLocalStorage(List<String> uuids, String calendarId) async {
    if (uuids.isEmpty) return;
    final localStorage = locator.get<LocalStorage>();
    final uuidSet = uuids.toSet();
    final filtered = localStorage.eventInfoList.where((e) => !(uuidSet.contains(e['uuid']) && e['calendarId'] == calendarId)).toList();
    await localStorage.cacheEventInfoList(filtered);
  }

  /// Collects events from the specified calendars (by id). If [calendarIds] is null or empty, returns an empty list.
  Future<List<(Event, Calendar)>> _collectEventsFromCalendars(DateTime from, DateTime to, {Set<String>? calendarIds}) async {
    final List<(Event, Calendar)> allEvents = [];
    if (calendarIds == null || calendarIds.isEmpty) {
      return allEvents;
    }
    final Iterable<Calendar> selectedCalendars = calendars.value.where((c) => calendarIds.contains(c.id));
    for (final calendar in selectedCalendars) {
      final events = await retrieveCalendarEvents(calendar, from, to);
      allEvents.addAll(events.map((event) => (event, calendar)));
    }
    _logd('Collected ${allEvents.length} events from selected calendars');
    // Filter out duplicate events by eventId (keep the first occurrence)
    final seenEventIds = <String>{};
    final filteredEvents = <(Event, Calendar)>[];
    for (final tuple in allEvents) {
      final eventId = tuple.$1.eventId ?? '';
      if (eventId.isEmpty) continue;
      if (!seenEventIds.contains(eventId)) {
        seenEventIds.add(eventId);
        filteredEvents.add(tuple);
      }
    }
    _logd('Filtered to ${filteredEvents.length} unique events by eventId');
    return filteredEvents;
  }

  /// Processes events in batches for import.
  Future<void> _processEventsInBatches({
    required List<EventParameter> allParams,
    required String logLabel,
    int chunkSize = 50,
    int maxConcurrent = 3,
  }) async {
    final chunks = BatchUtils.chunkList(allParams, chunkSize);

    int processed = 0;
    Future<void> processChunk(List<EventParameter> chunk) async {
      await eventBulkUsecase.call(chunk);
      processed += chunk.length;
      _logd('Processed $logLabel events: $processed/${allParams.length}');
    }

    await BatchUtils.processChunksWithPool<EventParameter>(
      chunks,
      processChunk,
      maxConcurrent: maxConcurrent,
    );

    _logd('Successfully imported all $logLabel events');
  }

  /// Logs debug messages for CalendarService.
  void _logd(String message) => log("CalendarService: $message");
}
