import 'package:injectable/injectable.dart';
import 'package:family_app/data/model/list_item.dart';
import 'package:family_app/data/model/item.dart';
import 'package:family_app/data/repository/list/ilist_repository.dart';

@singleton
class ChecklistService {
  final IListRepository listRepository;

  ChecklistService(this.listRepository);

  final Map<String, List<ListItem>> _checklistCache = {};
  final Map<String, List<Item>> _tasksCache = {};

  /// Gets checklists for the given family. By default, always fetches from server unless forceRefresh is set to false.
  Future<List<ListItem>> getChecklistsWithRefresh(String familyId, {bool forceRefresh = true}) async {
    if (!forceRefresh && _checklistCache.containsKey(familyId)) {
      return _checklistCache[familyId]!;
    }
    final checklists = await listRepository.getListByFamilyId(familyId);
    _checklistCache[familyId] = checklists;
    return checklists;
  }

  /// Gets tasks for the given checklist. By default, always fetches from server unless forceRefresh is set to false.
  Future<List<Item>> getTasksWithRefresh(String checklistUuid, {bool forceRefresh = true}) async {
    if (!forceRefresh && _tasksCache.containsKey(checklistUuid)) {
      return _tasksCache[checklistUuid]!;
    }
    final items = await listRepository.getAllItemInList(checklistUuid);
    _tasksCache[checklistUuid] = items;
    return items;
  }

  void clearCache() {
    _checklistCache.clear();
    _tasksCache.clear();
  }
}
