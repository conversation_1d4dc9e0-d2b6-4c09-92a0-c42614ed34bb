import 'package:flutter/material.dart' show AppLifecycleState, WidgetsBinding, WidgetsBindingObserver;
import 'package:injectable/injectable.dart';

typedef LifecycleCallback = void Function(AppLifecycleState state);

@singleton
class AppLifecycleService with WidgetsBindingObserver {
  final List<LifecycleCallback> _listeners = [];

  AppLifecycleService() {
    WidgetsBinding.instance.addObserver(this);
  }

  @disposeMethod
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _listeners.clear();
  }

  void addListener(LifecycleCallback listener) {
    _listeners.add(listener);
  }

  void removeListener(LifecycleCallback listener) {
    _listeners.remove(listener);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    for (var listener in _listeners) listener(state);
  }
}
