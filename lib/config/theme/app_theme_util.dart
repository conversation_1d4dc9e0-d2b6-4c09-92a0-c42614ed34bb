import 'package:family_app/config/theme/app_theme.dart';
import 'package:family_app/config/theme/base_app_theme.dart';
import 'package:family_app/config/theme/base_theme_data.dart';
import 'package:flutter/material.dart';

class AppThemeUtil {
  final theme = MainAppTheme();

  final ValueNotifier<ThemeType> themeType = ValueNotifier(ThemeType.light);

  BaseAppTheme get appTheme => theme;

  void dispose() {
    themeType.dispose();
  }

  List<Color> selectionColor() => [
        getAppTheme().typeCyanAccentColor,
        getAppTheme().typeOrangeColor,
        getAppTheme().typeGreenColor,
        getAppTheme().typeRedAccentColor,
        getAppTheme().typeRedColor,
        getAppTheme().typeRaditzColor,
        getAppTheme().typeNappaColor,
        getAppTheme().typeWhisColor,
        getAppTheme().typeFriezaColor,
        getAppTheme().typePiccoloColor,
      ];

  // List<Color> selectionColor() => [
  //       getAppTheme().blueColor,
  //       getAppTheme().purpleColor,
  //       getAppTheme().darkPurpleColor,
  //       getAppTheme().pinkColor,
  //       getAppTheme().redColor,
  //       getAppTheme().orangeColor,
  //       getAppTheme().lightYellowColor,
  //       getAppTheme().lightGreenColor,
  //     ];

  ThemeData getThemeData() {
    return theme.getThemeData(themeType.value);
  }

  BaseThemeData getAppTheme() {
    return theme.getBaseTheme(themeType.value);
  }

  onChangeLightDarkMode() {
    if (themeType.value == ThemeType.light) {
      themeType.value = ThemeType.dark;
    } else {
      themeType.value = ThemeType.light;
    }
  }
}
