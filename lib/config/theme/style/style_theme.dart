import 'package:family_app/extension.dart';
import 'package:family_app/main.dart';
import 'package:flutter/material.dart';

class AppStyle {
  static TextStyle regular10({Color? color}) => TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4.h,
      );

  static TextStyle regular10V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 10,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackTextV2,
        height: 1.6,
      );

  static TextStyle regular12({Color? color}) => TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle regular12V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackTextV2,
        height: 1.33,
      );

  static TextStyle regular13({Color? color}) => TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle regular14({Color? color}) => TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle regular14V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackTextV2,
        height: 1.71,
      );

  static TextStyle regular16({Color? color}) => TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle regular16V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackTextV2,
        height: 1.5,
      );

  static TextStyle regular17({Color? color}) => TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle regular18({Color? color}) => TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle regular22({Color? color}) => TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle regular24V2({Color? color, double height = 1.33}) =>
      TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 24,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackTextV2,
        height: height,
      );

  static TextStyle regular40({Color? color}) => TextStyle(
        fontSize: 40,
        fontWeight: FontWeight.w500,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold9V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 9,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.78,
      );

  static TextStyle bold10({Color? color}) => TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold10V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 10,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.6,
      );

  static TextStyle bold11({Color? color}) => TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold13({Color? color}) => TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
      );

  static TextStyle bold14({Color? color}) => TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold17({Color? color}) => TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold12({Color? color}) => TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle normal12({Color? color}) => TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle normal13({Color? color}) => TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: color ?? appTheme.blackText,
        height: 1.8,
      );

  static TextStyle normal14({Color? color}) => TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle normal15({Color? color}) => TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.w500,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle normal16({Color? color}) => TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle normal160({Color? color}) => TextStyle(
        fontSize: 160,
        fontWeight: FontWeight.w500,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold12V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 12,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.33,
      );

  static TextStyle bold13_5V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 13.5,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.6,
      );

  static TextStyle bold14V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 14,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.71,
      );

  static TextStyle bold16({Color? color}) => TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold16V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 16,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.5,
      );

  static TextStyle bold18({Color? color}) => TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold18V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 18,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.56,
      );

  static TextStyle bold19({Color? color}) => TextStyle(
        fontSize: 19,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold20({Color? color}) => TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold20V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 20,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.6,
      );

  static TextStyle bold22V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 20,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.6,
      );

  static TextStyle bold24({Color? color}) => TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold28({Color? color}) => TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold30({Color? color}) => TextStyle(
        fontSize: 30,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold32V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.25,
        letterSpacing: -0.5,
      );

  static TextStyle bold32({Color? color}) => TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bold40({Color? color}) => TextStyle(
        fontSize: 40,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackText,
        height: 1.2,
      );

  static TextStyle bold40V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 40,
        fontWeight: FontWeight.w700,
        color: color ?? appTheme.blackTextV2,
        height: 1.2,
        letterSpacing: -0.5,
      );

  static TextStyle medium10({Color? color}) => TextStyle(
        fontSize: 10,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium12({Color? color}) => TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium13({Color? color, double? height}) => TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: height ?? 1.4,
      );

  static TextStyle medium14({Color? color}) => TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium14V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium16({Color? color}) => TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold, // FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium17({Color? color}) => TextStyle(
        fontSize: 17,
        fontWeight: FontWeight.bold, //FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium17V2({Color? color}) => TextStyle(
        fontFamily: 'DM Sans',
        fontSize: 17,
        fontWeight: FontWeight.bold, //FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium18({Color? color}) => TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium19({Color? color}) => TextStyle(
        fontSize: 19,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium20({Color? color}) => TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium22({Color? color}) => TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium24({Color? color}) => TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium28({Color? color}) => TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium30({Color? color}) => TextStyle(
        fontSize: 30,
        fontWeight: FontWeight.w600,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle medium32({Color? color}) => TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w400,
        color: color ?? appTheme.blackText,
        height: 1.4,
      );

  static TextStyle bannerTitle = const TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: Colors.black,
  );

  static TextStyle bannerButton = const TextStyle(
    color: Colors.white,
  );

  static TextStyle emptyViewTapHere = const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );

  static TextStyle emptyViewNoActivity = const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );

  static TextStyle emptyView2NoActivities = const TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w700,
    color: Colors.black,
  );

  static TextStyle emptyView2StartCreating = const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: Colors.grey,
  );

  static TextStyle emptyView2CreateActivityButton = const TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w700,
    color: Colors.white,
  );

  static TextStyle emptyView2HowToPlanTrip = const TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w700,
    color: Color(0xFF4E46B4),
  );

  static TextStyle activityItemAbbreviation = const TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.bold,
    fontSize: 20,
  );

  static TextStyle activityItemDate = const TextStyle(
    color: Colors.black,
    fontSize: 14.0,
  );

  static TextStyle activityItemName = const TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.bold,
    fontSize: 14.0,
  );

  static TextStyle textMdS = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontWeight: FontWeight.w700,
    fontSize: 16.0,
    height: 1.50,
  );

  static TextStyle textMdR = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontWeight: FontWeight.w400,
    fontSize: 16.0,
    height: 1.50,
  );

  static TextStyle textXsS = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontSize: 12,
    fontWeight: FontWeight.w700,
    height: 1.33,
  );

  static TextStyle textXsR = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.33,
  );

  static TextStyle text2XsR = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontSize: 10,
    fontWeight: FontWeight.w400,
    height: 1.60,
  );

  static TextStyle textSmR = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.71,
  );

  static TextStyle textSmS = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontSize: 14,
    fontWeight: FontWeight.w700,
    height: 1.71,
  );

  static TextStyle textLgS = const TextStyle(
    fontFamily: 'DM Sans',
    color: Colors.black,
    fontSize: 18,
    fontWeight: FontWeight.w700,
    height: 1.56,
  );

  static TextStyle modeViewTitle = normal14();

  static TextStyle modeViewContent = regular12(color: appTheme.fadeTextColor);

  static TextStyle bold24V2({Color? color, double? height}) => TextStyle(
    fontFamily: 'DM Sans',
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: color ?? appTheme.blackTextV2,
    height: height ?? 1.33,
  );

  static TextStyle bold28V2({Color? color, double? height, FontWeight? fontWeight}) => TextStyle(
    fontFamily: 'DM Sans',
    fontSize: 28,
    fontWeight: fontWeight ?? FontWeight.w700,
    color: color ?? appTheme.blackTextV2,
    height: height ?? 1.33,
  );
}
