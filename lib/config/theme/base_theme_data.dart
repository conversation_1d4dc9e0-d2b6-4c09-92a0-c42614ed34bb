import 'package:flutter/material.dart';

abstract class BaseThemeData {
  Color primaryColor = const Color(0XFF6BA9FA);
  Color primaryColorV2 = const Color(0XFF4E46B4);

  Color secondaryColor = const Color(0XFFFFB934);
  Color secondaryColorV2 = const Color(0XFF3B97EC);
  Color thirdColor = const Color(0x0ff00000);
  Color background = const Color(0XFFF8FAFC);
  Color backgroundWhite = const Color(0XFFFFFFFF);
  Color backgroundV2 = const Color(0xFFF7F7F7);
  Color backgroundSelago = const Color(0xFFE8E9F6);
  Color greenV2 = const Color(0xFF2E7D32);

  Color primaryTextColor = const Color(0XFF6BA9FA);

  Color successColor = const Color(0XFF0CAF60);
  Color rejectColor = const Color(0XFFFF333B);
  Color warningColor = const Color(0XFFEC1C24);
  Color errorColor = const Color(0XFFFD4F4F);
  Color failColor = const Color(0XFFEC1C24);

  Color whiteText = const Color(0xFFFFFFFF);
  Color blackText = const Color(0xFF35373B);
  Color blackTextV2 = Colors.black;

  Color fadeBackgroundColor = Colors.grey;

  Color progressBackground = const Color(0XFFBFC6CE);

  Color hintColor = const Color(0XFFC5CEDB);

  Color fadeTextColor = const Color(0XFFA2A9B2);

  Color borderColor = const Color(0XFFEAECEE);
  Color borderColorV2 = const Color(0xFFE2E2E2);
  Color dividerColor = const Color(0XFFC6C6C6);

  Color blackColor = const Color(0xFF0F172A);

  Color transparentColor = Colors.transparent;

  Color greyColor = const Color(0xFF4E535F);
  Color grayV2 = const Color(0XFF595D62);
  Color errorV2 = const Color(0xffD33030);
  Color alto200 = const Color(0xffD9D9D9);

  Color greenColor = const Color(0xFF36DFB4);
  Color purpleColor = const Color(0xFF746AFA);
  Color redColor = const Color(0xFFFF6458);
  Color grayColor = const Color(0xFF808080);
  Color gray2Color = const Color(0xFFECECEC);
  Color grayEEColor = const Color(0xFFEEEEEE);
  Color grayE7Color = const Color(0xFFDADDE7);
  Color gray87Color = const Color(0xFF7F8187);
  Color grayF6Color = const Color(0xFFEEF2F6);
  Color gray80Color = const Color(0xFF7D7E80);
  Color grayA8Color = const Color(0xFFA8A8A8);



  Color redE9Color = const Color(0xFFE9E9E9);
  Color red44Color = const Color(0xFFFF7344);
  Color red3CColor = const Color(0xFFFE4A3C);
  Color red87Color = const Color(0xFFFFF8F7);

  Color bgInputColor = const Color(0xFFF8FBFF);
  Color borderInputColor = const Color(0xFFDDE8F6);
  Color yellowColor = const Color(0xFFF69B23);

  Color labelColor = const Color(0xFF6E747B);

  Color blueColor = const Color(0xFF5BA0FA);
  Color blueColorV2 = const Color(0xFF3448F0);
  Color darkPurpleColor = const Color(0xFF9248FF);
  Color pinkColor = const Color(0xFFED57E4);
  Color orangeColor = const Color(0xFFD46F1D);
  Color orangeColorV2 = const Color(0xFFFFB319);
  Color lightYellowColor = const Color(0xFFFDC14E);
  Color lightGreenColor = const Color(0xFF89D660);
  Color blueFFColor = const Color(0xFFF7FAFF);

  Color monthColor = const Color(0xFFF2F3F5);

  Color gray767680 = const Color(0xFF767680);

  Color backgroundChat = const Color(0xFFF2F1F6);
  Color appBarChat = const Color(0xFFD8D8D8);

  Color secondaryPrimaryColor = const Color(0xFF6370FF);

  Color firstTripColor = const Color(0xFFFFA237);
  Color secondTripColor = const Color(0xFFFF7979);
  Color firstListColor = const Color(0xFF3EE7F9);
  Color secondListColor = const Color(0xFF6A7DFF);
  Color firstEventColor = const Color(0xFFE788FF);
  Color secondEventColor = const Color(0xFF3740FF);

  Color firstRecordColor = const Color(0xFF43E4FF);
  Color secondRecordColor = const Color(0xFF7FA0FA);

  Color fadePrimaryColor = const Color(0xFFC3DDFF);

  Color green65Color = const Color(0xFF6AD665);
  Color greenF7Color = const Color(0xFFF8FDF7);

  Color lightGreyColor = Color(0xFFE2E4E4);
  Color buttonColor = const Color(0xFF636ae8);
  Color activityTitleColor = const Color(0x8Ac6c6c4);

  Color lightBotColor = Color(0xFF4E46B4);
  Color suggestColor = Color(0x1F4E46B4);
  Color transparentYellowishGreenColor = Color(0x1FF5A609);
  Color transparentYellowColor = Color(0xFF595D62);
  Color transparentWhiteColor = Color(0xFFF5F5F5);
  Color activeFamilyColor = Color(0xFF2E7D321F);

  Color calendarDateBackgroundColor = Color.fromARGB(255, 8, 12, 245);
  Color taskCardLightBlue = const Color(0xFFc3dfee);
  Color borderGreyColor = const Color(0x80808080);
  Color dropdownSelectedColor = const Color(0x1F4E46B4);

  Color typeCyanAccentColor = const Color(0xFF95F1D5);
  Color typeOrangeColor = const Color(0xFFFFB319);
  Color typeGreenColor = const Color(0xFF2E7D32);
  Color typeRedAccentColor = const Color(0xFFFF4E64);
  Color typeRedColor = const Color(0xFFD33030);
  Color typeRaditzColor = const Color(0xFFB3804A);
  Color typeNappaColor = const Color(0xFF725550);
  Color typeWhisColor = const Color(0xFF3448F0);
  Color typeFriezaColor = const Color(0xFF5C33CF);
  Color typePiccoloColor = const Color(0xFF4E46B4);

  Color activeSubItemColor = const Color(0xFF2E7D32);

  Color silverColor = const Color(0xFFC0C0C0);
}
