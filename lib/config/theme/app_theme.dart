import 'package:family_app/config/theme/base_app_theme.dart';
import 'package:family_app/config/theme/base_theme_data.dart';

class MainAppTheme
    extends BaseAppTheme<MainAppLightThemeDefault, MainAppDartThemeDefault> {
  @override
  MainAppDartThemeDefault get darkTheme => MainAppDartThemeDefault();

  @override
  MainAppLightThemeDefault get lightTheme => MainAppLightThemeDefault();
}

class MainAppLightThemeDefault extends BaseThemeData {}

class MainAppDartThemeDefault extends BaseThemeData {}
