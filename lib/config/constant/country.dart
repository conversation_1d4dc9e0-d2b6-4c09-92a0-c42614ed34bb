import 'package:easy_localization/easy_localization.dart';

class CountryConstants {
  static const List<String> countryCodes = [
    'us', 'ca', 'mx', 'fr', 'de', 'it', 'es', 'jp', 'cn', 'au', 'br', 'in', 'gb',
    'kr', 'th', 'vn', 'sg', 'my', 'id', 'tr', 'ru', 'eg', 'za', 'ar', 'cl', 'nz',
    'ch', 'nl', 'se', 'no', 'fi', 'dk', 'pl', 'pt', 'gr', 'cz', 'hu', 'at', 'be',
    'ie', 'hr', 'ma', 'ae', 'sa', 'il', 'qa', 'ph', 'pk', 'bd', 'np', 'lk', 'mm',
    'kh', 'la', 'mn', 'kz', 'uz', 'ge', 'am', 'az', 'ua', 'by', 'ro', 'bg', 'sk',
    'si', 'ee', 'lv', 'lt', 'lu', 'li', 'is', 'mt', 'cy', 'jo', 'lb', 'om', 'kw',
    'bh', 'ye', 'iq', 'sy', 'af', 'ir', 'ps', 'tn', 'dz', 'ly', 'sd', 'et', 'ke',
    'tz', 'ug', 'gh', 'ng', 'ci', 'sn', 'cm', 'ao', 'mz', 'zw', 'bw', 'na', 'zm',
    'mw', 'mg', 'mu', 'sc', 'fj', 'ws', 'to', 'vu', 'sb', 'pg', 'tl', 'bn', 'bt',
    'mv', 'mc', 'sm', 'va', 'ad', 'gi', 'gl', 'fo', 'gg', 'je', 'im', 'bm', 'ky',
    'bb', 'bs', 'jm', 'tt', 'lc', 'vc', 'gd', 'ag', 'dm', 'kn', 'ht', 'do', 'cu',
    'pr', 'gp', 'mq', 'aw', 'cw', 'sx', 'mf', 'bl', 'ai', 'ms', 'tc', 'vg', 'vi',
    'pm', 'gf', 'sr', 'gy', 'py', 'uy', 'bo', 'pe', 'ec', 'co', 've', 'pa', 'cr',
    'ni', 'hn', 'sv', 'gt', 'bz', 'sj', 'aq'
  ];

  static String getLocalizedCountryName(String countryCode) {
    return 'country.$countryCode'.tr();
  }
}
