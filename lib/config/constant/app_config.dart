// ignore_for_file: non_constant_identifier_names

import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  static String API_URL = dotenv.get('BASE_URL');
  static String API_KEY = dotenv.get('API_KEY');
  static String SOCKET_URL = dotenv.get('SOCKET_URL');
  static String HOLIDAY_API_KEY = dotenv.get('HOLIDAY_API_KEY');
  static String GOOGLE_MAPS_API_KEY = dotenv.get('GOOGLE_MAPS_API_KEY');
  static String PEXELS_API_KEY = dotenv.get('PEXELS_API_KEY');
  static List<String> SCOPES = dotenv.get('SCOPES', fallback: "openid,email,profile").split(',').map((item) => item.trim()).toList();

  //; AMADEUS ENV
  // AMADEUS_API_KEY=Z2Y0Y2QzYjAtM2Q5Ny00YjA3LWI1NzItZDYxN2E4YzI3ZGI5
  static String AMADEUS_API_KEY = dotenv.get('AMADEUS_API_KEY');
  // AMADEUS_API_SECRET=ZDYxYjY5YzAtM2Q5Ny00YjA3LWI1NzItZDYxN2E4YzI3ZGI5
  static String AMADEUS_API_SECRET = dotenv.get('AMADEUS_API_SECRET');
  // AMADEUS_API_URL=https://test.api.amadeus.com/v1
  static String AMADEUS_API_URL = dotenv.get('AMADEUS_API_URL');

  static const String notificationRoleReceivePort = 'FAMILY-ROLE-NOTIFICATION-CHANNEL-ID';
}
