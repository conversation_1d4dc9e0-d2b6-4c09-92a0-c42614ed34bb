import 'dart:io' show Platform;

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';

const DAY_FORMAT = 'dd/MM/yyyy';
const DAY_MONTH_FORMAT = 'dd/MM';
const HOUR_FORMAT = 'HH:mm';
const FULL_DAY_FORMAT = 'EEEE, MMM d';

const LIMIT = 20;

RegExp spaceRegExp = RegExp(' +');

const REMIND_TIME = 15;

enum ListType { Shopping, Todo, Trip, Other }

extension ListTypeExtension on ListType {
  String typeStr() {
    switch (this) {
      case ListType.Shopping:
        return 'shopping';
      case ListType.Todo:
        return 'todo';
      case ListType.Trip:
        return 'trip';
      case ListType.Other:
        return 'other';
      default:
        return '';
    }
  }

  /// Convert ListType to index (0-3)
  int toIndex() {
    switch (this) {
      case ListType.Shopping:
        return 0;
      case ListType.Todo:
        return 1;
      case ListType.Trip:
        return 2;
      case ListType.Other:
        return 3;
    }
  }
}

/// Convert index to ListType. Returns null if index is -1 (no filter)
ListType? indexToListType(int index) {
  switch (index) {
    case 0:
      return ListType.Shopping;
    case 1:
      return ListType.Todo;
    case 2:
      return ListType.Trip;
    case 3:
      return ListType.Other;
    default:
      return null;
  }
}

enum StatusMember { pending, accepted, rejected }

abstract class Role {
  static const String editor = 'editor';
  static const String viewer = 'viewer';
  static const String owner = 'owner';

  static const List<String> roles = [editor, viewer];
}


abstract class TimeType {
  static const String am = 'AM';
  static const String pm = 'PM';
}

abstract class FamilyMemberRelationship {
  static var dad = LocaleKeys.dad.tr();
  static var mother = LocaleKeys.mom.tr();
  static var son = LocaleKeys.son.tr();
  static var daughter = LocaleKeys.daughter.tr();

  static var relationship = [dad, mother, son, daughter];
}

abstract class SocketEvent {
  static String MESSAGE = 'ai_chat_repond';
  static String SEND_MESSAGE = 'ai_chat';
  static String MESSAGE_DELETED = '';

  static const String TRIP = 'activity';
  static const String EVENT = 'event';
  static const String LIST = 'list';
  static const String TASK_ASSIGNED = 'task_assigned';
  static const String MEMBER = 'member';
  static const String THREAD = 'thread';

  static bool isAiMess(String command) {
    if (command == MESSAGE) {
      return true;
    }

    return false;
  }
}

abstract class MessageType {
  static const String TRIP = 'trip';
  static const String EVENT = 'event';
  static const String LIST = 'list';
  static const String MEMBER = 'member';
}
final premiums = Platform.isIOS
    ? {'com.gencare.family.premium.1month', 'com.gencare.family.premium.1year'}
    : Platform.isAndroid
        ? {'com.gencare.family.premium'}
        : <String>{};

const premiumBasePlans = {'com-gencare-family-premium-1month': 'month', 'com-gencare-family-premium-1year': 'year'};
abstract class ThreadMessageType {
  static const String TEXT = 'text';
  static const String LOCATION = 'location';
  static const String IMAGE = 'image';
  static const String FILE = 'file';
  static const String VIDEO = 'video';
  static const String AUDIO = 'audio';
  static const String POLL = 'poll';
  static const String EVENT = 'event';
  static const String TASK = 'task';
  static const String LIST = 'list';

  /// List of all supported message types
  static const List<String> allTypes = [
    TEXT,
    LOCATION,
    IMAGE,
    FILE,
    VIDEO,
    AUDIO,
    POLL,
    EVENT,
    TASK,
    LIST,
  ];

  /// Check if a message type is valid
  static bool isValidType(String type) {
    return allTypes.contains(type);
  }

  /// Check if a message type is a media type
  static bool isMediaType(String type) {
    return [IMAGE, FILE, VIDEO, AUDIO].contains(type);
  }

  /// Check if a message type is an interactive type
  static bool isInteractiveType(String type) {
    return [POLL, EVENT, TASK].contains(type);
  }
}

/// Constants for main bottom navigation tab indices
/// These correspond to the order of routes in MainRoute children in app_route.dart
abstract class TabConstants {
  static const int homeTab = 0;
  static const int calendarTab = 1;
  static const int checkListTab = 2;
  static const int profileTab = 3;
  
  /// Total number of tabs in main navigation
  static const int tabCount = 4;
  
  /// Validate if a tab index is valid
  static bool isValidTabIndex(int index) {
    return index >= 0 && index < tabCount;
  }
  
  /// Get tab name for debugging purposes
  static String getTabName(int index) {
    switch (index) {
      case homeTab:
        return 'Home';
      case calendarTab:
        return 'Calendar';
      case checkListTab:
        return 'CheckList';
      case profileTab:
        return 'Profile';
      default:
        return 'Unknown';
    }
  }
}
