const List<int> setupReminderMinutes = [
  0,
  5,
  10,
  15,
  30,
  60,
  120,
];

/// Aliases for legacy or alternate timezone names to canonical IANA names.
const Map<String, String> timezoneAliases = {
  'Asia/Saigon': 'Asia/Ho_Chi_Minh',
  'Asia/Calcutta': 'Asia/Kolkata',
  'Asia/Chongqing': 'Asia/Shanghai',
  'Asia/Chungking': 'Asia/Shanghai',
  'Asia/Katmandu': 'Asia/Kathmandu',
  'Asia/Ujung_Pandang': 'Asia/Makassar',
  'Asia/Ulan_Bator': 'Asia/Ulaanbaatar',
  'Europe/Kiev': 'Europe/Kyiv',
  'Europe/Belfast': 'Europe/London',
  'Europe/Tiraspol': 'Europe/Chisinau',
  'Pacific/Truk': 'Pacific/Chuuk',
  'Pacific/Ponape': 'Pacific/Pohnpei',
  'Pacific/Samoa': 'Pacific/Pago_Pago',
  'US/Eastern': 'America/New_York',
  'US/Central': 'America/Chicago',
  'US/Mountain': 'America/Denver',
  'US/Pacific': 'America/Los_Angeles',
  'Etc/Greenwich': 'Etc/GMT',
  'Etc/GMT0': 'Etc/GMT',
  'Etc/GMT-0': 'Etc/GMT',
  'Etc/GMT+0': 'Etc/GMT',
  'Zulu': 'Etc/UTC',
};

/// Combined IANA <-> Windows timezone mapping table
const List<Map<String, String>> ianaWindowsTimeZoneTable = [
  {'iana': 'Etc/UTC', 'windows': 'UTC'},
  {'iana': 'Etc/GMT', 'windows': 'GMT Standard Time'},
  {'iana': 'Europe/London', 'windows': 'GMT Standard Time'},
  {'iana': 'Europe/Berlin', 'windows': 'W. Europe Standard Time'},
  {'iana': 'Europe/Paris', 'windows': 'Romance Standard Time'},
  {'iana': 'Europe/Kyiv', 'windows': 'FLE Standard Time'},
  {'iana': 'Europe/Moscow', 'windows': 'Russian Standard Time'},
  {'iana': 'Asia/Ho_Chi_Minh', 'windows': 'SE Asia Standard Time'},
  {'iana': 'Asia/Bangkok', 'windows': 'SE Asia Standard Time'},
  {'iana': 'Asia/Singapore', 'windows': 'Singapore Standard Time'},
  {'iana': 'Asia/Tokyo', 'windows': 'Tokyo Standard Time'},
  {'iana': 'Asia/Shanghai', 'windows': 'China Standard Time'},
  {'iana': 'Asia/Hong_Kong', 'windows': 'China Standard Time'},
  {'iana': 'Asia/Kolkata', 'windows': 'India Standard Time'},
  {'iana': 'Asia/Jakarta', 'windows': 'SE Asia Standard Time'},
  {'iana': 'Asia/Seoul', 'windows': 'Korea Standard Time'},
  {'iana': 'Asia/Ulaanbaatar', 'windows': 'Ulaanbaatar Standard Time'},
  {'iana': 'Australia/Sydney', 'windows': 'AUS Eastern Standard Time'},
  {'iana': 'Australia/Perth', 'windows': 'W. Australia Standard Time'},
  {'iana': 'America/New_York', 'windows': 'Eastern Standard Time'},
  {'iana': 'America/Chicago', 'windows': 'Central Standard Time'},
  {'iana': 'America/Denver', 'windows': 'Mountain Standard Time'},
  {'iana': 'America/Los_Angeles', 'windows': 'Pacific Standard Time'},
  {'iana': 'America/Toronto', 'windows': 'Eastern Standard Time'},
  {'iana': 'America/Vancouver', 'windows': 'Pacific Standard Time'},
  {'iana': 'America/Sao_Paulo', 'windows': 'E. South America Standard Time'},
  {'iana': 'America/Mexico_City', 'windows': 'Central Standard Time (Mexico)'},
  {'iana': 'America/Bogota', 'windows': 'SA Pacific Standard Time'},
  {'iana': 'Africa/Johannesburg', 'windows': 'South Africa Standard Time'},
  {'iana': 'Africa/Cairo', 'windows': 'Egypt Standard Time'},
  {'iana': 'Africa/Lagos', 'windows': 'W. Central Africa Standard Time'},
];

/// Helper maps for fast lookup (generated at runtime)
final Map<String, String> ianaToWindowsTimeZone = {
  for (final entry in ianaWindowsTimeZoneTable) entry['iana']!: entry['windows']!,
};
final Map<String, String> windowsToIanaMap = {
  for (final entry in ianaWindowsTimeZoneTable) entry['windows']!: entry['iana']!,
};

/// Syncfusion Calendar Windows <-> IANA <-> Offset Mapping Table
const List<Map<String, String>> sfCalendarTimeZoneTable = [
  // Windows, IANA, Offset
  {'windows': 'Samoa Standard Time', 'iana': 'Pacific/Apia', 'offset': 'GMT-13:00'},
  {'windows': 'Dateline Standard Time', 'iana': 'Etc/GMT+12', 'offset': 'GMT-12:00'},
  {'windows': 'UTC-11', 'iana': 'Pacific/Midway', 'offset': 'GMT-11:00'},
  {'windows': 'Hawaiian Standard Time', 'iana': 'Pacific/Honolulu', 'offset': 'GMT-10:00'},
  {'windows': 'Alaskan Standard Time', 'iana': 'America/Anchorage', 'offset': 'GMT-09:00'},
  {'windows': 'Pacific Standard Time', 'iana': 'America/Los_Angeles', 'offset': 'GMT-08:00'},
  {'windows': 'Pacific Standard Time (Mexico)', 'iana': 'America/Santa_Isabel', 'offset': 'GMT-08:00'},
  {'windows': 'Mountain Standard Time', 'iana': 'America/Denver', 'offset': 'GMT-07:00'},
  {'windows': 'Mountain Standard Time (Mexico)', 'iana': 'America/Chihuahua', 'offset': 'GMT-07:00'},
  {'windows': 'US Mountain Standard Time', 'iana': 'America/Phoenix', 'offset': 'GMT-07:00'},
  {'windows': 'Canada Central Standard Time', 'iana': 'America/Regina', 'offset': 'GMT-06:00'},
  {'windows': 'Central America Standard Time', 'iana': 'America/Guatemala', 'offset': 'GMT-06:00'},
  {'windows': 'Central Standard Time', 'iana': 'America/Chicago', 'offset': 'GMT-06:00'},
  {'windows': 'Eastern Standard Time', 'iana': 'America/New_York', 'offset': 'GMT-05:00'},
  {'windows': 'SA Pacific Standard Time', 'iana': 'America/Bogota', 'offset': 'GMT-05:00'},
  {'windows': 'US Eastern Standard Time', 'iana': 'America/Indianapolis', 'offset': 'GMT-05:00'},
  {'windows': 'Venezuela Standard Time', 'iana': 'America/Caracas', 'offset': 'GMT-04:30'},
  {'windows': 'Atlantic Standard Time', 'iana': 'America/Halifax', 'offset': 'GMT-04:00'},
  {'windows': 'Central Brazilian Standard Time', 'iana': 'America/Cuiaba', 'offset': 'GMT-04:00'},
  {'windows': 'Pacific SA Standard Time', 'iana': 'America/Santiago', 'offset': 'GMT-04:00'},
  {'windows': 'Paraguay Standard Time', 'iana': 'America/Asuncion', 'offset': 'GMT-04:00'},
  {'windows': 'SA Western Standard Time', 'iana': 'America/La_Paz', 'offset': 'GMT-04:00'},
  {'windows': 'Newfoundland Standard Time', 'iana': 'America/St_Johns', 'offset': 'GMT-03:30'},
  {'windows': 'Bahia Standard Time', 'iana': 'America/Bahia', 'offset': 'GMT-03:00'},
  {'windows': 'Argentina Standard Time', 'iana': 'America/Buenos_Aires', 'offset': 'GMT-03:00'},
  {'windows': 'E. South America Standard Time', 'iana': 'America/Sao_Paulo', 'offset': 'GMT-03:00'},
  {'windows': 'Greenland Standard Time', 'iana': 'America/Godthab', 'offset': 'GMT-03:00'},
  {'windows': 'Montevideo Standard Time', 'iana': 'America/Montevideo', 'offset': 'GMT-03:00'},
  {'windows': 'SA Eastern Standard Time', 'iana': 'America/Cayenne', 'offset': 'GMT-03:00'},
  {'windows': 'UTC-02', 'iana': 'America/Noronha', 'offset': 'GMT-02:00'},
  {'windows': 'Azores Standard Time', 'iana': 'Atlantic/Azores', 'offset': 'GMT-01:00'},
  {'windows': 'Cape Verde Standard Time', 'iana': 'Atlantic/Cape_Verde', 'offset': 'GMT-01:00'},
  {'windows': 'GMT Standard Time', 'iana': 'Europe/London', 'offset': 'GMT+00:00'},
  {'windows': 'Greenwich Standard Time', 'iana': 'Atlantic/Reykjavik', 'offset': 'GMT+00:00'},
  {'windows': 'Morocco Standard Time', 'iana': 'Africa/Casablanca', 'offset': 'GMT+00:00'},
  {'windows': 'UTC', 'iana': 'America/Danmarkshavn', 'offset': 'GMT+00:00'},
  {'windows': 'Central Europe Standard Time', 'iana': 'Europe/Budapest', 'offset': 'GMT+01:00'},
  {'windows': 'Central European Standard Time', 'iana': 'Europe/Warsaw', 'offset': 'GMT+01:00'},
  {'windows': 'Namibia Standard Time', 'iana': 'Africa/Windhoek', 'offset': 'GMT+01:00'},
  {'windows': 'Romance Standard Time', 'iana': 'Europe/Paris', 'offset': 'GMT+01:00'},
  {'windows': 'W. Central Africa Standard Time', 'iana': 'Africa/Lagos', 'offset': 'GMT+01:00'},
  {'windows': 'W. Europe Standard Time', 'iana': 'Europe/Berlin', 'offset': 'GMT+01:00'},
  {'windows': 'Egypt Standard Time', 'iana': 'Africa/Cairo', 'offset': 'GMT+02:00'},
  {'windows': 'FLE Standard Time', 'iana': 'Europe/Kiev', 'offset': 'GMT+02:00'},
  {'windows': 'GTB Standard Time', 'iana': 'Europe/Bucharest', 'offset': 'GMT+02:00'},
  {'windows': 'Israel Standard Time', 'iana': 'Asia/Jerusalem', 'offset': 'GMT+02:00'},
  {'windows': 'Libya Standard Time', 'iana': 'Africa/Tripoli', 'offset': 'GMT+02:00'},
  {'windows': 'Middle East Standard Time', 'iana': 'Asia/Beirut', 'offset': 'GMT+02:00'},
  {'windows': 'South Africa Standard Time', 'iana': 'Africa/Johannesburg', 'offset': 'GMT+02:00'},
  {'windows': 'Syria Standard Time', 'iana': 'Asia/Damascus', 'offset': 'GMT+02:00'},
  {'windows': 'Turkey Standard Time', 'iana': 'Europe/Istanbul', 'offset': 'GMT+02:00'},
  {'windows': 'Arab Standard Time', 'iana': 'Asia/Riyadh', 'offset': 'GMT+03:00'},
  {'windows': 'Arabic Standard Time', 'iana': 'Asia/Baghdad', 'offset': 'GMT+03:00'},
  {'windows': 'Belarus Standard Time', 'iana': 'Europe/Minsk', 'offset': 'GMT+03:00'},
  {'windows': 'E. Africa Standard Time', 'iana': 'Africa/Nairobi', 'offset': 'GMT+03:00'},
  {'windows': 'Jordan Standard Time', 'iana': 'Asia/Amman', 'offset': 'GMT+03:00'},
  {'windows': 'Kaliningrad Standard Time', 'iana': 'Europe/Kaliningrad', 'offset': 'GMT+03:00'},
  {'windows': 'Iran Standard Time', 'iana': 'Asia/Tehran', 'offset': 'GMT+03:30'},
  {'windows': 'Arabian Standard Time', 'iana': 'Etc/GMT-4', 'offset': 'GMT+04:00'},
  {'windows': 'Azerbaijan Standard Time', 'iana': 'Asia/Baku', 'offset': 'GMT+04:00'},
  {'windows': 'Caucasus Standard Time', 'iana': 'Asia/Yerevan', 'offset': 'GMT+04:00'},
  {'windows': 'Georgian Standard Time', 'iana': 'Asia/Tbilisi', 'offset': 'GMT+04:00'},
  {'windows': 'Mauritius Standard Time', 'iana': 'Indian/Mauritius', 'offset': 'GMT+04:00'},
  {'windows': 'Russia Time Zone 3', 'iana': 'Europe/Samara', 'offset': 'GMT+04:00'},
  {'windows': 'Russian Standard Time', 'iana': 'Europe/Moscow', 'offset': 'GMT+04:00'},
  {'windows': 'Afghanistan Standard Time', 'iana': 'Asia/Kabul', 'offset': 'GMT+04:30'},
  {'windows': 'Pakistan Standard Time', 'iana': 'Asia/Karachi', 'offset': 'GMT+05:00'},
  {'windows': 'West Asia Standard Time', 'iana': 'Asia/Tashkent', 'offset': 'GMT+05:00'},
  {'windows': 'India Standard Time', 'iana': 'Asia/Calcutta', 'offset': 'GMT+05:30'},
  {'windows': 'Sri Lanka Standard Time', 'iana': 'Asia/Colombo', 'offset': 'GMT+05:30'},
  {'windows': 'Nepal Standard Time', 'iana': 'Asia/Kathmandu', 'offset': 'GMT+05:45'},
  {'windows': 'Bangladesh Standard Time', 'iana': 'Asia/Dhaka', 'offset': 'GMT+06:00'},
  {'windows': 'Central Asia Standard Time', 'iana': 'Asia/Almaty', 'offset': 'GMT+06:00'},
  {'windows': 'Ekaterinburg Standard Time', 'iana': 'Asia/Yekaterinburg', 'offset': 'GMT+06:00'},
  {'windows': 'Myanmar Standard Time', 'iana': 'Asia/Rangoon', 'offset': 'GMT+06:30'},
  {'windows': 'SE Asia Standard Time', 'iana': 'Asia/Bangkok', 'offset': 'GMT+07:00'},
  {'windows': 'N. Central Asia Standard Time', 'iana': 'Asia/Novosibirsk', 'offset': 'GMT+07:00'},
  {'windows': 'China Standard Time', 'iana': 'Asia/Shanghai', 'offset': 'GMT+08:00'},
  {'windows': 'North Asia Standard Time', 'iana': 'Asia/Krasnoyarsk', 'offset': 'GMT+08:00'},
  {'windows': 'Singapore Standard Time', 'iana': 'Asia/Singapore', 'offset': 'GMT+08:00'},
  {'windows': 'Taipei Standard Time', 'iana': 'Asia/Taipei', 'offset': 'GMT+08:00'},
  {'windows': 'Ulaanbaatar Standard Time', 'iana': 'Asia/Ulaanbaatar', 'offset': 'GMT+08:00'},
  {'windows': 'W. Australia Standard Time', 'iana': 'Australia/Perth', 'offset': 'GMT+08:00'},
  {'windows': 'Korea Standard Time', 'iana': 'Asia/Seoul', 'offset': 'GMT+09:00'},
  {'windows': 'North Asia East Standard Time', 'iana': 'Asia/Irkutsk', 'offset': 'GMT+09:00'},
  {'windows': 'Tokyo Standard Time', 'iana': 'Asia/Tokyo', 'offset': 'GMT+09:00'},
  {'windows': 'AUS Central Standard Time', 'iana': 'Australia/Darwin', 'offset': 'GMT+09:30'},
  {'windows': 'Cen. Australia Standard Time', 'iana': 'Australia/Adelaide', 'offset': 'GMT+09:30'},
  {'windows': 'AUS Eastern Standard Time', 'iana': 'Australia/Sydney', 'offset': 'GMT+10:00'},
  {'windows': 'E. Australia Standard Time', 'iana': 'Australia/Brisbane', 'offset': 'GMT+10:00'},
  {'windows': 'Tasmania Standard Time', 'iana': 'Australia/Hobart', 'offset': 'GMT+10:00'},
  {'windows': 'West Pacific Standard Time', 'iana': 'Pacific/Port_Moresby', 'offset': 'GMT+10:00'},
  {'windows': 'Yakutsk Standard Time', 'iana': 'Asia/Yakutsk', 'offset': 'GMT+10:00'},
  {'windows': 'Central Pacific Standard Time', 'iana': 'Pacific/Guadalcanal', 'offset': 'GMT+11:00'},
  {'windows': 'Russia Time Zone 10', 'iana': 'Asia/Srednekolymsk', 'offset': 'GMT+11:00'},
  {'windows': 'Vladivostok Standard Time', 'iana': 'Asia/Vladivostok', 'offset': 'GMT+10:00'},
  {'windows': 'Fiji Standard Time', 'iana': 'Pacific/Fiji', 'offset': 'GMT+12:00'},
  {'windows': 'Magadan Standard Time', 'iana': 'Asia/Magadan', 'offset': 'GMT+12:00'},
  {'windows': 'New Zealand Standard Time', 'iana': 'Pacific/Auckland', 'offset': 'GMT+12:00'},
  {'windows': 'Russia Time Zone 11', 'iana': 'Asia/Kamchatka', 'offset': 'GMT+12:00'},
  {'windows': 'UTC+12', 'iana': 'Pacific/Tarawa', 'offset': 'GMT+12:00'},
  {'windows': 'Tonga Standard Time', 'iana': 'Pacific/Tongatapu', 'offset': 'GMT+13:00'},
  {'windows': 'Line Islands Standard Time', 'iana': 'Pacific/Kiritimati', 'offset': 'GMT+14:00'},
];

/// priorityTimezones
/// Maps each UTC offset to a preferred IANA timezone name for user selection.
/// - Used to ensure only one timezone per offset is shown in dropdowns.
/// - Update this table if you want to change the default city for an offset.
/// - Source: IANA Time Zone Database (https://www.iana.org/time-zones)
/// - To update: Add/remove entries as needed, or use a script to regenerate.
/// Preferred IANA timezone for each UTC offset (for unique offset selection)
const Map<String, String> priorityTimezones = {
  'UTC-12:00': 'Etc/GMT+12',
  'UTC-11:00': 'Pacific/Pago_Pago',
  'UTC-10:00': 'Pacific/Honolulu',
  'UTC-09:30': 'Pacific/Marquesas',
  'UTC-09:00': 'America/Adak',
  'UTC-08:00': 'America/Los_Angeles',
  'UTC-07:00': 'America/Denver',
  'UTC-06:00': 'America/Chicago',
  'UTC-05:00': 'America/New_York',
  'UTC-04:00': 'America/Toronto',
  'UTC-03:30': 'America/St_Johns',
  'UTC-03:00': 'America/Sao_Paulo',
  'UTC-02:00': 'Atlantic/South_Georgia',
  'UTC-01:00': 'Atlantic/Azores',
  'UTC+00:00': 'Europe/London',
  'UTC+01:00': 'Europe/Berlin',
  'UTC+02:00': 'Europe/Athens',
  'UTC+03:00': 'Europe/Moscow',
  'UTC+03:30': 'Asia/Tehran',
  'UTC+04:00': 'Asia/Dubai',
  'UTC+04:30': 'Asia/Kabul',
  'UTC+05:00': 'Asia/Karachi',
  'UTC+05:30': 'Asia/Kolkata',
  'UTC+05:45': 'Asia/Kathmandu',
  'UTC+06:00': 'Asia/Dhaka',
  'UTC+06:30': 'Asia/Yangon',
  'UTC+07:00': 'Asia/Bangkok',
  'UTC+08:00': 'Asia/Shanghai',
  'UTC+08:30': 'Asia/Pyongyang',
  'UTC+08:45': 'Australia/Eucla',
  'UTC+09:00': 'Asia/Tokyo',
  'UTC+09:30': 'Australia/Adelaide',
  'UTC+10:00': 'Australia/Sydney',
  'UTC+10:30': 'Australia/Lord_Howe',
  'UTC+11:00': 'Pacific/Guadalcanal',
  'UTC+12:00': 'Pacific/Auckland',
  'UTC+12:45': 'Pacific/Chatham',
  'UTC+13:00': 'Pacific/Apia',
  'UTC+14:00': 'Pacific/Kiritimati',
};
