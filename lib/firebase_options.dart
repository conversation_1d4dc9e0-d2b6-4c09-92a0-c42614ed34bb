// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA72i3-9omJqMzb-V9UXpaLisn03yZyxGk',
    appId: '1:944633290096:android:801b4fe69892b548f283b5',
    messagingSenderId: '944633290096',
    projectId: 'fl-smarthome',
    storageBucket: 'fl-smarthome.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCNGovjxS_jPnR97Liv6LPFjRWxqLpPmto',
    appId: '1:944633290096:ios:a5eb86936bf042dbf283b5',
    messagingSenderId: '944633290096',
    projectId: 'fl-smarthome',
    storageBucket: 'fl-smarthome.firebasestorage.app',
    androidClientId: '944633290096-8lg4n3980kskcd1erkksggkeujhvp3lt.apps.googleusercontent.com',
    iosClientId: '944633290096-vkk616j60gvvq1akmf13m6vvr5kcagjg.apps.googleusercontent.com',
    iosBundleId: 'com.gencare.family',
  );
}
